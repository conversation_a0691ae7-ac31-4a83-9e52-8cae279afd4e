Guidance from <PERSON> at Pragma:

We manually updated your chat and lobbies custom dashboard jsons to set the id property to null and remove the uuid property.  These were things we found we have to do to make grafana accept these dashboard yaml files.  Sorry, we haven't gotten around to making documentation for how to make custom dashboards yet, as these steps would have been in there, so you are the guinea pigs.
The dev grafana folder now has your custom chat and lobbies dashbaords, but the <<shardid>> template in the label name needs an upper case "I" such as <<shardId>>
Lastly, from looking at those two custom dashboards, they show datasource errors.  If you are just making dashboards that show events from your pragma engine metrics, you should tag along with our default datasource which uses the <<datasourceName>> template in your custom dashboard json (take a look at one of the default dashboards for an example). 


Ok, we finished migrating the other remain shards (platform, qa, staging, live).  You will need to run a deploy job for each shard and only deploy the grafana dashboard by only setting a value in the "Available metrics-dashboards versions" dropdown (we did latest from main for the dev shard) and leaving all other dropdowns empty (also do not check "restart" for the last option).
