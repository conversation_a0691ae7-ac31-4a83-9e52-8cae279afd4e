{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "The Friend service allows issuing friend requests and the the maintenance of friend lists for each player. It also allows you to set players' presences. This dashboard enables you to monitor some key observables.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 19, "title": "RPCs", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "Number of client related RPC requests per minute received by the Friend service to retrieve, invite, and remove friends. This also includes calls to setting a player's presence.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"getFriendListAndRegisterOnlineV1\"}[1m])", "interval": "", "legendFormat": "Get Friend List - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"sendFriendInviteBySocialIdV1\"}[1m])", "hide": false, "legendFormat": "Send Friend Invite by Social Id - {{nodeName}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"sendFriendInviteV1\"}[1m])", "hide": false, "legendFormat": "Send Friend Invite - {{nodeName}}", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"removeFriendV1\"}[1m])", "hide": false, "interval": "", "legendFormat": "Remove Friend - {{nodeName}}", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"setPresenceV1\"}[1m])", "hide": false, "interval": "", "legendFormat": "Set Presence (Player) - {{nodeName}}", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"setPresencePartnerV1\"}[1m])", "hide": false, "interval": "", "legendFormat": "Set Presence (Partner) - {{nodeName}}", "range": true, "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"respondToFriendInviteV1\"}[1m])", "hide": false, "interval": "", "legendFormat": "Respond to Friend Invite - {{nodeName}}", "range": true, "refId": "G"}], "title": "Client RPC requests per minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "Number of internal RPC requests per minute made by the platform to maintain friend lists and query presence.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"friendListUpdateServiceV1\"}[1m])", "legendFormat": "Friend List Update - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"getPresencesAndRegisterOnlineServiceV1\"}[1m])", "hide": false, "legendFormat": "Get Presences - {{nodeName}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"deleteFriendDataServiceV1\"}[1m])", "hide": false, "legendFormat": "Delete Friend Data - {{nodeName}}", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(pragma_engine_rpc_incoming_seconds_count{method=\"getPersonalDataSectionsServiceV1\"}[1m])", "hide": false, "legendFormat": "Get Personal Data Sections (GDPR) - {{nodeName}}", "range": true, "refId": "D"}], "title": "Service to Service RPC requests per minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "The 99th percentile value for the duration of incoming RPC requests handled by the Friends service collected within a minute. In all, 99% of the samples taken within that minute will have a shorter duration than the plotted value, the other 1% an equal of longer duration.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"log": 10, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 23, "interval": "30", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "ninetyninth:pragma_engine_rpc_incoming_seconds_friends:histogram", "interval": "", "legendFormat": "{{method}} - {{sessionType}}", "range": true, "refId": "A"}], "title": "99th percentile RPC request duration over a minute interval [log scale]", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 17, "panels": [{"collapsed": true, "datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "The Friend service uses several caches for efficient lookups. When sampled, the number of entries in each Friend service related cache is plotted in this graph. Note this is NOT the memory footprint taken up by these caches.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "pragma_caches_cacheSize{service=\"FriendService\", cacheName=\"friend\"}", "legendFormat": "Friend - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "pragma_caches_cacheSize{service=\"FriendService\", cacheName=\"accountLookupResultBySocialId\"}", "hide": false, "legendFormat": "Account Lookup Result - {{nodeName}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "pragma_caches_cacheSize{service=\"FriendService\", cacheName=\"socialIdByFullDisplayName\"}", "hide": false, "legendFormat": "Social Id By Full Display Name - {{nodeName}}", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "pragma_caches_cacheSize{service=\"FriendService\", cacheName=\"presence\"}", "hide": false, "legendFormat": "Presence - {{nodeName}}", "range": true, "refId": "D"}], "title": "Friend service cache sizes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "The Friend service uses several caches for efficient lookups. This graph pertains to the friend cache and indicates the number of hits (Cache Access) and misses (Cache Misses) per minute. Entries in cache are removed when the cache has become too large, controlled by the sizeBeforeExpirationEnforced config value, and the cache record has not been accessed for a time exceeding the maxTimeBetweenAccessMillis configuration value. The number of times this is happening per minute is indicated by the Entries Swept curve.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheAccess_total{service=\"FriendService\", cacheName=\"friend\"}[1m]))", "legendFormat": "Cache Access - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheMiss_total{service=\"FriendService\", cacheName=\"friend\"}[1m]))", "hide": false, "legendFormat": "<PERSON><PERSON> - {{nodeName}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheSweepItems_total{service=\"FriendService\", cacheName=\"friend\"}[1m]))", "hide": false, "legendFormat": "Items Swept - {{nodeName}}", "range": true, "refId": "C"}], "title": "Friend Service cache operations per minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "The Friend service uses several caches for efficient lookups. This graph pertains to the socialIdByFullDisplayName cache and indicates the number of hits (Cache Access) and misses (Cache Misses) per minute. Entries in cache are removed when the cache has become too large, controlled by the sizeBeforeExpirationEnforced config value, and the cache record has not been accessed for a time exceeding the maxTimeBetweenAccessMillis configuration value. The number of times this is happening per minute is indicated by the Entries Swept curve.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheAccess_total{service=\"FriendService\", cacheName=\"socialIdByFullDisplayName\"}[1m]))", "legendFormat": "Cache Access - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheMiss_total{service=\"FriendService\", cacheName=\"socialIdByFullDisplayName\"}[1m]))", "hide": false, "legendFormat": "<PERSON><PERSON> - {{nodeName}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheSweepItems_total{service=\"FriendService\", cacheName=\"socialIdByFullDisplayName\"}[1m]))", "hide": false, "legendFormat": "Items Swept - {{nodeName}}", "range": true, "refId": "C"}], "title": "Identity cache operations per minute - Social ID by display name", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "The Friend service uses several caches for efficient lookups. This graph pertains to the presence cache and indicates the number of hits (Cache Access) and sweeps (Cache Removes) per minute. Entries are swept when players log out.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 26}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheAccess_total{service=\"FriendService\", cacheName=\"presence\"}[1m]))", "legendFormat": "Cache Access - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheSweepItems_total{service=\"FriendService\", cacheName=\"presence\"}[1m]))", "hide": false, "legendFormat": "<PERSON>ache Remove - {{nodeName}}", "range": true, "refId": "B"}], "title": "Presence cache operations per minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "The Friend service uses several caches for efficient lookups. This graph pertains to the accountLookupResultBySocialId cache and indicates the number of hits (Cache Access) and misses (Cache Misses) per minute. Entries in cache are removed when the cache has become too large, controlled by the sizeBeforeExpirationEnforced config value, and the cache record has not been accessed for a time exceeding the maxTimeBetweenAccessMillis configuration value. The number of times this is happening per minute is indicated by the Entries Swept curve.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 34}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheAccess_total{service=\"FriendService\", cacheName=\"accountLookupResultBySocialId\"}[1m]))", "legendFormat": "Cache Access - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheMiss_total{service=\"FriendService\", cacheName=\"accountLookupResultBySocialId\"}[1m]))", "hide": false, "legendFormat": "<PERSON><PERSON> - {{nodeName}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "sum by(nodeName) (increase(pragma_caches_cacheSweepItems_total{service=\"FriendService\", cacheName=\"accountLookupResultBySocialId\"}[1m]))", "hide": false, "legendFormat": "Items Swept - {{nodeName}}", "range": true, "refId": "C"}], "title": "Identity cache operations per minute - Account lookup", "type": "timeseries"}], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 15, "panels": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "Number of writes per minute to the database to update a player's friend list.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 43}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(service_database_seconds_count{method=\"writeFriendListUpdates\"}[1m])", "legendFormat": "Friend List Write - {{nodeName}}", "range": true, "refId": "A"}], "title": "DAO - Write Friend list per minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "description": "Number of reads per minute from the database to retrieve a player's friend list separated by run commands and transactions.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 43}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(service_database_seconds_count{method=\"getFriendList\", executeType=\"runCommand\"}[1m])", "legendFormat": "Run Commands - {{nodeName}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasourceUid"}, "editorMode": "code", "expr": "increase(service_database_seconds_count{method=\"getFriendList\", executeType=\"transact\"}[1m])", "hide": false, "legendFormat": "Transactions - {{nodeName}}", "range": true, "refId": "B"}], "title": "DAO - Get Friend list per minute", "type": "timeseries"}], "title": "Database", "type": "row"}], "refresh": "5s", "revision": 2, "schemaVersion": 38, "style": "dark", "tags": ["generated"], "templating": {"list": [{"current": {"selected": false, "text": "<<datasourceName>>", "value": "<<datasourceName>>"}, "hide": 0, "includeAll": false, "label": "Datasource", "multi": false, "name": "datasourceUid", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "<<shardId>> - friends", "version": 1, "weekStart": ""}