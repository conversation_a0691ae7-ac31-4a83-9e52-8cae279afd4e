testClients:
  count: 1
  startDelay: 10
  config:
    totalPlayerCount: 1000
    #loginRatePerSecond: 15
    #connectionTimeout: 600000
    accountPrefix: LoadSimulator_{{index}}_
    notificationsToPersist:
      - pragma.party.PartyRpc.ReceivedPartyInviteDetailsV1Notification
    pragmaAddress: "https://live.lastflag.nsg.pragmaengine.com:10000"
    partnerAddress: "https://live.lastflag.nsg.pragmaengine.com:10100"
    operatorAddress: "https://live.internal.lastflag.nsg.pragmaengine.com:10200"
defaultNotificationTimeoutSeconds: 30
playerSelectionsTimeoutSeconds: 55
startGameCountdownTimeoutSeconds: 5
matchLengthSeconds: 300
# see LoadSimulatorMainKt.getPhasesForScenario
testScenario: "lobby.fullFlow"
