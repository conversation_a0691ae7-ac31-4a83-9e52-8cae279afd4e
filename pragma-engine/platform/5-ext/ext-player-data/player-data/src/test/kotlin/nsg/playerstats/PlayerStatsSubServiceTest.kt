@file:OptIn(ExperimentalCoroutinesApi::class)

package nsg.playerstats

import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import nsg.playerstats.PlayerStatsRpc.AchievementEntry
import nsg.playerstats.PlayerStatsRpc.CharacterStatEntry
import nsg.playerstats.PlayerStatsRpc.GenericStatEntry
import nsg.playerstats.PlayerStatsRpc.InvalidAchievementIdApplicationError
import nsg.playerstats.PlayerStatsRpc.InvalidCharacterIdApplicationError
import nsg.playerstats.PlayerStatsRpc.InvalidStatIdApplicationError
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import pragma.playerdata.ComponentContainer
import pragma.playerdata.Context
import pragma.playerdata.Entity
import pragma.playerdata.EntityData
import pragma.playerdata.EntityName
import pragma.playerdata.PlayerDataContentLibrary
import pragma.playerdata.PlayerDataSnapshot
import pragma.services.Service
import pragma.utils.TimeProxy
import pragma.utils.coAssertThrowsApplicationErrorException

class PlayerStatsSubServiceTest {
    private val playerDataContentLibrary = mockk<PlayerDataContentLibrary>()
    private val timeProxy = mockk<TimeProxy>()
    private val context = mockk<Context>()
    private val service = mockk<Service>()

    private lateinit var underTest: PlayerStatsSubService

    @BeforeEach
    fun setup() {
        underTest = PlayerStatsSubService(playerDataContentLibrary, timeProxy)
        underTest.registerService(service)
        every { timeProxy.currentEpochMillis() } returns 1000
    }

    @Nested
    inner class UpdateGenericStatsTests {
        @Test
        fun `throws if stat id is invalid`() =
            runTest {
                every { playerDataContentLibrary.getContentData<GenericStatEntry>("GenericStats.json").ids } returns setOf("stat-1")
                val snapshot = PlayerDataSnapshot(EntityData(mutableMapOf()))
                every { context.snapshot } returns snapshot

                coAssertThrowsApplicationErrorException(
                    InvalidStatIdApplicationError.newBuilder().setMessage("Stat id invalid-stat is not supported.").build()
                ) {
                    underTest.updateGenericStats(UpdateGenericStatsRequest(mapOf("invalid-stat" to 10)), context)
                }
            }

        @Test
        fun `updates generic stat`() =
            runTest {
                every { playerDataContentLibrary.getContentData<GenericStatEntry>("GenericStats.json").ids } returns setOf("stat-1")
                val snapshot = PlayerDataSnapshot(EntityData(mutableMapOf()))
                every { context.snapshot } returns snapshot

                underTest.updateGenericStats(UpdateGenericStatsRequest(mapOf("stat-1" to 10)), context)

                val entity = snapshot.getUniqueEntity("GenericStats")!!
                assertEquals(listOf(AggregateStat("stat-1", 10)), entity.getComponents())
            }

        @Test
        fun `updates existing generic stat`() =
            runTest {
                every { playerDataContentLibrary.getContentData<GenericStatEntry>("GenericStats.json").ids } returns setOf("stat-1")
                val entityId = UUID.randomUUID()
                val existing = AggregateStat("stat-1", 5)
                val snapshot =
                    PlayerDataSnapshot(
                        EntityData(
                            mutableMapOf(
                                entityId to
                                    Entity(
                                        EntityName("GenericStats"),
                                        entityId,
                                        mutableListOf(ComponentContainer(UUID.randomUUID(), existing))
                                    )
                            )
                        )
                    )
                every { context.snapshot } returns snapshot

                underTest.updateGenericStats(UpdateGenericStatsRequest(mapOf("stat-1" to 10)), context)

                assertEquals(15, (snapshot.getUniqueEntity("GenericStats")!!.getComponents().single() as AggregateStat).value)
            }
    }

    @Nested
    inner class UpdateCharacterStatsTests {
        @Test
        fun `throws if character id is invalid`() =
            runTest {
                every { playerDataContentLibrary.getContentData<CharacterStatEntry>("CharacterStats.json")["char-1"] } returns null

                coAssertThrowsApplicationErrorException(
                    InvalidCharacterIdApplicationError.newBuilder().setMessage("Character id char-1 is not supported.").build()
                ) {
                    underTest.updateCharacterStats(UpdateCharacterStatsRequest("char-1", mapOf("stat-1" to 10)), context)
                }
            }

        @Test
        fun `throws if stat id is invalid for character`() =
            runTest {
                every {
                    playerDataContentLibrary.getContentData<CharacterStatEntry>("CharacterStats.json")["char-1"]
                } returns CharacterStatEntry.newBuilder().addStatIds("stat-1").build()
                val snapshot = PlayerDataSnapshot(EntityData(mutableMapOf()))
                every { context.snapshot } returns snapshot

                coAssertThrowsApplicationErrorException(
                    InvalidStatIdApplicationError.newBuilder().setMessage("Stat id invalid-stat is not supported.").build()
                ) {
                    underTest.updateCharacterStats(UpdateCharacterStatsRequest("char-1", mapOf("invalid-stat" to 10)), context)
                }
            }

        @Test
        fun `updates character stat`() =
            runTest {
                every {
                    playerDataContentLibrary.getContentData<CharacterStatEntry>("CharacterStats.json")["char-1"]
                } returns CharacterStatEntry.newBuilder().addStatIds("stat-1").build()
                val snapshot = PlayerDataSnapshot(EntityData(mutableMapOf()))
                every { context.snapshot } returns snapshot

                underTest.updateCharacterStats(UpdateCharacterStatsRequest("char-1", mapOf("stat-1" to 5)), context)

                val entity = snapshot.getUniqueEntity("CharacterStats_char-1")!!
                assertEquals(listOf(AggregateStat("stat-1", 5)), entity.getComponents())
            }

        @Test
        fun `updates existing character stat`() =
            runTest {
                every {
                    playerDataContentLibrary.getContentData<CharacterStatEntry>("CharacterStats.json")["char-1"]
                } returns CharacterStatEntry.newBuilder().addStatIds("stat-1").build()
                val entityId = UUID.randomUUID()
                val existing = AggregateStat("stat-1", 7)
                val snapshot =
                    PlayerDataSnapshot(
                        EntityData(
                            mutableMapOf(
                                entityId to
                                    Entity(
                                        EntityName("CharacterStats_char-1"),
                                        entityId,
                                        mutableListOf(ComponentContainer(UUID.randomUUID(), existing))
                                    )
                            )
                        )
                    )
                every { context.snapshot } returns snapshot

                underTest.updateCharacterStats(UpdateCharacterStatsRequest("char-1", mapOf("stat-1" to 5)), context)

                assertEquals(12, (snapshot.getUniqueEntity("CharacterStats_char-1")!!.getComponents().single() as AggregateStat).value)
            }
    }

    @Nested
    inner class UpdateAchievementsProgressTests {
        @Test
        fun `throws if achievement id is invalid`() =
            runTest {
                every { playerDataContentLibrary.getContentData<AchievementEntry>("Achievements.json")["ach-1"] } returns null
                val snapshot = PlayerDataSnapshot(EntityData(mutableMapOf()))
                every { context.snapshot } returns snapshot

                coAssertThrowsApplicationErrorException(
                    InvalidAchievementIdApplicationError.newBuilder().setMessage("Achievement id ach-1 is not supported.").build()
                ) {
                    underTest.updateAchievementsProgress(UpdateAchievementsProgressRequest(mapOf("ach-1" to 10)), context)
                }
            }

        @Test
        fun `updates achievement progress and completes achievement`() =
            runTest {
                every { playerDataContentLibrary.getContentData<AchievementEntry>("Achievements.json")["ach-1"] } returns
                    AchievementEntry.newBuilder().setTargetValue(5).build()
                every { timeProxy.currentEpochMillis() } returns 6000
                val snapshot = PlayerDataSnapshot(EntityData(mutableMapOf()))
                every { context.snapshot } returns snapshot

                underTest.updateAchievementsProgress(UpdateAchievementsProgressRequest(mapOf("ach-1" to 10)), context)

                val progress = snapshot.getUniqueEntity("Achievements")!!.getComponentsByType<AchievementProgress>().single()
                assertEquals("ach-1", progress.achievementId)
                assertEquals(10, progress.currentProgress)
                assertEquals(6, progress.completedAtSec)
            }

        @Test
        fun `updates achievement progress without completing achievement`() =
            runTest {
                every { playerDataContentLibrary.getContentData<AchievementEntry>("Achievements.json")["ach-1"] } returns
                    AchievementEntry.newBuilder().setTargetValue(15).build()
                val snapshot = PlayerDataSnapshot(EntityData(mutableMapOf()))
                every { context.snapshot } returns snapshot

                underTest.updateAchievementsProgress(UpdateAchievementsProgressRequest(mapOf("ach-1" to 10)), context)

                val progress = snapshot.getUniqueEntity("Achievements")!!.getComponentsByType<AchievementProgress>().single()
                assertEquals("ach-1", progress.achievementId)
                assertEquals(10, progress.currentProgress)
                assertNull(progress.completedAtSec)
            }

        @Test
        fun `updates existing achievement progress`() =
            runTest {
                every { playerDataContentLibrary.getContentData<AchievementEntry>("Achievements.json")["ach-1"] } returns
                    AchievementEntry.newBuilder().setTargetValue(50).build()
                val entityId = UUID.randomUUID()
                val existing = AchievementProgress("ach-1", 5, null)
                val snapshot =
                    PlayerDataSnapshot(
                        EntityData(
                            mutableMapOf(
                                entityId to
                                    Entity(
                                        EntityName("Achievements"),
                                        entityId,
                                        mutableListOf(ComponentContainer(UUID.randomUUID(), existing))
                                    )
                            )
                        )
                    )
                every { context.snapshot } returns snapshot

                underTest.updateAchievementsProgress(UpdateAchievementsProgressRequest(mapOf("ach-1" to 7)), context)

                val progress = snapshot.getUniqueEntity("Achievements")!!.getComponentsByType<AchievementProgress>().single()
                assertEquals("ach-1", progress.achievementId)
                assertEquals(12, progress.currentProgress)
                assertNull(progress.completedAtSec)
            }
    }
}
