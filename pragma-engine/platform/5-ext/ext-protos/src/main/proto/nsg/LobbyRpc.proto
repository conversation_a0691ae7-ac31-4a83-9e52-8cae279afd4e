syntax = "proto3";

package nsg.lobby;

import "pragma/account/accountCommon.proto";
import "pragma/types.proto";
import "pragmaOptions.proto";
import "shared/gameInstanceExt.proto";

option (pragma.unreal_namespace) = "Lobby";

enum LobbyState {
  UNINITIALIZED = 0;
  PREGAME = 1;
  TEAMS_LOCKED = 2;
  LAUNCH_READY = 3;
  STARTING = 4;
  IN_MATCH = 5;
  POST_MATCH = 6;
  IN_MATCHMAKING = 7;
  SHUT_DOWN = 8;
}

enum LobbyRemovalReason {
  REMOVAL_REASON_UNSPECIFIED = 0;
  reserved 1, 2;
  KICKED = 3;
  LEFT = 4;
  DISCONNECTED = 5;
  MATCH_END = 6;
  PARTY_LEADER_LEFT = 7;
  OPERATOR_DELETION = 8;
  PARTNER_DELETION = 9;
}

message BroadcastLobbyPlayer {
  reserved 1;

  pragma.Fixed128 player_id = 2;

  pragma.account.DisplayName display_name = 3;

  bool is_ready = 4;

  bool is_owner = 5;

  pragma.Fixed128 social_id = 6;

  int32 selected_character = 7;
  int32 team_number = 8;
  bool is_flag_volunteer = 9; // should be removed?
  bool is_flag_carrier = 10;
  string selected_character_id = 11;
  bool run_it_back = 12;
}

message BroadcastLobby {
  reserved 1, 2;
  pragma.Fixed128 lobby_id = 3;
  string lobby_name = 4;
  repeated BroadcastLobbyPlayer lobby_members = 5;
  LobbyState lobby_state = 6;
  string rules = 7;
  map<string, string> server_args = 8;
  LobbyTimers timers = 9;
}

message LobbyTimers {
  int64 selection_end_time = 1; //in UTC seconds, 0 if not set
  int64 post_selection_end_time = 2; //in UTC seconds, 0 if not set
  int32 selection_end_seconds = 3;
  int32 post_selection_end_seconds = 4;
}

message QueryListLobby {
  pragma.Fixed128 lobby_id = 1;
  reserved 2;
  string game_server_version = 3;
  string lobby_name = 4;
  int32 player_count = 5;
  LobbyState lobby_state = 6;
}

message CreateLobbyV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  reserved 1, 2, 3, 4;
  string lobby_name = 5;
  pragma.gameinstance.GameRegion game_region = 6;
  bool local_server = 7;
  string map_filepath = 8;

  string rules = 9;
  map<string, string> server_args = 10;
  string original_client_version = 11;
}

message CreateLobbyV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;

  BroadcastLobby lobby = 1;
  VoiceToken lobby_channel_token = 2;
}

message CreateLobbyServiceV1Request {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 player_id = 1;
  string lobby_name = 2;
  optional pragma.gameinstance.GameRegion game_region = 3;
}

message CreateLobbyServiceV1Response {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = RESPONSE;

  BroadcastLobby lobby = 1;
}

message QueryLobbiesV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  reserved 1;
  string game_server_version = 2;
}

message QueryLobbiesV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;

  repeated QueryListLobby lobby_list = 1;
}

message JoinLobbyV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;

  reserved 2, 3, 4;
}

message JoinLobbyV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;

  BroadcastLobby lobby = 1;
  VoiceToken lobby_channel_token = 3;
}

message JoinMatchmakingLobbyServiceV1Request {
  message LobbyPlayer {
    pragma.Fixed128 player_id = 1;
    string game_client_version = 2;
    int32 team_number = 3;
  }

  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  repeated LobbyPlayer lobby_players = 2;
}

message JoinMatchmakingLobbyServiceV1Response {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = RESPONSE;
}

message JoinLobbyServiceV1Request {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  pragma.Fixed128 player_id = 2;
  string game_client_version = 3;
  pragma.Fixed128 party_id = 4;
  pragma.account.DisplayName display_name = 5;
}

message JoinLobbyServiceV1Response {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = RESPONSE;

  BroadcastLobby lobby = 1;
}

message LeaveLobbyV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message LeaveLobbyV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbySelectCharacterV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  int32 selected_character = 2;
}

message LobbySelectCharacterV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbySelectCharacterV2Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  string selected_character = 2;
}

message LobbySelectCharacterV2Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbyVolunteerForFlagV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  bool volunteer = 2;
}

message LobbyVolunteerForFlagV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbySelectTeamV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  int32 selected_team = 2;
}

message LobbySelectTeamV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbySetReadyV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  bool is_ready = 2;
}

message LobbySetReadyV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbyLockTeamsV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message LobbyLockTeamsV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbyStartGameV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  reserved 2;
}

message LobbyStartGameV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbyGameStartedV1Request {
  option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
  pragma.Fixed128 game_instance_id = 2;
}

message LobbyGameStartedV1Response {
  option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = RESPONSE;

  string config_version = 1;
  map<string, string> config_data = 2;
}

message LobbyGameEndedV1Request {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message LobbyGameEndedV1Response {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbyRemovedFromGameV1Request {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 player_id = 1;
  pragma.Fixed128 lobby_id = 2;
}

message LobbyRemovedFromGameV1Response {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = RESPONSE;
}

message GetLobbyInfoServiceV1Request {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message GetLobbyInfoServiceV1Response {
  option (pragma.pragma_session_type) = SERVICE;
  option (pragma.pragma_message_type) = RESPONSE;
  BroadcastLobby lobby = 1;
}

message GetLobbyInfoV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;
  pragma.Fixed128 lobby_id = 1;
}

message GetLobbyInfoV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
  BroadcastLobby lobby = 1;
}

message LobbyDetailsV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  BroadcastLobby lobby = 1;
  int64 created_at = 2;
}

message RemovedFromLobbyV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  pragma.Fixed128 lobby_id = 1;
  LobbyRemovalReason removal_reason = 2;
}

message LobbyStartLocalServerV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  string connection_address = 1;
  string social_token = 2;
  string auth_token = 3;
  string game_instance_id = 4;
  string map_filepath = 5;
}

message LobbyConfigVersionV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  string config_version = 1;
}

message LobbyInvalidSessionApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyInvalidNameApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyNotOwnerApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyCouldNotRetrieveDisplayNameApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyInvalidStateApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyAlreadyLaunchReadyApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyWrongLobbyIdApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyNotInPartyApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyNotInLobbyApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyStartGameFailedApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyJoinGameFailedApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyNotFoundApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}


message LobbyNoCharacterSelectedApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyInvalidTeamApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyNotEnoughRoomApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyTeamFullApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyGameAlreadyStartedApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyAlreadyReadyApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyAlreadyInLobbyApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyFullApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyWrongVersionApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyInvalidCharacterApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message CannotAddToPartyLobbyApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message AddedToLobbyV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  BroadcastLobby lobby = 1;
  VoiceToken lobby_channel_token = 2;
  bool lock_teams_in = 3;
}

message LobbyUnableToGenerateChatTokenApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyTeamVoiceChatV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  VoiceToken team_channel_token = 1;
}

message LobbyPlayerSelectionsCountdownStartedV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  BroadcastLobby lobby = 1;
}

message LobbyStartGameCountdownV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;

  BroadcastLobby lobby = 1;
}

message VoiceToken {
  string token = 1;
  string channel_name = 2;
}

message LobbyNotPartyLeaderApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message LobbyMaxLobbySizeExceededApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message EnterMatchmakingV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message EnterMatchmakingV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LeaveMatchmakingV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message LeaveMatchmakingV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbyToRetrieveAppIdApplicationError {
  option (pragma.force_include_type) = true;

  string message = 1;
}

message RunItBackV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message RunItBackV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message CancelRunItBackV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message CancelRunItBackV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message UpdateLobbyPlayerServiceV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 player_id = 1;
  pragma.account.DisplayName updated_display_name = 2;
}

message UpdateLobbyPlayerServiceV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message EnterPostMatchV1Request {
  option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message EnterPostMatchV1Response {
  option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message LobbyPlayerLeaveMatchmakingV1Request {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = REQUEST;
}

message LobbyPlayerLeaveMatchmakingV1Response {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = RESPONSE;
}

message GameInstanceStartFailureV1Notification {
  option (pragma.pragma_session_type) = PLAYER;
  option (pragma.pragma_message_type) = NOTIFICATION;
}

message DeleteLobbyOperatorV1Request {
  option (pragma.pragma_session_type) = OPERATOR;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message DeleteLobbyOperatorV1Response {
  option (pragma.pragma_session_type) = OPERATOR;
  option (pragma.pragma_message_type) = RESPONSE;
}

message ListAllLobbiesPartnerV1Request {
   option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = REQUEST;
}

message ListAllLobbiesPartnerV1Response {
  option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = RESPONSE;

  repeated QueryListLobby lobby_list = 1;
}

message DeleteLobbyPartnerV1Request {
  option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = REQUEST;

  pragma.Fixed128 lobby_id = 1;
}

message DeleteLobbyPartnerV1Response {
  option (pragma.pragma_session_type) = PARTNER;
  option (pragma.pragma_message_type) = RESPONSE;
}
