game:
  core:
    shardId: "00000000-0000-0000-0000-000000000001"
    clusterName: "lastflag-staging-game"
    distributedServices:
      FleetService:
        nodeByInstanceId:
          0: ~
      InMemoryGameServerSimulatorService:
        nodeByInstanceId:
          0: ~
  serviceConfigs:
    SocialBackendPartnerClientConfig:
      bearerToken: "EbjDy39sPOK/WI5ynE9mtlVolAOj4ns8SZeoURyyuqdlwzuRvv+3qyhsBzzLwJyxF63w+ppKaAkbd9w3if65v6yvmUzoCoM6qa7yLD9gu0gi1BlO7NKqYEM5m7SdSoGlE1sV5uTUx0pTfGig1yq7fzpBbRnQL3X6VFit1ByzsokbFBrHEY0n5uqgXSWfoDpGOmG1elwjX6rqF+wmrxB1ZTWcAEIs67aae7AIKe1xTu15SKXlBxqg9RrcBqYdPmre0NKCsoEFkwYsPMDH95Q49xPiczsKg7lOQHWwT7Pl1G9qy8kthtSjH05OgIlRulbUtWJ5RSnUaIIkn6lzanylyPW61DiYOTMkpeub30bk4MIQQJLxr1leMkZ7zfLUlpY0Hw7ml9Y9VNlw5uneVGp705c4HLEBAGBLj+NeDtv/mAoK4RBv7uQf+7u9ZCqvMFTMp+fJhOvKEvnI3oVHL2G1IVoPSsI77CCXdOr8lopBeCOPEdfI8m+eMAuJlb/Dd7gqWKkqsu2iUDxtuiWemGdKYm2JWsY5qc/bth88c7t4i93AyxT3R4YG3MDTnGdxc3s+1MGxXnFcNjEfFuxB05/ZYx9O+ydWpUd6r2MOfMj/eK+U1YETCE1THm2RUd8E5KcBveZn+CZWPgSUP2dbaR2V8uHApZkVKEaxQC1RWbE181H/bIn/Zy4WP5dRs12uF+YU+T1WaNNiiuGRmXMHhOod0PxyHkpa8FO46gQSv1TACpGb+Q7AZW8xzD3kjnjjXPUrNF4nGwFUG1lmtwMUrV3q0w1ucUPoT5dl7quRUe9q3PaNdW5D7tVhEKcxld6zLeSBHVZLI3QJNP6r5UKi/DuWd3GHk5g2/kUVKYVzThTSYdoDkCjR/7aVBhpoM3LjAYtN7UD9AxdyAVL3TZzs6HTCtVsKP11kp47cmfaPAuzulmr24GErKaXunmGqLE6D/Dl26rQT3AU7MlLpKHt6MJQieIdLQ1xQjkH7K1VR2TlhhVrQLTvYrsgHUbximOQFw1jB/qSwofPwgDAc9YEmexx657N6M2QoezqaEzb9QSj+ZXknXwpaH6+a7Kt4fYny49vntuGS0nMVHvMIEb9jzgWYENC+7sZGCOjQFo0AUlj3xlFRXFYcaAesjWKjQ7T1aYptd8srqCeUP7QQ1wZtUjk+8qDxCv2P0aoEEHwb12wl3v8EKdMIH3/xQUdv9OypkxZfHt0Q1l0uxO5+GQZr99uRaFCcqi7FYp6mCaj/Wx22UwEuT3EOnEnoPU/2ootYt+jCe3uBq3eHaD4jz5jnBB4zWIFD/N32lAqYk+xdDOtdYc3x8qQodBt3ANr/MDpFD5iZ7x+su/yTlYD4WKjeNlC2zkDfiVpY8bdAf8A0fEUDf+jRyHKJYfyTF3eZdkBD5QVdWCuGFLDT8wNhZobR9+of867LtE+qhDtuux/hYH2nlOP0R//Hfw4s+ZTQeq6YhsCpu9OX6iFNA2tzGOVMLD85BVUadBf4rK4gffVWO4oRbqdzQGCViQRr98FDkhaJ6E6Q03Lh6DkapJ32hLjy6d2lTtkEaHTopuaBZU1/Vz3fmPcom0GCVNUf992s4JyukSJJ4xW8tvnK9n0aEybQSi+dMFOv/xDDYRCcb4yZcbp9UNdYbVj4EqFGNS+c8HcTA03IteUpu6t2n4ChnrfR6EEMbfpXr0uOKLSkat1bxFUhh7fZNvndB2jUnroctzF4+ymNyE9jlrkBQiTEeEfFR3v/FJKYo0fj+L56Xws="
    SharedDatabaseConfigServiceConfig:
      databaseConfigsByIdentifier:
        defaultIdentifier:
          username: "superuser"
          password: "osGKlsbqjTAcD9r/VMwAVPjFvoBiYzyDKBUYlIDX+Sr/xPhdCsokamByp/vd6UFwgx8s0bJNqRUOj2Nj"
          host: "database.staging.lastflag.nsg.pragmaengine.com"
    InventoryDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "staging_game_player_inventory1"
          2:
            identifier: "defaultIdentifier"
            schema: "staging_game_player_inventory2"
    TelemetryDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "staging_game_telemetry"
    PlayerDataDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "staging_game_player_data1"
    FulfillmentDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "staging_game_fulfillment1"
    BuildMapConfig:
      s3AccessKey: "ieLCzHx4rjXV4vTyEoI0L105pdzXM89sfJAoFataJkHWi3eABV/tqbcjEQC4XD6J"
      s3SecretKey: "sQDlFx1FAJmZKI15a1NS9nhryvYuip94NyzVqbiF9IN9SJLWa/Ja+8DWsYorqrV+Rz2rCYPnMq3tjADmZiXL5O71S1g="
    BuildMapDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "staging_build_map"
    InventoryServiceConfig:
      inventoryCacheConfig:
        sizeBeforeExpirationEnforced: 10000
        maxTimeBetweenAccessMillis: 720000
        sweepIntervalMillis: 900000
    LobbyConfig:
      clientLocalSocialToken: "NcSRgkik1bnDUunMLAN4zqvwo9KTUs8dzm+jNRABLMHoWa7+duO00mBoiiL6biqPR/l5qALZoH3YPxez92yqpsq0FRJkTggQz16uKcl4CmwKjbtzWUCMHjJGGxxqqmXCJW1xMt3qGatBCXpxlaYdFn/1FQGU1Xn2ZG73thr5lZ4j+x5iZlyDUd6NVuKor/kger1N3BYJlJuVNseFKVru80Rb4eHGTOBXptOx+CdeqSM93qy5BwfZUOO/bvJ5anGVNxvZSOaw+rabPbcHAhaHWzWlV9nOcAEJTO0PlBObLfHmpNgba5Mwzo4bjtkF1Cruh8E43cbWOCt/DbQyjYda8zziDGuvIk/flaL8k6re5BjczwIl8YoRh1dFEBpLnNCbWONVzCS1n8Qilg/dOFWMfiuccjr7uaE3G3kvGROX/Ni3zm8gjKcd/F44plU6Nk15hnFMtWFcH+Z7foANH4C6860H4MKlKFOLTIUGkoFC1p9A17+9grIVTf8gJGdqUpJfFi3YsrBrQDCE9ubmwEESPGOUu70urTy8sADoluXxqm4bjgxP0ULm2NL8VsMDuJ0C28zktwEQ9VRusTlH54J+IIRZKEZTs3XstquXEcQiaXT1ekRPHVXdh0mxAeV2MdHVcHGO2uAr6HsJEMuSCWj+hDDJnPsh0VyTifz1f46dL5Xrpqn2UZMsmk/KYBSi4hHJlMncuZIFgxF1f/4kXKMlFlMnmsDsPQ9e7CjxTNRugrDTf+uDJIdwc1swRqw7c8ndrPvPLXImgrFSQtwmqSBHf8uQpYkYyiTiweLcVZFndBENa+k+zEKR+2T36WJFmYeWByj6AO2+njO+Uj5+j1DSv6B31gt4Do87NBITtI/CoihoWfvKBAsAkhmQsgA1hwH8lV2kcahcaY3Z+Fa4wV86DTzScz92QPCHRyVU03pMZQTh6TIN/CBpmHJrDnOQI7cUUkD9kyk2HnNDUXIynrp7tU41ygJ2MucGs8eTqtkYYoOph3OjFJjFhTP3JJDKEVtdttR7aewRoKbQgbjq2Hk4pq0HuTSKfvMM1wfqRfuKP1NuBzBmh3uXG7Ij/O+Y1/WMVUD8E/4hVxUSv6R65LCMSbYu6T/MU8Yy9pw1k5Vlg5gOMJqtfocLZz1PpXcOfBK37PpBVSxQ0kwCIbPUHHwztTR6I5gPW4Uq6omccLRvHIPwhrfruxoNGeNrA0h+ziCqnZtcmuhwo+PStVFQmMSgozLOqRo3YMEu6l0cxjS2ocifVWwoUQg8ybFzQg73TIz1jAXwhP7Sx+yNbiWuyDsO402Qh9aSEcnqiWdBtwvLYmiPCdCFqVsOpKxtbiWyVBJWLq+vUUEN8tgwKi08JbmXYNMMKZ7t9ohhSjYELeNJa+LenGLHiKCcos/gXppsnf5UHNyJif2BYPg8v3Dd1myNlJqX3PIpLQT+C1pVdCwT8I+Rmj8X2XqjOusCqR2qUoU8GXNvq18rszm44JEdFg6jLrmt/1g948EKXbGJLP0jqfrOwXC1Tk/64tXMuK00vzV7O9oKczSIxAS2Kzl087EuHcnUdmb4BGsrnW50EYxD5Ls2E3URh5MRWFM4Bksq+P/XiyHetSp5fiLy2TRT0QOdOzw+920xkOHvg1nuyJ9V0QwVefHGHNu7oddhEtKndRmA6MNhPXnW00HVKH8+J1Mu+urwY2hVM9ZBOa1shc+rqnpLEmVOwf7HtRWqz+NcTkOei2wUQenGcSz75BAOChZ72Mn7tv14lpVF8F8="
      clientLocalAuthToken: "GNzqkl0fB1R4yPFS1wfvX9LtB7sngbJIX460jNL1tHEiAUGpl2WE6iqS88LBJKd2uGebWFMuhsHiRZoZH8d6DsCYIR88qR2UAxVHGJjArASQtD/L3EHy4R3lRwmPvuXhK7u9Vj7rNDw/ADvLUwodCkSarB5nECbUYRQi7r9dWOTnm2/8qUq9QReqbfmCnwaF96a4sI30ldKokZj60i+FpfWckiBdmSfzU+Z+ysZePQENwvAAXq2A+40VvpFasWk5M3JBwu9QwDrK0jIumBEPc0megRCx0h2Gw+u/mfXD1v9ZwVgPJBmUJoA2d4NVOGREqt9McubHMPmkdai/YZOfhLQnUMiWMiaYfDOSQkhjDaWKgpZUFQpm06RD/lR4UTJhPDm3v772ln/UXE4XLQK27Q7RGzCtwrk75rkCdBm3M8PzAjo/UNNOQZLyCXlx0Od1ri4ACFEcT9Pyg+C+mhK8l+JYQf4FZvkKaWuaJRVOGlBXdPEohg9a9SD3Oys0AGdRKDc2VR+LNeUhHfvrZv/tmALBPgh0hkkdE5T8EsufL4rKB96pG6ILmFbAvekh+9JvJZr2FrqNq5/JR6oJk11LsoowarDltIpMWrrwGqotR68x7K4S3u23CUbVAJnQNvrco1rjR+f9FUP/iX6I2r3Gmg0ehlfme1/OfBs4aTXBu3PTTMZQuGM771DIDVmQrNkYW+DoIMzYkbvcVL5b9EZLH/nXAAEq707bOGDYXl8Btd6tZCX1zbCAVmXaAPKMdSfxUsq2jChUGRCfJL39yFnSo7NJqeJ2jOcqJT0P5upFH/NcU1uHDqzus4K8rNf618pNTwyEBHmlEHdRWKtAhuP6bOe4zH5E4hlSVZiHJz4FQd8M7d8lGTatC4918LMMngds/c6ngqIXqDtvhFhIOrgzL2M5fTWXt6aAthwXlTd5g1osvn8prfNjMBFA942S4zTz8Ccl205aqVGucLsZye0bqaTHjiVkXE6wocDDRPql/8uRvT3fZcqBjeldKbBEfepDPhKU2NZDyAURIp1iEL7PMYp4oA0jhSVf3WoN26r6Tadqf+EBOV2PnpzDg+XEFwLnAcCmJaLF+GBWj27/RbbF6nR9UpN39MkEXws02Ht3ymnWjJtYlO0qo/qzGacsI4mJQIUuzX8/gxeF3dSXUK5Zx1xDhiQITnzf8XDh1pFAuQTplDycGVE05W0sRb3g86O5jdsAAFS0rnPBFCmUkji2ezC/o1wlac/PzCc0X6JW6zUJM5CWXR3xQNTvgamx/bUQB62C+PItD2v5VsUMdiUYKYSzip/AXhJKFdj2uZd+5dxkAdBfoXAmV9b9PlchHxFXNnIODD10tfMEm8mMGyXL0hZM3BMFCdQ+2b4GcZmmJwih7EHANtvFvWizNCBXu+2xwTS7W+XMXO9W16BabWvCyvkpXjHm0ky5lxzBgfTuILhcUKUzCGEFhvd+5clfuGwm720OqsGGDc0D1SG+Nmqwm2+7K4/w/Xbyb3khgRk1zYvhcruhmrVkjivALYzUs27FI1YrmQkY720/lwuMO3QPymIDDKdpsFfHZ6Ka8BqSQ9YDIw9IeppIrzcF4uBgzGCG2r9WXg9EMymEM1PG5ANTNqCFTIVmDCGOqBrdQc1lROGpkz78ZteUc28G3XTquFCc8X+wUXd6ECyr0a0gnVT8aHgFQYPxPJgAXZAqsugotjsUquXHgsEPtgiMYuGrtRXu5V342rkWETbyhDhYZRSPxM+DT/iRYRfwIyqhylvPMlKJlGohfPdO7eJ9n+ycBWa944k0Vh0Gqq7fOREEncmwGFSdcyEGwfq2HRxtlRZzGxKpaRR5sNq2lQ7EKi5++8u4vsRWVa1mm11XRbBqu4XqytgWrdmt/7R0nqI68LRF/REIokFZG9WfxJ7Z4DpVGFcjb2Y2n3jfa4vB1ySz5Y+9eo5Zaz9cudsG"
      clientLocalGamePartnerBackendAddress: "https://staging.lastflag.nsg.pragmaengine.com:10100"
    ChatConfig:
      tokenSecretKey: "QWkgnNMn7zIN8SkbvdI/QPjugnSulqckpbmu9riU/BnrrCQnnW1csA+Nll4kgw+46J0SPYtggYD+23Ax"
    PlayerReportsDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "staging_game_player_reports"
    BanRecommendationsDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "staging_game_ban_recommendations"
    EntitlementTransactionsDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "staging_game_entitlement_transactions"
    TextProfanityServiceConfig:
      s3AccessKey: "lqrZp25c3vPTfng5AaBiKodbYSyPOQsVPm9Iy1TroYRpphUGS6+cVy4Gq93M/3aN"
      s3SecretKey: "6mt15CdqrBMmR//oe7B/Er3iAT8yC4st9vP5+skjo9PvqpoiGSBizag/VoojwUoxgJiShYgBp9A2druFO5FXuERDkr4="
# =================================  LOADTESTING =================================
    InMemoryGameServerSimulatorServiceConfig:
      matchLengthSeconds: 60
      partnerEndpoint: "https://qa.lastflag.nsg.pragmaengine.com:10100"
# =================================  LOADTESTING =================================
  pluginConfigs:
    GameInstanceService.gameServerProviderPlugin:
      class: "pragma.gameinstance.impl.HathoraGameServerProviderPlugin"
      config:
        pragmaGamePartnerBackendAddress: "https://staging.lastflag.nsg.pragmaengine.com:10000"
        hathoraAppId: ""
        hathoraDevToken: "7NF9CKA0v5d2iIOEpU+WW3KmlxEaK87DONFnwMAxNAkPoK7HDF3lznU2nNxEI26K2sOdKa9oQmpy0hKgMJwe+OmA4/v42C6UqQpi9PmxIfROssBLCN7D3u8D6aBFt0V+e/KlpsDZWNMsdNyt1zNx05TdqtAsvIjoFy0Ga1Kl"
        fallbackRegion: "Los_Angeles"
    GameOperatorGatewayNodeService.routePlugins:
      plugins:
        Portal:
          class: "pragma.gateway.FilePortalModulePlugin"
          config:
            portalSource: "portal/operator-game"
    TelemetryService.telemetryPlugin:
      class: "pragma.telemetry.s3.S3TelemetryPlugin"
      config:
        eventBatchSize: 10000
        timeWindowPerBatchMillis: 60000
        s3BucketName: "nsg-telemetry-platform"
        shardName: "staging"
social:
  core:
    shardId: "00000000-0000-0000-0000-000000000001"
    clusterName: "lastflag-staging-social"
  serviceConfigs:
    SharedDatabaseConfigServiceConfig:
      databaseConfigsByIdentifier:
        defaultIdentifier:
          username: "superuser"
          password: "osGKlsbqjTAcD9r/VMwAVPjFvoBiYzyDKBUYlIDX+Sr/xPhdCsokamByp/vd6UFwgx8s0bJNqRUOj2Nj"
          host: "database.staging.lastflag.nsg.pragmaengine.com"
    FriendDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "staging_social_friend"
    AccountServiceConfig:
      verificationEmailEndpoint: ""
      gameManagementPollingFrequencyInMillis: 500
    AccountDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "account"
    DataRightsDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "staging_social_data_rights"
    GameDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "game"
    PaymentDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "staging_social_orderhistory1"
          2:
            identifier: "defaultIdentifier"
            schema: "staging_social_orderhistory2"
    OrderDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "staging_social_order1"
    UnsafeIdentityDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "unsafe_identity_provider"
  pluginConfigs:
    AccountService.identityProviderPlugins:
      plugins:
        Steam:
          class: "pragma.account.SteamIdentityProviderPlugin"
          config:
            appId: "2721340"
            steamWebAPIKey: "aznNGmlFFArOgm3H9VeYJT6qfYmRsJPfI6chRK7tV4QzJB+Nn3NIsReNSz+MRRu0TkD2L3LkKDgn4Bpr"
            restrictByAppOwnership: false
            restrictByAccountBan: false
            playerLoginEnabled: true
            operatorLoginEnabled: true
            accountLinkingEnabled: true
            showPortalLoginButton: true
    SocialOperatorGatewayNodeService.routePlugins:
      plugins:
        Portal:
          class: "pragma.gateway.FilePortalModulePlugin"
          config:
            portalSource: "portal/operator-social"
    SocialPlayerGatewayNodeService.routePlugins:
      plugins:
        Portal:
          class: "pragma.gateway.FilePortalModulePlugin"
          config:
            portalSource: "portal/player-social"
