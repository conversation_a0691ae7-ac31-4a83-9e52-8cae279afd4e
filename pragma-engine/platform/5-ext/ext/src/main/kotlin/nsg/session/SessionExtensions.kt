package nsg.session

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import nsg.telemetry.ServerTelemetryService
import pragma.PragmaNode
import pragma.logging.PragmaLoggerFactory
import pragma.session.PragmaSessionWrapper
import pragma.session.PlayerPragmaSessionWrapper
import pragma.utils.toUUID

private val logger = PragmaLoggerFactory.getLogger(SessionExtensions::class)

// Coroutine scope for telemetry operations - uses SupervisorJob so failures don't affect other operations
private val telemetryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

// Helper class for logger
class SessionExtensions

/**
 * Extension function that bridges recordLogin(PragmaNode) to recordLogin(MetricsManager).
 * This provides the missing signature that SessionContext expects.
 */
fun PragmaSessionWrapper.recordLogin(pragmaNode: PragmaNode) {
    // Call the original metrics recording - PragmaNode implements MetricsManager
    recordLogin(pragmaNode as pragma.metrics.MetricsManager)

    // Add CCU telemetry for game player sessions only (not social-only sessions)
    if (this is PlayerPragmaSessionWrapper) {
        try {
            val playerId = pragmaId.toUUID().toString()

            // Only track CCU for game backend sessions, not social-only sessions
            // Check if this is a game backend by looking at the pragma node's backend type
            val backendType = pragmaNode.backendType
            if (backendType == pragma.settings.BackendType.GAME) {
                logger.debug("Recording player_online event for game CCU tracking: $playerId")

                // Use a dedicated coroutine scope for telemetry operations
                telemetryScope.launch {
                    try {
                        // Get the telemetry service instance using lookupServiceInstances
                        val telemetryServiceInstances = pragmaNode.lookupServiceInstances(ServerTelemetryService::class)
                        if (telemetryServiceInstances.isNotEmpty()) {
                            val serverTelemetryService = telemetryServiceInstances.values.first() as ServerTelemetryService
                            serverTelemetryService.recordPlayerOnline(
                                playerId = playerId,
                                metadata = mapOf(
                                    "login_type" to "game_session_created",
                                    "backend_type" to backendType.name,
                                    "pragma_id" to playerId
                                )
                            )
                            logger.debug("Successfully recorded player_online event for game player: $playerId")
                        } else {
                            logger.debug("ServerTelemetryService not available - no instances found")
                        }
                    } catch (serviceException: Exception) {
                        logger.debug("ServerTelemetryService failed to record player_online event: ${serviceException.message}")
                    }
                }
            } else {
                logger.debug("Skipping CCU tracking for social-only session: $playerId (backend: $backendType)")
            }
        } catch (e: Exception) {
            logger.error("Failed to initiate player_online telemetry recording", e)
        }
    }
}
