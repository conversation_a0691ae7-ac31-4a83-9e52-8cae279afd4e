@file:OptIn(ExperimentalCoroutinesApi::class)

package nsg.banner

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import nsg.banner.BannerServiceRpc.CouldNotRetrieveBannerDetailsApplicationError
import nsg.banner.BannerServiceRpc.GetBannerDetailsForPlayersV1Request
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import pragma.AccountTestFactory.playerId
import pragma.AccountTestFactory.playerIdUuid
import pragma.PragmaCoreTestFactory
import pragma.PragmaResult
import pragma.ServiceError
import pragma.SessionTestFactory
import pragma.playerdata.kotlinclient.PlayerDataClient
import pragma.rpcs.PragmaFailure
import pragma.utils.coAssertThrowsApplicationErrorException
import pragma.utils.toFixed128

class BannerServiceTest {
    private val instanceId = PragmaCoreTestFactory.uuid(99)
    private val pragmaNode = PragmaCoreTestFactory.pragmaNode()
    private val playerDataClient = mockk<PlayerDataClient>(relaxed = true)
    private var playerDataClientProvider: (pragma.services.Service) -> PlayerDataClient = { playerDataClient }

    private lateinit var testObj: BannerService

    @BeforeEach
    fun setup() {
        testObj = BannerService(pragmaNode, instanceId, playerDataClientProvider)
    }

    @Test
    fun `getBannerDetailsForPlayersV1 returns banner info for each player`() =
        runTest {
            val playerId = playerIdUuid(1)

            val request =
                GetBannerDetailsForPlayersV1Request
                    .newBuilder()
                    .addPlayerIds(playerId(1))
                    .build()

            val bannerSelections =
                BannerSelections(
                    finialId = "finial123",
                    titleId = "title123",
                    sigilLayoutId = "layout123",
                    sigils = listOf(SigilSlot("sigil1", "red"), SigilSlot("sigil2", "blue")),
                    accents = listOf(AccentSlot("accent1", "gold"), AccentSlot("accent2", "silver")),
                    patternId = "pattern123",
                    primaryColorId = "primary123",
                    secondaryColorId = "secondary123",
                    shapeId = "shape123",
                    shapeColorId = "shapeColor123"
                )

            coEvery {
                playerDataClient.doOperation(any<GetPlayerBannerRequest>(), playerId)
            } returns PragmaResult.success(GetPlayerBannerResponse(bannerSelections))

            val response =
                testObj.getBannerDetailsForPlayersV1(
                    SessionTestFactory.playerSession(1),
                    request
                )

            assertEquals(1, response.playerBannersCount)
            val details = response.getPlayerBanners(0)
            assertEquals(playerId.toFixed128(), details.playerId)
            assertEquals("finial123", details.bannerDetails.finialId)
            assertEquals("title123", details.bannerDetails.titleId)
            assertEquals("layout123", details.bannerDetails.sigilLayoutId)
            assertEquals(2, details.bannerDetails.sigilsCount)
            assertEquals(2, details.bannerDetails.accentsCount)
            assertEquals("pattern123", details.bannerDetails.patternId)
            assertEquals("primary123", details.bannerDetails.primaryColorId)
            assertEquals("secondary123", details.bannerDetails.secondaryColorId)
            assertEquals("shape123", details.bannerDetails.shapeId)
            assertEquals("shapeColor123", details.bannerDetails.shapeColorId)
        }

    @Test
    fun `getBannerDetailsForPlayersV1 throws error if banner info not found`() =
        runTest {
            val playerId = playerIdUuid(1)

            val request =
                GetBannerDetailsForPlayersV1Request
                    .newBuilder()
                    .addPlayerIds(playerId(1))
                    .build()

            coEvery {
                playerDataClient.doOperation(any<GetPlayerBannerRequest>(), playerId)
            } returns PragmaResult.Failure(PragmaFailure(ServiceError.getDefaultInstance()))

            coAssertThrowsApplicationErrorException(
                CouldNotRetrieveBannerDetailsApplicationError
                    .newBuilder()
                    .setMessage("Could not retrieve banner details for $playerId.")
                    .build()
            ) {
                testObj.getBannerDetailsForPlayersV1(
                    SessionTestFactory.playerSession(1),
                    request
                )
            }
        }
}
