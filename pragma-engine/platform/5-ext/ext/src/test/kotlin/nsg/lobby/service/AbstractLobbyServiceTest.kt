@file:OptIn(ExperimentalCoroutinesApi::class)

package nsg.lobby.service

import io.mockk.coJustRun
import io.mockk.every
import io.mockk.mockk
import kotlin.random.Random
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import nsg.chat.VoiceChatApi
import nsg.lobby.Lobby
import nsg.lobby.LobbyCache
import nsg.lobby.LobbyConfig
import nsg.lobby.LobbyCountdownManager
import nsg.lobby.LobbyId
import nsg.lobby.LobbyOpLock
import nsg.lobby.LobbyProxy
import nsg.lobby.LobbyService
import nsg.lobby.manager.LobbyManager
import nsg.lobby.manager.MatchmakingManager
import nsg.utils.SessionClient
import org.junit.jupiter.api.BeforeEach
import pragma.PragmaCoreTestFactory
import pragma.ProtoTestFactory
import pragma.client.SocialBackendPartnerClientNodeService
import pragma.config.delegates.ConfigMap
import pragma.gameinstance.GameInstanceApi
import pragma.party.PartyApi
import pragma.services.NodeServicesContainer
import pragma.services.Service
import pragma.settings.BackendType.GAME
import pragma.utils.AlertingMutexFactory
import pragma.utils.RoutingUtils
import pragma.utils.TimeProxy
import pragma.utils.UUIDProxy

abstract class AbstractLobbyServiceTest {
    protected val instanceId = ProtoTestFactory.serviceInstanceId(1)
    protected val instanceCount = instanceId.leastSignificantBits.toInt() + 1
    protected val pragmaNode = PragmaCoreTestFactory.pragmaNode(instanceCount, LobbyService::class)
    protected val routingUtils: RoutingUtils = mockk(relaxUnitFun = true)
    protected val sessionClient: SessionClient = mockk(relaxUnitFun = true)
    protected val alertingMutexFactory = mockk<AlertingMutexFactory>(relaxUnitFun = true)
    protected val lobbyCache: LobbyCache = mockk(relaxed = true)
    protected val lobbyProxy: TestLobbyProxy = TestLobbyProxy(lobbyCache)
    protected val uuidProxy: UUIDProxy = mockk(relaxUnitFun = true)
    protected val timeProxy: TimeProxy = mockk(relaxUnitFun = true)
    protected val lobbyConfig: LobbyConfig =
        LobbyConfig.getFor(GAME).apply {
            maxPlayersPerLobby = MAX_PLAYERS_PER_LOBBY
            numTeams = NUMBER_OF_TEAMS
            maxPlayersPerTeam = MAX_PLAYERS_PER_TEAM
            clientLocalServer = false
            autoLobbyMaps =
                ConfigMap<String>()
                    .also {
                        it.put("0", MAP_FILEPATH)
                    }
        }
    protected val partyApi: PartyApi = mockk()
    protected val gameInstanceApi: GameInstanceApi = mockk()
    private var partyApiProvider: (Service) -> PartyApi = { partyApi }
    private var gameInstanceApiProvider: (Service) -> GameInstanceApi = { gameInstanceApi }
    protected val random = mockk<Random>()
    protected val countdownManager = mockk<LobbyCountdownManager>(relaxed = true)
    protected val voiceChatApi = mockk<VoiceChatApi>(relaxed = true)
    protected val lobbyManager = mockk<LobbyManager>(relaxed = true)
    protected val socialBackendPartnerClientNodeService = mockk<SocialBackendPartnerClientNodeService>(relaxed = true)
    protected val matchmakingManager = mockk<MatchmakingManager>(relaxed = true)

    val testObj =
        LobbyService(
            pragmaNode = pragmaNode,
            instanceId = instanceId,
            routingUtils = routingUtils,
            sessionClient = sessionClient,
            instanceCount = instanceCount,
            alertingMutexFactory = alertingMutexFactory,
            lobbyProxy = lobbyProxy,
            uuidProxy = uuidProxy,
            timeProxy = timeProxy,
            partyApiProvider = partyApiProvider,
            gameInstanceApiProvider = gameInstanceApiProvider,
            random = random,
            countdownManager = countdownManager,
            voiceChatApiProvider = { voiceChatApi },
            lobbyManager = lobbyManager,
            matchmakingManager = matchmakingManager
        ).apply {
            nodeServicesContainer =
                NodeServicesContainer(
                    mapOf(
                        SocialBackendPartnerClientNodeService::class to socialBackendPartnerClientNodeService
                    )
                )
        }

    @BeforeEach
    fun beforeEach() =
        runTest {
            testObj.onConfigChanged(lobbyConfig)
            testObj.run()

            every { random.nextInt(any()) } returns 0
        }

    class TestLobbyProxy(
        override var lobbyCache: LobbyCache,
        val mock: LobbyProxy =
            mockk<LobbyProxy>().apply {
                coJustRun { operateOn(any(), any()) }
            },
    ) : LobbyProxy() {
        var lobby: Lobby? = null

        override fun init(
            lobbyCache: LobbyCache,
            opLock: LobbyOpLock<LobbyId, Lobby>,
            sessionClient: SessionClient,
        ) {
            this.opLock = opLock
            this.sessionClient = sessionClient
        }

        override suspend fun operateOn(
            lobbyId: LobbyId,
            block: suspend (Lobby, LobbyCache) -> Unit,
        ) {
            mock.operateOn(lobbyId, block)
            block(lobby!!, lobbyCache)
        }
    }

    companion object {
        const val MAX_PLAYERS_PER_LOBBY = 3
        const val MAX_PLAYERS_PER_TEAM = 3
        const val NUMBER_OF_TEAMS = 2
        const val LOBBY_JOIN_TOKEN = "lobbyJoinToken"
        const val TEAM_JOIN_TOKEN = "teamJoinToken"
        const val CHANNEL_NAME = "channelName"
        const val MAP_FILEPATH = "/ShooterMaps/Maps/CopperFalls/L_CopperFalls"
    }
}
