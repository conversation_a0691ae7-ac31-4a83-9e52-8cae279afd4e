-T 1C
--update-snapshots
--no-transfer-progress
-Dl.executor.jvm.JdbcExecutor=warn
-Dktor.application=warn
-Dorg.slf4j.simpleLogger.dateTimeFormat=HH:mm:ss.SSS
-Dorg.slf4j.simpleLogger.defaultLogLevel=info
-Dorg.slf4j.simpleLogger.log.ktor.test=warn
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugin.compiler.CompilerMojo=error
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugin.compiler.TestCompilerMojo=warn
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugin.failsafe.IntegrationTestMojo=warn
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugin.failsafe.VerifyMojo=warn
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugins.clean.CleanMojo=warn
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugins.dependency.fromConfiguration.UnpackMojo=warn
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugins.jar.TestJarMojo=error
-Dorg.slf4j.simpleLogger.log.org.apache.maven.plugins.source.TestSourceJarNoForkMojo=warn
-Dorg.slf4j.simpleLogger.log.org.apache.maven.shared.filtering.DefaultMavenResourcesFiltering=warn
-Dorg.slf4j.simpleLogger.log.org.codehaus.mojo.buildhelper.AddSourceMojo=warn
-Dorg.slf4j.simpleLogger.log.org.codehaus.mojo.flatten.CleanMojo=warn
-Dorg.slf4j.simpleLogger.log.org.codehaus.mojo.flatten.FlattenMojo=warn
-Dorg.slf4j.simpleLogger.log.org.jetbrains.kotlin.maven.K2JVMCompileMojo=warn
-Dorg.slf4j.simpleLogger.log.org.jetbrains.kotlin.maven.KotlinTestCompileMojo=warn
-Dorg.slf4j.simpleLogger.log.org.xolstice.maven.plugin.protobuf.ProtocCompileCppMojo=error
-Dorg.slf4j.simpleLogger.log.org.xolstice.maven.plugin.protobuf.ProtocCompileCsharpMojo=error
-Dorg.slf4j.simpleLogger.log.org.xolstice.maven.plugin.protobuf.ProtocCompileCustomMojo=error
-Dorg.slf4j.simpleLogger.log.org.xolstice.maven.plugin.protobuf.ProtocCompileCustomMojo=error
-Dorg.slf4j.simpleLogger.log.org.xolstice.maven.plugin.protobuf.ProtocCompileMojo=error

-Dorg.slf4j.simpleLogger.showLogName=false
