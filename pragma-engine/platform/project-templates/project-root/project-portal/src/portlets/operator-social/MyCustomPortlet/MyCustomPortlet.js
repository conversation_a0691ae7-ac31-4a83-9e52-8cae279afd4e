import ActiveProvidersPage from "./pages/ActiveProvidersPage";
import InlineComponentPage from "./pages/PageLInksPage";

const MyCustomPortlet = (portlet) =>
  portlet
    .setName("[EXAMPLE] My Custom Portlet")
    .addPage("ActiveProviders", ActiveProvidersPage)

    .addPage("PageLinks", InlineComponentPage, (page) =>
      page.addPage("ChildPage")
    )

    .addPageSection((section) => section.addPage("CoolEmptyPage"));

export default MyCustomPortlet;
