import ProvidersLists from "../components/ProvidersLists.vue";

export default (page) =>
  page
    .addSocialRpcData(
      "providers",
      "AccountRpc.ViewActiveIdProvidersOperatorV1Request"
    )

    .setTitle("Active Providers - Example Page")
    .setSubtitle("A list of the providers that have been enabled.")

    .addAction({
      name: "Add Provider",
      icon: "plus",
      type: "danger",
      emit: "addProviderExampleEvent",
    })

    .addAction({
      name: "Better Be Cool",
      page: "CoolEmptyPage",
    })

    .addComponent(ProvidersLists, {
      providers: ({ rpc }) => rpc.providers?.idProviders,
    });
