<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Unit Tests" type="JUnit" factoryName="JUnit">
    <option name="PACKAGE_NAME" value="pragma" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="package" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="wholeProject" />
    </option>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>