PLATFORM_REALPATH:=$(realpath $(firstword $(MAKE<PERSON>LE_LIST)))
MAKEFILE_NAME:=$(firstword $(MAKEFILE_LIST))
# Please note the PLATFORM_ROOT_DIR is determined this way to support spaces in the path.
PLATFORM_ROOT_DIR:=$(subst $(MAKEFILE_NAME),,$(PLATFORM_REALPATH))
REPO_ROOT=$(PLATFORM_ROOT_DIR)..
HOME_DIR=$(subst \,/,$(HOME))

DEV_CONFIG=dev.yml
COMMON_CONFIG=common.yml
LOCAL_CONFIG=local-dev.yml
INTEGRATION_TEST_CONFIG=integrationtest.yml
CI_CONFIG=ci.yml
DEV_DEFAULTS_CONFIG=config/dev-defaults.yml

CONFIG_DIR := 5-ext/config/
DEV_CONFIG_PATH := $(CONFIG_DIR)$(DEV_CONFIG)
COMMON_CONFIG_PATH := $(CONFIG_DIR)$(COMMON_CONFIG)
LOCAL_CONFIG_PATH := $(CONFIG_DIR)$(LOCAL_CONFIG)
CONFIG_PATHS := "$(DEV_DEFAULTS_CONFIG),$(COMMON_CONFIG_PATH),$(DEV_CONFIG_PATH),$(LOCAL_CONFIG_PATH)"

# Must indent using spaces in order for cmd.exe or PowerShell to display the error message.
ifeq "$(OS)" "Windows_NT"
  CLASSPATH_SEP:=$(shell echo \;)
  ifeq ($(shell uname -s),) # $(shell uname -s) has null value on non-POSIX shells
    $(error ERROR: Use Git Bash to build Pragma Engine. PowerShell and cmd.exe are unsupported.)
  endif
else
  CLASSPATH_SEP:=$(shell echo \:)
endif

COMPANY := $(shell cat COMPANY_NAME)
COMPANY_LOWERCASE := $(shell echo "${COMPANY}" | tr '[:upper:]' '[:lower:]')
PRAGMA_VERSION := $(shell cat PRAGMA_VERSION)

ifneq ("$(wildcard VERSION)","")
	VERSION := $(shell cat VERSION)
else
	VERSION := DEFAULT
endif

MAVEN_COMMAND := mvn
ifdef MAVEN_EXECUTABLE
    MAVEN_COMMAND := $(MAVEN_EXECUTABLE)
endif

_MAVEN_TASK := install
ifdef MAVEN_TASK
    _MAVEN_TASK := $(MAVEN_TASK)
endif

BUILD_ARGS_VERSION :=
ifdef PRAGMA_REVISION
    BUILD_ARGS_VERSION := -Drevision=$(PRAGMA_REVISION) -Dpragma.mvn.version=$(PRAGMA_REVISION)
endif

BUILD_ARGS := -Dcompany=$(COMPANY) -Dpragma.version=$(VERSION)-$(PRAGMA_VERSION) $(BUILD_ARGS_VERSION)
JAR_PATH := "5-ext/target/pragma.jar"
REMOTE_DEBUG_PARAMS := -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
MVN_WITH_THREADS := $(MAVEN_COMMAND) -T 1C -Dwerrors

SHELL=/bin/sh
.DEFAULT_GOAL := help

help:
	@ echo "Run \"make ext\" to build your project. Run \"make skip-tests build\" to build the engine and your project, skipping tests."

INCREMENTAL_OR_CLEAN := clean
incremental:
	$(eval INCREMENTAL_OR_CLEAN :=)
clean:
	$(eval INCREMENTAL_OR_CLEAN := clean)

RUN_OR_SKIP_TESTS :=
skip-tests:
	@echo Skipping tests...
	$(eval RUN_OR_SKIP_TESTS := -s)
dont-compile-tests:
	@echo Not even compiling tests...
	$(eval RUN_OR_SKIP_TESTS := -Dmaven.test.skip=true)
run-tests:
	@echo Running tests...
	$(eval RUN_OR_SKIP_TESTS :=)
skip-its:
	@echo Skipping ITs...
	$(eval RUN_OR_SKIP_TESTS := -DskipITs)

run-unity-tests:
	cd "$(REPO_ROOT)/sdk/unity" \
	&& ./run-unity-tests.sh

gen-sdk-unity:
	./pragma sdk generate -s unity

gen-sdk-types-unreal4:
	./pragma sdk generate -s unreal

portal-init:
	./pragma portal init

portal-setup:
	./pragma portal setup

portal-package:
ifeq ($(PROJECT),web)
	./pragma portal package --project=web
else
	./pragma portal package
endif

portal-start:
	./pragma portal start

portal-test-all:
	./pragma portal test --all

portal-test:
	./pragma portal setup

portal-validate-pragmalib:
	./pragma portal update-sha


## CI Commands (CI-only targets)
$(PLATFORM_ROOT_DIR)$(CONFIG_DIR):
	@mkdir -p $@

# this command is deprecated and supports the legacy 5-ext project structure.
# moving forward, use try `./pragma build -h` to learn how to build with the new tool.
build: protos engine ext

protos:
	./pragma build engine-protos

engine:
	./pragma build engine-code $(RUN_OR_SKIP_TESTS)

ext: ext-protos ext-build

ext-build:
	./pragma build project-code $(RUN_OR_SKIP_TESTS)

ext-protos:
	./pragma build project-protos

ext-contentdata-apply: skip-tests ext-build
	./pragma content apply

run: $(CONFIG_FILES)
	cd "$(PLATFORM_ROOT_DIR)" \
	&& java $(REMOTE_DEBUG_PARAMS) -jar $(JAR_PATH) server \
		--backend-type BOTH \
		--database-validator CheckCreateMigrateDatabaseValidator \
		--mode DEVELOPMENT \
		--decrypter PassthroughDecrypter \
		--configuration-filepaths $(CONFIG_PATHS) \
		--content-data-directory 5-ext/content

# TODO move
config-validate:
	cd "$(PLATFORM_ROOT_DIR)" \
	&& java -jar $(JAR_PATH) config validate \
		--backend-type BOTH \
		--mode DEVELOPMENT \
		--decrypter PassthroughDecrypter \
		--configuration-filepaths $(CONFIG_PATHS) \
		--content-data-directory 5-ext/content
