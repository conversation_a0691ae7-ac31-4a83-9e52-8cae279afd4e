syntax = "proto3";

package pragma.account;

import "pragma/types.proto";
import "pragmaOptions.proto";
import "shared/accountRpcExt.proto";

option csharp_namespace = "Pragma.Account";
option (unreal_namespace) = "Account";

message DisplayName {
  string display_name = 1;
  string discriminator = 2;
}

/*
 * An arbitrary grouping of players that can be used to segment the player base.
 */
message PlayerGroup {
  Fixed128 player_group_id = 1;
  string name = 2;
  string description = 3;
  int64 player_count = 4;
}

/*
 * PragmaAccountOverview contains a smaller subset of account information.
 */
message PragmaAccountOverview {
  Fixed128 pragma_social_id = 1;
  DisplayName display_name = 2;
}

/*
 * PragmaPlayerOverview contains a smaller subset of player information.
 */
message PragmaPlayerOverview {
  Fixed128 pragma_player_id = 1;
  Fixed128 pragma_social_id = 2;
  DisplayName display_name = 3;
}

/*
 * IdProviderAccount contains identity provider related information for a single player.
 */
message IdProviderAccount {
  // Enum type of provider (STEAM, DISCORD, etc.).
  string id_provider_type = 1;

  // Specific provider account id used by the Id Provider.
  string account_id = 2;

  // Display name provided by the Id Provider with an optional discriminator.
  DisplayName provider_display_name = 3;

  // If this IdProvider has been deleted
  bool is_active = 5;

  // The time when this IdProvider was unlinked
  int64 last_link_updated_timestamp_millis = 6;
}

/*
 * GameIdentity bundles the a player id and the shard the player id belongs to.
 */
message GameIdentity {
  Fixed128 game_shard_id = 1;
  Fixed128 pragma_player_id = 2;
}

enum BanScope {
  BAN_SCOPE_UNUSED = 0;
  BAN_SCOPE_ALL_GAME_SHARDS = 1;
  BAN_SCOPE_GAME_SHARD = 2;
  BAN_SCOPE_FULL_ACCOUNT = 3;
}

/*
 * Basic account level ban information
 */
message Ban {
  Fixed128 ban_id = 1;
  Fixed128 pragma_social_id = 2;
  bool revoked = 3;
  Fixed128 game_shard_id = 4;
  int64 start_timestamp_millis = 5;
  BanScope ban_scope = 6;
  int64 duration_in_millis = 7;
  ExtBanReason ban_reason = 8;
}

/*
 * Ban object with internal information
 */
message BanRecord {
  Ban ban = 1;
  Fixed128 banned_by = 2;
  Fixed128 revoked_by = 3;
  string comment = 4;
}
