syntax = "proto3";

package pragma.gameinstance;

import "pragma/gameinstance/gameInstanceCommon.proto";
import "pragma/types.proto";
import "pragmaOptions.proto";
import "shared/gameInstanceExt.proto";
import "shared/matchmakingExt.proto";

option csharp_namespace = "Pragma.GameInstance";
option (unreal_namespace) = "GameInstance";

/********************************/
/***** Requests & Responses *****/
/********************************/

/**
 * A service request to create a game instance from the matchmaking service.
 */
message CreateGameInstanceV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    // The ExtMatchmakingKey used while in matchmaking.
    matchmaking.ExtMatchmakingKey ext_matchmaking_key = 1;

    // The desired game server version for the game instance.
    string game_server_version = 2;

    // The desired game server zone for the game instance.
    string game_server_zone = 3;

    reserved 4, 5;

    // Whether or not the game instance will be automatically put back into matchmaking.
    bool will_continue_matchmaking = 6;

    reserved 7;

    // A customer-defined ext for declaring any custom details about how the game instance should be created.
    ExtBackendCreateRequest request_ext = 8;

    // The players from matchmaking for the game instance.
    repeated BackendPlayerToAdd players_to_add = 9;
}

/**
 * A service response that reports the details of the created game instance back to the matchmaking service.
 */
message CreateGameInstanceV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;

    // The id of the created game instance.
    Fixed128 game_instance_id = 1;

    // Ext payload to re-enter matchmaking, populated if the game instance was marked to continue matchmaking.
    matchmaking.ExtMatchmakingGameInstance ext = 2;

    reserved 3;

    // Ext matchmaking payloads for each player, populated if the game instance was marked to continue matchmaking.
    repeated MatchmakingGamePlayerResponse ext_player = 4;
}

/**
 * A service request to create a game instance.
 */
message CreateGameInstanceServiceV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    // The desired game server version for the game instance.
    string game_server_version = 1;

    // The desired game server zone for the game instance.
    string game_server_zone = 2;

    // A customer-defined ext for declaring any custom details about how the game instance should be created.
    ExtBackendCreateRequest request_ext = 3;

    // The players from matchmaking for the game instance.
    repeated BackendPlayerToAdd players_to_add = 4;
}

/**
 * A service response that reports the details of the created game instance.
 */
message CreateGameInstanceServiceV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;

    // The id of the created game instance.
    Fixed128 game_instance_id = 1;
}

/*
 * A service request to retrieve a player's game instance state.
 */
message SyncServiceV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    Fixed128 player_id = 1;
    Fixed128 game_instance_id = 2;
    int64 player_payload_version = 3;
}

/*
 * A service response that describes the game instance state for the requested player.
 */
message SyncServiceV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;

    bool is_in_game_instance = 1;

    oneof result {
        // A full payload of the game instance's latest state.
        BroadcastGameInstance game_instance = 2;

        bool is_up_to_date = 3;
    }
}

/**
 * A service request to remove players from a game instance.
 */
message RemovePlayersServiceV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance the players are being removed from.
    Fixed128 game_instance_id = 1;

    // A customer-defined ext for declaring any custom details.
    ExtBackendRemovePlayersRequest request_ext = 2;

    // The players to remove.
    repeated BackendPlayerToRemove players = 3;
}

/**
 * A service response that acknowledges that the players have been removed from the game instance.
 */
message RemovePlayersServiceV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A partner request to create a new game instance and link the game server to it.
 */
message CreateAndLinkV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // List of player scoped information to facilitate adding players to the game instance.
    repeated BackendPlayerToAdd players_to_add = 1;

    // A customer-defined ext for declaring any custom details about how the game instance should be created.
    ExtBackendCreateRequest request_ext = 2;

    // The desired game server version for the game instance.
    string game_server_version = 3;

    // The desired game server zone for the game instance.
    string game_server_zone = 4;
}

/**
 * Response containing the GameStart data for the newly created and linked game instance.
 */
message CreateAndLinkV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;

    // The start data for the game.
    GameStart game_start = 1;

    // Whether the game server is expected to send keep alive ping or not.
    bool enable_keep_alive = 2;

    // The initial interval that the game server should send keep alive ping.
    int64 keep_alive_interval_millis = 3;
}

/**
 * A partner request to get the start data for a game instance.
 */
message GetGameStartDataV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that the start data is requested for.
    Fixed128 game_instance_id = 1;
}

/**
 * A partner response that contains the start data for the a game instance.
 */
message GetGameStartDataV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;

    // The start data for the game.
    GameStart game_start = 1;

    // Whether the game server is expected to send keep alive ping or not.
    bool enable_keep_alive = 2;

    // The initial interval that the game server should send keep alive ping.
    int64 keep_alive_interval_millis = 3;
}

/**
 * A partner request to unlink from a game instance.
 */
message UnlinkV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that the start data is requested for.
    Fixed128 game_instance_id = 1;

    ExtGameServerUnlinkRequest ext = 2;
}

/**
 * A partner response acknowledging the unlink.
 */
message UnlinkV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A player request to update a running game instance.
 */
message UpdateGameInstanceV1Request {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance.
    Fixed128 game_instance_id = 1;

    // The custom payload defining which updates to make to the game instance.
    ExtPlayerUpdateRequest request_ext = 2;
}

/**
 * A player response that acknowledges that the game instance was updated.
 */
message UpdateGameInstanceV1Response {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = RESPONSE;

    BroadcastGameInstance updated_game_instance = 1;
}

/**
 * A service request to update a game instance.
 */
message UpdateGameInstanceServiceV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance.
    Fixed128 game_instance_id = 1;

    // The custom payload defining which updates to make to the game instance.
    ExtBackendUpdateRequest request_ext = 2;
}

/**
 * A service response that acknowledges that the game instance was updated.
 */
message UpdateGameInstanceServiceV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A partner request used to update the game instance.
 */
message UpdateGameInstancePartnerV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance.
    Fixed128 game_instance_id = 1;

    // The custom payload defining which updates to make to the game instance.
    ExtBackendUpdateRequest request_ext = 2;
}

/**
 * A partner response that acknowledges that the game instance was updated.
 */
message UpdateGameInstancePartnerV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A partner request to provide connection details to the initial players in a game instance.
 */
message ConnectPlayersV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game that is ready for players to join.
    Fixed128 game_instance_id = 1;

    // The hostname for players to connect to.
    string hostname = 2;

    // The port for players to connect to.
    int32 port = 3;

    // A list of customer-defined exts for declaring player-specific connection details.
    repeated PlayerConnectionDetails player_connection_details = 4;
}

/**
 * A partner response that acknowledges the connection details have been sent to the players.
 */
message ConnectPlayersV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A partner request to provide connections details to more players in the game instance.
 */
message ConnectMorePlayersV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that the players should connect to.
    Fixed128 game_instance_id = 1;

    reserved 2, 3;

    // The hostname for players to connect to.
    string hostname = 4;

    // The port for players to connect to.
    int32 port = 5;

    // A list of customer-defined exts for declaring player-specific connection details.
    repeated PlayerConnectionDetails player_connection_details = 6;
}

/**
 * A partner response that acknowledges the connections details have been sent to the players.
 */
message ConnectMorePlayersV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A service request to add players to a game instance.
 */
message AddPlayersServiceV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that the players should be added to.
    Fixed128 game_instance_id = 1;

    // List of player scoped information to facilitate adding players to the GameInstance
    repeated BackendPlayerToAdd players_to_add = 2;

    // Ext for the request
    ExtBackendAddPlayersRequest ext_request = 3;
}

/**
 * A service response that acknowledges the players have been added to the game instance.
 */
message AddPlayersServiceV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A partner request to signal that the game server is healthy and running.
 */
message GameKeepAliveV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that is sending this keep alive ping.
    Fixed128 game_instance_id = 1;
}

/**
 * A partner response that returns the interval of when the next keep alive call should be made.
 */
message GameKeepAliveV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;

    // Whether the game server is expected to continue sending keep alive pings or not.
    bool enable_keep_alive = 1;

    // The updated interval that the game server should send keep alive ping.
    int64 keep_alive_interval_millis = 2;
}

/**
 * A partner request to enter matchmaking and find more players for a game instance.
 */
message EnterMatchmakingV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that is requesting more players.
    Fixed128 game_instance_id = 1;

    reserved 2;
}

/**
 * A partner response that acknowledges the game instance has been entered into matchmaking.
 */
message EnterMatchmakingV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A service request to add more players from matchmaking to a game instance.
 */
message FoundMorePlayersV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance these players should join.
    Fixed128 game_instance_id = 1;

    // The ExtMatchmakingKey used for this game instance in matchmaking.
    matchmaking.ExtMatchmakingKey ext_matchmaking_key = 2;

    reserved 3;

    // The players from matchmaking for the game instance.
    repeated GameInstancePlayer players = 4;

    // Customer populated field for passing custom data that applies to all players being added to a GameInstance
    ExtBackendAddPlayersRequest request_ext = 5;
}

/**
 * A service response that reports the found players were added to the game instance.
 */
message FoundMorePlayersV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;

    reserved 1, 2;
}

/**
 * A partner request to remove the game instance from matchmaking.
 */
message LeaveMatchmakingV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance to remove.
    Fixed128 game_instance_id = 1;
}

/**
 * A partner response that acknowledges the game instance has been removed from matchmaking.
 */
message LeaveMatchmakingV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A service request to alert a game instance that it has been removed from matchmaking.
 */
message RemovedGameInstanceFromMatchmakingV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that was removed from matchmaking.
    Fixed128 game_instance_id = 1;
}

/**
 * A service response that acknowledges the game instance has been removed from matchmaking.
 */
message RemovedGameInstanceFromMatchmakingV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A partner request to remove players from a game instance.
 */
message RemovePlayersV2Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance the players are being removed from.
    Fixed128 game_instance_id = 1;

    // A customer-defined ext for declaring any custom details.
    ExtBackendRemovePlayersRequest request_ext = 2;

    // The players to remove.
    repeated BackendPlayerToRemove players = 3;
}

/**
 * A partner response that acknowledges the players have been removed from the game instance.
 */
message RemovePlayersV2Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A partner request to end a game instance.
 */
message EndGameV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance that is ending.
    Fixed128 game_instance_id = 1;

    // The list of players being completing the game instance.
    repeated PlayerGameResult player_game_results = 2;

    // A customer-defined ext for declaring any custom details.
    ExtEndGameRequest ext = 3;
}

/**
 * A partner response that acknowledges that the game instance has ended.
 */
message EndGameV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;
}

/**
 * A player request to create a game instance.
 */
message CreateV1Request {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = REQUEST;

    ExtPlayerCreateRequest request_ext = 1;
}

/**
 * A player response that contains the id of the game instance that was created.
 */
message CreateV1Response {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = RESPONSE;

    BroadcastGameInstance game_instance = 1;
}

/**
 * A player request to join a game instance.
 */
message JoinGameInstanceV1Request {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance.
    Fixed128 game_instance_id = 1;

    // A customer-defined ext for declaring any custom details about joining the game instance.
    ExtPlayerJoinGameInstanceRequest request_ext = 2;
}

/**
 * A player response that reports the details of the game instance.
 */
message JoinGameInstanceV1Response {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = RESPONSE;

    // The id of the game instance.
    BroadcastGameInstance game_instance = 1;
}

/**
 * A player request leave a game instance.
 */
message LeaveV1Request {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance to leave
    Fixed128 game_instance_id = 1;

    ExtPlayerLeaveRequest request_ext = 2;
}

/**
 * A player response that reports the details of the game instance.
 */
message LeaveV1Response {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = RESPONSE;

    // The id of the game instance.
    BroadcastGameInstance game_instance = 1;
}

/**
 * A partner request to verify a connection token for a player in a game instance.
 */
message VerifyPlayerPartnerV1Request {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = REQUEST;

    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;

    // The player to verify.
    Fixed128 player_id = 2;

    // The client-provided connection token to verify.
    string connection_token = 3;
}

/**
 * A partner response that indicates if the connection token is valid.
 */
message VerifyPlayerPartnerV1Response {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = RESPONSE;

    // Whether the connection token is valid for the player.
    bool is_valid = 1;
}

/**
 * A service request to fetch information about a game instance's cached data.
 */
message GetCacheInfoServiceV1Request {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = REQUEST;

    Fixed128 game_instance_id = 1;
}

/**
 * A service response that contains information about a game instance's cached data.
 */
message GetCacheInfoServiceV1Response {
    option (pragma_session_type) = SERVICE;
    option (pragma_message_type) = RESPONSE;

    GameInstanceSummary summary = 1;
}

/**
 * An operator request to fetch information about a game instance's cached data.
 */
message GetCacheInfoOperatorV1Request {
    option (pragma_session_type) = OPERATOR;
    option (pragma_message_type) = REQUEST;

    Fixed128 game_instance_id = 1;
}

/**
 * An operator response that contains information about a game instance's cached data.
 */
message GetCacheInfoOperatorV1Response {
    option (pragma_session_type) = OPERATOR;
    option (pragma_message_type) = RESPONSE;

    GameInstanceSummary summary = 1;
}

/*************************/
/***** Notifications *****/
/*************************/

/**
 * A player notification that a game instance has terminated.
 */
message GameInstanceTerminationV1Notification {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance that was terminated.
    Fixed128 game_instance_id = 1;

    // The reason the game instance was terminated.
    GameInstanceTerminationReason reason = 2;
}

/**
 * A partner notification that a game instance has been terminated.
 */
message GameInstanceTerminationPartnerV1Notification {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance that was terminated.
    Fixed128 game_instance_id = 1;

    // The reason the game instance was terminated.
    GameInstanceTerminationReason reason = 2;

}

/**
* A partner notification that players have been added to a game instance.
*/
message PlayersAddedV1Notification {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;

    // A list of new players that need to be connected to the game.
    repeated GameServerPlayer game_server_players = 2;
}

/**
 * A partner notification that players have been removed from a game instance.
 */
message PlayersRemovedV1Notification {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;

    // The players removed from the game instance.
    repeated GameServerPlayer game_server_players = 2;
}

/**
 * A partner notification that a game instance has entered matchmaking.
 */
message GameInstanceEnteredMatchmakingV1Notification {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;
}

/**
 * A partner notification that a game instance has left matchmaking.
 */
message GameInstanceLeftMatchmakingV1Notification {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;
}

/**
 * A partner notification that informs GameServers that a game has been ended. Sent when the GameServer is not the one originating this request.
 */
message GameEndedPartnerV1Notification {
    option (pragma_session_type) = PARTNER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance that ended
    Fixed128 game_instance_id = 1;
}

/**
 * A player notification that contains custom removed from game data.
 */
message RemovedFromGameV1Notification {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;

    // A customer-defined ext for declaring any custom details about the player being removed from the game.
    ExtRemovedFromGame ext = 2;
}

/**
 * A player notification that contains custom end game data.
 */
message GameEndedV1Notification {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = NOTIFICATION;

    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;

    // A customer-defined ext for declaring any custom details about the player completing the game.
    ExtGameEnded ext = 2;
}

/**
 * A player notification that contains the updated state of a game instance.
 */
message GameInstanceDetailsV1Notification {
    option (pragma_session_type) = PLAYER;
    option (pragma_message_type) = NOTIFICATION;

    // A full payload of the latest game instance state.
    BroadcastGameInstance game_instance = 1;
}

/***************************/
/***** Shared Payloads *****/
/***************************/

/**
 * A server payload representing all of the details required to create a game instance.
 */
message GameStart {
    // The id of the game instance this data is related to.
    Fixed128 game_instance_id = 1;

    // The players for this game instance.
    repeated GameServerPlayer players = 2;

    // A customer-defined ext declaring any custom details about the game instance starting.
    ExtGameStart ext = 3;

    bool is_in_matchmaking = 4;
}

/**
 * A server payload representing all of the details required to create a game instance.
 */
message GameServerPlayer {
    // The game id of the player.
    Fixed128 player_id = 1;

    reserved 2, 3;

    // The id of the player's party.
    Fixed128 party_id = 4;

    // The team number the player is in.
    int64 team_number = 5;

    reserved 6;

    // A customer-defined ext declaring any custom player information for the player in the game instance.
    ExtGameServerPlayer ext = 7;
}

/**
 * Different reasons in which a player would be removed from a game instance due to termination.
 */
enum GameInstanceTerminationReason {
    UNSPECIFIED = 0;
    reserved 1, 2, 3;
    GAME_SERVER_DISCONNECTED = 4;
    GAME_SERVER_FAILED_TO_CONNECT_PLAYERS = 5;
    EXPIRED = 6;
    GAME_SERVER_ALLOCATION_FAILED = 7;
}

/**
 * Used to communicate new MatchmakingGamePlayer exts back to matchmaking that
 * are associated with players within the game.
 */
message MatchmakingGamePlayerResponse {
    Fixed128 player_id = 1;
    matchmaking.ExtMatchmakingGamePlayer ext = 2;
}

/**
 * Used to communicate new ExtPlayerConnectionDetails exts to player when
 * players are being added to a game server.
 */
message PlayerConnectionDetails {
    Fixed128 player_id = 1;
    ExtPlayerConnectionDetails ext_player_connection_details = 2;
}
