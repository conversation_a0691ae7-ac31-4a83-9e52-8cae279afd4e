syntax = "proto3";

package pragma.inventory;

import "pragma/inventory/inventoryCommon.proto";
import "pragma/inventory/inventoryContent.proto";
import "pragma/inventory/rewardContent.proto";
import "pragma/payment/paymentContent.proto";
import "pragma/response.proto";
import "pragma/types.proto";
import "pragmaOptions.proto";
import "shared/inventoryRpcExt.proto";

option csharp_namespace = "Pragma.Inventory";
option (unreal_namespace) = "Inventory";

message DeleteAllInventoryDbAndCacheV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;
}

message DeleteAllInventoryDbAndCacheV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;
}

message ClearAllInventoryCacheServiceV1Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;
}

message ClearAllInventoryCacheServiceV1Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;
}

message ClearAllInventoryCacheOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;
}

message ClearAllInventoryCacheOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;
}

message DeletePlayerInventoryV3Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;
}

message DeletePlayerInventoryV3Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;
}

message DeletePlayerInventoryServiceV1Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
}

message DeletePlayerInventoryServiceV1Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;
}

message DeletePlayerInventoryOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
}

message DeletePlayerInventoryOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;
}

message DeletePlayerInventoryPartnerV1Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
}

message DeletePlayerInventoryPartnerV1Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;
}

message ClearPlayerInventoryCacheServiceV1Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
}

message ClearPlayerInventoryCacheServiceV1Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;
}

message ClearPlayerInventoryCachePartnerV1Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
}

message ClearPlayerInventoryCachePartnerV1Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;
}

message ClearPlayerInventoryCacheOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
}

message ClearPlayerInventoryCacheOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;
}

// CODE_STANDARD_DOCUMENTATION_PROTO
/*
 * PurchaseRequest is a client payload that attempts to purchase an item from a store
 */
message PurchaseRequest {
  // customer-defined ext used to determine if the purchase is valid
  inventory.ext.ExtPurchaseRequest ext = 1;

  // ID of store the entry ID is found in
  string store_id = 2;

  // ID of store entry of above store
  string store_entry_id = 3;

  // the amount to purchase
  int64 amount = 4;

  // list of instance item ids used in the purchase. If purchase is valid, instanced items will be consumed
  repeated Fixed128 instanced_items_to_pay = 5;
}

/*
 * RPC wrapper of PurchaseRequest. Please see InventoryRpc.PurchaseRequest for more details
 */
message StorePurchaseV4Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  // wrapped data
  PurchaseRequest data = 1;
}

message StorePurchaseV4Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  inventory.InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the purchase as an UpdateSummary.
  inventory.UpdateSummaryV0 delta = 2;
}

/*
 * Initialize an order for a player as a Steam Microtransaction order.
 */
message InitializeOrderV1Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  // The id of the ProductEntry the player is purchasing.
  string product_entry_id = 1;

  // The ISO 639-1 language code of the product entry description
  // Must be a [Steam Api supported language code](https://partner.steamgames.com/doc/store/localization/languages)
  string language_code = 2;

  // The description of the product entry to display in the Steam purchase dialog
  // Must be less than 128 characters, as it passed to [Steam InitTxn api](https://partner.steamgames.com/doc/webapi/ISteamMicroTxn#InitTxn)
  string description = 3;
}

/*
 * Return an empty response in the event of the order successfully being initialized.
 */
message InitializeOrderV1Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;
}

/*
 * Finalize an order for a player.
 */
message FinalizeOrderV1Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  // The id of the order to finalize.
  string order_id = 1;
}

/*
 * Returns an update of a player's inventory once an order was successfully finalized.
 */
message FinalizeOrderV1Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // Contains update of items that were purchased as the result of an order.
  UpdateItemsResponse mtx_response = 1;
}

/*
 * Cancel an initialized order for a player.
 */
message CancelOrderV1Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  // The id of the order to cancel.
  string order_id = 1;
}

/*
 * Return an empty response in the event of the order successfully being cancelled.
 */
message CancelOrderV1Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;
}

/*
 * Finalize in-progress orders on behalf of a player.
 */
message FinalizeInProgressOrdersV1Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;
}

/*
 * Returns a list of bundle id strings for each unfulfilled order that has been fulfilled.
 */
message FinalizeInProgressOrdersV1Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // A list of the id for each ItemBundle that were awarded after processing any unfulfilled orders. See InventoryContent.ItemBundle for more detail.
  repeated string granted_bundle_ids = 1;
}

/*
 * Sync entitlements from configured providers and grant appropriate items on behalf of a player.
 */
message SyncEntitlementsV1Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  repeated ProviderData provider_data = 1;
}

message ProviderData {
  string provider = 1;
  string auth_code = 2;
}

/*
 * Represents an entitlement from a specific provider.
 */
message ProviderEntitlementV1 {
  // The identifier of the provider granting the entitlement.
  string provider_id = 1;

  // The provider's identifier of the entitlement, mapped to Pragma Inventory content in ProviderEntitlements.
  string provider_item_id = 2;
}

/*
 * Returns lists of fulfilled and unfulfilled entitlements by their provider and provider item ID.
 * Details of the items granted can be looked up in the ProviderEntitlementMappings content file.
 * Subsequent calls will attempt to fulfill any unfulfilled entitlements.
 */
message SyncEntitlementsV1Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // List of Provider and Item ID for all entitlements that were fulfilled by this request.
  repeated ProviderEntitlementV1 fulfilled = 1;

  // List of Provider and Item ID for all entitlements that were available but unable to be fulfilled
  repeated ProviderEntitlementV1 unfulfilled = 2;
}

/*
 * Sync entitlements from configured providers and grant appropriate items on behalf of a partner.
 */
message SyncEntitlementsPartnerV1Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
  repeated ProviderData provider_data = 2;
}

/*
 * Returns lists of fulfilled and unfulfilled entitlements by their provider and provider item ID.
 * Details of the items granted can be looked up in the ProviderEntitlementMappings content file.
 * Subsequent calls will attempt to fulfill any unfulfilled entitlements.
 */
message SyncEntitlementsPartnerV1Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // List of Provider and Item ID for all entitlements that were fulfilled by this request.
  repeated ProviderEntitlementV1 fulfilled = 1;

  // List of Provider and Item ID for all entitlements that were available but unable to be fulfilled
  repeated ProviderEntitlementV1 unfulfilled = 2;
}

/*
 * Sync entitlements from configured providers and grant appropriate items on behalf of a partner.
 */
message SyncEntitlementsServiceV1Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
  repeated ProviderData provider_data = 2;
}

/*
 * Returns lists of fulfilled and unfulfilled entitlements by their provider and provider item ID.
 * Details of the items granted can be looked up in the ProviderEntitlementMappings content file.
 * Subsequent calls will attempt to fulfill any unfulfilled entitlements.
 */
message SyncEntitlementsServiceV1Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  Fixed128 player_id = 1;

  // List of Provider and Item ID for all entitlements that were fulfilled by this request.
  repeated ProviderEntitlementV1 fulfilled = 2;

  // List of Provider and Item ID for all entitlements that were available but unable to be fulfilled
  repeated ProviderEntitlementV1 unfulfilled = 3;
}

/*
 * Sync entitlements from configured providers and grant appropriate items on behalf of a partner.
 */
message SyncEntitlementsOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
  repeated ProviderData provider_data = 2;
}

/*
 * Returns lists of fulfilled and unfulfilled entitlements by their provider and provider item ID.
 * Details of the items granted can be looked up in the ProviderEntitlementMappings content file.
 * Subsequent calls will attempt to fulfill any unfulfilled entitlements.
 */
message SyncEntitlementsOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // List of Provider and Item ID for all entitlements that were fulfilled by this request.
  repeated ProviderEntitlementV1 fulfilled = 1;

  // List of Provider and Item ID for all entitlements that were available but unable to be fulfilled
  repeated ProviderEntitlementV1 unfulfilled = 2;
}

/*
 * Player RPC to update an Instanced Item
 */
message UpdateItemV4Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  reserved 1;
  InstancedItemUpdate instanced_item_update = 2;
}

message UpdateItemV4Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the item update as an UpdateSummary.
  UpdateSummaryV0 delta = 2;
}

/*
 * Allows operator-to-service requests to update inventory items.
 * If any of the various item updates fails, the operation aborts and no change to the player's inventory occurs.
 */
message UpdateItemsOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  // The playerId of the inventory to perform updates on.
  Fixed128 player_id = 1;

  // Container for the various item updates to apply.
  repeated ServerItemUpdateV2 updates = 2;
}

message UpdateItemsOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the item update as an UpdateSummary.
  UpdateSummaryV0 delta = 2;
}

/*
 * Allows partner-to-service requests to update inventory items.
 * If any of the various item updates fails, the operation aborts and no change to the player's inventory occurs.
 */
message UpdateItemsPartnerV2Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  // The playerId of the inventory to perform updates on.
  Fixed128 player_id = 1;

  // Container for the various item updates to apply.
  repeated ServerItemUpdateV2 updates = 2;
}

message UpdateItemsPartnerV2Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // The UpdateItemsResponse as a result of the initial request, partitioned by visibility
  ServiceUpdateItemsResponse updates = 1;
}

/*
 * A response to an update items request split by the visibility of the contents
 */
message ServiceUpdateItemsResponse {
  // Contains updates for inventory items that should not be visible/known to the player.
  UpdateItemsResponse hidden = 1;

  // Contains updates for inventory items that the player is allowed to see/know about.
  UpdateItemsResponse player = 2;
}

message UpdateItemsResponse {
  // Represents a portion of an inventory as an InventorySegmentV1Data.
  InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the item update as an UpdateSummary.
  UpdateSummaryV0 delta = 2;
}

/*
 * Allows service-to-service requests to update inventory items.
 * If any of the various item updates fails, the operation aborts and no change to the player's inventory occurs.
 */
message UpdateItemsServiceV2Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  // The playerId of the inventory to perform updates on.
  Fixed128 player_id = 1;

  // Container for the various item updates to apply.
  repeated ServerItemUpdateV2 updates = 2;
}

message UpdateItemsServiceV2Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  // The UpdateItemsResponses as a result of the initial request, partitioned by visibility
  ServiceUpdateItemsResponse updates = 1;
}


/*
 * Player RPC for updating multiple instanced items
 */
message UpdateItemsV0Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  reserved 1;
  repeated InstancedItemUpdate instanced_item_updates = 2;
}

message UpdateItemsV0Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the items updates as an UpdateSummary.
  UpdateSummaryV0 delta = 2;
}

/*
 * RPC wrapper of CraftRequest. Please see InventoryRpc.CraftRequest for more details
 */
message CraftV1Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;

  // Wrapped CraftRequest data.
  CraftRequest craft_request = 1;
}

message CraftV1Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the crafting call as an UpdateSummary.
  UpdateSummaryV0 delta = 2;

  // A list of any errors as a result of crafting failure.
  repeated string errors = 3;
}

/*
 * CraftRequest is a client payload that attempts to craft an item based on a recipe.
 */
message CraftRequest {

  // A customer-defined ext used to add item information for the crafted item.
  inventory.ext.ExtCraftRequest ext = 1;

  // ID of crafting entry
  string crafting_entry_id = 2;

  // List of instanced ids for instance items to destroy as materials in performing crafting.
  repeated Fixed128 items_to_destroy = 3;
}

/*
 * Service request for data - used by the DefaultPragmaLoginDataPlugin
 */
message GetLoginDataServiceV2Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
}

/*
 *  Used by the DefaultPragmaLoginDataPlugin
 */
message GetLoginDataServiceV2Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  repeated string issued_limited_grant_tracking_ids = 1;
  inventory.ItemCatalog item_catalog = 2;
  repeated AnnotatedStore stores = 3;
  repeated inventory.UpdateEntry update_entries = 4;
  repeated inventory.CraftingEntry crafting_entries = 5;
  repeated inventory.ItemBundle item_bundles = 6;
  repeated inventory.ProviderEntitlement provider_entitlements = 7;
}

message GetInventoryV2Request {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = REQUEST;
}

message GetInventoryV2Response {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = RESPONSE;

  // Represents the entire inventory of a player as an InventoryFullV1Data.
  InventoryFullV1Data full = 1;
}

/*
 * A service to service request for a player's inventory.
 */
message GetInventoryServiceV3Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
  /* tags to filter player inventory on, only needs to match a single tag */
  repeated string tags_to_include = 2;
}

/*
 * A response containing a player's inventory.
 */
message GetInventoryServiceV3Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  // The player's inventory.
  ServiceInventoryData inventory = 1;
}

/*
 * A player's inventory split by the visibility of the contents.
 */
message ServiceInventoryData {
  // Contains inventory items that should not be visible/known to the player.
  InventoryFullV1Data hidden = 1;
  // Contains inventory items that the player is allowed to see/know about.
  InventoryFullV1Data player = 2;
}

/*
 * Represents a delta between stackable items.
 */
message StackableDelta {
  // Catalog ID for the stackable item.
  string catalog_id = 1;

  // List of tags associated to the stackable item.
  repeated string tags = 2;

  // Difference in stackable item quantity, either removed or added.
  int64 amount_changed = 3;
}

enum InstancedDeltaOperation {
  UNKNOWN = 0;
  ADDED = 1;
  REMOVED = 2;
  UPDATED = 3;
}

/*
 * Represents a delta between instanced items.
 */
message InstancedDeltaV2 {
  // Catalog ID for the instanced item.
  string catalog_id = 1;

  // List of tags associated to the instanced item.
  repeated string tags = 2;

  // The operation of this delta, represented as InstancedDeltaOperation enums.
  InstancedDeltaOperation operation = 3;

  // The original instanced item.
  inventory.InstancedItem initial = 4;

  // The new instanced item.
  inventory.InstancedItem final = 5;
}

/*
 * Represents a summary of differences in an inventory.
 */
message UpdateSummaryV0 {
  // A list of changes applied to stackable items.
  repeated StackableDelta stackables = 1;

  // A list of changes applied to instanced items.
  repeated InstancedDeltaV2 instanced = 2;
}

/*
 * RPC wrapper of RewardGrant for simulating what would happen if RewardGrants were to be applied to an inventory.
 * Please see InventoryRpc.RewardGrant for more details
 */
message SimulateRewardsV2Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  // List of grants to simulate rewards for, represented as RewardGrant.
  repeated RewardGrant reward_grants = 1;
}

message SimulateRewardsV2Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // Summary of changes to the inventory based on simulation of rewards, as an UpdateSummaryV0.
  UpdateSummaryV0 inventory_delta = 1;
}

message ServiceGrantRewardsV2Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
  repeated RewardGrant reward_grants = 2;
}

message ServiceGrantRewardsV2Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  UpdateSummaryV0 inventory_delta = 1;
}

/*
 * A call for directly rewarding grants to the inventory of a given player.
 */
message OperatorGrantRewardsV0Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  // List of rewards to be given, as RewardGrant.
  repeated RewardGrant reward_grants = 2;
}

message OperatorGrantRewardsV0Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // Summary of changes to the inventory with given rewards, as an UpdateSummaryV0.
  UpdateSummaryV0 inventory_delta = 1;
}

/*
 * A call for directly rewarding items to the inventory of a given player.
 */
message GrantItemsOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  // List of items to be given, represented by ItemServerGrantV1.
  repeated ItemServerGrantV1 item_grants = 2;
}

message GrantItemsOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  inventory.InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the direct item grant as an UpdateSummary.
  inventory.UpdateSummaryV0 delta = 2;
}

/*
 * A call for directly rewarding items to the inventory of a given list of players.
 */
message GrantItemsBulkOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  repeated Fixed128 player_ids = 1;

  // List of items to be given, represented by ItemServerGrantV1.
  repeated ItemServerGrantV1 item_grants = 2;
}

message GrantItemsBulkOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  map<string, GrantItemsResponse> grants = 1;
}

message GrantItemsResponse {
  // Represents a portion of an inventory as an InventorySegmentV1Data.
  inventory.InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the direct item grant as an UpdateSummary.
  inventory.UpdateSummaryV0 delta = 2;

  ServiceError error = 3;
}

/*
 * A call for directly rewarding items to the inventory of a given player.
 */
message GrantItemsPartnerV2Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  // List of items to be given, represented by ItemServerGrantV1.
  repeated ItemServerGrantV1 item_grants = 2;
}

message GrantItemsPartnerV2Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // The UpdateItemsResponses as a result of the initial request, partitioned by visibility
  ServiceUpdateItemsResponse updates = 1;
}

message GrantItemsServiceV2Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
  repeated ItemServerGrantV1 item_grants = 2;
}

message GrantItemsServiceV2Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  // The UpdateItemsResponses as a result of the initial request, partitioned by visibility
  ServiceUpdateItemsResponse updates = 1;
}

/*
 * A call for directly destroying items from the inventory of a given player.
 */
message DestroyItemsServiceV2Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  // The list of items to destroy, as ItemServerDestroy.
  repeated ItemServerDestroyV1 item_destroys = 2;
}

message DestroyItemsServiceV2Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  // The UpdateItemsResponses as a result of the initial request, partitioned by visibility
  ServiceUpdateItemsResponse updates = 1;
}

/*
 * A call for directly destroying items from the inventory of a given player.
 */
message DestroyItemsOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  // The list of items to destroy, as ItemServerDestroy.
  repeated ItemServerDestroyV1 item_destroys = 2;
}

message DestroyItemsOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  inventory.InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the destroy as an UpdateSummary.
  inventory.UpdateSummaryV0 delta = 2;
}

/*
 * A call for directly destroying items from the inventory of a given player.
 */
message DestroyItemsPartnerV2Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  // The list of items to destroy, as ItemServerDestroy.
  repeated ItemServerDestroyV1 item_destroys = 2;
}

message DestroyItemsPartnerV2Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // The UpdateItemsResponse as a result of the initial request, partitioned by visibility
  ServiceUpdateItemsResponse updates = 1;
}

/*
 * Represents a player's inventory in its entirety.
 */
message InventoryFullV1Data {
  // List of all stackable items in the inventory.
  repeated inventory.StackableItem stackables = 1;

  // List of all instanced items in the inventory.
  repeated inventory.InstancedItem instanced = 2;

  // Revision number of the inventory.
  int64 version = 3;
}

/*
 * Represents a portion of a player's inventory
 */
message InventorySegmentV1Data {
  // List of stackable items in this segment.
  repeated inventory.StackableItem stackables = 1;

  // List of instanced items in this segment.
  repeated inventory.InstancedItem instanced = 2;

  // List of stackable items removed.
  repeated Fixed128 removed_stackables = 3;

  // List of instanced items removed.
  repeated Fixed128 removed_instanced = 4;

  // Prior revision number of the inventory.
  int64 previous_version = 5;

  // Current revision number of the inventory.
  int64 version = 6;
}

/**
 * An RPC Service Request to update players' data on game end
 */
message MatchEndV4Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;
  MatchEndV4 match_end = 2;
}

/**
 * An RPC response to summarize a players' data updates on game end.
 */
message MatchEndV4Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  Fixed128 match_id = 1;
  Fixed128 player_id = 2;

  // The updates to the players inventory as a result of this game end.
  ServiceUpdateSummary update_summary = 3;
}

/*
 * A representation of updates made to a player's inventory split by the
 * visibility of the contents that were updated.
 */
message ServiceUpdateSummary {
  // Contains updates to inventory items that should not be visible/known to the player.
  UpdateSummaryV0 hidden = 1;

  // Contains updates to inventory items that the player is allowed to see/know about.
  UpdateSummaryV0 player = 2;
}

message GetInventoryOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  /* tags to filter player inventory on, only needs to match a single tag */
  repeated string tags_to_include = 2;
}

message GetInventoryOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // Represents the player's inventory in its entirety, as an InventoryFullV1Data.
  InventoryFullV1Data full = 1;
}

message GetInventoryPartnerV2Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  Fixed128 player_id = 1;

  /* tags to filter player inventory on, only needs to match a single tag */
  repeated string tags_to_include = 2;
}

message GetInventoryPartnerV2Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // The player's inventory partitioned into hidden and player visible.
  ServiceInventoryData inventory = 1;
}

message GetCatalogOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  enum Catalog {
    Undefined = 0;
    Stores = 1;
    Rewards = 2;
    Items = 3;
    Updates = 4;
    Crafting = 5;
    ItemBundles = 6;
    LimitedGrants = 7;
  }

  // The type of catalog to retrieve.
  Catalog catalog = 1;
}

message GetCatalogOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // The name of the catalog.
  string name = 1;

  oneof payload {
    // Represents store catalogs.
    StoresCatalog stores = 2;

    // Represents rewards catalogs.
    RewardsCatalog rewards = 3;

    // Represents item catalogs.
    inventory.ItemCatalog items = 4;

    // Represents update catalogs.
    UpdateEntryCatalog updates = 5;

    // Represents crafting catalogs.
    CraftingEntryCatalog crafting = 6;

    // Represents item bundles.
    ItemBundles item_bundles = 7;

    // Represents limited grants.
    LimitedGrants limited_grants = 8;
  }
}

/*
 * Contains all stores.
 */
message StoresCatalog {
  // List of all stores, as Store.
  repeated inventory.Store stores = 1;
}

/*
 * Contains all update entries.
 */
message UpdateEntryCatalog {
  // List of all update entries, as UpdateEntry.
  repeated inventory.UpdateEntry update_entries = 1;
}

/*
 * Contains all crafting entries.
 */
message CraftingEntryCatalog {
  // List of all crafting entries, as CraftingEntry.
  repeated inventory.CraftingEntry crafting_entries = 1;
}

message UpdateContentSourceDataV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  string content_type = 1;
  string filename = 2;
  string src_json = 3;
}

message UpdateContentSourceDataV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;
}

message UpdateRawContentSourceDataV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  string content_type = 1;
  string filename = 2;
  string src_json = 3;
}

message UpdateRawContentSourceDataV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;
}

message UpdateRawContentSourceDataServiceV1Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  string content_type = 1;
  string filename = 2;
  string src_json = 3;
}

message UpdateRawContentSourceDataServiceV1Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;
}

message ApplyContentChangesV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;
}

message ApplyContentChangesV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  oneof message {
    // Return payload for errors resulting from applying content changes.
    ApplyContentChangesMessages error_summary = 1;

    // Return payload for summation of inventory changes with a successful applied content change.
    ApplyContentChangesMessages delta_applied = 2;
  }
}

message MetaData {
  string name = 1;
  string type = 2;
}

message File {
  bytes content = 1;
}

enum Status {
  PENDING = 0;
  IN_PROGRESS = 1;
  SUCCESS = 2;
  FAILED = 3;
}

message ApplyContentChangesMessages {
  repeated string messages = 1;
}

message PlanContentChangesOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;
}

message PlanContentChangesOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  oneof message {
    // Return payload for errors resulting from planning content changes.
    PlanContentChangesMessages error_summary = 1;

    // Return payload for planned content changes.
    PlanContentChangesResponse content_changes = 2;
  }
}

message PlanContentChangesMessages {
  repeated string messages = 1;
}

message PlanContentChangesResponse {
  repeated PlanContentEntry new_content_data = 1;
  repeated PlanContentEntry changed_content_data = 2;
  repeated PlanContentEntry removed_content_data = 3;
}

message PlanContentEntry {
  string entry_id = 1;
  string content_type = 2;
}

/*
 * Request to obtain a list of items available for a content version.
 */
message ContentVersionCountsV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;
}

/*
 * Returns the list of items available for the content version.
 */
message ContentVersionCountsV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // List of instanced items available, as VersionCount.
  repeated VersionCount instanced_item_counts_by_version = 1;

  // List of stackable items available, as VersionCount.
  repeated VersionCount stackable_item_counts_by_version = 2;
}

/*
 * Represents a version for a catalog entry.
 */
message VersionCount {
  // The version for the catalog entry.
  int64 version = 1;

  // An incremental count for the catalog entry.
  int64 count = 2;
}

message RewardsCatalog {
  // A list of available individual rewards, as Reward. See rewardContent.Reward for more details.
  repeated Reward rewards = 1;

  // A list of available individual rewards as a range of potential items, as RewardTable. See rewardContent.RewardTable for more details.
  repeated RewardTable reward_tables = 2;

  // A list of available individual rewards as a weighted probability of potential items, as RewardBag. See rewardContent.RewardBag for more details.
  repeated RewardBag reward_bags = 3;

  // A list of available individual rewards with possibility of failure, as RewardSlot. See rewardContent.RewardSlot for more details.
  repeated RewardSlot reward_slots = 4;
}

/*
 * All operations that can be done on a players inventory in one request.
 */
message DataOperations {
  // list of stackables to grant
  repeated StackableItemGrant stackable_item_grants = 1;

  // list of stackables to update - can apply negative amounts
  repeated StackableItemUpdate stackable_item_updates = 2;

  // list of instanced items to grant
  repeated InstancedItemServerGrant instanced_item_server_grants = 3;

  // list of instanced items to update
  repeated ServerInstancedItemUpdate server_instanced_item_updates = 4;

  // list of instanced items to destroy
  repeated InstancedItemServerDestroy instanced_item_server_destroys = 5;

  // list of rewards to grant
  repeated RewardGrant reward_grants = 6;

  // assign this key to enforce these data operations are only applied one time
  string idempotency_key = 7;
}

/*
 * Operator request to apply a set of inventory operations to a player's inventory.
 */
message ApplyInventoryOperationsOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  // The playerId of the inventory to perform updates on.
  Fixed128 player_id = 1;

  // contains all inventory data operations that should be preformed
  DataOperations data_operations = 2;
}

/*
 * Operator response to apply inventory operations that includes modified inventory segment and update summary.
 */
message ApplyInventoryOperationsOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the item update as an UpdateSummary.
  UpdateSummaryV0 delta = 2;
}

/*
 * Partner request to apply a set of inventory operations to a player's inventory.
 */
message ApplyInventoryOperationsPartnerV1Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  // The playerId of the inventory to perform updates on.
  Fixed128 player_id = 1;

  // contains all inventory data operations that should be preformed
  DataOperations data_operations = 2;
}

/*
 * Operator response to apply inventory operations that includes update item summary of hidden and player items modified.
 */
message ApplyInventoryOperationsPartnerV1Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // contains hidden and player facing inventory segments and update summaries of affected items
  ServiceUpdateItemsResponse updates = 1;
}

/*
 * Service request to apply a set of inventory operations to a player's inventory.
 */
message ApplyInventoryOperationsServiceV1Request {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = REQUEST;

  // The playerId of the inventory to perform updates on.
  Fixed128 player_id = 1;

  // contains all inventory data operations that should be preformed
  DataOperations data_operations = 2;
}

/*
 * Service response to apply inventory operations that includes update item summary of hidden and player items modified.
 */
message ApplyInventoryOperationsServiceV1Response {
  option (pragma_session_type) = SERVICE;
  option (pragma_message_type) = RESPONSE;

  // contains hidden and player facing inventory segments and update summaries of affected items
  ServiceUpdateItemsResponse updates = 1;
}

/*
 *   Endpoint that returns list of a player's entitlement history records
 */
message GetProviderEntitlementHistoryPartnerV1Request {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = REQUEST;

  // The playerId to return records for
  Fixed128 player_id = 1;

  // The index of the page to view for pagination.
  int32 page_index = 2;

  // The size of the page to view for pagination, equivalent to the entry count of historical purchases.
  int32 page_size = 3;
}


message GetProviderEntitlementHistoryPartnerV1Response {
  option (pragma_session_type) = PARTNER;
  option (pragma_message_type) = RESPONSE;

  // list of player's entitlement history records
  repeated ProviderEntitlementRecord provider_entitlement_records = 1;
}

/*
 *   Endpoint that returns list of a player's entitlement history records - to use by portal for support
 */
message GetProviderEntitlementHistoryOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  // The playerId to return records for
  Fixed128 player_id = 1;

  // The index of the page to view for pagination.
  int32 page_index = 2;

  // The size of the page to view for pagination, equivalent to the entry count of historical purchases.
  int32 page_size = 3;
}


message GetProviderEntitlementHistoryOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  // list of player's entitlement history records
  repeated ProviderEntitlementRecord provider_entitlement_records = 1;
}

/*
 * A ProviderEntitlementRecord contains processing data for a provider entitlement
 */
message ProviderEntitlementRecord {
  // player id from request
  Fixed128 player_id = 1;
  // used in the db layer to ensure entitlement is only granted once
  string fulfillment_id = 2;
  // the providerId assigned in the ProviderEntitlementPlugin - identifies the provider for this entitlement record
  string provider_id = 3;
  // the id of ProviderEntitlement content entry for this entitlement - identifies the item grants for this entitlement (map item grants through the content catalog: src/content/ProviderEntitlements.json)
  string provider_entitlement_id = 4;
  // the provider's id for this entitlement entity - this is the id used by the third party provider to identify the purchased entity.
  string provider_item_id = 5;
  // status for the processing of this entitlement - either pending or completed. If pending can indicate some error occurred during the syncEntitlements flow
  ProviderEntitlementRecordStatus provider_entitlement_record_status = 6;
  // the time in millis the inventory item grants were fulfilled (will be 0 if item grants have not been fulfilled)
  int64 fulfilled_timestamp_millis = 7;
}

enum ProviderEntitlementRecordStatus {
  UNUSED_STATUS = 0; // do not use
  ENTITLEMENT_PENDING = 1;
  ENTITLEMENT_COMPLETED = 2;
}

/**
 * A notification that indicates to a player that their inventory has been updated (e.g. through an operator or partner endpoint).
 */
message InventoryUpdatedV1Notification {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = NOTIFICATION;

  // Represents a portion of an inventory as an InventorySegmentV1Data.
  InventorySegmentV1Data segment = 1;

  // Represents a delta of changes applied to an inventory from the crafting call as an UpdateSummary.
  UpdateSummaryV0 delta = 2;
}

/**
 * A notification that indicates to a player that their inventory has been updated (e.g. through an operator or partner endpoint).
 */
message ResetClientInventoryCacheVersionV1Notification {
  option (pragma_session_type) = PLAYER;
  option (pragma_message_type) = NOTIFICATION;
}

/*
 * Operator request to get the raw content of all source files of a specific content type
 */
message GetRawContentSourceOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  string content_type = 1;
}

/*
 * Operator request to get the raw content of all source files of a specific content type
 */
message GetRawContentSourceOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  map<string, string> source_files = 1;
}

/*
 * Operator request to view content of a specific type grouped by source file.
 */
message GetContentSourceOperatorV1Request {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = REQUEST;

  enum ContentType {
    Undefined = 0;
    CraftingEntries = 1;
    InstancedSpecs = 2;
    LimitedGrants = 3;
    RewardBags = 4;
    Rewards = 5;
    RewardSlots = 6;
    RewardTables = 7;
    StackableSpecs = 8;
    Stores = 9;
    UpdateEntries = 10;
    ItemBundles = 11;
  }

  ContentType content_type = 1;
}

/*
 * Operator response to view content of a specific type grouped by source file.
 */
message GetContentSourceOperatorV1Response {
  option (pragma_session_type) = OPERATOR;
  option (pragma_message_type) = RESPONSE;

  map<string, SourceDataArray> source_files = 1;
}


message SourceDataArray {
  oneof type {
    CraftingEntries crafting_entries = 1;
    InstancedSpecs instanced_specs = 2;
    LimitedGrants limited_grants = 3;
    RewardBags reward_bags = 4;
    Rewards rewards = 5;
    RewardSlots reward_slots = 6;
    RewardTables reward_tables = 7;
    StackableSpecs stackable_specs = 8;
    Stores stores = 9;
    UpdateEntries update_entries = 10;
    ItemBundles item_bundles = 11;
  }
}

message CraftingEntries {
  repeated CraftingEntry crafting_entries = 1;
}

message InstancedSpecs {
  repeated InstancedSpec instanced_specs = 1;
}

message LimitedGrants {
  repeated LimitedGrant limited_grants = 1;
}

message ProductEntries {
  repeated payment.ProductEntry product_entries = 1;
}

message ItemBundles {
  repeated ItemBundle item_bundles = 1;
}

message RewardBags {
  repeated RewardBag reward_bags = 1;
}

message Rewards {
  repeated Reward rewards = 1;
}

message RewardSlots {
  repeated RewardSlot reward_slots = 1;
}

message RewardTables {
  repeated RewardTable reward_tables = 1;
}

message StackableSpecs {
  repeated StackableSpec stackable_specs = 1;
}

message Stores {
  repeated Store stores = 1;
}

message UpdateEntries {
  repeated UpdateEntry update_entries = 1;
}
