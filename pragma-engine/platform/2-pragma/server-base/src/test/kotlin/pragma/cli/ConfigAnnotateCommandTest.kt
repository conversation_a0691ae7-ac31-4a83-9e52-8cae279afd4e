package pragma.cli

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import pragma.config.PragmaConfigProvider
import pragma.settings.BackendType
import pragma.settings.Decrypter
import pragma.config.YamlConfigContext
import pragma.utils.TerminalProxy

class ConfigAnnotateCommandTest {
    private val capturedBackendType = mutableListOf<BackendType>()
    private var capturedSourceFileNames: List<String>? = null
    private val output = mockk<TerminalProxy>()
    private val mockDecrypter = mockk<Decrypter>()
    private val gameConfigProvider = mockk<PragmaConfigProvider>(relaxUnitFun = true){
        every { decrypter } returns mockDecrypter
    }
    private val socialConfigProvider = mockk<PragmaConfigProvider>(relaxUnitFun = true){
        every { decrypter } returns mockDecrypter
    }
    private val pragmaComponentFactory = mockk<PragmaComponentFactory> {
        every { createConfigProvider(BackendType.GAME)} returns gameConfigProvider
        every { createConfigProvider(BackendType.SOCIAL)} returns socialConfigProvider
    }
    private val testObj = ConfigAnnotateCommand(output, pragmaComponentFactory, ::mockNodeFactory)
    private val yamlConfigContexts = mockk<List<YamlConfigContext>>()

    private fun mockNodeFactory(sourceFileNames: List<String>?, @Suppress("UNUSED_PARAMETER") decrypter: Decrypter, type: BackendType) : List<YamlConfigContext> {
        capturedSourceFileNames = sourceFileNames
        capturedBackendType.add(type)
        return yamlConfigContexts
    }

    @Test
    fun `Command properly constructed`() {
        assertEquals("annotate", testObj.commandName)
        assertEquals("Annotate config with source file information", testObj.commandHelp)

        val confPaths = testObj.registeredOptions()[0]
        assertTrue("--configuration-filepath" in confPaths.names)
        assertTrue("--configuration-filepaths" in confPaths.names)
        assertEquals("List of path to the configuration YAML files for the game and social backend, comma-separated", confPaths.help)
        assertTrue("--backend-type" in testObj.registeredOptions()[1].names)
    }

    @Test
    fun run() {
        testObj.parse(listOf("--configuration-filepath", "path1,path2", "--backend-type", "BOTH"))
        verify { gameConfigProvider.printAnnotated(output, yamlConfigContexts) }
        verify { socialConfigProvider.printAnnotated(output, yamlConfigContexts) }
        assertEquals(listOf("path1", "path2"), capturedSourceFileNames)
        assertEquals(2, capturedBackendType.size)
        assertEquals(setOf(BackendType.GAME, BackendType.SOCIAL), capturedBackendType.toSet())
    }
}