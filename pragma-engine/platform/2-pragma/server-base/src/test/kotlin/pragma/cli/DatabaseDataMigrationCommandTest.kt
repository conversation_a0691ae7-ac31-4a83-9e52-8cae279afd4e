package pragma.cli

import com.github.ajalt.clikt.core.MissingParameter
import com.github.ajalt.clikt.core.ProgramResult
import com.zaxxer.hikari.HikariDataSource
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.mockk.verifyOrder
import java.sql.Connection
import java.sql.PreparedStatement
import java.sql.ResultSet
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.CoroutineScope
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import pragma.AccountTestFactory.playerIdUuid
import pragma.AccountTestFactory.socialIdUuid
import pragma.PragmaCoreTestFactory
import pragma.PragmaCoreTestFactory.anyInt
import pragma.account.config.AccountDaoConfig
import pragma.config.ConfigManagerImpl
import pragma.content.ContentDataNodeService
import pragma.databases.DatabaseConfigResource
import pragma.databases.DatabaseConnectionComponentFactory
import pragma.databases.DatabaseExecutor
import pragma.databases.NodeDaoConfig
import pragma.databases.SharedDatabaseConfigNodeService
import pragma.databases.TestablePartitionedConfig
import pragma.inventory.InventoryDaoConfig
import pragma.order.config.OrderDaoConfig
import pragma.settings.BackendType.GAME
import pragma.settings.BackendType.SOCIAL
import pragma.settings.PassthroughDecrypter
import pragma.utils.PlayerId
import pragma.utils.SocialId
import pragma.utils.TerminalProxy

internal class DatabaseDataMigrationCommandTest {
    private val logDisabler: LogDisabler = mockk {
        every { disable() } returns Unit
    }
    private val coroutineScope = slot<CoroutineScope>()
    private val someDataMigrator = "pragma.cli.TestDataMigrator"

    private val aResultSet = mockk<ResultSet> {
        every { next() } returnsMany listOf(true, false)
    }
    private val queryStatement = mockk<PreparedStatement>(relaxUnitFun = true) {
        every { executeQuery() } returns aResultSet
    }

    private val mockConnection = mockk<Connection>(relaxUnitFun = true) {
        every { prepareStatement(any()) } returns queryStatement
    }

    @Suppress("UNCHECKED_CAST")
    private val dbConfigResource = mockk<DatabaseConfigResource>(relaxUnitFun = true) {
        coEvery { transact(any<suspend (Connection) -> Unit>()) } coAnswers {
            val block = args[0] as suspend (Connection) -> Unit
            block(mockConnection)
        }
    }

    private val dbExecutor = mockk<DatabaseExecutor>(relaxUnitFun = true) {
        every { getResources() } returns listOf(dbConfigResource)
    }
    private val datasource = mockk<HikariDataSource>(relaxUnitFun = true) {
        every { connection } returns mockConnection
    }
    private val dbComponentFactory = mockk<DatabaseConnectionComponentFactory> {
        every { connectionPoolToWaitForSchemaCreation(any()) } returns datasource
        every { buildDatabaseExecutor(any()) } returns dbExecutor
    }

    companion object DaoConfigs {
        val sourceDao = NodeDaoConfig(GAME, InventoryDaoConfig::class)
        val destinationDao = NodeDaoConfig(SOCIAL, OrderDaoConfig::class)
        val additionalSourceDao = NodeDaoConfig(SOCIAL, AccountDaoConfig::class)
    }

    private val destinationPartitionedDaoConfig = TestablePartitionedConfig.getFor(SOCIAL).apply {
        databaseConfig.columnNameOfPropertyToConsistentHash = "socialKeyColumn"
    }
    private val sourcePartitionedDaoConfig = TestablePartitionedConfig.getFor(GAME).apply {
        databaseConfig.columnNameOfPropertyToConsistentHash = "gameKeyColumn"
    }
    private val additionalSourceDaoConfig = TestablePartitionedConfig.getFor(SOCIAL).apply {
        databaseConfig.columnNameOfPropertyToConsistentHash = "gameKeyColumn"
    }

    private val socialConfigManager = mockk<ConfigManagerImpl>(relaxUnitFun = true) {
        every { configFor(destinationDao.daoName) } returns destinationPartitionedDaoConfig
        every { configFor(additionalSourceDao.daoName) } returns additionalSourceDaoConfig
    }
    private val gameConfigManager = mockk<ConfigManagerImpl>(relaxUnitFun = true) {
        every { configFor(sourceDao.daoName) } returns sourcePartitionedDaoConfig
    }
    private val socialPragmaNode = PragmaCoreTestFactory.pragmaNode().apply {
        every { backendType } returns SOCIAL
    }
    private val gamePragmaNode = PragmaCoreTestFactory.pragmaNode()

    val socialContentDataDaoNodeService = ContentDataNodeService(gamePragmaNode)
    val gameContentDataDaoNodeService = ContentDataNodeService(gamePragmaNode)
    private val pragmaComponentFactory = mockk<PragmaComponentFactory> {
        coEvery {
            startPragmaNode(
                capture(coroutineScope),
                any(),
                SOCIAL,
                any(),
                NullSessionServiceSelector,
                any()
            )
        } returns socialPragmaNode

        coEvery { socialPragmaNode.lookupServiceInstances(SharedDatabaseConfigNodeService::class) } returns ConcurrentHashMap(
            mapOf(
                PragmaCoreTestFactory.anyUuid() to SharedDatabaseConfigNodeService(socialPragmaNode)
            )
        )

        coEvery { socialPragmaNode.lookupServiceInstances(ContentDataNodeService::class) } returns ConcurrentHashMap(
            mapOf(
                PragmaCoreTestFactory.anyUuid() to socialContentDataDaoNodeService
            )
        )

        coEvery {
            startPragmaNode(
                capture(coroutineScope),
                any(),
                GAME,
                any(),
                NullSessionServiceSelector,
                any()
            )
        } returns gamePragmaNode

        coEvery { gamePragmaNode.lookupServiceInstances(SharedDatabaseConfigNodeService::class) } returns ConcurrentHashMap(
            mapOf(
                PragmaCoreTestFactory.anyUuid() to SharedDatabaseConfigNodeService(gamePragmaNode)
            )
        )

        coEvery { gamePragmaNode.lookupServiceInstances(ContentDataNodeService::class) } returns ConcurrentHashMap(
            mapOf(
                PragmaCoreTestFactory.anyUuid() to gameContentDataDaoNodeService
            )
        )

        coEvery {
            createConfigManager(SOCIAL, any(), any(), any())
        } returns socialConfigManager
        coEvery {
            createConfigManager(GAME, any(), any(), any())
        } returns gameConfigManager
    }

    private val terminal = mockk<TerminalProxy>(relaxed = true)

    private val testObj =
        DatabaseDataMigrationCommand(terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)

    @Test
    fun `normal logging disabled for run near beginning of method call`() {
        runBothBackendType()
        verifyOrder {
            logDisabler.disable()
            terminal.display("Checking if database(s) need migration(s)...", any())
        }
    }

    @Test
    fun `cancels if not confirmed by user`() {
        processCommandLineArgs(testObj, listOf("--data-migrator", someDataMigrator, "--backend-type", "Game"), false)
        verify {
            terminal.display("Migration cancelled.", any())
        }

        verify(exactly = 0) {
            gameConfigManager.configFor(someDataMigrator)
        }
    }

    @Test
    fun `auto confirm help text`() {
        val confirmOption = testObj.registeredOptions().single { it.names.contains("--auto-confirm") }
        assertEquals("Set this field to 'y' to skip confirmation prompt", confirmOption.help)
    }

    @Test
    fun `loads db config from both backend type`() {
        runBothBackendType()
        coVerify { gameConfigManager.configFor(sourceDao.daoName) }
        coVerify { socialConfigManager.configFor(additionalSourceDao.daoName) }
        coVerify { socialConfigManager.configFor(destinationDao.daoName) }
    }

    @Test
    fun `Command properly constructed`() {
        assertEquals("data-migrate", testObj.commandName)
        assertEquals("Migrate data with a specified data migrator class", testObj.commandHelp)

        val dataMigratorOption = testObj.registeredOptions()[8]
        assertTrue("--data-migrator" in dataMigratorOption.names)
        assertTrue("-m" in dataMigratorOption.names)
        assertEquals("The fully qualified name of the data migrator to use", dataMigratorOption.help)
    }

    @Test
    fun `requires data-migrator to migrate`() {
        assertThrows<MissingParameter> { processCommandLineArgs(testObj, listOf()) }
        assertThrows<MissingParameter> { processCommandLineArgs(testObj, listOf("--data-migrator")) }
    }

    @Test
    fun `notifies user we will check database schemas on all partitions`() {
        runBothBackendType()
        verify {
            terminal.display("Checking if database(s) need migration(s)...", any())
        }
    }

    @Test
    fun `notifies user for of invalid data migrator`() {
        val someBadDataMigrator = "pragma.cli.FakeDataMigrator"
        assertThrows<ProgramResult> {
            processCommandLineArgs(testObj, listOf("--data-migrator", someBadDataMigrator, "--backend-type", "Both"))
        }
    }

    @Test
    fun `get ContentDaoNodeServices for both backend types`() {
        runBothBackendType()
        coVerify {
            socialPragmaNode.lookupServiceInstances(ContentDataNodeService::class)
            gamePragmaNode.lookupServiceInstances(ContentDataNodeService::class)
        }
    }

    @Test
    fun `respects batch size parameter`() {
        val predefinedDataMigrator = spyk(TestDataMigrator())
        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)
        val batchSize = anyInt()
        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both", "--batch-size", batchSize.toString())
        )

        coVerify { predefinedDataMigrator.selectRecordsFromSource(any(), 0, batchSize) }
    }

    @Test
    fun `runs single batch if records less than batch size`() {
        val batchSize = 10
        val predefinedDataMigrator = spyk(TestDataMigrator()) {
            coEvery { selectRecordsFromSource(any(), 0, batchSize) } returns (1..<batchSize).map { TestDataMigratorSourceRecord(it) }
        }

        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)
        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both", "--batch-size", batchSize.toString())
        )

        coVerify(exactly = 1) { predefinedDataMigrator.selectRecordsFromSource(any(), any(), batchSize) }
    }

    @Test
    fun `runs multiple batches if more records than batch number`() {
        val batchSize = 10
        val totalRecords = batchSize + 1
        val predefinedDataMigrator = spyk(TestDataMigrator()) {
            coEvery { selectRecordsFromSource(any(), 0, batchSize) } returnsMany listOf(
                (1..batchSize).map { TestDataMigratorSourceRecord(it) },
                (1..totalRecords - batchSize).map { TestDataMigratorSourceRecord(it) }
            )
        }

        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)

        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both", "--batch-size", batchSize.toString())
        )

        coVerifyOrder {
            predefinedDataMigrator.selectRecordsFromSource(any(), 0, batchSize)
            predefinedDataMigrator.selectRecordsFromSource(any(), batchSize, batchSize)
        }
    }

    @Test
    fun `runs multiple batches if exact number of records as batch number`() {
        val batchSize = 10
        val predefinedDataMigrator = spyk(TestDataMigrator()) {
            coEvery { selectRecordsFromSource(any(), 0, batchSize) } returns (1..batchSize).map { TestDataMigratorSourceRecord(it) }
        }

        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)
        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both", "--batch-size", batchSize.toString())
        )

        coVerifyOrder {
            predefinedDataMigrator.selectRecordsFromSource(any(), 0, batchSize)
            predefinedDataMigrator.selectRecordsFromSource(any(), batchSize, batchSize)
        }
    }

    @Test
    fun `skips record if it has already been migrated`() {
        val record = TestDataMigratorSourceRecord(1)
        val predefinedDataMigrator = spyk(TestDataMigrator()) {
            coEvery { hasRecordBeenMigrated(record) } returns true
            coEvery { selectRecordsFromSource(any(), 0, any()) } returns listOf(record)
        }

        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)
        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both")
        )

        coVerify(exactly = 0) {
            predefinedDataMigrator.insertRecordIntoDestination(record, any())
        }
    }

    @Test
    fun `migrates record if it has not been migrated`() {
        val record = TestDataMigratorSourceRecord(1)
        val predefinedDataMigrator = spyk(TestDataMigrator()) {
            coEvery { hasRecordBeenMigrated(record) } returns false
            coEvery { selectRecordsFromSource(any(), 0, any()) } returns listOf(record)
        }

        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)
        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both")
        )

        coVerify(exactly = 1) {
            predefinedDataMigrator.insertRecordIntoDestination(record, any())
        }
    }

    @Test
    fun `data migrator is loaded with destination and additional source executors and content node services`() {
        val record = TestDataMigratorSourceRecord(1)
        val predefinedDataMigrator = spyk(TestDataMigrator()) {
            coEvery { hasRecordBeenMigrated(record) } returns false
            coEvery { selectRecordsFromSource(any(), 0, any()) } returns listOf(record)
        }

        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)
        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both")
        )

        coVerify {
            predefinedDataMigrator.load(
                mapOf(SOCIAL to socialContentDataDaoNodeService, GAME to gameContentDataDaoNodeService), mapOf(
                    additionalSourceDao.daoName to dbExecutor
                ), dbExecutor
            )
        }
    }

    @Test
    fun `source connection is passed in to select records function in data migrator`() {
        val predefinedDataMigrator = spyk(TestDataMigrator()) {
            coEvery { hasRecordBeenMigrated(any()) } returns true
        }

        val testObjPredefinedMigrator =
            TestableDatabaseMigrationCommand(predefinedDataMigrator, terminal, pragmaComponentFactory, logDisabler, dbComponentFactory)
        processCommandLineArgs(
            testObjPredefinedMigrator,
            listOf("--data-migrator", someDataMigrator, "--backend-type", "Both")
        )

        coVerify {
            predefinedDataMigrator.selectRecordsFromSource(mockConnection, 0, any())
        }
    }

    private fun runBothBackendType() {
        processCommandLineArgs(testObj, listOf("--data-migrator", someDataMigrator, "--backend-type", "Both"))
    }

    private fun processCommandLineArgs(
        migrationCommand: DatabaseDataMigrationCommand,
        daoOptions: List<String>,
        autoConfirm: Boolean = true,
    ) {
        migrationCommand.parse(
            daoOptions + listOf(
                "--mode",
                "DEVELOPMENT",
                "--decrypter",
                PassthroughDecrypter::class.simpleName!!,
                "--content-data-directory",
                "content/",
                "--configuration-filepaths",
                "file1.yml,file2.yml",
                "--auto-confirm",
                if (autoConfirm) "y" else "n"
            )
        )
    }
}

internal class TestableDatabaseMigrationCommand(
    private val dataMigrator: DataMigrator<*>,
    terminal: TerminalProxy,
    pragmaComponentFactory: PragmaComponentFactory = PragmaComponentFactory(),
    logDisabler: LogDisabler = LogDisabler(),
    databaseConnectionComponentFactory: DatabaseConnectionComponentFactory = DatabaseConnectionComponentFactory(),
) : DatabaseDataMigrationCommand(terminal, pragmaComponentFactory, logDisabler, databaseConnectionComponentFactory) {
    override fun getDataMigrator(): DataMigrator<*> {
        return dataMigrator
    }
}

internal class TestDataMigratorSourceRecord(
    val i: Int = 0,
    val socialId: SocialId = socialIdUuid(i),
    val playerId: PlayerId = playerIdUuid(i),
) : SourceRecord

internal class TestDataMigrator : DataMigrator<TestDataMigratorSourceRecord>() {

    private val addMigrationRecords = mockk<PreparedStatement>(relaxUnitFun = true) {
        every { executeUpdate() } returns 1
    }

    override fun getSource(): NodeDaoConfig {
        return DatabaseDataMigrationCommandTest.sourceDao
    }

    override fun getAdditionalSources(): List<NodeDaoConfig> {
        return listOf(DatabaseDataMigrationCommandTest.additionalSourceDao)
    }

    override fun getDestination(): NodeDaoConfig {
        return DatabaseDataMigrationCommandTest.destinationDao
    }

    override suspend fun selectRecordsFromSource(
        sourceConnection: Connection,
        offset: Int,
        limit: Int,
    ): List<TestDataMigratorSourceRecord> {
        return listOf(TestDataMigratorSourceRecord(1), TestDataMigratorSourceRecord(2))
    }

    override suspend fun insertRecordIntoDestination(
        sourceRecord: TestDataMigratorSourceRecord,
        executeStatement: (statement: PreparedStatement) -> Unit,
    ) {
        executeStatement(addMigrationRecords)
    }

    override suspend fun hasRecordBeenMigrated(sourceRecord: TestDataMigratorSourceRecord): Boolean {
        return false
    }
}
