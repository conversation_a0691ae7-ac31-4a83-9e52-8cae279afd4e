package pragma.party.impls

import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import pragma.AccountTestFactory.playerId
import pragma.AccountTestFactory.playerIdUuid
import pragma.PartyProtoTestFactory.broadcastParty
import pragma.PartyProtoTestFactory.defaultExtBroadcastParty
import pragma.PartyProtoTestFactory.defaultExtBroadcastPlayer
import pragma.PartyProtoTestFactory.defaultExtPlayerJoinRequest
import pragma.PartyProtoTestFactory.defaultExtPrivatePlayer
import pragma.PragmaCoreTestFactory.anyUuid
import pragma.PragmaError
import pragma.PragmaError.PartyService_NotInParty
import pragma.multiplayer.RoutingIdGenerator
import pragma.party.ExtParty
import pragma.party.PartyPlugin
import pragma.party.PartyRpc.RemovalReason.DISCONNECTED
import pragma.party.PartyRpc.RemovalReason.EXPIRED
import pragma.party.PartyRpc.RemovalReason.KICKED
import pragma.party.PartyTestFactory.assertThrowsPartyApplicationErrorException
import pragma.party.PartyTestFactory.coAssertThrowsPartyApplicationErrorException
import pragma.party.PartyTestFactory.party
import pragma.party.PartyTestFactory.partyConfig
import pragma.party.PartyTestFactory.partyPlayer
import pragma.party.impls.PartyImpl.InviteRemovalReason.Accepted
import pragma.party.impls.PartyImpl.InviteRemovalReason.Cancelled
import pragma.party.impls.PartyImpl.InviteRemovalReason.Declined
import pragma.utils.InviteId
import pragma.utils.toFixed128
import pragma.utils.toUUID

@ExperimentalCoroutinesApi
class PartyImplTest {
    private val partyPlugin: PartyPlugin = mockk()
    private val partyConfig = partyConfig()
    private val routingIdGenerator: RoutingIdGenerator = mockk()
    private lateinit var testObj: PartyImpl
    private val extPlayerJoinRequest = defaultExtPlayerJoinRequest()
    private val exampleNewList = listOf("here", "there", "everywhere")

    @BeforeEach
    fun setUp() {
        testObj = party(1, partyConfig = partyConfig, routingIdGenerator = routingIdGenerator)

        coEvery { partyPlugin.buildExtBroadcastParty(any()) } returns defaultExtBroadcastParty()
        coEvery { partyPlugin.buildExtPrivatePlayer(any(), any()) } returns defaultExtPrivatePlayer()
        coEvery { partyPlugin.buildExtBroadcastPlayer(any(), any()) } returns defaultExtBroadcastPlayer()
        every { routingIdGenerator.generateRoutingId() } answers { anyUuid() }
    }

    @Nested
    inner class Snapshot {
        @Test
        fun `maxPlayerCount is tracked in the snapshot`() = runTest {
            val initial = testObj.maxPlayerCount
            val newCount = 122
            testObj.maxPlayerCount = newCount
            assertTrue(testObj.snapshot.maxPlayerCount.hasChanged)
            testObj.clearSnapshot()
            assertEquals(initial, testObj.maxPlayerCount)
            assertNotEquals(newCount, testObj.maxPlayerCount)

            testObj.maxPlayerCount = newCount
            testObj.commitSnapshot()
            assertEquals(newCount, testObj.maxPlayerCount)
            assertNotEquals(initial, testObj.maxPlayerCount)
        }

        @Test
        fun `gameServerVersion is tracked in the snapshot`() = runTest {
            val initialVersion = testObj.gameServerVersion
            val newVersion = "NewOne"
            testObj.gameServerVersion = newVersion
            assertTrue(testObj.snapshot.gameServerVersion.hasChanged)
            testObj.clearSnapshot()
            assertEquals(initialVersion, testObj.gameServerVersion)
            assertNotEquals(newVersion, testObj.gameServerVersion)

            testObj.gameServerVersion = "NewOne"
            testObj.commitSnapshot()
            assertEquals("NewOne", testObj.gameServerVersion)
            assertNotEquals(initialVersion, testObj.gameServerVersion)
        }

        @Test
        fun `shouldEnterMatchmaking is tracked in the snapshot and not persisted on the party`() = runTest {
            testObj.enterMatchmaking()

            assertTrue(testObj.snapshot.shouldEnterMatchmaking.hasChanged)
            assertEquals(true, testObj.shouldEnterMatchmaking)

            testObj.clearSnapshot()
            assertEquals(false, testObj.shouldEnterMatchmaking)

            testObj.enterMatchmaking()

            testObj.commitSnapshot()
            assertEquals(false, testObj.shouldEnterMatchmaking)
        }

        @Test
        fun `ext is included in the snapshot`() = runTest {
            assertNull(testObj.snapshot.ext.value)
            testObj.ext = ExtParty.getDefaultInstance()
            assertTrue(testObj.snapshot.ext.hasChanged)
            assertNotNull(testObj.snapshot.ext.value)

            testObj.clearSnapshot()
            assertNull(testObj.snapshot.ext.value)

            testObj.ext = ExtParty.getDefaultInstance()
            testObj.commitSnapshot()

            assertFalse(testObj.snapshot.ext.hasChanged)
            assertNotNull(testObj.ext)
        }

        @Test
        fun `preferredGameServerZones is tracked on the snapshot`() = runTest {
            val initialZones = testObj.preferredGameServerZones
            val newZones = listOf("theMoon")
            testObj.preferredGameServerZones = newZones
            assertTrue(testObj.snapshot.preferredGameServerZones.hasChanged)
            testObj.clearSnapshot()
            assertEquals(initialZones, testObj.preferredGameServerZones)
            assertNotEquals(newZones, testObj.preferredGameServerZones)

            testObj.preferredGameServerZones = newZones
            testObj.commitSnapshot()
            assertEquals(newZones, testObj.preferredGameServerZones)
            assertNotEquals(initialZones, testObj.preferredGameServerZones)
        }

        @Test
        fun `added players are included in the snapshot`() = runTest {
            val initialPlayers = testObj.players
            val newPlayer = partyPlayer(4)
            testObj.addPlayerToParty(newPlayer)

            assertEquals(1, testObj.players.size - initialPlayers.size)

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.newPlayers.isEmpty())
            assertEquals(0, testObj.players.size - initialPlayers.size)

            testObj.addPlayerToParty(newPlayer)

            assertEquals(1, testObj.players.size - initialPlayers.size)

            testObj.commitSnapshot()

            assertTrue(testObj.snapshot.newPlayers.isEmpty())
            assertEquals(1, testObj.players.size - initialPlayers.size)
        }

        @Test
        fun `removed players are removed from the snapshot`() = runTest {
            val initialPlayer = testObj.players.first()
            testObj.removePlayer(initialPlayer.playerId, DISCONNECTED)

            assertFalse(testObj.players.contains(initialPlayer))

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.removedPlayers.isEmpty())
            assertTrue(testObj.players.contains(initialPlayer))

            testObj.removePlayer(initialPlayer.playerId, DISCONNECTED)

            assertFalse(testObj.players.contains(initialPlayer))

            testObj.commitSnapshot()

            assertTrue(testObj.snapshot.removedPlayers.isEmpty())
            assertFalse(testObj.players.contains(initialPlayer))
        }

        @Test
        fun `newly added players can be removed in the snapshot`() = runTest {
            val newPlayer = partyPlayer(20)
            testObj.addPlayerToParty(newPlayer)

            assertTrue(testObj.players.contains(newPlayer))

            testObj.removePlayer(newPlayer.playerId, KICKED)

            assertFalse(testObj.players.contains(newPlayer))

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.removedPlayers.isEmpty())
            assertFalse(testObj.players.contains(newPlayer))
        }

        @Test
        fun `kicked players are removed from the snapshot`() = runTest {
            val playerToKick = testObj.players.first() as PartyPlayerImpl
            coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, playerToKick, testObj, partyConfig) }
            testObj.addToKicked(playerToKick.playerId)

            coAssertThrowsPartyApplicationErrorException(PragmaError.PartyService_PlayerIsKicked) {
                testObj.addPlayerToParty(playerToKick)
            }

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.newlyKickedPlayers.isEmpty())
            assertDoesNotThrow {
                testObj.addPlayerToParty(playerToKick)
            }

            testObj.addToKicked(playerToKick.playerId)
            testObj.commitSnapshot()

            assertTrue(testObj.snapshot.newlyKickedPlayers.isEmpty())
            coAssertThrowsPartyApplicationErrorException(PragmaError.PartyService_PlayerIsKicked) {
                testObj.addPlayerToParty(playerToKick)
            }
        }

        @Test
        fun `newly kicked players can be unkicked in the snapshot`() = runTest {
            val playerToKick = testObj.players.first() as PartyPlayerImpl
            coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, playerToKick, testObj, partyConfig) }
            testObj.addToKicked(playerToKick.playerId)
            testObj.removeFromKicked(playerToKick.playerId)

            assertDoesNotThrow {
                testObj.addPlayerToParty(playerToKick)
            }

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.clearFromKickedPlayers.isEmpty())
            assertDoesNotThrow {
                testObj.addPlayerToParty(playerToKick)
            }
        }

        @Test
        fun `players kicked and unkicked in the same transaction have no effect`() = runTest {
            val playerToKick = testObj.players.first() as PartyPlayerImpl
            coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, playerToKick, testObj, partyConfig) }
            testObj.addToKicked(playerToKick.playerId)
            testObj.removeFromKicked(playerToKick.playerId)

            assertTrue(testObj.snapshot.newlyKickedPlayers.isEmpty())
            assertTrue(testObj.snapshot.clearFromKickedPlayers.isEmpty())

            testObj.commitSnapshot()

            assertDoesNotThrow {
                testObj.addPlayerToParty(playerToKick)
            }
            testObj.clearSnapshot()

            testObj.addToKicked(playerToKick.playerId)
            testObj.commitSnapshot()

            testObj.removeFromKicked(playerToKick.playerId)
            testObj.addToKicked(playerToKick.playerId)

            assertTrue(testObj.snapshot.newlyKickedPlayers.isEmpty())
            assertTrue(testObj.snapshot.clearFromKickedPlayers.isEmpty())

            testObj.commitSnapshot()

            assertThrowsPartyApplicationErrorException(PragmaError.PartyService_PlayerIsKicked) {
                testObj.addPlayerToParty(playerToKick)
            }
        }

        @Test
        fun `added invites are included in the snapshot`() = runTest {
            val initialInvitesSize = testObj.invites.size
            val inviter = testObj.players.first()
            val invitee = partyPlayer(22)

            testObj.sendInvite(inviter.playerId, invitee.playerId)

            assertEquals(1, testObj.invites.size - initialInvitesSize)

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.newInvitesToProcess.isEmpty())
            assertEquals(0, testObj.invites.size - initialInvitesSize)

            testObj.sendInvite(inviter.playerId, invitee.playerId)

            assertEquals(1, testObj.invites.size - initialInvitesSize)

            testObj.commitSnapshot()

            assertTrue(testObj.snapshot.newInvitesToProcess.isEmpty())
            assertEquals(1, testObj.invites.size - initialInvitesSize)
        }

        @Test
        fun `cancelled invites are removed from the snapshot`() = runTest {
            val invite = testObj.sendInvite(testObj.players.first().playerId, partyPlayer(22).playerId)
            testObj.commitSnapshot()

            testObj.cancelInvite(invite.id)

            assertNull(testObj.getInviteByInviteId(invite.id))

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.invitesToCancel.isEmpty())
            assertNotNull(testObj.getInviteByInviteId(invite.id))

            testObj.cancelInvite(invite.id)

            assertNull(testObj.getInviteByInviteId(invite.id))

            testObj.commitSnapshot()

            assertTrue(testObj.snapshot.invitesToCancel.isEmpty())
            assertNull(testObj.getInviteByInviteId(invite.id))
        }

        @Test
        fun `if an invite is sent and cancelled in the same transaction, nothing should happen`() = runTest {
            val invite = testObj.sendInvite(testObj.players.first().playerId, partyPlayer(22).playerId)
            testObj.cancelInvite(invite.id)

            assertTrue(testObj.snapshot.newInvitesToProcess.isEmpty())
            assertTrue(testObj.snapshot.invitesToCancel.isEmpty())
        }

        @Test
        fun `accepted invites are tracked on the snapshot`() = runTest {
            val inviter = testObj.players.first()
            val invitee = partyPlayer(22)
            val initialInvitesSize = testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).acceptedInvitesCount

            val invite = testObj.sendInvite(inviter.playerId, invitee.playerId)
            testObj.commitSnapshot()

            testObj.removeInviteByInviteId(invite.id, Accepted)

            assertEquals(1, testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).acceptedInvitesCount - initialInvitesSize)

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.acceptedInvites.isEmpty())
            assertEquals(0, testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).acceptedInvitesCount - initialInvitesSize)

            testObj.removeInviteByInviteId(invite.id, Accepted)
            testObj.commitSnapshot()

            assertTrue(testObj.snapshot.acceptedInvites.isEmpty())
            assertEquals(1, testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).acceptedInvitesCount - initialInvitesSize)
        }

        @Test
        fun `declined invites are tracked on the snapshot`() = runTest {
            val inviter = testObj.players.first()
            val invitee = partyPlayer(22)
            val initialInvitesSize = testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).declinedInvitesCount

            val invite = testObj.sendInvite(inviter.playerId, invitee.playerId)
            testObj.commitSnapshot()

            testObj.removeInviteByInviteId(invite.id, Declined)

            assertEquals(1, testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).declinedInvitesCount - initialInvitesSize)

            testObj.clearSnapshot()

            assertTrue(testObj.snapshot.declinedInvites.isEmpty())
            assertEquals(0, testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).declinedInvitesCount - initialInvitesSize)

            testObj.removeInviteByInviteId(invite.id, Declined)
            testObj.commitSnapshot()

            assertTrue(testObj.snapshot.declinedInvites.isEmpty())
            assertEquals(1, testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin).declinedInvitesCount - initialInvitesSize)
        }

        @Test
        fun `clearing the snapshot clears underlying player snapshots`() = runTest {
            testObj.players.forEach { it.isReady = true }

            testObj.clearSnapshot()

            testObj.players.forEach { assertFalse(it.isReady) }
        }

        @Test
        fun `committing the snapshot commits underlying player snapshots`() = runTest {
            testObj.players.forEach { it.isReady = true }

            testObj.commitSnapshot()
            testObj.clearSnapshot()

            testObj.players.forEach { assertTrue(it.isReady) }
        }

        @Test
        fun `player SDK relevant data results in a version increment`() = runTest {
            testObj.commitSnapshot()
            var initialVersion = testObj.getVersion()

            testObj.ext = ExtParty.getDefaultInstance()
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            val player = partyPlayer(33)
            coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, player, testObj, partyConfig) }
            testObj.addPlayerToParty(player)
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            testObj.removePlayer(testObj.players.last().playerId, DISCONNECTED)
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            testObj.gameServerVersion = "Version10"
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            testObj.preferredGameServerZones = listOf()
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            val kickedPlayer = testObj.players.last().playerId
            testObj.addToKicked(kickedPlayer)
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            testObj.removeFromKicked(kickedPlayer)
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            val invite = testObj.sendInvite(testObj.players.first().playerId, playerId(8).toUUID())
            assertEquals(++initialVersion, testObj.getVersion())

            testObj.commitSnapshot()

            testObj.removeInviteByInviteId(invite.id, Cancelled)
            assertEquals(++initialVersion, testObj.getVersion())
        }

        @Test
        fun `shouldNotify is true when there is a change to be broadcast to the SDK`() = runTest {
            assertFalse(testObj.shouldNotify())
            testObj.gameServerVersion = "Cool Version"
            assertTrue(testObj.shouldNotify())
            testObj.clearSnapshot()
            assertFalse(testObj.shouldNotify())
            testObj.players.first().isReady = true
            assertTrue(testObj.shouldNotify())
        }
    }

    @Test
    fun `getPartyPlayerOrThrow throws if that player is not found in the party`() {
        assertThrowsPartyApplicationErrorException(PartyService_NotInParty) {
            testObj.getPlayerByIdOrThrow(playerId(50).toUUID())
        }
    }

    @Test
    fun `tryGetPlayers does not throw if that player is not found in the party`() {
        var result = listOf<PartyPlayerImpl>()
        assertDoesNotThrow {
            result = testObj.tryGetPlayers(listOf(testObj.players.first().playerId, playerId(50).toUUID()))
        }.also {
            assertEquals(1, result.size)
            assertEquals(testObj.players.first(), result.first())
        }
    }

    @Test
    fun `areAllPlayersReady reflects whether all players are ready`() {
        testObj.players.forEach { it.isReady = true }
        assertTrue(testObj.areAllPlayersReady())
        testObj.players.first { it.playerId == playerIdUuid(1) }.isReady = false
        assertFalse(testObj.areAllPlayersReady())
    }

    @Test
    fun `changes to party updates the version of the broadcast payload`() = runTest {
        val initialVersion = testObj.getVersion()
        assertEquals(broadcastParty(1, version = initialVersion), testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin))
        testObj.preferredGameServerZones = exampleNewList
        assertEquals(
            broadcastParty(1, version = initialVersion + 1, preferredGameServerZones = exampleNewList),
            testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin)
        )
        testObj.commitSnapshot()
        testObj.ext = ExtParty.getDefaultInstance()
        assertEquals(
            broadcastParty(1, version = initialVersion + 2, preferredGameServerZones = exampleNewList),
            testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin)
        )
    }

    @Test
    fun `uncommitted changes don't increment the version twice`() = runTest {
        val initialVersion = testObj.getVersion()
        assertEquals(broadcastParty(1, version = initialVersion), testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin))
        testObj.preferredGameServerZones = exampleNewList
        assertEquals(
            broadcastParty(1, version = initialVersion + 1, preferredGameServerZones = exampleNewList),
            testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin)
        )
        testObj.ext = ExtParty.getDefaultInstance()
        assertEquals(
            broadcastParty(1, version = initialVersion + 1, preferredGameServerZones = exampleNewList),
            testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin)
        )
    }

    @Test
    fun `party version does not increase multiple versions when multiple changes before committing`() = runTest {
        val initialVersion = testObj.getVersion()
        testObj.preferredGameServerZones = exampleNewList
        testObj.preferredGameServerZones = listOf("nevermind")
        testObj.preferredGameServerZones = exampleNewList
        assertEquals(
            broadcastParty(1, version = initialVersion + 1, preferredGameServerZones = exampleNewList),
            testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin)
        )
    }

    @Test
    fun `party version is rolled back when snapshot is cleared`() = runTest {
        val initialVersion = testObj.getVersion()
        testObj.preferredGameServerZones = exampleNewList
        testObj.preferredGameServerZones = listOf("nevermind")
        testObj.preferredGameServerZones = exampleNewList
        testObj.clearSnapshot()
        assertEquals(
            broadcastParty(1, version = initialVersion),
            testObj.toBroadcastPartyProto(playerId(1).toUUID(), partyPlugin)
        )
    }

    @Test
    fun `players version is rolled back if party's version is rolled back`() = runTest {
        testObj.players.forEach { it.isReady = true }
        testObj.players.forEach { assertTrue((it as PartyPlayerImpl).shouldNotify()) }
        testObj.clearSnapshot()
        testObj.players.forEach { assertFalse((it as PartyPlayerImpl).shouldNotify()) }
    }

    @Test
    fun `removePlayer increments party version`() = runTest {
        val initialVersion = testObj.getVersion()
        val playerToRemove = testObj.players.first()
        testObj.removePlayer(playerToRemove.playerId, EXPIRED)

        assertFalse(testObj.contains(playerToRemove.playerId))
        assertEquals(
            initialVersion + 1,
            testObj.toBroadcastPartyProto(playerId(2).toUUID(), partyPlugin).version
        )
    }

    @Test
    fun `removePlayer throws exception if player not in party`() = runTest {
        coAssertThrowsPartyApplicationErrorException(PartyService_NotInParty) {
            testObj.removePlayer(anyUuid(), EXPIRED)
        }
    }

    @Test
    fun `addPlayerToParty throws if max player count reached`() = runTest {
        val testObj = party(1, numPlayers = 3, partyConfig = partyConfig.apply { maxPlayersPerParty = 3 })
        coAssertThrowsPartyApplicationErrorException(PragmaError.PartyService_PartyFull) {
            testObj.addPlayerToParty(partyPlayer(4))
        }
    }

    @Test
    fun `addPlayerToParty checks kicked status when adding players`() = runTest {
        val testObj = party(1, numPlayers = 1, partyConfig = partyConfig.apply { maxPlayersPerParty = 3 })
        val kickedPlayer = partyPlayer(10)
        testObj.addToKicked(kickedPlayer.sessionKey.pragmaId)

        coAssertThrowsPartyApplicationErrorException(PragmaError.PartyService_PlayerIsKicked) {
            testObj.addPlayerToParty(kickedPlayer)
        }

        testObj.removeFromKicked(kickedPlayer.sessionKey.pragmaId)

        coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, kickedPlayer, testObj, partyConfig) }

        testObj.addPlayerToParty(kickedPlayer)

        assertTrue(testObj.contains(kickedPlayer.sessionKey.pragmaId))
    }

    @Test
    fun `addPlayerToParty marks party as having been updated`() = runTest {
        val testObj = party(1, numPlayers = 1, partyConfig = partyConfig.apply { maxPlayersPerParty = 3 })
        val partyPlayer = partyPlayer(2)
        coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, partyPlayer, testObj, partyConfig) }

        testObj.addPlayerToParty(partyPlayer)

        assertTrue(testObj.hasBeenUpdated())
    }

    @Test
    fun `addPlayerToParty removes any pending invites for that player`() = runTest {
        val testObj = party(1, numPlayers = 2, partyConfig = partyConfig.apply { maxPlayersPerParty = 3 })
        val invitee = partyPlayer(77)

        testObj.sendInvite(testObj.players.first().playerId, invitee.playerId)
        testObj.sendInvite(testObj.players[1].playerId, invitee.playerId)

        coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, any(), testObj, partyConfig) }

        assertEquals(2, testObj.getInvitesSentToPlayerId(invitee.playerId).size)
        testObj.addPlayerToParty(invitee)
        assertEquals(0, testObj.getInvitesSentToPlayerId(invitee.playerId).size)
    }

    @Test
    fun `addPlayerToParty adds player to party anyways if max player count disabled`() = runTest {
        val initialVersion = testObj.getVersion()
        val testObj = party(1, numPlayers = 1, partyConfig = partyConfig.apply {
            maxPlayersPerParty = 1
            disableMaxPlayerCount = true
        })
        val partyPlayer = partyPlayer(2)
        coJustRun { partyPlugin.onAddPlayer(extPlayerJoinRequest, partyPlayer, testObj, partyConfig) }
        testObj.addPlayerToParty(partyPlayer)
        assertTrue(testObj.contains(partyPlayer.sessionKey.pragmaId))
        assertEquals(
            initialVersion + 1,
            testObj.toBroadcastPartyProto(playerId(2).toUUID(), partyPlugin).version,
            "Version incremented when addToParty called"
        )
    }

    @Test
    fun `actionedInvites are broadcast to a player, and actionedInvites are bounded, removing the oldest invites first, all invites are cancelled when actioned`() = runTest {
        val inviteIds = mutableListOf<InviteId>()

        val inviter = testObj.players.first()
        repeat(PartyImpl.MAX_ACTIONED_INVITES_PER_TYPE + 3) {
            val invite = testObj.sendInvite(inviter.playerId, anyUuid())
            testObj.removeInviteByInviteId(invite.id, Accepted)
            inviteIds.add(invite.id)
        }

        val declinedInvite = testObj.sendInvite(inviter.playerId, anyUuid())
        testObj.removeInviteByInviteId(declinedInvite.id, Declined)

        val cancelledInvite = testObj.sendInvite(inviter.playerId, anyUuid())
        testObj.removeInviteByInviteId(cancelledInvite.id, Cancelled)

        val broadcast = testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin)

        assertEquals(PartyImpl.MAX_ACTIONED_INVITES_PER_TYPE, broadcast.acceptedInvitesCount)
        assertNull(broadcast.acceptedInvitesList.firstOrNull { it.inviteId == inviteIds[0].toFixed128() })
        assertNull(broadcast.acceptedInvitesList.firstOrNull { it.inviteId == inviteIds[1].toFixed128() })
        assertNull(broadcast.acceptedInvitesList.firstOrNull { it.inviteId == inviteIds[2].toFixed128() })
        assertEquals(inviteIds[3].toFixed128(), broadcast.acceptedInvitesList[0].inviteId)

        assertEquals(1, broadcast.declinedInvitesCount)
        assertEquals(declinedInvite.id.toFixed128(), broadcast.declinedInvitesList[0].inviteId)

        val allAcceptedDeclinedAndCancelledInvites = PartyImpl.MAX_ACTIONED_INVITES_PER_TYPE + 3 + 1 + 1
        assertEquals(allAcceptedDeclinedAndCancelledInvites, testObj.getNewlyCanceledInvites().size)
        assertNull(broadcast.acceptedInvitesList.firstOrNull { it.inviteId == cancelledInvite.id.toFixed128() })
        assertNull(broadcast.declinedInvitesList.firstOrNull { it.inviteId == cancelledInvite.id.toFixed128() })

        testObj.commitSnapshot()

        val broadcastAfterCommit = testObj.toBroadcastPartyProto(inviter.playerId, partyPlugin)

        assertTrue(testObj.getNewlyCanceledInvites().isEmpty())

        assertEquals(PartyImpl.MAX_ACTIONED_INVITES_PER_TYPE, broadcastAfterCommit.acceptedInvitesCount)
        assertNull(broadcastAfterCommit.acceptedInvitesList.firstOrNull { it.inviteId == inviteIds[0].toFixed128() })
        assertNull(broadcastAfterCommit.acceptedInvitesList.firstOrNull { it.inviteId == inviteIds[1].toFixed128() })
        assertNull(broadcastAfterCommit.acceptedInvitesList.firstOrNull { it.inviteId == inviteIds[2].toFixed128() })
        assertEquals(inviteIds[3].toFixed128(), broadcastAfterCommit.acceptedInvitesList[0].inviteId)

        assertEquals(1, broadcastAfterCommit.declinedInvitesCount)
        assertEquals(declinedInvite.id.toFixed128(), broadcastAfterCommit.declinedInvitesList[0].inviteId)

        assertNull(broadcastAfterCommit.acceptedInvitesList.firstOrNull { it.inviteId == cancelledInvite.id.toFixed128() })
        assertNull(broadcastAfterCommit.declinedInvitesList.firstOrNull { it.inviteId == cancelledInvite.id.toFixed128() })
    }

    @Test
    fun `removeInviteByInviteId will not increment version if the requested invite was not found`() = runTest {
        testObj.removeInviteByInviteId(anyUuid(), Accepted)

        assertFalse(testObj.hasBeenUpdated())
    }
}
