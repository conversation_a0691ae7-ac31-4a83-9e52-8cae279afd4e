package pragma.loadsimulator

import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import pragma.utils.TimeProxy.Companion.secondsToMillis

@OptIn(ExperimentalCoroutinesApi::class)
class LoadSimulatorConfigLoaderTest {
    private val fullConfig = resourcePathArg("load-simulator-config-full.yml")
    private val logger = mockk<Logger>(relaxUnitFun = true)
    private val testObj = LoadSimulatorConfigLoader( Utils(logger) )
    private val defaultConfig = LoadSimulatorConfig()

    @Test
    fun `if file not provided on command line the default config is returned`() = runTest {
        assertEquals(defaultConfig, testObj.load(arrayOf("")))
    }

    @Test
    fun `load return null if file does not exist`() = runTest {
        val nonExistent = resourcePathArg("non_existent.yml")
        assertNull(testObj.load(nonExistent))
        coVerify { logger.error("File \"${nonExistent.first()}\" does not exist.") }
    }

    @Test
    fun `value in file update default value in LoadSimulatorConfigObject`() = runTest {
        val actualConfig = testObj.load(fullConfig)!!

        assertEquals(30.secondsToMillis, defaultConfig.heartbeatMillis)
        assertEquals(54_321L, actualConfig.heartbeatMillis)

        assertEquals("LoadSimulatorPlayer", defaultConfig.accountPrefix)
        assertEquals("SomePlayer", actualConfig.accountPrefix)
        assertEquals("https://game.localhost:443", actualConfig.pragmaAddress)

        assertTrue(actualConfig.phaseConfig.isNotEmpty())
        assertTrue(actualConfig.phaseConfig.contains("ExamplePartySetupPhase"))
        assertEquals(1, actualConfig.phaseConfig["ExamplePartySetupPhase"]!!.runCount)
        assertEquals(15, actualConfig.phaseConfig["ExampleMultiplayerPhase"]!!.targetRunDurationMinutes)

        assertTrue(actualConfig.stepGroupConfig.isNotEmpty())
        assertTrue(actualConfig.stepGroupConfig.contains("ExamplePartyStepGroup"))
        val examplePartyStepGroup = actualConfig.stepGroupConfig["ExamplePartyStepGroup"]!!
        assertTrue(examplePartyStepGroup.percentOfPlayersByStep.isNotEmpty())
        assertEquals(50, examplePartyStepGroup.percentOfPlayersByStep["SoloPartyStep"])
        assertEquals(40, examplePartyStepGroup.percentOfPlayersByStep["DuoPartyStep"])
        assertEquals(10, examplePartyStepGroup.percentOfPlayersByStep["TrioPartyStep"])

        assertTrue(actualConfig.stepGroupConfig.contains("ExampleMultiplayerStepGroup"))

        assertTrue(actualConfig.stepConfig.isNotEmpty())
        assertEquals(7, actualConfig.stepConfig.size)
        assertEquals(3, actualConfig.stepConfig["TrioPartyStep"]!!.playerCount)

        assertTrue(actualConfig.notificationsToPersist.isNotEmpty())
        assertEquals("someNotification", actualConfig.notificationsToPersist.first())
    }

    @Test
    fun `absence of value in file will not affect default value in LoadSimulatorConfigObject`() = runTest {
        val resourcePath = resourcePathArg("load-simulator-config-missing-values.yml")
        val actualConfig = testObj.load(resourcePath)!!

        assertEquals(30.secondsToMillis, actualConfig.connectionTimeout)
        assertEquals(defaultConfig.connectionTimeout, actualConfig.connectionTimeout)
        assertEquals("http://localhost:10200", actualConfig.operatorAddress)
        assertEquals(defaultConfig.operatorAddress, actualConfig.operatorAddress)

        assertTrue(actualConfig.stepConfig.isEmpty())
        assertTrue(defaultConfig.stepConfig.isEmpty())
    }

    @Test
    fun `loading file with incorrect field name throws`() = runTest {
        val fileWithIncorrectField = resourcePathArg("load-simulator-config-incorrect_field_name.yml")
        assertNull( testObj.load(fileWithIncorrectField) )
        coVerify { logger.error("File \"${fileWithIncorrectField.first()}\" cannot be parsed:\n" +
                                "Unrecognized field \"wrongField\" (class pragma.loadsimulator.PhaseConfig), not marked as ignorable (2 known properties: \"targetRunDurationMinutes\", \"runCount\"])\n" +
                                " at [Source: (File); line: 4, column: 3] (through reference chain: pragma.loadsimulator.LoadSimulatorConfig[\"phaseConfig\"]->java.util.LinkedHashMap[\"ExampleSetupPhase\"]->pragma.loadsimulator.PhaseConfig[\"wrongField\"]).") }
    }

    @Test
    fun `loading file with incorrect field value type throws`() = runTest {
        val fileWithIncorrectValueType = resourcePathArg("load-simulator-config-incorrect_field_value_type.yml")
        assertNull( testObj.load(fileWithIncorrectValueType) )
        coVerify { logger.error("File \"${fileWithIncorrectValueType.first()}\" cannot be parsed:\n" +
                                "Cannot deserialize value of type `int` from String \"NaN\": not a valid `int` value\n" +
                                " at [Source: (File); line: 1, column: 19] (through reference chain: pragma.loadsimulator.LoadSimulatorConfig[\"totalPlayerCount\"]).") }
    }

    @Test
    fun `loading file with incorrect percentages in stepGroupConfig throws`() = runTest {
        val fileWithIncorrectPercentages = resourcePathArg("load-simulator-config-wrong-percentages.yml")
        assertNull( testObj.load(fileWithIncorrectPercentages) )
        coVerify { logger.error("Sum of percentage in file \"${fileWithIncorrectPercentages.first()}\" do not add up to 100%. Fields: stepGroupConfig.ExamplePartyStepGroup, stepGroupConfig.ExampleMultiplayerStepGroup.") }
    }

    private fun resourcePathArg(fileName: String) = arrayOf("src/test/resources/$fileName")
}
