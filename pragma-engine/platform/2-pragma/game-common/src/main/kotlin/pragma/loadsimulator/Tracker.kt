package pragma.loadsimulator

import java.util.concurrent.atomic.AtomicInteger
import pragma.utils.PragmaSerializationUtils
import pragma.utils.TimeProxy.Companion.secondsToMillis

class LoadSimulatorTrackerException(message:String): IllegalStateException(message)

class Tracker {
    internal val phases = ConcurrentKeyedListWithReadOnlyEntries<String, PhaseOutput>()
    internal val clientSpinup = ClientSpinupOutput(AtomicInteger(0), null, "", "", AtomicInteger(0), AtomicInteger(0))
    internal val clientTeardown = ClientTeardownOutput(AtomicInteger(0), AtomicInteger(0), AtomicInteger(0))

    companion object {
        internal fun computePercentage(numerator: Int, denominator: Int): String {
            val decimalPlaces = 2
            val percentage = if (0 == denominator) 0.0 else numerator * 100.0 / denominator
            return "${"%.${decimalPlaces}f".format(percentage)}%"
        }

        internal fun computeRatePerSecond(total: Long, totalTimeMillis: Long): String {
            val decimalPlaces = 3
            val rate = if (0L == totalTimeMillis) 0.0 else total * 1.secondsToMillis.toDouble() / totalTimeMillis
            return "${"%.${decimalPlaces}f".format(rate)}/sec"
        }
    }

    fun getPlayerClientTracker() : PlayerClientTracker {
        return PlayerClientTracker(this)
    }

    fun startLogins(startTime: LoadSimulatorTimeStamp) {
        clientSpinup.startTimestamp = startTime
    }

    fun recordLoginSucceeded() {
        clientSpinup.loginAttempts.incrementAndGet()
        clientSpinup.logins.incrementAndGet()
    }

    fun recordLoginFailed() {
        clientSpinup.loginAttempts.incrementAndGet()
        clientSpinup.loginErrors.incrementAndGet()
    }

    fun endLogins(endTime: LoadSimulatorTimeStamp) {
        clientSpinup.duration = endTime.getDurationForAbsoluteDelta(clientSpinup.startTimestamp!!) //Todo: Null check + test
        val delta = endTime.epochMillis - clientSpinup.startTimestamp!!.epochMillis
        clientSpinup.loginRate = computeRatePerSecond(clientSpinup.loginAttempts.toLong(), delta)
    }

    fun recordLogoutSucceeded() {
        clientTeardown.logouts.incrementAndGet()
    }

    fun recordLogoutFailed() {
        clientTeardown.logoutErrors.incrementAndGet()
    }

    fun recordCancelledClients() {
        clientTeardown.cancelledClients.incrementAndGet()
    }

    fun printStartupOutput(): String {
        return PragmaSerializationUtils.toYamlString(StartupOutput(clientSpinup))
    }

    fun printScenarioOutput(): String {
        return PragmaSerializationUtils.toYamlString(buildScenarioOutput())
    }

    fun printTeardownOutput(): String {
        return PragmaSerializationUtils.toYamlString(TeardownOutput(clientTeardown))
    }

    private fun buildScenarioOutput(): ScenarioOutput {
        return ScenarioOutput(PhasesOutput(phases))
    }

    fun getPhaseTracker(phase: Phase): PhaseTracker {
        return PhaseTracker(this, phase)
    }

    fun startPhase(phaseName: String) {
        phases.putIfAbsent(phaseName, PhaseOutput(phaseName))
    }

    fun startIteration(phase: Phase, iteration:Int, startTime: LoadSimulatorTimeStamp) {
        val phaseOutput = getPhaseOutput(phase)

        phaseOutput.iterations.compute(iteration) { _, oldValue ->
            val iterationKeys = phaseOutput.iterations.keys
            val lastIteration = if (iterationKeys.isEmpty()) { 0 } else { iterationKeys.last() }

            oldValue
                ?: if (lastIteration == iteration - 1) {
                    IterationOutput(iteration, startTime, null, null, null)
                } else {
                    throw LoadSimulatorTrackerException("Cannot start iteration $iteration out of order. Only $lastIteration iterations have been started so far.")
                }
        }
    }

    fun endIteration(phase: Phase, iteration: Int, endTime: LoadSimulatorTimeStamp) {
        val phaseOutput = getPhaseOutput(phase)

        val element = phaseOutput.iterations[iteration]
            ?: throw LoadSimulatorTrackerException("Cannot end iteration $iteration because it has not been started.")

        element.endTimestamp = endTime
        element.duration = endTime.getDurationForAbsoluteDelta(element.startTimestamp)
    }

    fun startStep(phase: Phase, iteration: Int, step: Step) {
        val iterationOutput = getIterationOutput(phase, iteration)

        iterationOutput.steps.compute(step.name) { _, oldValue ->
            if (oldValue == null) {
                StepOutput(step.name, totalExecutions = AtomicInteger(1),  totalPlayers = step.playerCount, percentSuccess = "")
            } else {
                oldValue.totalExecutions.incrementAndGet()
                oldValue.totalPlayers += step.playerCount
                oldValue
            }
        }
    }

    fun endStep(phase: Phase, iteration: Int, step: Step) {
        val stepOutput = getStepOutput(phase, iteration, step)
        stepOutput.successExecutions.incrementAndGet()
        stepOutput.percentSuccess = computePercentage(stepOutput.successExecutions.get(), stepOutput.totalExecutions.get())
    }

    fun errorStep(phase: Phase, iteration: Int, step: Step) {
        val stepOutput = getStepOutput(phase, iteration, step)
        stepOutput.errorExecutions.incrementAndGet()
        stepOutput.percentSuccess = computePercentage(stepOutput.successExecutions.get(), stepOutput.totalExecutions.get())
    }

    fun notificationSuccess(phase: Phase, iteration: Int, step: Step, name: String) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val notification = getNotificationOutput(stepOutput, name)
        notification.total.incrementAndGet()
        notification.success.incrementAndGet()
        notification.percentSuccess = computePercentage(notification.success.get(), notification.total.get())
    }

    fun notificationError(phase: Phase, iteration: Int, step: Step, name: String) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val notification = getNotificationOutput(stepOutput, name)
        notification.total.incrementAndGet()
        notification.timedOut.incrementAndGet()
        notification.percentSuccess = computePercentage(notification.success.get(), notification.total.get())
    }

    fun successRpc(phase: Phase, iteration: Int, step: Step, name: String) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val rpc = getRpcOutput(stepOutput, name)
        rpc.total.incrementAndGet()
        rpc.success.incrementAndGet()
        rpc.percentSuccess = computePercentage(rpc.success.get(), rpc.total.get())
    }

    fun rpcApplicationError(phase: Phase, iteration: Int, step: Step, name: String) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val rpc = getRpcOutput(stepOutput, name)
        rpc.total.incrementAndGet()
        rpc.applicationErrors.incrementAndGet()
        rpc.percentSuccess = computePercentage(rpc.success.get(), rpc.total.get())
    }

    fun rpcServiceError(phase: Phase, iteration: Int, step: Step, name: String) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val rpc = getRpcOutput(stepOutput, name)
        rpc.total.incrementAndGet()
        rpc.serviceErrors.incrementAndGet()
        rpc.percentSuccess = computePercentage(rpc.success.get(), rpc.total.get())
    }

    fun customSuccess(phase: Phase, iteration: Int, step: Step, name: String) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val custom = getCustomOutput(stepOutput, name)
        custom.total.incrementAndGet()
        custom.success.incrementAndGet()
        custom.percentSuccess = computePercentage(custom.success.get(), custom.total.get())
    }

    fun customError(phase: Phase, iteration: Int, step: Step, name: String) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val custom = getCustomOutput(stepOutput, name)
        custom.total.incrementAndGet()
        custom.error.incrementAndGet()
        custom.percentSuccess = computePercentage(custom.success.get(), custom.total.get())
    }

    fun equalsCustom(phase: Phase, iteration: Int, step: Step, name: String, eventId: String, expected: Any, actual: Any) {
        val stepOutput = getStepOutput(phase, iteration, step)
        val custom = getCustomOutput(stepOutput, name)

        custom.equalsCustom.compute(eventId) { _, oldValue ->
            if (oldValue == null) {
                EqualsCustomOutput(eventId, expected, actual)
            } else {
                oldValue.expected = expected
                oldValue.actual = actual
                oldValue
            }
        }
    }

    fun collectSummary(scenarioSummary: ScenarioSummary) {
        var rpcsTotal = 0
        var rpcsSuccesses = 0
        var notificationsTotal = 0
        var notificationsSuccesses = 0
        phases.forEach { phaseName, phase ->
            val rpcsSummary = RpcsSummary(0, 0, 0, 0, "")
            val notificationsSummary = NotificationsSummary(0, 0, 0, "")
            val phaseSummary = PhaseSummary(phaseName, "", rpcsSummary, notificationsSummary)
            (scenarioSummary.phases as MutableList).add(phaseSummary)
            var firstStart:Long = Long.MAX_VALUE
            var lastEnd:Long = 0
            phase.iterations.forEach { _, iteration ->
                if(firstStart > iteration.startTimestamp.epochMillis){
                    firstStart = iteration.startTimestamp.epochMillis
                }
                if(lastEnd < iteration.endTimestamp!!.epochMillis) {
                    lastEnd = iteration.endTimestamp!!.epochMillis
                }
                iteration.steps.forEach { _, step ->
                    scenarioSummary.totalErrors += step.errorExecutions.get()
                    step.custom.forEach { _, custom ->
                        scenarioSummary.totalErrors += custom.error.get()
                    }
                    step.rpcs.forEach { _, rpcOutput ->
                        rpcsSummary.total += rpcOutput.total.get()
                        rpcsTotal += rpcOutput.total.get()
                        rpcsSummary.success += rpcOutput.success.get()
                        rpcsSuccesses += rpcOutput.success.get()
                        rpcsSummary.applicationErrors += rpcOutput.applicationErrors.get()
                        rpcsSummary.serviceErrors += rpcOutput.serviceErrors.get()
                    }
                    rpcsSummary.percentSuccess = computePercentage(rpcsSummary.success, rpcsSummary.total)

                    step.notifications.forEach { _, notificationOutput ->
                        notificationsSummary.total += notificationOutput.total.get()
                        notificationsTotal += notificationOutput.total.get()
                        notificationsSummary.success += notificationOutput.success.get()
                        notificationsSuccesses += notificationOutput.success.get()
                        notificationsSummary.timedOut += notificationOutput.timedOut.get()
                    }
                    notificationsSummary.percentSuccess = computePercentage(notificationsSummary.success, notificationsSummary.total)
                }
            }
            phaseSummary.duration = LoadSimulatorTimeStamp.toDurationString(lastEnd - firstStart)
        }
        scenarioSummary.totalRpcsPercentSuccess = computePercentage(rpcsSuccesses, rpcsTotal)
        scenarioSummary.totalNotificationsPercentSuccess = computePercentage(notificationsSuccesses, notificationsTotal)
    }

    private fun getPhaseOutput(phase: Phase): PhaseOutput {
        return phases[phase.name]
            ?: throw LoadSimulatorTrackerException("Cannot track data to a phase that has not been started. Phase name:${phase.name}")
    }

    private fun getIterationOutput(phase: Phase, iteration: Int): IterationOutput {
        val phaseOutput = getPhaseOutput(phase)
        return phaseOutput.iterations[iteration]
            ?: throw LoadSimulatorTrackerException("Cannot track data to an iteration that has not been started. Iteration: $iteration")
    }

    private fun getStepOutput(phase: Phase, iteration: Int, step: Step): StepOutput {
        val iterationOutput = getIterationOutput(phase, iteration)
        return iterationOutput.steps[step.name]
            ?: throw LoadSimulatorTrackerException("Cannot track data to a step that has not been started. Step name: ${step.name}")
    }

    private fun getNotificationOutput(stepOutput: StepOutput, name: String): NotificationOutput {
        return getOutputOrPutIfAbsent(name, stepOutput.notifications) { NotificationOutput(name, percentSuccess = "") }
    }

    private fun getRpcOutput(stepOutput: StepOutput, name: String): RpcOutput {
        return getOutputOrPutIfAbsent(name, stepOutput.rpcs) { RpcOutput(name, percentSuccess = "") }
    }

    private fun getCustomOutput(stepOutput: StepOutput, name: String): CustomOutput {
        return getOutputOrPutIfAbsent(name, stepOutput.custom) { CustomOutput(name, percentSuccess = "") }
    }

    private fun <T> getOutputOrPutIfAbsent(name: String, collection:  ConcurrentKeyedListWithReadOnlyEntries<String,T>, create: (String) -> T): T {
        var returnValue: T? = null
        collection.compute(name) { _, oldValue ->
            returnValue = oldValue ?: create(name)
            returnValue
        }
        return returnValue!!
    }

    fun setStepGroup(phase: Phase, iteration: Int, name: String) {
        val iterationOutput = getIterationOutput(phase, iteration)
        if (iterationOutput.stepGroup == null)
            iterationOutput.stepGroup = StepGroupOutput(name)
    }
}

interface ISpinup {
    fun start(startTime: LoadSimulatorTimeStamp)
    fun loginSucceeded()
    fun loginFailed()
    fun end(endTime: LoadSimulatorTimeStamp)
}

interface ITeardown {
    fun cancelled()
    fun logoutSucceeded()
    fun logoutFailed()
}

class PlayerClientTracker(private val tracker: Tracker): ISpinup, ITeardown {
    override fun start(startTime: LoadSimulatorTimeStamp) {
        tracker.startLogins(startTime)
    }

    override fun loginSucceeded() {
        tracker.recordLoginSucceeded()
    }

    override fun loginFailed() {
        tracker.recordLoginFailed()
    }

    override fun end(endTime: LoadSimulatorTimeStamp) {
        tracker.endLogins(endTime)
    }

    override fun cancelled() {
        tracker.recordCancelledClients()
    }

    override fun logoutSucceeded() {
        tracker.recordLogoutSucceeded()
    }

    override fun logoutFailed() {
        tracker.recordLogoutFailed()
    }
}


class PhaseTracker(private val tracker: Tracker, private val phase: Phase) {
    private fun makeIterationTracker(iteration:Int): IterationTracker {
        return IterationTracker(tracker, phase, iteration)
    }

    fun startPhase() {
        tracker.startPhase(phase.name)
    }

    suspend fun withIterationTracker(iteration: Int, loadSimulatorContext: LoadSimulatorContext, block: suspend (IterationTracker) -> Any): Any {
        val iterationTracker = makeIterationTracker(iteration)
        return iterationTracker.use(loadSimulatorContext, block)
    }
}

class IterationTracker(private val tracker: Tracker, private val phase: Phase, private val iteration:Int) {
    suspend fun use(loadSimulatorContext: LoadSimulatorContext, block: suspend (IterationTracker) -> Any): Any {
        val startTime = loadSimulatorContext.getCurrentTimestamp()
        tracker.startIteration(phase, iteration, startTime)
        try {
            return block(this)
        } finally {
            val endTime = loadSimulatorContext.getCurrentTimestamp()
            tracker.endIteration(phase, iteration, endTime)
        }
    }

    suspend fun withStepTracker(step: Step, block: suspend (StepTracker) -> Unit) {
        val stepTracker = StepTracker(tracker, phase, iteration, step)
        return stepTracker.use(block)
    }

    fun setStepGroup(name: String) {
        tracker.setStepGroup(phase, iteration, name)
    }
}


interface ICustomStepTracker {
    fun trackCustomSuccess(name: String)
    fun trackCustomError(name: String)
    fun trackNotificationSuccess(name: String)
    fun trackNotificationError(name: String)
    fun trackRpcSuccess(name: String)
    fun trackRpcApplicationError(name: String)
    fun trackRpcServiceError(name: String)
    fun trackEqualsCustom(name: String, eventId: String, expected: Any, actual: Any)
}

class NullStepTracker : ICustomStepTracker {
    override fun trackCustomSuccess(name: String) {
    }

    override fun trackCustomError(name: String) {
    }

    override fun trackNotificationSuccess(name: String) {
    }

    override fun trackNotificationError(name: String) {
    }

    override fun trackRpcSuccess(name: String) {
    }

    override fun trackRpcApplicationError(name: String) {
    }

    override fun trackRpcServiceError(name: String) {
    }

    override fun trackEqualsCustom(name: String, eventId: String, expected: Any, actual: Any) {
    }
}

class StepTracker(private val tracker: Tracker, private val phase: Phase, private val iteration: Int, private val step: Step):
    ICustomStepTracker {
    suspend fun use(block: suspend (StepTracker) -> Unit) {
        startStep()
        try {
            block(this)
            endStep()
        } catch (e: Exception) {
            errorStep()
            throw e
        }
    }

    private fun startStep() {
        tracker.startStep(phase, iteration, step)
    }

    private fun endStep() {
        tracker.endStep(phase, iteration, step)
    }

    private fun errorStep() {
        tracker.errorStep(phase, iteration, step)
    }

    override fun trackCustomSuccess(name: String) {
        tracker.customSuccess(phase, iteration, step, name)
    }

    override fun trackCustomError(name: String) {
        tracker.customError(phase, iteration, step, name)
    }

    override fun trackNotificationSuccess(name: String) {
        tracker.notificationSuccess(phase, iteration, step, name)
    }

    override fun trackNotificationError(name: String) {
        tracker.notificationError(phase, iteration, step, name)
    }

    override fun trackEqualsCustom(name: String, eventId: String, expected: Any, actual: Any) {
        tracker.equalsCustom(phase, iteration, step, name, eventId, expected, actual)
    }

    override fun trackRpcSuccess(name: String) {
        tracker.successRpc(phase, iteration, step, name)
    }

    override fun trackRpcApplicationError(name: String) {
        tracker.rpcApplicationError(phase, iteration, step, name)
    }

    override fun trackRpcServiceError(name: String) {
        tracker.rpcServiceError(phase, iteration, step, name)
    }
}
