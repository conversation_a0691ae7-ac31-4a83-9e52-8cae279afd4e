package pragma.gameinstance.impls

import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import org.slf4j.Logger
import pragma.PragmaError
import pragma.PragmaError.GameInstanceService_GameServerAlreadyAllocated
import pragma.PragmaError.GameInstanceService_PlayerAlreadyInGameInstance
import pragma.gameinstance.ExtAllocateGameServer
import pragma.gameinstance.ExtGameEnded
import pragma.gameinstance.ExtRemovedFromGame
import pragma.gameinstance.GameInstance
import pragma.gameinstance.GameInstance.DataStore
import pragma.gameinstance.GameInstance.GamePlayer
import pragma.gameinstance.GameInstance.GameServerAllocationStatus
import pragma.gameinstance.GameInstance.GameServerAllocationStatus.COMPLETE
import pragma.gameinstance.GameInstance.GameServerAllocationStatus.IN_PROGRESS
import pragma.gameinstance.GameInstance.GameServerAllocationStatus.NOT_STARTED
import pragma.gameinstance.GameInstanceCommon
import pragma.gameinstance.GameInstanceCommon.BroadcastDataStore
import pragma.gameinstance.GameInstanceCommon.BroadcastGameInstance
import pragma.gameinstance.GameInstanceCommon.HostConnectionDetails
import pragma.gameinstance.GameInstanceRpc.GameInstanceTerminationReason
import pragma.gameinstance.gameInstanceApplicationError
import pragma.gameinstance.impls.PlayerGameStatus.PlayerPending
import pragma.gameinstance.impls.PlayerGameStatus.Removed
import pragma.logging.PragmaLoggerFactory
import pragma.multiplayer.GameInstanceSnapshot
import pragma.multiplayer.MultiplayerDataVersionTracker
import pragma.multiplayer.SnapshotTrackable
import pragma.utils.GameInstanceId
import pragma.utils.PartyId
import pragma.utils.PlayerId
import pragma.utils.TimeProxy
import pragma.utils.toFixed128
import pragma.utils.toUUID

class GameInstanceImpl(
    override val id: GameInstanceId,
    override val gameServerVersion: String,
    override val gameServerZone: String,
    private val removedPlayersList: MutableList<GamePlayerImpl> = mutableListOf(),
    var version: MultiplayerDataVersionTracker = MultiplayerDataVersionTracker(),
    private val _dataStore: DataStoreImpl = DataStoreImpl(version),
    private val timeProxy: TimeProxy = TimeProxy.defaultInstance,
) : GameInstance.GameInstance, GameInstanceSnapshot {
    private val logger: Logger = PragmaLoggerFactory.getLogger(GameInstanceImpl::class)

    // Tracked to evaluate if the game instance is stale
    var lastAccessTimestampMillis: Long = timeProxy.currentEpochMillis()

    internal var snapshot: Snapshot = Snapshot(version)
    private var inMatchmakingData: Boolean = false
    private var endGameMapData: Map<PlayerId, ExtGameEnded>? = null
    private var terminateReasonData: GameInstanceTerminationReason? = null
    private var gameServerAllocationStatusData: GameServerAllocationStatus = NOT_STARTED
    private var gameServerAllocatedTimestampMillisData: Long? = null
    private var serverPragmaIdData: UUID? = null
    private val connectPlayerTimeoutData: ConcurrentHashMap<Set<PlayerId>, ConnectPlayersTimeout> =
        ConcurrentHashMap<Set<PlayerId>, ConnectPlayersTimeout>()
    private val internalPlayersData: MutableList<GamePlayerImpl> = mutableListOf()
    private var hasExpiredData: Boolean = false
    private var lastKeepAliveTimestampMillisData: Long? = null

    override val dataStore: DataStore
        get() = _dataStore
    override val players: List<GamePlayer>
        get() = stagedPlayers()
    override val removedPlayers: List<GamePlayer>
        get() = (removedPlayersList + snapshot.newRemovedPlayers).filter {
            !snapshot.clearFromRemovedPlayerList.contains(it.playerId)
        }
    override var inMatchmaking: Boolean
        get() = when (snapshot.inMatchmaking.hasChanged) {
            true -> snapshot.inMatchmaking.value!!
            false -> inMatchmakingData
        }
        set(value) {
            snapshot.inMatchmaking.value = value
        }
    override val gameServerAllocationStatus: GameServerAllocationStatus
        get() = when (snapshot.gameServerAllocationStatus.hasChanged) {
            true -> snapshot.gameServerAllocationStatus.value!!
            false -> gameServerAllocationStatusData
        }
    val serverPragmaId: UUID?
        get() = when (snapshot.serverPragmaId.hasChanged) {
            true -> snapshot.serverPragmaId.value
            false -> serverPragmaIdData
        }
    val gameServerAllocatedTimestampMillis: Long?
        get() = when (snapshot.gameServerAllocatedTimestampMillis.hasChanged) {
            true -> snapshot.gameServerAllocatedTimestampMillis.value
            false -> gameServerAllocatedTimestampMillisData
        }
    var lastKeepAliveTimestampMillis: Long?
        get() = when (snapshot.lastKeepAliveTimestampMillis.hasChanged) {
            true -> snapshot.lastKeepAliveTimestampMillis.value
            false -> lastKeepAliveTimestampMillisData
        }
        set(value) {
            snapshot.lastKeepAliveTimestampMillis.value = value
        }
    val extAllocateGameServer: ExtAllocateGameServer?
        get() = snapshot.extAllocateGameServer.value
    val endGameMap: Map<PlayerId, ExtGameEnded>?
        get() = when (snapshot.endGameMap.hasChanged) {
            true -> snapshot.endGameMap.value
            false -> endGameMapData
        }
    val terminateReason: GameInstanceTerminationReason?
        get() = when (snapshot.terminateReason.hasChanged) {
            true -> snapshot.terminateReason.value
            false -> terminateReasonData
        }
    var hasExpired: Boolean
        get() = when (snapshot.hasExpired.hasChanged) {
            true -> snapshot.hasExpired.value!!
            false -> hasExpiredData
        }
        set(value) {
            snapshot.hasExpired.value = value
        }

    override fun allocateGameServer(extAllocateGameServer: ExtAllocateGameServer) {
        if (gameServerAllocationStatus == IN_PROGRESS || gameServerAllocationStatus == COMPLETE) {
            gameInstanceApplicationError(
                GameInstanceService_GameServerAlreadyAllocated,
                "GameInstance with id $id has already allocated a game server. GameServerAllocationStatus: $gameServerAllocationStatus"
            )
        }
        setGameServerAllocationStatus(IN_PROGRESS)
        snapshot.extAllocateGameServer.value = extAllocateGameServer
    }

    override fun addPlayer(playerId: PlayerId, partyId: PartyId, teamNumber: Int): GamePlayer {
        if (stagedPlayers().map { it.playerId }.contains(playerId)) {
            gameInstanceApplicationError(
                GameInstanceService_PlayerAlreadyInGameInstance,
                "Attempting to add player $playerId already in this game instance $id."
            )
        }

        snapshot.clearFromRemovedPlayerList.add(playerId)

        val gamePlayer = GamePlayerImpl(
            playerId,
            partyId,
            teamNumber,
            PlayerPending,
            version
        )
        snapshot.newPlayers.add(gamePlayer)

        version.incrementVersion()
        return gamePlayer
    }

    override fun removePlayer(playerId: PlayerId, ext: ExtRemovedFromGame) {
        internalRemovePlayer(playerId, ext)
    }

    fun internalRemovePlayer(playerId: PlayerId, ext: ExtRemovedFromGame? = null) {
        val player = stagedPlayers().firstOrNull { it.playerId == playerId }
            ?: gameInstanceApplicationError(
                PragmaError.GameInstanceService_PlayerNotInGameInstance,
                "Player `$playerId` not in game instance `$id`."
            )

        snapshot.newPlayers.removeIf { it.playerId == playerId && it.status != Removed }

        player.status = Removed
        player.extRemovedFromGame = ext
        snapshot.newRemovedPlayers.add(player)

        version.incrementVersion()
    }

    override fun end(extMap: Map<PlayerId, ExtGameEnded>) {
        snapshot.endGameMap.value = extMap
    }

    override fun terminate(reason: GameInstanceTerminationReason) {
        snapshot.terminateReason.value = reason
    }

    override fun getPlayer(playerId: PlayerId): GamePlayer? {
        return stagedPlayers().firstOrNull { it.playerId == playerId }
    }

    override fun getPlayersByPartyId(): Map<PartyId, List<GamePlayer>> {
        return stagedPlayers().groupBy { it.partyId }
    }

    override fun getPlayersByTeamNumber(): Map<Int, List<GamePlayer>> {
        return stagedPlayers().groupBy { it.teamNumber }
    }

    private fun stagedPlayers(): List<GamePlayerImpl> {
        return (internalPlayersData + snapshot.newPlayers).toSet().filter { it.status != Removed }
    }

    fun getNewlyAddedPlayers(): List<GamePlayerImpl> {
        return snapshot.newPlayers.filter { it.status != Removed }
    }

    fun getNewlyRemovedPlayers(): List<GamePlayerImpl> {
        return snapshot.newRemovedPlayers
    }

    fun getAllPlayers(): List<GamePlayerImpl> {
        return (stagedPlayers() + removedPlayers).map { it as GamePlayerImpl }
    }

    fun getPendingPlayers(): List<GamePlayerImpl> {
        return stagedPlayers().filter { it.status == PlayerPending }
    }

    fun getActivePlayers(): List<GamePlayerImpl> {
        return stagedPlayers().filter { it.status == PlayerGameStatus.Active }
    }

    fun getPlayerByIdOrThrow(playerId: PlayerId): GamePlayerImpl {
        return getAllPlayers().find { it.playerId == playerId }
            ?: gameInstanceApplicationError(
                PragmaError.GameInstanceService_PlayerNotInGameInstance,
                "Player `$playerId` not in game instance `$id`."
            )
    }

    fun getPlayersByIdsOrThrow(playerIds: Collection<PlayerId>): List<GamePlayerImpl> {
        return playerIds.map { getPlayerByIdOrThrow(it) }
    }

    fun containsPlayer(playerId: PlayerId): Boolean {
        return getAllPlayers().find { it.playerId == playerId } != null
    }

    fun addMatchmakingPlayers(playersFromMatchmaking: List<GameInstanceCommon.BackendPlayerToAdd>): List<GamePlayerImpl> {
        return playersFromMatchmaking.map {
            addPlayer(it.playerId.toUUID(), it.partyId.toUUID(), it.teamNumber.toInt()) as GamePlayerImpl
        }
    }

    fun setPlayersToActive(playersToHostConnectionDetails: List<Pair<PlayerId, HostConnectionDetails>>) {
        playersToHostConnectionDetails.forEach {
            val player = getPlayerByIdOrThrow(it.first)
            player.status = PlayerGameStatus.Active
            player.hostConnectionDetails = it.second
        }
        version.incrementVersion()
    }

    fun hasServerPragmaIdChanged(): Boolean {
        return snapshot.serverPragmaId.hasChanged
    }

    fun isServerPragmaIdInitialized(): Boolean {
        return serverPragmaId != null
    }

    fun setGameServerAllocationStatus(status: GameServerAllocationStatus) {
        snapshot.gameServerAllocationStatus.value = status

        if (status == IN_PROGRESS) {
            snapshot.gameServerAllocatedTimestampMillis.value = timeProxy.currentEpochMillis()
        } else {
            snapshot.gameServerAllocatedTimestampMillis.value = null
        }
    }

    fun containsGameServerAllocationTimeout(): Boolean {
        return gameServerAllocatedTimestampMillis != null
    }

    fun link(serverPragmaId: UUID) {
        snapshot.serverPragmaId.value = serverPragmaId
        setGameServerAllocationStatus(COMPLETE)
    }

    fun unlink(serverPragmaId: UUID) {
        if (this.serverPragmaId == serverPragmaId) {
            snapshot.serverPragmaId.value = null
            snapshot.lastKeepAliveTimestampMillis.value = null
            setGameServerAllocationStatus(NOT_STARTED)
            stagedPlayers().forEach {
                it.status = PlayerPending
                it.hostConnectionDetails = HostConnectionDetails.getDefaultInstance()
            }
        } else {
            gameInstanceApplicationError(
                PragmaError.GameInstanceService_ServerNotLinkedToThisInstance,
                "Game server $serverPragmaId not linked to game instance $id."
            )
        }
    }

    fun createConnectPlayersTimeouts(playerBatch: Set<PlayerId>, isInitialPlayerBatch: Boolean, startedAtMillis: Long) {
        snapshot.newConnectPlayersTimeouts[playerBatch] = ConnectPlayersTimeout(playerBatch, startedAtMillis, isInitialPlayerBatch)
    }

    fun containsConnectPlayersTimeout(playerBatch: Set<PlayerId>): Boolean {
        return getConnectPlayersTimeouts().any { it.playerIds == playerBatch }
    }

    fun getConnectPlayersTimeouts(): List<ConnectPlayersTimeout> {
        return (connectPlayerTimeoutData + snapshot.newConnectPlayersTimeouts).values.filter {
            !snapshot.removedConnectPlayersTimeouts.contains(it.playerIds)
        }.toList()
    }

    fun removeConnectPlayersTimeout(playerBatch: Set<PlayerId>) {
        snapshot.removedConnectPlayersTimeouts.add(playerBatch)
    }

    fun clearAllConnectPlayerTimeouts() {
        connectPlayerTimeoutData.forEach { removeConnectPlayersTimeout(it.key) }
    }

    fun getVersion(): Long {
        return version.getVersion()
    }

    fun toBroadcastGameInstanceProto(player: GamePlayerImpl): BroadcastGameInstance {
        val broadcast = BroadcastGameInstance.newBuilder()
            .setGameInstanceId(id.toFixed128())
            .setHasHostConnectionDetails(player.hasHostConnectionDetails())
            .setHostConnectionDetails(player.hostConnectionDetails)
            .setVersion(version.getVersion())
            .addAllPlayers(players.map { (it as GamePlayerImpl).toBroadcastGamePlayerProto(player) })

        val dataStoreDataForPlayer = _dataStore.getAllForPlayer(player)
        if (dataStoreDataForPlayer.isNotEmpty()) {
            broadcast.setDataStore(BroadcastDataStore.newBuilder().putAllData(dataStoreDataForPlayer).build())
        }

        return broadcast.build()
    }

    data class ConnectPlayersTimeout(
        val playerIds: Set<PlayerId>, // The 'batch id' for any given group is the set of all its player ids together
        val startedTimestampMillis: Long = 0,
        val isInitialPlayerBatch: Boolean = false, // if true, slightly different final processing
    )

    inner class Snapshot(val version: MultiplayerDataVersionTracker) {
        var hasExpired = SnapshotTrackable<Boolean>()
        var inMatchmaking = SnapshotTrackable<Boolean>()

        val newPlayers: MutableList<GamePlayerImpl> = mutableListOf()
        val clearFromRemovedPlayerList: MutableList<PlayerId> = mutableListOf()
        val newRemovedPlayers: MutableList<GamePlayerImpl> = mutableListOf()
        val newConnectPlayersTimeouts: MutableMap<Set<PlayerId>, ConnectPlayersTimeout> = mutableMapOf()
        val removedConnectPlayersTimeouts: MutableList<Set<PlayerId>> = mutableListOf()

        var serverPragmaId = SnapshotTrackable<UUID?>()
        var gameServerAllocatedTimestampMillis = SnapshotTrackable<Long>()
        var lastKeepAliveTimestampMillis = SnapshotTrackable<Long?>()
        var gameServerAllocationStatus = SnapshotTrackable<GameServerAllocationStatus>()
        var extAllocateGameServer = SnapshotTrackable<ExtAllocateGameServer>()

        var endGameMap = SnapshotTrackable<Map<PlayerId, ExtGameEnded>>()
        var terminateReason = SnapshotTrackable<GameInstanceTerminationReason>()

        fun shouldNotify(player: GamePlayer): Boolean {
            return newRemovedPlayers.isNotEmpty()
                || newPlayers.isNotEmpty()
                || _dataStore.shouldNotify(player)
                || stagedPlayers().any { it.shouldNotify(player) }
        }

        fun clear() {
            hasExpired = SnapshotTrackable()
            inMatchmaking = SnapshotTrackable()
            gameServerAllocatedTimestampMillis = SnapshotTrackable()
            lastKeepAliveTimestampMillis = SnapshotTrackable()
            serverPragmaId = SnapshotTrackable()
            gameServerAllocationStatus = SnapshotTrackable()
            newPlayers.clear()
            newRemovedPlayers.clear()
            clearFromRemovedPlayerList.clear()
            newConnectPlayersTimeouts.clear()
            removedConnectPlayersTimeouts.clear()
            endGameMap = SnapshotTrackable()
            terminateReason = SnapshotTrackable()
            extAllocateGameServer = SnapshotTrackable()
            version.rollbackUncommittedVersion()
        }
    }

    override fun commitSnapshot() {
        if (snapshot.hasExpired.hasChanged) {
            hasExpiredData = snapshot.hasExpired.value!!
        }
        if (snapshot.inMatchmaking.hasChanged) {
            inMatchmakingData = snapshot.inMatchmaking.value!!
        }
        if (snapshot.gameServerAllocationStatus.hasChanged) {
            gameServerAllocationStatusData = snapshot.gameServerAllocationStatus.value!!
        }
        if (snapshot.serverPragmaId.hasChanged) {
            serverPragmaIdData = snapshot.serverPragmaId.value
        }
        if (snapshot.gameServerAllocatedTimestampMillis.hasChanged) {
            gameServerAllocatedTimestampMillisData = snapshot.gameServerAllocatedTimestampMillis.value
        }
        if (snapshot.lastKeepAliveTimestampMillis.hasChanged) {
            lastKeepAliveTimestampMillisData = snapshot.lastKeepAliveTimestampMillis.value
        }
        if (snapshot.terminateReason.hasChanged) {
            terminateReasonData = snapshot.terminateReason.value
        }
        if (snapshot.endGameMap.hasChanged) {
            endGameMapData = snapshot.endGameMap.value
        }

        snapshot.newConnectPlayersTimeouts.forEach {
            connectPlayerTimeoutData[it.key] = it.value
        }

        snapshot.newPlayers.forEach { newPlayer ->
            internalPlayersData.add(newPlayer)
        }

        snapshot.clearFromRemovedPlayerList.forEach { idToClear ->
            removedPlayersList.removeIf { it.playerId == idToClear }
        }
        removedPlayersList.addAll(snapshot.newRemovedPlayers)
        internalPlayersData.removeAll(snapshot.newRemovedPlayers)

        snapshot.removedConnectPlayersTimeouts.forEach { playerIds ->
            connectPlayerTimeoutData.remove(playerIds)
        }

        internalPlayersData.forEach { it.commitSnapshot() }
        _dataStore.commitSnapshot()

        version.commitVersion()
        clearSnapshot()
    }

    override fun clearSnapshot() {
        snapshot.clear()
        internalPlayersData.forEach { it.clearSnapshot() }
        _dataStore.clearSnapshot()
    }

    override fun shouldNotify(player: GamePlayer): Boolean {
        return snapshot.shouldNotify(player)
    }

    fun makeTags(): Array<Pair<String, String>> {
        return arrayOf(
            Pair("gameServerVersion", gameServerVersion),
            Pair("gameServerZone", gameServerZone),
            Pair("hasDedicatedServer", (serverPragmaId != null).toString()),
            Pair("inMatchmaking", inMatchmaking.toString())
        )
    }
}
