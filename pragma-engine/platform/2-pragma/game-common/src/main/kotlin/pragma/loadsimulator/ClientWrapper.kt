package pragma.loadsimulator

import com.google.protobuf.ByteString
import com.google.protobuf.GeneratedMessageV3
import kotlin.reflect.KClass
import kotlin.reflect.jvm.kotlinFunction
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.async
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import pragma.Fixed128
import pragma.PragmaError
import pragma.PragmaResult
import pragma.PragmaResult.Failure
import pragma.ServiceError
import pragma.account.AccountRpc.RefreshTokensV1Request
import pragma.account.AccountRpc.RefreshTokensV1Response
import pragma.auth.PragmaDisplayName
import pragma.client.BackendConnection
import pragma.getOrElse
import pragma.rpc.Notification
import pragma.session.PlayerSessionRpc.HeartbeatV1Request
import pragma.session.PlayerSessionRpc.HeartbeatV1Response
import pragma.session.PlayerSessionRpc.SessionTerminatedForServiceV1Notification
import pragma.utils.TimeProxy.Companion.minutesToMillis
import pragma.utils.TimeProxy.Companion.secondsToMillis

open class LoadSimulatorException(message: String, cause: Throwable? = null) : Exception(message, cause)
class LoadSimulatorRpcException(error: PragmaError, message: String, cause: Throwable? = null) : LoadSimulatorException("$error: $message", cause)
class LoadSimulatorTimeoutException(message: String, cause: Throwable? = null) : LoadSimulatorException("timed out: $message", cause)

class GameRpcClient(
    client: pragma.client.AuthingClient,
    delayer: Delayer,
) : RpcClient(client, delayer) {
    override suspend fun connect() {
        client.connectToGame()
        backendConnection = client.gameConnection
    }
}

class SocialRpcClient(client: pragma.client.AuthingClient, delayer: Delayer) : RpcClient(client, delayer) {
    override suspend fun connect() {
        client.connectToSocial()
        backendConnection = client.socialConnection
    }
}

abstract class RpcClient(
    internal val client: pragma.client.AuthingClient,
    private val delayer: Delayer
) {
    val playerId: Fixed128
        get() = client.playerId

    val socialId: Fixed128
        get() = client.socialId

    val displayName: PragmaDisplayName
        get() = client.displayName

    lateinit var backendConnection: BackendConnection

    abstract suspend fun connect()

    suspend fun <T : Notification> wait(
        notificationKClass: KClass<T>,
        timeoutMillis: Long,
        tracker: ICustomStepTracker,
        onTimeout: suspend () -> Unit = {},
    ): T? {
        return WaitForNotification(backendConnection, tracker, notificationKClass, timeoutMillis).waitForNotification(onTimeout)
    }

    suspend fun <T : Notification> waitOrThrow(
        notificationKClass: KClass<T>,
        timeoutMillis: Long,
        tracker: ICustomStepTracker,
        onTimeout: suspend () -> Unit = {},
    ): T {
        return WaitForNotificationWithThrow(backendConnection, tracker, notificationKClass, timeoutMillis).waitForNotification(onTimeout)!!
    }

    suspend fun <T : Notification> waitForConditionOrThrow(
        notificationKClass: KClass<T>,
        timeoutMillis: Long,
        tracker: ICustomStepTracker,
        condition: (T) -> Boolean,
        onTimeout: suspend () -> Unit = {},
    ): T {
        return WaitForNotificationConditionWithThrow(backendConnection, tracker, notificationKClass, condition, timeoutMillis).waitForNotification(onTimeout)
    }

    suspend fun clearAllNotificationChannels() {
        clearNotificationChannelsExceptFor(listOf())
    }

    suspend fun clearNotificationChannelsExceptFor(notificationsToPersist: List<String>) {
        backendConnection.clearAllNotificationChannelsExcept(notificationsToPersist)
    }

    suspend fun <Request : pragma.rpc.Request, Response : GeneratedMessageV3> send(
        request: Request,
        responseClass: KClass<Response>,
        tracker: ICustomStepTracker,
        humanDelay: Boolean = true,
        onError: suspend (result: Failure<Response, GeneratedMessageV3>) -> Unit = {},
    ): Response? {
        return RequestSender(backendConnection, tracker, request, responseClass, delayer).send(humanDelay, onError)
    }

    suspend fun <Request : pragma.rpc.Request, Response : GeneratedMessageV3> sendOrThrow(
        request: Request,
        responseClass: KClass<Response>,
        tracker: ICustomStepTracker,
        humanDelay: Boolean = true,
        onError: suspend (result: Failure<Response, GeneratedMessageV3>) -> Unit = {},
    ): Response {
        return RequestSenderWithThrow(backendConnection, tracker, request, responseClass, delayer).send(humanDelay, onError)!!
    }

    suspend fun disconnect() {
        if (::backendConnection.isInitialized && backendConnection.isConnected()) {
            backendConnection.stop()
        }
    }
}

interface RpcClients {
    val game: GameRpcClient
    val social: SocialRpcClient

    suspend fun connect()
    suspend fun disconnect()
}

class ClientWrapper(override val game: GameRpcClient, override val social: SocialRpcClient) : RpcClients {
    internal val nullStepTracker = NullStepTracker()
    private val localCache = mutableMapOf<String, Any>()

    val playerId: Fixed128
        get() = game.playerId

    val socialId: Fixed128
        get() = social.socialId

    val displayName: PragmaDisplayName
        get() = game.displayName

    val providerAccountId: String
        get() = game.client.providerAccountId

    private lateinit var refreshJob : Job
    private lateinit var heartbeatJob : Job
    private lateinit var sessionTerminationJob : Job

    override suspend fun connect() {
        social.connect()
        game.connect()
    }

    internal suspend fun startHeartbeats(parentScope:CoroutineScope, delayInMillis: Long = 30.secondsToMillis) {
        if ( ! ::heartbeatJob.isInitialized ) {
            heartbeatJob = SupervisorJob(parentScope.coroutineContext.job)
            val heartbeatScope = CoroutineScope(parentScope.coroutineContext + heartbeatJob + CoroutineName("clientHeartbeats"))
            heartbeatScope.launch {
                val request = HeartbeatV1Request.getDefaultInstance()
                while (isActive) {
                    delay(delayInMillis)
                    if (null == game.send(request, HeartbeatV1Response::class, nullStepTracker, false) ||
                        null == social.send(request, HeartbeatV1Response::class, nullStepTracker, false)) {
                        disconnect()
                        return@launch
                    }
                }
            }
        }
    }

    internal suspend fun startRefreshTokens(parentScope:CoroutineScope){
        if ( ! ::refreshJob.isInitialized ) {
            refreshJob = SupervisorJob(parentScope.coroutineContext.job)
            val refreshScope = CoroutineScope(parentScope.coroutineContext + refreshJob + CoroutineName("refreshTokens"))
            refreshScope.launch {
                while (isActive) {
                    val refreshInMillis = social.client.pragmaTokens.refreshTokenInMillis()
                    delay(refreshInMillis)
                    val request = RefreshTokensV1Request.newBuilder().setPragmaTokens(social.client.pragmaTokens.toProto()).build()
                    val response = social.send(request, RefreshTokensV1Response::class, nullStepTracker, false)
                    if (null == response) {
                        disconnect()
                        return@launch
                    }
                    val tokens = response.pragmaTokens
                    social.client.pragmaTokens.fromProto(tokens)
                    game.client.pragmaTokens.fromProto(tokens)
                }
            }
        }
    }

    fun addToLocalCache(key: String, value: Any) {
        localCache[key] = value
    }

    fun <T>getFromLocalCache(key: String): T? {
        @Suppress("UNCHECKED_CAST")
        return localCache.getOrDefault(key, null) as T
    }

    internal suspend fun waitForSessionDisconnectNotification(parentScope:CoroutineScope) {
        if ( ! ::sessionTerminationJob.isInitialized ) {
            sessionTerminationJob = SupervisorJob(parentScope.coroutineContext.job)
            val sessionTerminationScope = CoroutineScope(parentScope.coroutineContext + sessionTerminationJob + CoroutineName("sessionTermination"))

            sessionTerminationScope.launch {
                while ( isActive ) {
                    val gameNotification = async { game.wait(SessionTerminatedForServiceV1Notification::class, 1.minutesToMillis, nullStepTracker) }
                    val socialNotification = async { social.wait(SessionTerminatedForServiceV1Notification::class, 1.minutesToMillis, nullStepTracker) }
                    if ( null != gameNotification.await() || null != socialNotification.await() ) {
                        disconnect()
                        return@launch
                    }
                }
            }
        }
    }

    override suspend fun disconnect() {
        social.disconnect()
        game.disconnect()
        stopCreatedJobs()
    }

    internal suspend fun stopCreatedJobs() {
        if (::refreshJob.isInitialized && refreshJob.isActive) {
            refreshJob.cancelAndJoin()
        }

        if (::heartbeatJob.isInitialized && heartbeatJob.isActive) {
            heartbeatJob.cancelAndJoin()
        }

        if (::sessionTerminationJob.isInitialized && sessionTerminationJob.isActive) {
            sessionTerminationJob.cancelAndJoin()
        }
    }
}

internal open class RequestSenderWithThrow<Request : pragma.rpc.Request, Response : GeneratedMessageV3>(
    private val backendConnection: BackendConnection,
    private val tracker: ICustomStepTracker,
    private val request: Request,
    responseClass: KClass<Response>,
    private val delayer: Delayer
) {
    private val requestName = request.javaClass.simpleName
    private val responseName = responseClass.simpleName
    private val parseMethodForResponse: (ByteString) -> Response

    init {
        val method = responseClass.java.getMethod("parseFrom", ByteString::class.java) // TODO: Do we want to cache this? IE getOrElse on a hashmap of this
        val canKFunction = GeneratedMessageV3::class.java.isAssignableFrom(method.returnType)

        @Suppress("UNCHECKED_CAST")
        parseMethodForResponse = if (canKFunction) method.kotlinFunction as (ByteString) -> Response else {
            error("Unable to find parseFrom on data object - should not be possible")
        }
    }

    internal suspend fun send(
        humanDelay: Boolean,
        onError: suspend (result: Failure<Response, GeneratedMessageV3>) -> Unit = {},
    ) : Response? {
        if (humanDelay) delayer.delay()

        val ret = backendConnection.makeRequestV2(request, parseMethodForResponse).getOrElse { message ->
            onError(PragmaResult.failure(message))
            returnNullOrThrow(message)
        }
        if ( null != ret ) tracker.trackRpcSuccess(requestName)
        return ret
    }

    protected fun incrementErrorType(error: GeneratedMessageV3) {
        when (error.javaClass) {
            ServiceError::class.java -> tracker.trackRpcServiceError(requestName)
            else -> tracker.trackRpcApplicationError(requestName)
        }
    }

    protected open fun returnNullOrThrow(error: GeneratedMessageV3): Response? {
        incrementErrorType(error)
        when (error.javaClass) {
            ServiceError::class.java -> {
                val serviceError = error as ServiceError
                throw LoadSimulatorRpcException(
                    serviceError.error,
                    "When making request $requestName, expected response $responseName, got an unexpected ServiceError: $serviceError"
                )
            }
            else -> {
                val details = "class: ${error.javaClass.simpleName} - $error"
                throw LoadSimulatorRpcException(
                    PragmaError.UNRECOGNIZED,
                    "When making request $requestName, expected response $responseName, got an unexpected Application error: $details"
                )
            }
        }
    }
}

internal class RequestSender<Request : pragma.rpc.Request, Response : GeneratedMessageV3> (
    backendConnection: BackendConnection,
    tracker: ICustomStepTracker,
    request: Request,
    responseClass: KClass<Response>,
    delayer: Delayer
) : RequestSenderWithThrow<Request, Response>(backendConnection, tracker, request, responseClass, delayer) {
    override fun returnNullOrThrow(error: GeneratedMessageV3): Response? {
        incrementErrorType(error)
        return null
    }
}

internal open class WaitForNotificationWithThrow<T : Notification>(
    private val backendConnection: BackendConnection,
    private val tracker: ICustomStepTracker,
    private val notificationKClass: KClass<T>,
    private val timeoutMillis: Long
) {
    private val notificationName : String = notificationKClass.simpleName.toString()

    internal suspend fun waitForNotification(
        onTimeout: suspend () -> Unit = {},
    ) : T? {
        val ret = try {
            backendConnection.waitForNextNotificationWithTimeout(notificationKClass, timeoutMillis)
        } catch (e: TimeoutCancellationException) {
            val errorMessage = "TimeoutCancellationException while waiting for notification $notificationName for $timeoutMillis milliseconds"
            onTimeout()
            returnNullOrThrow(errorMessage, e)
        }
        if ( null != ret ) tracker.trackNotificationSuccess(notificationName)
        return ret

    }

    protected fun incrementErrorType() {
        tracker.trackNotificationError(notificationName)
    }

    protected open fun returnNullOrThrow(errorMessage: String, exception: TimeoutCancellationException): T? {
        incrementErrorType()
        throw LoadSimulatorTimeoutException(
            errorMessage, exception
        )
    }
}

internal open class WaitForNotificationConditionWithThrow<T : Notification>(
    private val backendConnection: BackendConnection,
    private val tracker: ICustomStepTracker,
    private val notificationKClass: KClass<T>,
    private val condition: (T) -> Boolean,
    private val timeoutMillis: Long
) {
    private val notificationName : String = notificationKClass.simpleName.toString()

    internal suspend fun waitForNotification(
        onTimeout: suspend () -> Unit = {},
    ) : T {
        var notification: T
        val ret = try {
            do {
                notification = backendConnection.waitForNextNotificationWithTimeout(notificationKClass, timeoutMillis)
            } while (
                !condition(notification)
            )
            return notification
        } catch (e: TimeoutCancellationException) {
            val errorMessage = "TimeoutCancellationException while waiting for notification $notificationName for $timeoutMillis milliseconds"
            onTimeout()
            incrementErrorAndThrow(errorMessage, e)
        }
        tracker.trackNotificationSuccess(notificationName)
        return ret

    }

    protected open fun incrementErrorAndThrow(errorMessage: String, exception: TimeoutCancellationException): T {
        tracker.trackNotificationError(notificationName)
        throw LoadSimulatorTimeoutException(
            errorMessage, exception
        )
    }
}

internal open class WaitForNotification<T : Notification>(
    backendConnection: BackendConnection,
    tracker: ICustomStepTracker,
    notificationKClass: KClass<T>,
    timeoutMillis: Long
) : WaitForNotificationWithThrow<T>(backendConnection, tracker, notificationKClass, timeoutMillis) {
    override fun returnNullOrThrow(errorMessage: String, exception: TimeoutCancellationException): T? {
        incrementErrorType()
        return null
    }
}
