package pragma.fleet

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import pragma.FleetProtoTestFactory
import pragma.FleetProtoTestFactory.reportCapacityV1Request
import pragma.GameInstanceProtoTestFactory.gameInstanceId
import pragma.PragmaCoreTestFactory
import pragma.PragmaCoreTestFactory.anyInt
import pragma.PragmaCoreTestFactory.anyLong
import pragma.PragmaCoreTestFactory.anyString
import pragma.utils.toUUID

@ExperimentalCoroutinesApi
internal class ReportCapacityCommandTest {
    private val capacityManager = mockk<CapacityManager>(relaxed = true)
    private val testObj = ReportCapacityCommand(capacityManager)

    private val serverId = PragmaCoreTestFactory.anyFixed128()
    private val serverPoolId = anyString()
    private val reportedUsedCapacity = anyInt(10, 20)
    private val reportedTotalCapacity = reportedUsedCapacity + 3

    @Test
    fun `reportCapacity informs CapacityManager of this server's capacity`() = runTest {
        val totalCapacity = 2
        val usedCapacity = 3

        val reportCapacityRequest = ReportCapacityRequest(
            reportCapacityV1Request(
                i = 1,
                totalCapacity = totalCapacity,
                usedCapacity = usedCapacity,
                serverId = serverId,
                serverPoolId = serverPoolId
                )
        )
        testObj.reportCapacity(reportCapacityRequest)

        coVerify {
            capacityManager.updateServerCapacity(
                serverId.toUUID(),
                serverPoolId,
                ServerCapacity(totalCapacity, usedCapacity)
            )
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2, 3, 2",
        "3, 2, 2",
        "2, 0, 0",
        "0, 2, 0",
        "0, -1, 0",
        "1, -1, 0",
        "0, 0, 0",
    )
    fun `reportCapacity allocates requests to the server based on its available capacity`(
        matchesInQueue: Int,
        capacityAvailable: Int,
        capacityReturned: Int
    ) = runTest {
        trainNQueuedAllocationRequests(matchesInQueue)

        val reportCapacityRequest = ReportCapacityRequest(
            reportCapacityV1Request(
                i = 1,
                totalCapacity = reportedUsedCapacity + capacityAvailable,
                usedCapacity = reportedUsedCapacity,
                serverId = serverId,
                serverPoolId = serverPoolId
            )
        )
        val response = testObj.reportCapacity(reportCapacityRequest)

        assertEquals(capacityReturned, response.gameInstanceIds.size)
        response.gameInstanceIds.forEachIndexed { index, responseGameInstanceId ->
            assertEquals(gameInstanceId(index).toUUID(), responseGameInstanceId)
        }
        verify {
            capacityManager.logState()
        }
    }

    @Test
    fun `reportCapacity removes allocated games from the queue`() = runTest {
        trainNQueuedAllocationRequests(1)

        val reportCapacityRequest = ReportCapacityRequest(
            reportCapacityV1Request(
                i = 1,
                totalCapacity = reportedTotalCapacity,
                usedCapacity = reportedUsedCapacity,
                serverId = serverId,
                serverPoolId = serverPoolId
            )
        )

        val response1 = testObj.reportCapacity(reportCapacityRequest)
        assertEquals(1, response1.gameInstanceIds.size)

        val response2 = testObj.reportCapacity(reportCapacityRequest)
        assertEquals(0, response2.gameInstanceIds.size)
    }

    @Test
    fun `reportCapacity responds with heartbeat period and whether or not to exit`() = runTest {
        val nextServerPing = anyLong(1)
        coEvery { capacityManager.updateServerCapacity(any(), any(), any()) } returns nextServerPing

        val reportCapacityRequest = ReportCapacityRequest(
            reportCapacityV1Request(
                i = 1,
                totalCapacity = 2,
                usedCapacity = 0,
                serverId = serverId,
                serverPoolId = serverPoolId
            )
        )
        val response = testObj.reportCapacity(reportCapacityRequest)

        assertEquals(nextServerPing, response.nextHeartbeatFrequency)
    }

    private fun trainNQueuedAllocationRequests(matchCount: Int) {
        coEvery {
            capacityManager.getNextAllocationRequest(serverId.toUUID(), any())
        } returnsMany List(matchCount) {
            AllocationRequest(
                FleetProtoTestFactory.startGameInstanceV1Request(it),
                anyLong()
            )
        } andThen null
    }
}
