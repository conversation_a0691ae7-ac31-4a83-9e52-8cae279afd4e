package pragma.inventory

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import pragma.InventoryProtoTestFactory.instancedItemUpdate
import pragma.InventoryProtoTestFactory.instancedItemUpdates
import pragma.InventoryProtoTestFactory.serverInstancedItemUpdate
import pragma.InventoryProtoTestFactory.serverItemUpdateV2WithInstancedItem
import pragma.InventoryProtoTestFactory.serverItemUpdateV2WithStackableItem
import pragma.InventoryProtoTestFactory.stackableItemUpdate
import pragma.InventoryProtoTestFactory.updateItemV4Request
import pragma.InventoryProtoTestFactory.updateItemsServiceV2Request
import pragma.InventoryProtoTestFactory.updateItemsV0Request
import pragma.PragmaError
import pragma.inventory.InventoryCommon.ServerItemUpdateV2
import pragma.utils.assertThrowsPragmaError

class UpdateItemsRequestTest {
    @Nested
    inner class ClientUpdateItemRequests {
        @Test
        fun `empty request UpdateItemsV0 request returns empty update`() {
            val itemsV0Response = ClientUpdateItemsRequest.create(InventoryRpc.UpdateItemsV0Request.getDefaultInstance())
            assertEquals(ClientUpdateItemsRequest(listOf(), listOf()), itemsV0Response)
        }

        @Test
        fun `empty request UpdateItemV4 request returns empty update`() {
            val result = ClientUpdateItemsRequest.create(InventoryRpc.UpdateItemV4Request.getDefaultInstance())
            assertEquals(ClientUpdateItemsRequest(listOf(), listOf()), result)
        }

        @Test
        fun `translates from UpdateItemV4Request for an instanced item`() {
            val expected = ClientUpdateItemsRequest(
                listOf(ClientInstancedItemUpdate(instancedItemUpdate(1))),
                listOf()
            )

            val updateRequest = ClientUpdateItemsRequest.create(updateItemV4Request(1))
            assertEquals(expected, updateRequest)
        }

        @Test
        fun `translates from UpdateItemsV0Request`() {
            val expected = ClientUpdateItemsRequest(
                instancedItemUpdates(1).map{ ClientInstancedItemUpdate(it)},
                listOf()
            )

            val updateRequest = ClientUpdateItemsRequest.create(updateItemsV0Request(1))
            assertEquals(expected, updateRequest)
        }
    }

    @Nested
    inner class ServerUpdateItemsRequests {
        @Test
        fun `empty request returns empty update`() {
            val itemServiceV1 = ServerUpdateItemsRequest.create(InventoryRpc.UpdateItemsServiceV2Request.getDefaultInstance())
            assertEquals(ServerUpdateItemsRequest(listOf(), listOf()), itemServiceV1)
        }

        @Test
        fun `unknown item cases throw exceptions`() {
            assertThrowsPragmaError(PragmaError.InventoryService_InstanceNotFound) {
                val request = InventoryRpc.UpdateItemsServiceV2Request.newBuilder().addUpdates(ServerItemUpdateV2.getDefaultInstance()).build()
                ServerUpdateItemsRequest.create(request)
            }
        }

        @Test
        fun `translates from UpdateItemsServiceV1Request`() {
            val serverInstancedUpdate = serverItemUpdateV2WithInstancedItem(1)
            val stackableUpdate = serverItemUpdateV2WithStackableItem(2)

            val expected = ServerUpdateItemsRequest(
                listOf(ServerInstancedItemUpdate(serverInstancedItemUpdate(1))),
                listOf(StackableItemUpdate(stackableItemUpdate(2)))
            )

            val updateRequest = ServerUpdateItemsRequest.create(updateItemsServiceV2Request(1, updates = listOf(serverInstancedUpdate, stackableUpdate)))

            assertEquals(expected, updateRequest)
        }
    }
}