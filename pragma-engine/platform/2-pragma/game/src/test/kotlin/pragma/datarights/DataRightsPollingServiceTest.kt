package pragma.datarights

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.UUID
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import pragma.DataRightsTestFactory.dataRightsGameRequest
import pragma.DataRightsTestFactory.failGameReportPartnerV1Request
import pragma.DataRightsTestFactory.personalDataReport
import pragma.DataRightsTestFactory.submitGameReportPartnerV1Request
import pragma.DataRightsTestFactory.submitGameReportPartnerV1Response
import pragma.PragmaCoreTestFactory
import pragma.PragmaCoreTestFactory.anyUuid
import pragma.PragmaError
import pragma.PragmaException
import pragma.PragmaResult.Failure
import pragma.PragmaResult.Success
import pragma.ProtoTestFactory.serviceError
import pragma.client.SocialBackendPartnerClientNodeService
import pragma.config.CoreConfig
import pragma.config.PragmaConfig
import pragma.datarights.DataRightsRpc.GetPendingGameRequestsPartnerV1Response
import pragma.datarights.DataRightsRpc.SubmitGameReportPartnerV1Response
import pragma.rpcs.PragmaFailure
import pragma.services.NodeServicesContainer
import pragma.settings.BackendType
import pragma.utils.TimeProxy.Companion.minutesToMillis
import pragma.utils.advanceTimeByAndRunCurrent
import pragma.utils.toFixed128
import pragma.utils.toUUID

@OptIn(ExperimentalCoroutinesApi::class)
class DataRightsPollingServiceTest {
    private val socialBackendPartnerClientNodeService: SocialBackendPartnerClientNodeService = mockk()
    private val dataRightsPlugin: DataRightsGamePlugin = mockk()
    private val fakeBackgroundManager = FakeBackgroundManager()
    private val gameShardId = anyUuid()
    private val pragmaConfig: PragmaConfig = PragmaConfig.getFor(BackendType.GAME).apply {
        core = CoreConfig.getFor(BackendType.GAME).apply {
            shardId = gameShardId
        }
    }
    private val pragmaNode = PragmaCoreTestFactory.pragmaNode().apply {
        every { createChild(any()) } returns fakeBackgroundManager
        every { pragmaConfig } returns <EMAIL>
    }
    private val instanceId = mockk<UUID> {
        every { mostSignificantBits } returns 0
        every { leastSignificantBits } returns 0
    }
    private val mockLogger: Logger = mockk(relaxUnitFun = true)
    private val testObj = DataRightsPollingService(
        pragmaNode,
        instanceId,
    ).apply {
        nodeServicesContainer =
            NodeServicesContainer(mapOf(SocialBackendPartnerClientNodeService::class to socialBackendPartnerClientNodeService))
        dataRightsGamePlugin = dataRightsPlugin
        logger = mockLogger
        val config = DataRightsPollingServiceConfig.getFor(BackendType.GAME).apply {
            enabled = true
        }
        runBlocking { onConfigChanged(config) }
    }

    @Test
    fun `run Calls BackgroundManager`() = runTest {
        val checkRequestsFrequencyMinutes = 60
        val config = DataRightsPollingServiceConfig.getFor(BackendType.GAME).apply {
            enabled = true
            checkRequestsFrequencyInMinutes = checkRequestsFrequencyMinutes
        }

        testObj.onConfigChanged(config)
        initializeBackgroundManager(this)

        assertEquals(checkRequestsFrequencyMinutes.minutesToMillis, fakeBackgroundManager.runForeverDelay)
        assertTrue(fakeBackgroundManager.runForeverBlockInitialized)
    }

    @Test
    fun `run polls after a delay`() = runTest {
        testObj.run()

        verifyBackgroundManagerNotRegistered()

        launch { fakeBackgroundManager.fireAndForgetBlock.invoke(this) }
        advanceTimeByAndRunCurrent(1.minutesToMillis - 1)
        assertFalse(fakeBackgroundManager.runForeverBlockInitialized)
        advanceTimeByAndRunCurrent(1)
        assertTrue(fakeBackgroundManager.runForeverBlockInitialized)
    }

    @Test
    fun `run does not trigger runForever on instance id != 0`() = runTest {
        every { instanceId.leastSignificantBits } returns PragmaCoreTestFactory.anyLong(1, 100)

        initializeBackgroundManager(this)

        verifyBackgroundManagerNotRegistered()
    }

    @Test
    fun `run triggers runForever on instance id == 0`() = runTest {
        initializeBackgroundManager(this)

        verifyBackgroundManagerRegistered()
    }

    @Test
    fun `run does not trigger when disabled through config by default`() = runTest {
        val config = DataRightsPollingServiceConfig.getFor(BackendType.GAME)
        testObj.onConfigChanged(config)

        initializeBackgroundManager(this)

        verifyBackgroundManagerNotRegistered()
    }

    private fun verifyBackgroundManagerRegistered() {
        assertNotEquals(-1, fakeBackgroundManager.runForeverDelay)
        assertTrue(fakeBackgroundManager.runForeverBlockInitialized)
        verify(exactly = 1) { mockLogger.trace("Starting DataRightsPollingService.") }
    }

    private fun verifyBackgroundManagerNotRegistered() {
        assertEquals(-1, fakeBackgroundManager.runForeverDelay)
        assertFalse(fakeBackgroundManager.runForeverBlockInitialized)
        verify(exactly = 0) { mockLogger.trace("Starting DataRightsPollingService.") }
    }

    @Test
    fun `run on primary instance prints out a trace`() = runTest {
        initializeBackgroundManager(this)

        verify { mockLogger.trace("Starting DataRightsPollingService.") }
    }

    @Test
    fun `checkRequests gets pending game requests from social node`() = runTest {
        val pendingGameRequest = dataRightsGameRequest(1)
        val submitGameReportRequest = trainPendingGameRequest(1, gameShardId, pendingGameRequest)

        val pendingGameRequest2 = dataRightsGameRequest(2)
        val submitGameReport2Request = trainPendingGameRequest(2, gameShardId, pendingGameRequest2)

        val getPendingGameRequestsRequest = DataRightsRpc.GetPendingGameRequestsPartnerV1Request.newBuilder()
            .setGameShardId(gameShardId.toFixed128())
            .build()
        val getPendingGameRequestsResponse = GetPendingGameRequestsPartnerV1Response.newBuilder()
            .addAllRequests(listOf(pendingGameRequest, pendingGameRequest2))
            .build()
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                getPendingGameRequestsRequest,
                GetPendingGameRequestsPartnerV1Response::parseFrom
            )
        } returns Success(getPendingGameRequestsResponse)

        trigger(this)

        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReportRequest,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReport2Request,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
    }

    @Test
    fun `checkRequests logs error when get pending game requests fails`() = runTest {
        val getPendingGameRequestsRequest = DataRightsRpc.GetPendingGameRequestsPartnerV1Request.newBuilder()
            .setGameShardId(gameShardId.toFixed128())
            .build()
        val error = serviceError(1, error = PragmaError.Database_Error)
        val failure = PragmaFailure(serviceError = error)
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                getPendingGameRequestsRequest,
                GetPendingGameRequestsPartnerV1Response::parseFrom
            )
        } returns Failure(failure)

        trigger(this)

        verify { mockLogger.error("Failed to get pending data rights game requests from the social backend: $failure") }
        coVerify(exactly = 0) { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            any(),
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
    }

    @Test
    fun `checkRequests logs error and continues processing requests when plugin build report fails`() = runTest {
        val pendingGameRequest = dataRightsGameRequest(1)
        val submitGameReportRequest = trainPendingGameRequest(1, gameShardId, pendingGameRequest)

        val pendingGameRequest2 = dataRightsGameRequest(2)
        val submitGameReport2Request = trainPendingGameRequest(2, gameShardId, pendingGameRequest2)

        val pendingGameRequest3 = dataRightsGameRequest(3)
        val submitGameReport3Request = trainPendingGameRequest(3, gameShardId, pendingGameRequest3)

        val getPendingGameRequestsRequest = DataRightsRpc.GetPendingGameRequestsPartnerV1Request.newBuilder()
            .setGameShardId(gameShardId.toFixed128())
            .build()
        val getPendingGameRequestsResponse = GetPendingGameRequestsPartnerV1Response.newBuilder()
            .addAllRequests(listOf(pendingGameRequest, pendingGameRequest2, pendingGameRequest3))
            .build()
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                getPendingGameRequestsRequest,
                GetPendingGameRequestsPartnerV1Response::parseFrom
            )
        } returns Success(getPendingGameRequestsResponse)

        val failReport2Request = failGameReportPartnerV1Request(2, pendingGameRequest2.requestId, gameShardId.toFixed128())
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                failReport2Request,
                DataRightsRpc.FailGameReportPartnerV1Response::parseFrom
            )
        } returns mockk()

        val playerId2 = pendingGameRequest2.gameIdentity.pragmaPlayerId.toUUID()
        val requestId2 = pendingGameRequest2.requestId.toUUID()

        val exception = PragmaException(PragmaError.DataRightsService_ErrorFetchingAccountData, "Could not fetch account data.")
        coEvery { dataRightsPlugin.buildPersonalDataReport(playerId2, requestId2) } throws exception

        trigger(this)

        verify { mockLogger.error("Data rights plugin could not build report for requestId: $requestId2", exception) }

        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReportRequest,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
        coVerify(exactly = 0) {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                submitGameReport2Request,
                SubmitGameReportPartnerV1Response::parseFrom
            )
        }
        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReport3Request,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
        coVerify(exactly = 1) {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                failReport2Request,
                DataRightsRpc.FailGameReportPartnerV1Response::parseFrom
            )
        }
    }

    @Test
    fun `checkRequests logs error and continues processing requests when rpc submit game report fails`() = runTest {
        val pendingGameRequest = dataRightsGameRequest(1)
        val submitGameReportRequest = trainPendingGameRequest(1, gameShardId, pendingGameRequest)

        val pendingGameRequest2 = dataRightsGameRequest(2)
        val submitGameReport2Request = trainPendingGameRequest(2, gameShardId, pendingGameRequest2)

        val pendingGameRequest3 = dataRightsGameRequest(3)
        val submitGameReport3Request = trainPendingGameRequest(3, gameShardId, pendingGameRequest3)

        val getPendingGameRequestsRequest = DataRightsRpc.GetPendingGameRequestsPartnerV1Request.newBuilder()
            .setGameShardId(gameShardId.toFixed128())
            .build()
        val getPendingGameRequestsResponse = GetPendingGameRequestsPartnerV1Response.newBuilder()
            .addAllRequests(listOf(pendingGameRequest, pendingGameRequest2, pendingGameRequest3))
            .build()
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                getPendingGameRequestsRequest,
                GetPendingGameRequestsPartnerV1Response::parseFrom
            )
        } returns Success(getPendingGameRequestsResponse)

        val requestId2 = pendingGameRequest2.requestId.toUUID()
        val databaseError = serviceError(1, error = PragmaError.Database_Error)
        val failure = PragmaFailure(serviceError = databaseError)
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                submitGameReport2Request,
                SubmitGameReportPartnerV1Response::parseFrom
            )
        } returns Failure(failure)

        trigger(this)

        verify { mockLogger.error("Unable to submit game data report to the social backend for requestId: $requestId2. Failure: $failure") }

        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReportRequest,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReport2Request,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReport3Request,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
    }

    @Test
    fun `checkRequests logs correct error when requestRpc throws an exception`() = runTest {
        val pendingGameRequest = dataRightsGameRequest(1)
        val submitGameReportRequest = trainPendingGameRequest(1, gameShardId, pendingGameRequest)

        val getPendingGameRequestsRequest = DataRightsRpc.GetPendingGameRequestsPartnerV1Request.newBuilder()
            .setGameShardId(gameShardId.toFixed128())
            .build()
        val getPendingGameRequestsResponse = GetPendingGameRequestsPartnerV1Response.newBuilder()
            .addAllRequests(listOf(pendingGameRequest))
            .build()
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                getPendingGameRequestsRequest,
                GetPendingGameRequestsPartnerV1Response::parseFrom
            )
        } returns Success(getPendingGameRequestsResponse)

        val exception = PragmaException(PragmaError.UNKNOWN_ERROR, "Unknown error")
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                submitGameReportRequest,
                SubmitGameReportPartnerV1Response::parseFrom
            )
        } throws exception

        trigger(this)

        val requestId = pendingGameRequest.requestId.toUUID()
        verify {
            mockLogger.error(
                "Unable to submit game data report to the social backend for requestId: $requestId.",
                exception
            )
        }
        verify(exactly = 0) { mockLogger.error("Data rights plugin could not build report for requestId: $requestId", any()) }

        coVerify { socialBackendPartnerClientNodeService.makeSocialRequestV2(
            submitGameReportRequest,
            SubmitGameReportPartnerV1Response::parseFrom
        ) }
    }

    private fun trainPendingGameRequest(
        i: Int,
        gameShardId: UUID,
        pendingGameRequest: DataRightsRpc.DataRightsGameRequest,
    ): DataRightsRpc.SubmitGameReportPartnerV1Request {
        val playerId = pendingGameRequest.gameIdentity.pragmaPlayerId.toUUID()
        val requestId = pendingGameRequest.requestId.toUUID()
        val gameReport = personalDataReport(i)
        val submitGameReportRequest = submitGameReportPartnerV1Request(1, requestId.toFixed128(), gameShardId.toFixed128(), gameReport)
        val submitGameReportResponse = submitGameReportPartnerV1Response()
        coEvery {
            socialBackendPartnerClientNodeService.makeSocialRequestV2(
                submitGameReportRequest,
                SubmitGameReportPartnerV1Response::parseFrom
            )
        } returns Success(submitGameReportResponse)
        coEvery { dataRightsPlugin.buildPersonalDataReport(playerId, requestId) } returns PersonalDataReport(gameReport)
        return submitGameReportRequest
    }

    private suspend fun initializeBackgroundManager(testScope: TestScope) {
        testObj.run()
        fakeBackgroundManager.fireAndForgetBlock.invoke(testScope)
    }

    private suspend fun trigger(testScope: TestScope) {
        initializeBackgroundManager(testScope)
        fakeBackgroundManager.runForeverBlock?.invoke()
    }
}

