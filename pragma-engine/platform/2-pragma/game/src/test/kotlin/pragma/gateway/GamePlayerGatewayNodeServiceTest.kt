package pragma.gateway

import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.header
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.request.url
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.OverrideMockKs
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import pragma.PragmaCoreTestFactory
import pragma.PragmaResult.Success
import pragma.loginqueue.LoginQueueRpc.CheckTicketV1Request
import pragma.loginqueue.LoginQueueRpc.CheckTicketV1Response
import pragma.loginqueue.LoginQueueRpc.GetInQueueV1Request
import pragma.loginqueue.LoginQueueRpc.GetInQueueV1Response
import pragma.loginqueue.LoginQueueTestFactory.loginQueueTicket
import pragma.loginqueue.TicketDetails
import pragma.settings.BackendType
import pragma.utils.PragmaSerializationUtils

@OptIn(ExperimentalCoroutinesApi::class)
@ExtendWith(MockKExtension::class)
class GamePlayerGatewayNodeServiceTest {
    private val pragmaNode = PragmaCoreTestFactory.pragmaNode()
    private val aggregateSessionManager = mockk<AggregateSessionManager>(relaxUnitFun = true)

    @OverrideMockKs
    private lateinit var testObj: GamePlayerGatewayNodeService

    @Test
    fun `onConfigChanged sets config`() = runTest {
        val config = GamePlayerGatewayConfig.getFor(BackendType.GAME)

        testObj.onConfigChanged(config)

        assertEquals(config.gameBindPort(), testObj.getBindPort())
        verify { aggregateSessionManager.onConfigChanged(config) }
    }

    @Test
    fun gatewayName() {
        assertEquals("PlayerGateway", testObj.gatewayName)
    }

    @Test
    fun invalidPostBodyRespondsWithBadRequest() = runTest {
        runTestWithJsonBody(
            body = "",
            asserts = {response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)
                assertEquals(
                    """
                No content to map due to end-of-input
                 at [Source: (String)""; line: 1, column: 0]
            """.trimIndent(), response.bodyAsText()
                )
            })
    }

    @Test
    fun invalidPostParamRespondsWithBadRequest() = runTest {
        runTestWithJsonBody(
            body = """
                {
                    "someUnexpectedParam": "why, hello there!"
                }
            """.trimIndent(),
            asserts = {response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)
                val expectedToContain = """
                Instantiation of [simple type
                """.trimIndent()
                assertTrue(
                    response.bodyAsText().contains(expectedToContain),
                    "\nresponse should have contained: $expectedToContain\n\nbut was: ${response.bodyAsText()}"
                )
            })
    }

    @Test
    fun invalidPostBodyJsonRespondsWithBadRequest() = runTest {
        runTestWithJsonBody(
            body = """
                this is not json
            """.trimIndent(),
            asserts = {response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)
                val expectedToContain = """
                    Unrecognized token 'this': was expecting (JSON String, Number, Array, Object or token 'null', 'true' or 'false')
                """.trimIndent()

                assertTrue(
                    response.bodyAsText().contains(expectedToContain),
                    "\nresponse should have contained: $expectedToContain\n\nbut was: ${response.bodyAsText()}"
                )
            })
    }

    private fun runTestWithJsonBody(body: String, asserts: suspend  (response: HttpResponse) -> Unit) {
        runCheckTicketTest(requestParams = {
                                           header(HttpHeaders.ContentType , ContentType.Application.Json.toString())
                                           header(HttpHeaders.Accept , ContentType.Application.Json.toString())
            setBody(body)
        }, asserts = asserts)
    }

    @Test
    fun `given dispenser configured, expects trained response`(){
        trainAlwaysAllowingTicketChecker()
        runLoginQueueTest(asserts = { response ->
            assertEquals(HttpStatusCode.OK, response.status)
            verifyAllowedInResponse(response.bodyAsText())
        })
    }

    @Test
    fun closeAllConnections() = runTest{
        testObj.closeAllConnections()
        coVerify {
            aggregateSessionManager.closeAllConnections()
        }
    }

    private fun trainAlwaysAllowingTicketChecker() {
        coEvery { pragmaNode.requestRpc(any(), any<GetInQueueV1Request>(), GetInQueueV1Response::parseFrom, any()) } returns Success(GetInQueueV1Response.newBuilder().setTicketDetails(loginQueueTicket(123).toProto()).build())
        coEvery { pragmaNode.requestRpc(any(), any<CheckTicketV1Request>(), CheckTicketV1Response::parseFrom, any()) } returns Success(CheckTicketV1Response.newBuilder().setTicketDetails(loginQueueTicket(123).toProto()).build())
    }

    private fun verifyAllowedInResponse(content: String) {
        val ticket: TicketDetails = PragmaSerializationUtils.jsonMapper.readValue(content)
        assertTrue(ticket.isAllowedIn)
    }

    private fun runLoginQueueTest(asserts: suspend  (response: HttpResponse) -> Unit){
        runTicketQueueing(HttpMethod.Get, "/v1/loginqueue/getinqueuev1", {}, asserts)
    }
    private fun runCheckTicketTest(requestParams: HttpRequestBuilder.() -> Unit = {}, asserts: suspend (response: HttpResponse) -> Unit) {
        runTicketQueueing(HttpMethod.Post, "/v1/loginqueue/checkticketv1", requestParams, asserts)
    }

    private fun runTicketQueueing(
        httpMethod: HttpMethod, uri: String, requestParams: HttpRequestBuilder.() -> Unit = {}, asserts: suspend (response: HttpResponse) -> Unit) {
        testApplication {
            application {
                testObj.addTicketQueueing(this)
            }
            val response = client.request {
                method = httpMethod
                url(uri)
                requestParams()
            }
            asserts(response)
        }

    }
}
