package pragma.matchmaking

import io.micrometer.core.instrument.Timer
import java.util.Deque
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap
import java.util.concurrent.TimeUnit
import java.util.function.ToDoubleFunction
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.slf4j.Logger
import pragma.PragmaError
import pragma.PragmaException
import pragma.logging.PragmaLoggerFactory
import pragma.matchmaking.MatchmakingRpc.LeftMatchmakingReason
import pragma.matchmaking.MatchmakingRpc.LeftMatchmakingReason.LEFT_MATCHMAKING_REASON_UNSPECIFIED
import pragma.matchmaking.MatchmakingRpc.MatchmakingFailureReason
import pragma.matchmaking.MatchmakingRpc.MatchmakingFailureReason.ERROR_SENDING_PLAYERS_TO_GAME_INSTANCE
import pragma.matchmaking.MatchmakingRpc.MatchmakingQueueKey
import pragma.matchmaking.MatchmakingRpc.PlayerMatchmakingStatus
import pragma.matchmaking.MatchmakingRpc.QueueInfoV2
import pragma.matchmaking.MatchmakingService.Metrics.ENTER_MATCHMAKING_ADD_PLAYERS_KEY
import pragma.matchmaking.MatchmakingService.Metrics.ENTER_MATCHMAKING_ADD_TO_QUEUE_KEY
import pragma.matchmaking.MatchmakingService.Metrics.ENTER_MATCHMAKING_INITIALIZE_KEY
import pragma.matchmaking.MatchmakingService.Metrics.ENTER_MATCHMAKING_MUTEX_LOCK_KEY
import pragma.matchmaking.MatchmakingService.Metrics.GAME_INSTANCE
import pragma.matchmaking.MatchmakingService.Metrics.HOLDING_CHANNEL_ENTER_COUNTER
import pragma.matchmaking.MatchmakingService.Metrics.MATCHABLE
import pragma.matchmaking.MatchmakingService.Metrics.MatchmakingOutcome
import pragma.matchmaking.MatchmakingService.Metrics.MatchmakingOutcome.FailedSendingToGameInstance
import pragma.matchmaking.MatchmakingService.Metrics.MatchmakingOutcome.GameServerVersionNoLongerCompatible
import pragma.matchmaking.MatchmakingService.Metrics.MatchmakingOutcome.Left
import pragma.matchmaking.MatchmakingService.Metrics.MatchmakingOutcome.PlayerRequested
import pragma.matchmaking.MatchmakingService.Metrics.MatchmakingOutcome.PluginError
import pragma.matchmaking.MatchmakingService.Metrics.TIME_TO_PROCESS_QUEUE_KEY
import pragma.matchmaking.impls.MatchableImpl
import pragma.matchmaking.impls.MatchmakingGameInstanceImpl
import pragma.matchmaking.impls.MatchmakingGamePlayerImpl
import pragma.matchmaking.impls.MatchmakingPartyImpl
import pragma.metrics.MetricsManager
import pragma.utils.AlertingMutexFactory
import pragma.utils.GameInstanceId
import pragma.utils.InstanceId
import pragma.utils.PlayerId
import pragma.utils.TimeProxy
import pragma.utils.toFixed128
import pragma.utils.toInstanceIdString

@ExperimentalCoroutinesApi
internal class MatchmakingQueues(
    private val mutex: Mutex = AlertingMutexFactory().get(MatchmakingQueues::class.qualifiedName!!), // Single mutex to block player/party from being added to two queues at once
    private val queues: ConcurrentMap<MatchmakingQueueKey, MatchableQueue> = ConcurrentHashMap(),
    private val matchablesByPlayerId: ConcurrentMap<UUID, MatchableImpl> = ConcurrentHashMap(),
    private val matchmakingGameInstancesById: ConcurrentMap<UUID, MatchmakingGameInstanceImpl> = ConcurrentHashMap(),
    private val logger: Logger = PragmaLoggerFactory.getLogger(MatchmakingQueues::class),
    private val timeProxy: TimeProxy = TimeProxy(),
) {
    private lateinit var config: MatchmakingConfig
    private lateinit var service: MatchmakingService
    private lateinit var matchmakingPlugin: MatchmakingPlugin
    private lateinit var matchReleaser: MatchReleaser
    private lateinit var metricsManager: MetricsManager
    private lateinit var newGameInstanceChannel: Channel<NewGame>
    private lateinit var joinGameInstanceChannel: Channel<JoinGame>
    private lateinit var removeMatchmakingGameInstanceChannel: Channel<MatchmakingGameInstanceImpl>
    private lateinit var serviceInstanceId: InstanceId
    private lateinit var matchmakingNotificationClient: MatchmakingNotificationClient

    fun init(
        service: MatchmakingService,
        matchmakingPlugin: MatchmakingPlugin,
        metricsManager: MetricsManager,
        newGameInstanceChannel: Channel<NewGame>,
        joinGameInstanceChannel: Channel<JoinGame>,
        removeMatchmakingGameInstanceChannel: Channel<MatchmakingGameInstanceImpl>,
        serviceInstanceId: UUID,
        matchmakingNotificationClient: MatchmakingNotificationClient,
    ) {
        this.service = service
        this.matchmakingPlugin = matchmakingPlugin
        this.metricsManager = metricsManager
        this.matchReleaser = service
        this.newGameInstanceChannel = newGameInstanceChannel
        this.joinGameInstanceChannel = joinGameInstanceChannel
        this.removeMatchmakingGameInstanceChannel = removeMatchmakingGameInstanceChannel
        this.serviceInstanceId = serviceInstanceId
        this.matchmakingNotificationClient = matchmakingNotificationClient
    }

    fun onConfigChanged(config: MatchmakingConfig) {
        this.config = config
    }

    private suspend fun <R> timeIfConfigEnabled(key: String, block: suspend () -> R): R {
        return if (config.enableExtraEnterMatchmakingTimers) {
            val timer = metricsManager.startTimer()
            val result = block()
            timer.stop(metricsManager.metricsTimer(key))
            result
        } else {
            block()
        }
    }

    fun getOrPutQueue(key: MatchmakingQueueKey): MatchableQueue {
        return queues.getOrPut(key) {
            val queue = MatchableQueue(key, matchmakingPlugin.getQueueName(key.extMatchmakingKey))
            registerMetricsGauge(
                key,
                queue,
                MatchmakingService.Metrics.PLAYER_COUNT_KEY,
                MatchableQueue.getMatchablePlayerCount
            )
            registerMetricsGauge(
                key,
                queue,
                MatchmakingService.Metrics.GAME_INSTANCE_COUNT_KEY,
                MatchableQueue.getMatchmakingGameInstanceCount
            )
            registerMetricsGauge(
                key,
                queue,
                MatchmakingService.Metrics.PAUSED_GAME_INSTANCE_COUNT_KEY,
                MatchableQueue.getPausedMatchmakingGameInstanceCount
            )
            queue
        }
    }

    fun getMatchmakingStatus(playerIds: List<UUID>): List<PlayerMatchmakingStatus> {
        return playerIds.map {
            PlayerMatchmakingStatus.newBuilder()
                .setPlayerId(it.toFixed128())
                .setIsInMatchmaking(matchablesByPlayerId.containsKey(it))
                .build()
        }
    }

    suspend fun addMatchableToNewMatchableChannel(
        queueKey: MatchmakingQueueKey,
        matchable: MatchableImpl,
    ) {
        timeIfConfigEnabled(ENTER_MATCHMAKING_ADD_PLAYERS_KEY) {
            putPlayersIntoCache(matchable)
        }
        val queue = getOrPutQueue(queueKey)
        matchable.parties.forEach { it.startedChannelTimestampMillis = timeProxy.currentEpochMillis() }
        queue.addNewMatchable(matchable)

        if (logger.isDebugEnabled) {
            logger.debug(
                "Adding matchable with players ({}) to queue ({}) with key ({})",
                matchable.players.joinToString { it.playerId.toString() }.ifEmpty { "None" },
                queue.queueName,
                queueKey
            )
        }
        metricsManager.metricsCounter(
            HOLDING_CHANNEL_ENTER_COUNTER,
            Pair("queue", queue.queueName),
            Pair("groupType", MATCHABLE),
            Pair("gameServerVersion", queueKey.gameServerVersion),
        ).increment()
    }

    suspend fun addMatchmakingGameInstanceToNewGameInstanceChannel(
        queueKey: MatchmakingQueueKey,
        matchmakingGameInstance: MatchmakingGameInstanceImpl,
    ) {
        matchmakingGameInstancesById[matchmakingGameInstance.id] = matchmakingGameInstance

        val queue = getOrPutQueue(queueKey)

        if (logger.isDebugEnabled) {
            logger.debug(
                "Adding game instance with players ({}) to queue ({}) with key ({}) and game server zone ({})",
                matchmakingGameInstance.players.joinToString { it.playerId.toString() }.ifEmpty { "None" },
                queue.queueName,
                queueKey,
                matchmakingGameInstance.gameServerZone
            )
        }
        metricsManager.metricsCounter(
            HOLDING_CHANNEL_ENTER_COUNTER,
            Pair("queue", queue.queueName),
            Pair("groupType", GAME_INSTANCE),
            Pair("gameServerVersion", queueKey.gameServerVersion),
            Pair("gameServerZone", matchmakingGameInstance.gameServerZone)
        ).increment()

        matchmakingGameInstance.startTimeInChannel(timeProxy.currentEpochMillis())
        queue.addNewGameInstance(matchmakingGameInstance)
    }

    suspend fun addMatchableToQueue(queueKey: MatchmakingQueueKey, matchable: MatchableImpl) {
        val queue = getOrPutQueue(queueKey)

        val gameStarted = timeIfConfigEnabled(ENTER_MATCHMAKING_INITIALIZE_KEY) {
            doInitialize(queue, matchable)
        }
        if (gameStarted) {
            return
        }

        var timer: Timer.Sample? = null
        if (config.enableExtraEnterMatchmakingTimers) {
            timer = metricsManager.startTimer()
        }

        mutex.withLock {
            if (config.enableExtraEnterMatchmakingTimers) {
                timer?.stop(metricsManager.metricsTimer(ENTER_MATCHMAKING_MUTEX_LOCK_KEY))
            }

            timeIfConfigEnabled(ENTER_MATCHMAKING_ADD_TO_QUEUE_KEY) {
                metricsManager.metricsCounter(
                    MatchmakingService.Metrics.ENTER_QUEUE_KEY,
                    Pair("queue", queue.queueName),
                    Pair("gameServerVersion", queueKey.gameServerVersion),
                ).increment()

                matchable.parties.forEach { it.startedQueueTimestampMillis = timeProxy.currentEpochMillis() }
                queue.queue.add(matchable)
            }
        }
    }

    suspend fun addMatchmakingGameInstanceToQueue(queueKey: MatchmakingQueueKey, matchmakingGameInstance: MatchmakingGameInstanceImpl) {
        mutex.withLock {
            val queue = getOrPutQueue(queueKey)

            matchmakingGameInstance.startTimeInQueue(timeProxy.currentEpochMillis())
            queue.queue.add(matchmakingGameInstance)

            metricsManager.metricsCounter(
                MatchmakingService.Metrics.ENTER_QUEUE_WITH_GAME_INSTANCE_KEY,
                Pair("queue", queue.queueName),
                Pair("gameServerVersion", queueKey.gameServerVersion),
                Pair("gameServerZone", matchmakingGameInstance.gameServerZone)
            ).increment()
        }
    }

    suspend fun runMatchmaking() {
        queues.forEach { (_, queue) ->
            queue.beginLoopTimestampMillis = timeProxy.currentEpochMillis()

            runMatchmakingOnQueue(queue)

            metricsManager.metricsTimer(
                TIME_TO_PROCESS_QUEUE_KEY,
                Pair("queue", queue.queueName)
            ).record(timeProxy.currentEpochMillis() - queue.beginLoopTimestampMillis, TimeUnit.MILLISECONDS)
        }
    }

    private suspend fun runMatchmakingOnQueue(queue: MatchableQueue) {
        admitNewMatchablesAndMatchmakingGameInstances(queue)

        mutex.withLock {
            queue.cleanAndResetAnchor()
        }

        while (queue.hasMoreAnchors()) {
            mutex.withLock {
                if (!queue.hasMoreAnchors()) {
                    return
                }
                val anchor = queue.getNextAnchor()
                val matchableIterator = queue.queue.iterator()
                Inner@ while (matchableIterator.hasNext()) {
                    if (runMatchmakingOnMatchables(queue, anchor, matchableIterator)) {
                        queue.cleanQueue()
                        break@Inner
                    }
                    if (!matchableIterator.hasNext()) {
                        if (doEndOfLoop(queue, anchor)) {
                            queue.cleanQueue()
                        }
                    }
                }
            }
            admitNewMatchablesAndMatchmakingGameInstances(queue)
        }
    }

    private suspend fun admitNewMatchablesAndMatchmakingGameInstances(queue: MatchableQueue) {
        queue.getNewMatchables(timeProxy, metricsManager).forEach {
            addMatchableToQueue(queue.queueKey, it)
        }

        queue.getNewGameInstances(timeProxy, metricsManager).forEach {
            addMatchmakingGameInstanceToQueue(queue.queueKey, it)
        }
    }

    private suspend fun runMatchmakingOnMatchables(
        queue: MatchableQueue,
        anchor: MatchableImpl,
        matchableIterator: MutableIterator<MatchableQueueMember>,
    ): Boolean {
        val matchable = matchableIterator.next()
        if (anchor == matchable) {
            return false
        }

        when (matchable) {
            is MatchableImpl -> {
                return doMatchParties(queue, anchor, matchable, matchableIterator)
            }

            is MatchmakingGameInstanceImpl -> {
                return doMatchPartiesWithGame(queue, matchable, anchor, matchableIterator)
            }

            else -> {
                throw PragmaException(
                    PragmaError.MatchmakingService_InternalError,
                    "Unhandled Matchable type ${matchable::class} from queue with queue key [${queue.queueKey} (${queue.queueName})]."
                )
            }
        }
    }

    private suspend fun doInitialize(queue: MatchableQueue, matchable: MatchableImpl): Boolean {
        val queueKey = queue.queueKey

        val newGame = try {
            matchmakingPlugin.initialize(queueKey, matchable)
        } catch (e: Exception) {
            logger.error(
                "Exception thrown during matchmakingPlugin.initialize. Queue: `$queueKey` (${queue.queueName}) Party: `${matchable.parties.map { it.partyId }}`",
                e
            )
            throw e
        }

        processNewGameInstance(queueKey, matchable.parties, newGame) {
            metricsManager.metricsCounter(
                MatchmakingService.Metrics.SKIP_QUEUE_KEY,
                Pair("queue", queue.queueName),
                Pair("gameServerVersion", queueKey.gameServerVersion),
                Pair("gameServerZone", it.gameServerZone)
            ).increment()
        }

        return newGame?.players?.isNotEmpty() ?: false
    }

    private suspend fun doMatchParties(
        queue: MatchableQueue,
        anchor: MatchableImpl,
        matchable: MatchableImpl,
        matchableIterator: MutableIterator<MatchableQueueMember>,
    ): Boolean {
        val newGame = try {
            matchmakingPlugin.matchParties(queue.queueKey, anchor, matchable)
        } catch (e: Exception) {
            handleMatchPartiesPluginException(queue, anchor, matchable, e)
            return true
        }

        processNewGameInstance(queue.queueKey, anchor.parties + matchable.parties, newGame) { validNewGame ->
            val partyIds = validNewGame.players.map { it.partyId }.toSet()
            anchor.removeParties(partyIds)
            matchable.removeParties(partyIds)
        }

        putPlayersIntoCache(anchor)
        putPlayersIntoCache(matchable)

        if (matchable.parties.isEmpty()) {
            matchableIterator.remove()
        }

        anchor.shouldRemoveFromQueue = anchor.players.isEmpty()
        return anchor.players.isEmpty()
    }

    private suspend fun doMatchPartiesWithGame(
        queue: MatchableQueue,
        gameInstance: MatchmakingGameInstanceImpl,
        anchor: MatchableImpl,
        matchableIterator: MutableIterator<MatchableQueueMember>,
    ): Boolean {
        if (gameInstance.shouldPauseProcessing) {
            return false
        }

        val gameUpdate = try {
            matchmakingPlugin.matchPartiesWithGame(queue.queueKey, anchor, gameInstance)
        } catch (e: Exception) {
            handleMatchPartiesPluginException(queue, anchor, gameInstance, e)
            return true
        }

        if (gameUpdate != null) {
            if (gameUpdate.isProcessable()) {
                val partiesAddedToGame = anchor.parties.filter { gameUpdate.players.groupBy { p -> p.partyId }.containsKey(it.partyId) }

                // mark game instance as waiting on an update
                gameInstance.setPendingPlayers(gameUpdate.players.map { it.playerId })

                anchor.removeParties(partiesAddedToGame.map { it.partyId }.toSet())
                removePlayersFromCache(gameUpdate.players)

                if (!gameUpdate.markedToContinueMatchmaking) {
                    matchableIterator.remove()
                    removeMatchmakingGameInstance(gameInstance)
                }

                joinGameInstanceChannel.send(
                    JoinGame.fromGameInstanceUpdate(
                        gameInstance,
                        gameUpdate,
                        queue.queueKey.extMatchmakingKey,
                        partiesAddedToGame.associateBy({ it.partyId }, { it.startedQueueTimestampMillis }),
                        queue.queueKey.gameServerVersion,
                    )
                )
            } else {
                if (logger.isDebugEnabled) {
                    logger.debug("Empty GameInstanceUpdate received from matchmakingPlugin.matchPartiesWithGame. Ignoring.")
                }
            }
        }

        anchor.shouldRemoveFromQueue = anchor.parties.isEmpty()
        return anchor.shouldRemoveFromQueue
    }

    private suspend fun doEndOfLoop(queue: MatchableQueue, anchor: MatchableImpl): Boolean {
        val newGame = try {
            matchmakingPlugin.endOfLoop(queue.queueKey, anchor)
        } catch (e: Exception) {
            handleEndOfLoopPluginException(queue, anchor, e)
            return true
        }

        processNewGameInstance(queue.queueKey, anchor.parties, newGame) { validNewGame ->
            anchor.removeParties(validNewGame.players.map { it.partyId }.toSet())
        }

        anchor.shouldRemoveFromQueue = anchor.parties.isEmpty()
        return anchor.shouldRemoveFromQueue
    }

    private suspend fun processNewGameInstance(
        queueKey: MatchmakingQueueKey,
        potentialParties: List<MatchmakingPartyImpl>,
        newGame: NewGameInstance?,
        postProcessBlock: (NewGameInstance) -> Unit,
    ) {
        if (newGame != null) {
            if (newGame.isProcessable()) {
                val partiesAddedToGame = potentialParties.filter { newGame.players.groupBy { p -> p.partyId }.containsKey(it.partyId) }
                val startedQueueTimestampByParty = partiesAddedToGame.associateBy({ it.partyId }, { it.startedQueueTimestampMillis })
                postProcessBlock(newGame)
                removePlayersFromCache(partiesAddedToGame.flatMap { it.players })
                newGameInstanceChannel.send(NewGame.fromNewGameInstance(queueKey, newGame, startedQueueTimestampByParty))
            } else {
                if (logger.isDebugEnabled) {
                    logger.debug("Empty NewGameInstance received from matchmakingPlugin.matchParties. Ignoring.")
                }
            }
        }
    }

    private suspend fun handleMatchPartiesPluginException(
        queue: MatchableQueue,
        anchor: MatchableImpl,
        matchable: MatchableQueueMember,
        error: Exception,
    ) {
        logger.error(
            "Plugin exception during matchmaking, removing involved matchables from queue `${queue.queueKey}` (${queue.queueName}).",
            error
        )

        matchReleaser.releaseMatchableFromMatchmakingDueToError(anchor)
        anchor.shouldRemoveFromQueue = true
        val removedPlayers = removePlayersFromCache(anchor.players)
        notifyPlayersFailedMatchmaking(removedPlayers, PluginError)

        matchReleaser.releaseMatchableFromMatchmakingDueToError(matchable)
        matchable.shouldRemoveFromQueue = true
        if (matchable is MatchableImpl) {
            val removedMatchablePlayers = removePlayersFromCache(matchable.players)
            notifyPlayersFailedMatchmaking(removedMatchablePlayers, PluginError)
        }
        if (matchable is MatchmakingGameInstanceImpl) {
            matchmakingGameInstancesById.remove(matchable.id)
        }
    }

    private suspend fun handleEndOfLoopPluginException(
        queue: MatchableQueue,
        anchor: MatchableImpl,
        error: Exception,
    ) {
        logger.error(
            "Exception thrown from matchmakingPlugin.endOfLoop, removing parties [${anchor.parties.map { it.partyId }}] from queue [${queue.queueKey} (${queue.queueName})].",
            error
        )

        matchReleaser.releaseMatchableFromMatchmakingDueToError(anchor)
        anchor.shouldRemoveFromQueue = true
        val removedPlayers = removePlayersFromCache(anchor.players)
        notifyPlayersFailedMatchmaking(removedPlayers, PluginError)
    }

    suspend fun updateMatchmakingGameInstance(
        gameInstanceId: GameInstanceId,
        ext: ExtMatchmakingGameInstance,
        playerExts: Map<PlayerId, ExtMatchmakingGamePlayer>,
        removedPlayerIds: List<PlayerId>,
        addedPlayers: List<MatchmakingGamePlayerImpl>,
    ) {
        mutex.withLock {
            val gameInstance = matchmakingGameInstancesById[gameInstanceId]
            gameInstance?.addPlayers(addedPlayers)
            gameInstance?.syncRemovedPlayers(removedPlayerIds)
            gameInstance?.updateExts(ext, playerExts)
        }
    }

    suspend fun unpauseGameInstance(gameInstanceId: GameInstanceId) {
        mutex.withLock {
            val gameInstance = matchmakingGameInstancesById[gameInstanceId]
            gameInstance?.clearPausedPlayers()
        }
    }

    fun tryRemoveMatchmakingGameInstanceFromQueue(gameInstanceId: GameInstanceId) {
        service.fireAndForget("MatchmakingQueues.tryRemoveMatchmakingGameInstanceFromQueue") {
            mutex.withLock {
                val gameInstance = matchmakingGameInstancesById[gameInstanceId]
                if (gameInstance != null) {
                    gameInstance.shouldRemoveFromQueue = true
                    removeMatchmakingGameInstance(gameInstance)
                }
            }
        }
    }

    private suspend fun removeMatchmakingGameInstance(gameInstance: MatchmakingGameInstanceImpl) {
        matchmakingGameInstancesById.remove(gameInstance.id)
        removeMatchmakingGameInstanceChannel.send(gameInstance)
    }

    suspend fun removePartyFromMatchableWithPlayer(playerId: PlayerId, outcome: MatchmakingOutcome): MatchmakingPartyImpl? {
        queues.forEach {
            admitNewMatchablesAndMatchmakingGameInstances(it.value)
        }
        mutex.withLock {
            val matchable = matchablesByPlayerId[playerId]
            if (matchable != null) {
                val partyToRemove = matchable.findPartyWithPlayer(playerId) as? MatchmakingPartyImpl
                if (partyToRemove != null) {
                    matchable.removeParty(partyToRemove.partyId)

                    if (matchable.parties.isEmpty()) {
                        matchable.shouldRemoveFromQueue = true
                    }

                    val playersToNotify = removePlayersFromCache(partyToRemove.players)
                    notifyPlayersLeftMatchmaking(playersToNotify, outcome, playerId)

                    return partyToRemove
                }
            }
            return null
        }
    }

    suspend fun removeQueuesWithGameServerVersion(gameServerVersion: String): List<Matchmaking.Party> {
        mutex.withLock {
            val queuesToRemove = queues.filter { it.key.gameServerVersion == gameServerVersion }
            val partiesToRemove = queuesToRemove.flatMap { q -> q.value.getPartiesFromAllMatchables() }
            val removedPlayers = removePlayersFromCache(partiesToRemove.flatMap { it.players })
            notifyPlayersFailedMatchmaking(removedPlayers, GameServerVersionNoLongerCompatible)
            queuesToRemove.forEach { queues.remove(it.key) }

            return partiesToRemove
        }
    }

    private fun putPlayersIntoCache(matchable: MatchableImpl) {
        matchable.players.forEach {
            matchablesByPlayerId[it.playerId] = matchable
        }
    }

    private fun removePlayersFromCache(players: Iterable<Matchmaking.Player>): List<PlayerId> {
        val playersRemoved = mutableListOf<PlayerId>()
        players.forEach {
            if (matchablesByPlayerId.remove(it.playerId) != null) {
                playersRemoved.add(it.playerId)
            }
        }

        return playersRemoved
    }

    private suspend fun notifyPlayersLeftMatchmaking(playersToNotify: List<PlayerId>, outcome: MatchmakingOutcome, playerId: PlayerId) {
        val reason = when (outcome) {
            Left -> LeftMatchmakingReason.PLAYER_LEFT_PARTY
            PlayerRequested -> LeftMatchmakingReason.PLAYER_REQUESTED
            else -> LEFT_MATCHMAKING_REASON_UNSPECIFIED
        }

        if (playersToNotify.isNotEmpty() && reason != LEFT_MATCHMAKING_REASON_UNSPECIFIED) {
            matchmakingNotificationClient.sendLeftMatchmaking(playersToNotify, reason, playerId)
        }
    }

    suspend fun notifyPlayersFailedMatchmaking(playersToNotify: List<PlayerId>, outcome: MatchmakingOutcome) {
        val reason = when (outcome) {
            GameServerVersionNoLongerCompatible -> MatchmakingFailureReason.GAME_SERVER_VERSION_NO_LONGER_COMPATIBLE
            PluginError -> MatchmakingFailureReason.MATCHMAKING_PLUGIN_EXCEPTION
            FailedSendingToGameInstance -> ERROR_SENDING_PLAYERS_TO_GAME_INSTANCE
            else -> MatchmakingFailureReason.UNSPECIFIED
        }

        if (playersToNotify.isNotEmpty() && reason != MatchmakingFailureReason.UNSPECIFIED) {
            matchmakingNotificationClient.sendMatchmakingFailed(playersToNotify, reason)
        }
    }

    fun getAllInfoV2(): List<QueueInfoV2> {
        val queueInfo = mutableListOf<QueueInfoV2>()
        queues.forEach {
            queueInfo.add(it.value.getQueueInfoV2())
        }
        return queueInfo
    }

    private fun registerMetricsGauge(
        key: MatchmakingQueueKey,
        queue: MatchableQueue,
        name: String,
        toDoubleFunction: ToDoubleFunction<Deque<MatchableQueueMember>>,
    ) {
        metricsManager.metricsGauge(
            name,
            queue.queue,
            toDoubleFunction,
            Pair("queue", queue.queueName),
            Pair("gameServerVersion", key.gameServerVersion),
            Pair("instanceId", serviceInstanceId.toInstanceIdString())
        )
    }
}
