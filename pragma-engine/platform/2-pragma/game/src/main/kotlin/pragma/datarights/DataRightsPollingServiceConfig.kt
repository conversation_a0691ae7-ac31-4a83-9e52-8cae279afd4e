package pragma.datarights

import pragma.config.ConfigBackendModeFactory
import pragma.config.ServiceConfig
import pragma.settings.BackendType

class DataRightsPollingServiceConfig private constructor(type: BackendType) : ServiceConfig<DataRightsPollingService>(type) {
    override val description = ""

    var enabled by types.boolean("Enables the DataRightsPollingService for collecting game data for data rights requests")
    var checkRequestsFrequencyInMinutes by types.int(
        "How frequently the DataRightsPollingService should check for new data rights game requests.",
        min = 1
    )

    init {
        enabled = false
        checkRequestsFrequencyInMinutes = 10
    }

    companion object : ConfigBackendModeFactory<DataRightsPollingServiceConfig> {
        override fun getFor(type: BackendType): DataRightsPollingServiceConfig {
            return DataRightsPollingServiceConfig(type)
        }
    }
}
