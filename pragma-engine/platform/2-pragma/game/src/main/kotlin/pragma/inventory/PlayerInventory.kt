package pragma.inventory

import pragma.PragmaError
import pragma.PragmaException
import pragma.utils.CatalogId
import pragma.utils.InstanceId
import pragma.utils.PlayerId

interface PlayerInventorySnapshot {
    fun getInstancedEntry(instanceId: InstanceId): InstancedItem
    fun getStackableEntryAmount(instanceId: InstanceId): Long
    fun getStackableEntries(catalogId: CatalogId): List<StackableItem>
    fun toInventoryTransactionData(): InventoryTransactionResult
    fun toInventoryData(): InventoryData
    fun verifyRequirements(constraints: PurchaseConstraints)
    fun hasFulfillment(fulfillmentId: String): Boolean
    fun getPlayerId(): PlayerId

    fun copyInstanced(): Map<InstanceId, InventoryItem>
    fun copyStackables(): Map<InstanceId, InventoryItem>
    fun toPendingInventory(transaction: InventoryTransaction): InventoryData
}

data class PlayerInventory(
    private val playerId: PlayerId,
    private val stackables: MutableMap<InstanceId, StackableItem> = mutableMapOf(),
    private val instanced: MutableMap<InstanceId, InstancedItem> = mutableMapOf(),
    private val fulfillments: MutableSet<FulfillmentEntry> = mutableSetOf(),
): PlayerInventorySnapshot {
    private var version = 0L

    override fun getPlayerId(): PlayerId = playerId

    override fun getInstancedEntry(instanceId: InstanceId): InstancedItem {
        return instanced[instanceId] ?: throw PragmaException(
            PragmaError.InventoryService_InstanceNotFound,
            "Entry for instanced item $instanceId not found."
        )
    }

    override fun getStackableEntryAmount(instanceId: InstanceId): Long {
        return stackables[instanceId]?.amount ?: 0
    }

    override fun getStackableEntries(catalogId: CatalogId): List<StackableItem> {
        return stackables.values.filter { stackableResult ->
            catalogId == stackableResult.catalogId
        }
    }

    override fun hasFulfillment(fulfillmentId: String): Boolean = fulfillments.contains(FulfillmentEntry(fulfillmentId))

    // TODO - rename emptyInventoryTransaction
    override fun toInventoryTransactionData(): InventoryTransactionResult = buildInventoryTransactionResult(InventoryGameTransactionBuilder().build())

    override fun toInventoryData(): InventoryData = InventoryData(
        stackables = stackables.values.toList(),
        instanced = instanced.values.toList(),
        version = version,
        playerId = playerId
    )

    private fun applyTransaction(inventoryTransaction: InventoryTransaction) {
        setStackable(inventoryTransaction.stackablesToUpdate)
        removeStackables(inventoryTransaction.stackablesToDelete)
        setInstanced(inventoryTransaction.instancedEntriesToUpdate)
        removeInstanced(inventoryTransaction.instancedEntriesToDelete)
        addFulfillments(inventoryTransaction.fulfillmentEntriesToAdd)
        incrementVersion()
    }

    fun applyTransactionAndBuildTransactionResult(inventoryTransaction: InventoryTransaction): InventoryTransactionResult {
        applyTransaction(inventoryTransaction)
        return buildInventoryTransactionResult(inventoryTransaction)
    }

    private fun addFulfillments(fulfillmentEntriesToAdd: List<FulfillmentEntry>) {
        fulfillmentEntriesToAdd.forEach { fulfillments.add(it) }
    }

    private fun incrementVersion() = version++
    private fun getPreviousVersion() = getVersion() - 1
    private fun getVersion() = version

    override fun verifyRequirements(constraints: PurchaseConstraints) {
        if (!hasCatalogIds(constraints.purchaseRequirements.requiredCatalogIds)) {
            throw PragmaException(
                PragmaError.InventoryService_RequirementsNotMetError,
                "Missing catalog id from required catalog ids (${constraints.purchaseRequirements.requiredCatalogIds})."
            )
        }
    }

    private fun hasCatalogIds(requiredCatalogIds: Collection<CatalogId>): Boolean {
        return requiredCatalogIds.all { catalogId ->
            stackables.values.any { it.catalogId == catalogId } ||
            instanced.values.any { it.catalogId == catalogId }
        }
    }

    private fun buildInventoryTransactionResult(transaction: InventoryTransaction) =
        InventoryTransactionResult(toInventoryData(), transaction, getPreviousVersion())

    private fun setInstanced(instancedItems: Map<InstanceId, InstancedItem>) {
        instancedItems.forEach { (instanceId, instancedItem) ->
            instanced[instanceId] = instancedItem
        }
    }

    private fun removeInstanced(instancedEntriesToDelete: List<InventoryEntryId>) {
        instancedEntriesToDelete.forEach { itemToRemove -> instanced.remove(itemToRemove.instanceId) }
    }

    private fun setStackable(stackableItems: Map<InstanceId, StackableItem>) {
        stackableItems.values.forEach { stackables[it.instanceId] = it }
    }

    private fun removeStackables(stackablesToDelete: List<InventoryEntryId>) {
        stackablesToDelete.forEach { itemToRemove ->
            stackables.remove(itemToRemove.instanceId)
        }
    }

    override fun copyStackables(): MutableMap<InstanceId, InventoryItem> = LinkedHashMap(stackables)
    override fun copyInstanced(): MutableMap<InstanceId, InventoryItem> = LinkedHashMap(instanced)

    private fun copy(): PlayerInventory {
        return PlayerInventory(this.playerId, LinkedHashMap(stackables), LinkedHashMap(instanced), mutableSetOf())
    }

    override fun toPendingInventory(transaction: InventoryTransaction): InventoryData {
        val copy = copy()
        copy.applyTransaction(transaction)
        return  copy.toInventoryData()
    }
}
