package pragma.http

import io.ktor.client.plugins.*
import io.ktor.client.statement.*
import io.ktor.client.utils.*
import io.ktor.http.*
import io.ktor.http.content.*
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import pragma.HttpClientTestFactory.httpRequestHeaders
import pragma.HttpClientTestFactory.httpRequest
import pragma.PragmaCoreTestFactory
import pragma.ProtoTestFactory.externalRequest
import pragma.ProtoTestFactory.externalRequestJson
import pragma.metrics.TimerProxy
import pragma.metrics.TimerProxyFactory
import pragma.tags
import pragma.utils.MetricUtils
import pragma.utils.PragmaSerializationUtils.toJsonString
import pragma.utils.coAssertThrows

@ExperimentalCoroutinesApi
internal class TimedHttpClientTest {
    private val clientName = PragmaCoreTestFactory.anyString()
    private val timerFactory = mockk<TimerProxyFactory>(relaxUnitFun = true) { every { create() } returns timer }
    private val timer = mockk<TimerProxy>(relaxUnitFun = true)
    private val client = mockk<HttpClient>()

    private val responseStatusCode = HttpStatusCode.MultipleChoices
    private val response = mockk<HttpResponse>(relaxed = true) {
        every { status } returns responseStatusCode
    }

    private val testObj: TimedHttpClient = TimedHttpClient(client, clientName, timerFactory)

    @BeforeEach
    fun setup() {
        every { timerFactory.create() } returns timer
    }

    @Nested
    inner class CapturesTimers {
        private val request = httpRequest(1)

        @Test
        fun `captures a timer`() = runTest {
            coEvery { client.issueRequest(request) } returns response

            val result = testObj.issueRequest(request)

            assertEquals(response, result)
            validateTimer(request.httpMethod, responseStatusCode)
        }

        @Test
        fun `if ktor throws an exception, we still close our timer`() = runTest {
            coEvery { client.issueRequest(request) } throws RedirectResponseException(response, "boom")

            coAssertThrows(RedirectResponseException::class) {
                testObj.issueRequest(request)
            }
            validateTimer(request.httpMethod, responseStatusCode)
        }
    }

    @Test
    fun get() = runTest {
        val expectedRequest = httpRequest(1, HttpMethod.Get, body = EmptyContent)
        coEvery { client.issueRequest(expectedRequest) } returns response

        val result = testObj.get(expectedRequest.uri, expectedRequest.auth, expectedRequest.expectSuccess, expectedRequest.headers, expectedRequest.parameters, *expectedRequest.tags.toTypedArray())

        assertEquals(response, result)
    }

    @Test
    fun post() = runTest {
        val request = httpRequest(1, HttpMethod.Post)
        coEvery { client.issueRequest(request) } returns response

        val result = testObj.post(request.uri, request.body, request.auth, request.expectSuccess, request.headers, request.parameters, *request.tags.toTypedArray())

        assertEquals(response, result)
    }

    @Test
    fun put() = runTest {
        val request = httpRequest(1, HttpMethod.Put)
        coEvery { client.issueRequest(request) } returns response

        val result = testObj.put(request.uri, request.body, request.auth, request.expectSuccess, request.headers, request.parameters, *request.tags.toTypedArray())

        assertEquals(response, result)
    }

    @Test
    fun patch() = runTest {
        val request = httpRequest(1, HttpMethod.Patch)
        coEvery { client.issueRequest(request) } returns response

        val result = testObj.patch(request.uri, request.body, request.auth, request.expectSuccess, request.headers, request.parameters, *request.tags.toTypedArray())

        assertEquals(response, result)
    }

    @Test
    fun delete() = runTest {
        val request = httpRequest(1, HttpMethod.Delete)
        coEvery { client.issueRequest(request) } returns response

        val result = testObj.delete(request.uri, request.body, request.auth, request.expectSuccess, request.headers, request.parameters, *request.tags.toTypedArray())

        assertEquals(response, result)
    }

    @Test
    fun postProtoWithHeaders() = runTest {
        val expectedRequest =
            httpRequest(
                1,
                HttpMethod.Post,
                body = TextContent(toJsonString(externalRequestJson(1).payload), contentType = ContentType.Application.Json),
                headers = httpRequestHeaders(1).apply {
                    put(HttpHeaders.Accept, ContentType.Application.Json.toString())
                })
        coEvery { client.issueRequest(match {
            it.uri == expectedRequest.uri &&
                it.httpMethod == HttpMethod.Post &&
                it.body.toString() == TextContent(toJsonString(externalRequestJson(1)), contentType = ContentType.Application.Json).toString() &&
                it.headers == expectedRequest.headers &&
                it.parameters == expectedRequest.parameters &&
                it.auth == expectedRequest.auth &&
                it.expectSuccess == expectedRequest.expectSuccess &&
                it.tags == expectedRequest.tags
        }) } returns response

        val result =
            testObj.postProtoWithHeaders(
                expectedRequest.uri,
                externalRequestJson(1),
                expectedRequest.auth,
                expectedRequest.expectSuccess,
                httpRequestHeaders(1),
                expectedRequest.parameters,
                *expectedRequest.tags.toTypedArray()
            )

        assertEquals(response, result)
    }

    private fun validateTimer(method: HttpMethod, status: HttpStatusCode) {
        val convertedTags = MetricUtils.convertToTags("method", method.value, "result", status.value.toString(), *tags(1).toTypedArray())
        verify {
            timer.start()
            timer.stop(clientName, convertedTags)
        }
    }
}
