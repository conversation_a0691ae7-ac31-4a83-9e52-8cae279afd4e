package pragma.services

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.LoggerContext
import com.google.protobuf.Struct
import io.mockk.every
import io.mockk.mockk
import java.io.File
import java.nio.file.Paths
import kotlin.io.path.createTempDirectory
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Isolated
import pragma.OperatorSession
import pragma.PragmaCoreTestFactory
import pragma.SessionTestFactory
import pragma.config.LogLevel
import pragma.config.PragmaConfig
import pragma.logging.LoggingManager
import pragma.logging.PragmaLoggerFactory
import pragma.settings.BackendMode
import pragma.settings.BackendType
import pragma.settings.PragmaNodeSettings
import pragma.systemreporter.SystemReporterNodeService
import pragma.systemreporter.SystemReporterNodeServicePB
import pragma.utils.PragmaSerializationUtils.parser

@ExperimentalCoroutinesApi
@Isolated
internal class SystemReporterNodeServiceTest {
    private val pragmaNode = PragmaCoreTestFactory.pragmaNode()
    private val testObj = SystemReporterNodeService(pragmaNode)

    private val pragmaConfig = PragmaConfig.getFor(PragmaCoreTestFactory.anyBackendType())
    private val operatorSession = SessionTestFactory.operatorSession()

    @Test
    fun getCoroutinesState() = runTest {
        // This call blows up / throws an error if DebugProbes.install isn't called.
        // See https://kotlin.github.io/kotlinx.coroutines/kotlinx-coroutines-debug/kotlinx.coroutines.debug/-debug-probes/index.html
        testObj.getCoroutinesStateV1(
            operatorSession,
            SystemReporterNodeServicePB.GetCoroutinesStateV1Request.getDefaultInstance()
        )
    }

    @Test
    fun getConfig() = runTest {
        every { pragmaNode.pragmaConfig } returns pragmaConfig

        val response = testObj.getConfigV1(
            OperatorSession.getDefaultInstance(),
            SystemReporterNodeServicePB.GetConfigV1Request.getDefaultInstance()
        )

        val structBuilder = Struct.newBuilder()
        val objectNode = pragmaConfig.toObjectNode()
        objectNode.put("backendType", pragmaNode.backendType.toString())
        parser.merge(objectNode.toString(), structBuilder)

        assertEquals(
            structBuilder.build().getFieldsOrThrow("backendType"),
            response.config.getFieldsOrThrow("backendType")
        )
        assertEquals(structBuilder.build(), response.config)
    }

    @Test
    fun getConfigShowAnnotated() = runTest {
        every { pragmaNode.pragmaConfig } returns pragmaConfig

        val response = testObj.getConfigV1(
            OperatorSession.getDefaultInstance(),
            SystemReporterNodeServicePB.GetConfigV1Request.newBuilder()
                .setShowAnnotatedConfig(true)
                .build()
        )

        val structBuilder = Struct.newBuilder()
        val objectNode = pragmaConfig.toAnnotatedObjectNode()
        objectNode.put("backendType", pragmaNode.backendType.toString())
        parser.merge(objectNode.toString(), structBuilder)

        assertEquals(
            structBuilder.build().getFieldsOrThrow("backendType"),
            response.config.getFieldsOrThrow("backendType")
        )
        assertEquals(structBuilder.build(), response.config)
    }

    @Test
    fun `getLogs - file logging disabled`() = runTest {
        assertEquals(
            "PragmaConfig.core.logging.FileLoggerEnabled = false, no logs found",
            getLogLines(SystemReporterNodeService.DEFAULT_LINES)[0]
        )
    }

    @Test
    fun `getLogs - happy path`() = runTest {
        // This test runs from platform/2-pragma/core, just reading an arbitrary big file
        val filepath = "${System.getProperty("user.dir")}/pom.xml"
        every { pragmaNode.pragmaConfig.core.logging.fileLoggerEnabled } returns true
        every { pragmaNode.pragmaConfig.core.logging.fileLoggerCurrentFilename } returns filepath

        // Defaults when not unset
        assertEquals(
            getLogLines(SystemReporterNodeServicePB.GetLogsV1Request.getDefaultInstance().lines), getLogLines(
                SystemReporterNodeService.DEFAULT_LINES
            )
        )
        assertEquals(getLogLines(-1), getLogLines(SystemReporterNodeService.DEFAULT_LINES))

        val fileContents = File(filepath).readLines().toMutableList()

        // Weirdly the ReversedLinesFileReader doesn't read empty lines...
        fileContents.removeIf { line -> line == "" }

        val halfFileResponse = getLogLines(fileContents.size / 2)
        halfFileResponse.removeIf { line -> line == "" }

        for (i in 0 until halfFileResponse.size) {
            assertEquals(fileContents[fileContents.size - 1 - i], halfFileResponse[halfFileResponse.size - 1 - i])
        }

        listOf(fileContents.size, fileContents.size * 2).forEach {
            val result = getLogLines(it)
            result.removeIf { line -> line == "" }
            for (i in 0 until result.size) {
                assertEquals(fileContents[fileContents.size - 1 - i], result[result.size - 1 - i])
            }
        }
    }

    @Test
    fun getStartupSummaryV1() = runTest {
        val startupSummary =
            """
            This is
            a multi-line
            startup summary
            """.trimIndent()
        every { pragmaNode.getDetailedSummary() } returns startupSummary

        val response = testObj.getStartupSummaryV1(
            operatorSession,
            SystemReporterNodeServicePB.GetStartupSummaryV1Request.getDefaultInstance()
        )

        assertEquals(startupSummary.split("\n"), response.summaryList.toList())
    }

    private suspend fun getLogLines(it: Int): MutableList<String> = testObj.getLogsV1(
        operatorSession,
        SystemReporterNodeServicePB.GetLogsV1Request.newBuilder().setLines(it).build()
    ).logsList.toMutableList()
}
