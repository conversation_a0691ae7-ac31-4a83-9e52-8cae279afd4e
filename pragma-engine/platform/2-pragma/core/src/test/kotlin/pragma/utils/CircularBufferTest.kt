package pragma.utils

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class CircularBufferTest {
    @Test
    fun bufferSizeMustBePositive() {
        assertThrows<IllegalArgumentException> { CircularBuffer<Unit>(0) }
        assertThrows<IllegalArgumentException> { CircularBuffer<Unit>(-1) }
    }

    @Test
    fun testAddFirst() = CircularBuffer<Int>(32).let { buffer ->
        repeat(buffer.capacity) {
            buffer.addFirst(it)
            assertEquals(it, buffer.first())
            assertEquals(0, buffer.last())
        }
        repeat(buffer.capacity) {
            val newVal = it + buffer.capacity
            buffer.addFirst(newVal)
            assertEquals(newVal, buffer.first())
            assertEquals(it + 1, buffer.last())
        }
    }

    @Test
    fun testPopFirst() = CircularBuffer<Int>(32).let { buffer ->
        repeat(buffer.capacity) {
            buffer.addFirst(it)
        }
        repeat(buffer.capacity) {
            assertEquals(0, buffer.last())
            assertEquals(buffer.capacity - 1 - it, buffer.popFirst())
        }
    }

    @Test
    fun testAddLast() = CircularBuffer<Int>(32).let { buffer ->
        repeat(buffer.capacity) {
            buffer.addLast(it)
            assertEquals(it, buffer.last())
            assertEquals(0, buffer.first())
        }
        repeat(buffer.capacity) {
            val newVal = it + buffer.capacity
            buffer.addLast(newVal)
            assertEquals(newVal, buffer.last())
            assertEquals(it + 1, buffer.first())
        }
    }

    @Test
    fun testPopLast() = CircularBuffer<Int>(32).let { buffer ->
        repeat(buffer.capacity) {
            buffer.addLast(it)
        }
        repeat(buffer.capacity) {
            assertEquals(0, buffer.first())
            assertEquals(buffer.capacity - 1 - it, buffer.popLast())
        }
    }

    @Test
    fun getOnEmptyThrows(): Unit = CircularBuffer<Unit>(1).let { buffer ->
        assertThrows<IndexOutOfBoundsException> { buffer[0] }
    }

    @Test
    fun firstOnEmptyThrows(): Unit = CircularBuffer<Unit>(1).let { buffer ->
        assertThrows<NoSuchElementException> { buffer.first() }
    }

    @Test
    fun lastOnEmptyThrows(): Unit = CircularBuffer<Unit>(1).let { buffer ->
        assertThrows<NoSuchElementException> { buffer.last() }
    }

    @Test
    fun testIsFull() = CircularBuffer<Unit>(1).let { buffer ->
        assertFalse(buffer.isFull())
        buffer.addFirst(Unit)
        assertTrue(buffer.isFull())
        buffer.popLast()
        assertFalse(buffer.isFull())
    }

    @Test
    fun testIsEmpty() = CircularBuffer<Unit>(1).let { buffer ->
        assertTrue(buffer.isEmpty())
        buffer.addFirst(Unit)
        assertFalse(buffer.isEmpty())
        buffer.popLast()
        assertTrue(buffer.isEmpty())
    }

    @Test
    fun testSize() = CircularBuffer<Unit>(25).let { buffer ->
        assertEquals(0, buffer.size)
        repeat(buffer.capacity) {
            buffer.addFirst(Unit)
            assertEquals(it + 1, buffer.size)
        }
    }

    @Test
    fun testToListSimple() = CircularBuffer<Int>(1).let { buffer ->
        assertEquals(emptyList<Int>(), buffer.toList())
        buffer.addFirst(2)
        assertEquals(listOf(2), buffer.toList())

        buffer.addLast(4)
        assertEquals(listOf(4), buffer.toList())
    }

    @Test
    fun testToListComplex() = CircularBuffer<Int>(2).let { buffer ->
        buffer.addFirst(2)
        assertEquals(listOf(2), buffer.toList())

        buffer.addLast(4)
        assertEquals(listOf(2, 4), buffer.toList())

        buffer.addLast(8)
        assertEquals(listOf(4, 8), buffer.toList())

        buffer.addLast(16)
        assertEquals(listOf(8, 16), buffer.toList())

        buffer.addFirst(32)
        assertEquals(listOf(32, 8), buffer.toList())

        buffer.addFirst(64)
        assertEquals(listOf(64, 32), buffer.toList())
    }

    @Test
    fun testComplexUsage() = CircularBuffer<Int>(10).let { buffer ->
        (1..buffer.capacity).forEach { buffer.addFirst(it) }
        repeat(buffer.capacity) { assertEquals(buffer.capacity - it, buffer[it]) }

        buffer.popFirst()
        assertEquals(buffer.capacity - 1, buffer.first())
        assertEquals(1, buffer.last())

        buffer.popFirst()
        assertEquals(buffer.capacity - 2, buffer.first())
        assertEquals(1, buffer.last())

        buffer.popLast()
        assertEquals(2, buffer.last())
        assertEquals(buffer.capacity - 2, buffer.first())

        repeat(4) {
            buffer.addFirst(4096 + it)
        }
        assertEquals(listOf(4099, 4098, 4097, 4096, 8, 7, 6, 5, 4, 3), buffer.toList())
    }
}