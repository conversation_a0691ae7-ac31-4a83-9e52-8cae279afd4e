package pragma.client

import com.fasterxml.jackson.module.kotlin.readValue
import com.google.protobuf.Struct
import io.ktor.client.plugins.RedirectResponseException
import io.ktor.client.plugins.websocket.DefaultClientWebSocketSession
import io.ktor.client.statement.HttpResponse
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import io.ktor.utils.io.CancellationException
import io.ktor.utils.io.core.toByteArray
import io.ktor.websocket.Frame
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import pragma.ExternalNotification
import pragma.ExternalNotificationJson
import pragma.ExternalRequestJson
import pragma.ExternalResponse
import pragma.InventoryProtoTestFactory.getLoginDataServiceV2Request
import pragma.InventoryProtoTestFactory.getLoginDataServiceV2Response
import pragma.PragmaCoreTestFactory.algorithm
import pragma.PragmaCoreTestFactory.anyInt
import pragma.PragmaCoreTestFactory.pragmaGameAuthenticationToken
import pragma.PragmaOptions
import pragma.PragmaResult.Failure
import pragma.PragmaResult.Success
import pragma.ProtoTestFactory.externalMessage
import pragma.ProtoTestFactory.externalMessageJson
import pragma.ProtoTestFactory.externalRequest
import pragma.ProtoTestFactory.externalResponse
import pragma.ProtoTestFactory.externalResponseJson
import pragma.Response
import pragma.ServiceError
import pragma.ServiceErrorFacade
import pragma.SessionTerminationReason
import pragma.auth.PragmaJwtSigner
import pragma.client.BackendConnection.ErrorHandler
import pragma.client.BackendConnection.ResponseHandler
import pragma.client.TransportMode.JSON
import pragma.client.TransportMode.OCTET_STREAM
import pragma.inventory.InventoryRpc.GetLoginDataServiceV2Request
import pragma.inventory.InventoryRpc.GetLoginDataServiceV2Response
import pragma.jobs.BackgroundManager
import pragma.rpcs.JumpData
import pragma.session.PlayerSessionRpc.SessionTerminatedForServiceV1Notification
import pragma.utils.PragmaSerializationUtils
import pragma.utils.PragmaSerializationUtils.jsonMapper
import pragma.utils.protoShortName

@OptIn(ExperimentalCoroutinesApi::class)
class BackendConnectionTest {
    private val firstRequestId = 0
    private val transportModeBinary = OCTET_STREAM
    private val transportModeJson = JSON
    private val pragmaJwtSigner = PragmaJwtSigner()
    private val builtToken =
        pragmaJwtSigner.signGameAuthenticationJwt(algorithm, pragmaGameAuthenticationToken(1, PragmaOptions.PragmaSessionType.PLAYER))
    private val backendUrl = Url("http://localhost/TestUrl")
    private val serverTypesData = mockk<ServerTypesData>()

    private val receiveChannel = Channel<Frame>()

    private val protocol = "ws"

    private lateinit var outerTestObj: BackendConnection
    private lateinit var webSocketBinary: DefaultClientWebSocketSession
    private lateinit var webSocketJson: DefaultClientWebSocketSession
    private lateinit var websocketFactory: WebsocketFactory
    private val connectionJobBinary = SupervisorJob()
    private val connectionJobJson = SupervisorJob()

    private val notificationHandler = mockk<NotificationHandler>(relaxUnitFun = true)

    private fun runWithTestObj(block: suspend TestScope.() -> Unit) = runTest {
        val connectionScopeBinary =
            CoroutineScope(<EMAIL> + connectionJobBinary + CoroutineName("testConnectionScopeBinary"))
        val connectionScopeJson =
            CoroutineScope(<EMAIL> + connectionJobJson + CoroutineName("testConnectionScopeJson"))

        webSocketBinary = mockk<DefaultClientWebSocketSession> {
            coEvery { incoming } returns receiveChannel
            coEvery { coroutineContext } returns connectionScopeBinary.coroutineContext
        }

        webSocketJson = mockk<DefaultClientWebSocketSession> {
            coEvery { incoming } returns receiveChannel
            coEvery { coroutineContext } returns connectionScopeJson.coroutineContext
        }
        websocketFactory = mockk<WebsocketFactory> {
            coEvery { createWebSocketSession(backendUrl, protocol, transportModeBinary, builtToken) } returns webSocketBinary
            coEvery { createWebSocketSession(backendUrl, protocol, transportModeJson, builtToken) } returns webSocketJson
        }
        outerTestObj = BackendConnection(backendUrl, transportModeBinary, builtToken, protocol, websocketFactory, serverTypesData, false,
                                         notificationHandler
        )

        advanceUntilIdle()

        block()

        advanceUntilIdle()

    }

    @Test
    fun closeDoesNotFailWhenNotRunning() = runWithTestObj {
        assertFalse(outerTestObj.isConnected())
        outerTestObj.stop()
        assertFalse(outerTestObj.isConnected())
    }

    fun isConnected() = runWithTestObj {
        assertFalse(outerTestObj.isConnected())
        outerTestObj.start()
        assertTrue(outerTestObj.isConnected())


        stopBackendConnectionAndWebSocket(this)
        assertFalse(outerTestObj.isConnected())
    }

    @Test
    fun handlesRedirect() = runWithTestObj {
        val redirectBackendUrl = Url("$backendUrl/?gateway=0")
        val redirectResponse = mockk<HttpResponse> {
            every { call } returns mockk(relaxed = true)
            every { status } returns HttpStatusCode.Found
            every { headers[HttpHeaders.Location] } returns redirectBackendUrl.toString()
        }
        coEvery {
            websocketFactory.createWebSocketSession(
                backendUrl,
                protocol,
                transportModeBinary,
                builtToken
            )
        } throws RedirectResponseException(redirectResponse, "")
        coEvery {
            websocketFactory.createWebSocketSession(
                redirectBackendUrl,
                protocol,
                transportModeBinary,
                builtToken
            )
        } returns webSocketBinary
        outerTestObj.start()
        assertTrue(outerTestObj.isConnected())
        stopBackendConnectionAndWebSocket(this)
    }

    @Test
    fun waitForNextNotificationWithTimeout() = runWithTestObj {
        coEvery {notificationHandler.waitFor(SessionTerminatedForServiceV1Notification::class, 123)} returns SessionTerminatedForServiceV1Notification.getDefaultInstance()
        outerTestObj.waitForNextNotificationWithTimeout(SessionTerminatedForServiceV1Notification::class, 123)
        coVerify {
            notificationHandler.waitFor(SessionTerminatedForServiceV1Notification::class, 123)
        }
    }

    @Test
    fun canClearAllNotificationsExceptForNotificationToPersist() = runWithTestObj {
        val notificationsToPersist = listOf("notificationA", "otherNotification")
        outerTestObj.clearAllNotificationChannelsExcept(notificationsToPersist)
        coVerify {notificationHandler.clearAllNotificationChannelsExcept(notificationsToPersist) }
    }

    private suspend fun stopBackendConnectionAndWebSocket(testScope: TestScope) {
        testScope.advanceUntilIdle()
        assertTrue(outerTestObj.isConnected())
        val stopJob = testScope.launch { outerTestObj.stop() }

        testScope.advanceUntilIdle()

        receiveChannel.close()
        connectionJobBinary.complete()
        connectionJobJson.complete()
        testScope.advanceUntilIdle()
        stopJob.join()

        assertFalse(outerTestObj.isConnected())
    }

    abstract inner class MakeRequestOrThrow(private val mode: TransportMode) {
        private val responseIntType = anyInt(50, 100)
        protected val responseType = JumpData.getIntForType(GetLoginDataServiceV2Response::class)
        private val getLoginDataServiceRequest = getLoginDataServiceV2Request(1)


        val protoShortName = "GetLoginDataServiceV2Request"
        protected val notification: SessionTerminatedForServiceV1Notification =
            SessionTerminatedForServiceV1Notification.newBuilder().setReason(
                SessionTerminationReason.USURPED
            ).build()
        protected val notificationIntType = anyInt(101, 150)
        private val backgroundManager = mockk<BackgroundManager>(relaxed = true)
        private val capturedBlocks = mutableListOf<suspend CoroutineScope.() -> Unit>()

        private lateinit var nestedTestObj: BackendConnection
        private lateinit var webSocketBinary: DefaultClientWebSocketSession
        private lateinit var webSocketJson: DefaultClientWebSocketSession

        private val shortProtoClassName = "tacoRequest"
        private fun runWithTestObj(block: suspend TestScope.() -> Unit) = runTest {
            val connectionScopeBinary =
                CoroutineScope(<EMAIL> + connectionJobBinary + CoroutineName("testConnectionScopeBinary"))
            val connectionScopeJson =
                CoroutineScope(<EMAIL> + connectionJobJson + CoroutineName("testConnectionScopeJson"))

            webSocketBinary = mockk<DefaultClientWebSocketSession> {
                coEvery { incoming } returns receiveChannel
                coEvery { coroutineContext } returns connectionScopeBinary.coroutineContext
            }

            webSocketJson = mockk<DefaultClientWebSocketSession> {
                coEvery { incoming } returns receiveChannel
                coEvery { coroutineContext } returns connectionScopeJson.coroutineContext
            }
            val websocketFactory = mockk<WebsocketFactory> {
                coEvery { createWebSocketSession(backendUrl, protocol, transportModeBinary, builtToken) } returns webSocketBinary
                coEvery { createWebSocketSession(backendUrl, protocol, transportModeJson, builtToken) } returns webSocketJson
            }
            nestedTestObj = BackendConnection(backendUrl, mode, builtToken, protocol, websocketFactory, serverTypesData, false)

            every { serverTypesData.getIntType(protoShortName(GetLoginDataServiceV2Response::class.java)) } returns responseType
            every { serverTypesData.getIntType(protoShortName(SessionTerminatedForServiceV1Notification::class.java)) } returns notificationIntType
            every { serverTypesData.getClassFromIntType(responseType) } returns GetLoginDataServiceV2Response::class.java
            every { serverTypesData.getClassFromIntType(notificationIntType) } returns SessionTerminatedForServiceV1Notification::class.java
            every { serverTypesData.getIntTypeAndProtoShortName(getLoginDataServiceRequest::class.java) } returns (responseIntType - 1 to shortProtoClassName)

            coEvery { webSocketBinary.send(any()) } returns Unit
            coEvery { webSocketJson.send(any()) } returns Unit

            nestedTestObj.start(backgroundManager)

            verify { backgroundManager.fireAndForget(any(), capture(capturedBlocks)) }
            assertEquals(1, capturedBlocks.size)
            val capturedJobs = mutableListOf<Job>()
            capturedBlocks.forEach { block ->
                val captured = launch {
                    block.invoke(this)
                }
                capturedJobs.add(captured)
            }
            advanceUntilIdle()

            block()

            advanceUntilIdle()

            val stopJob = launch { nestedTestObj.stop() }

            advanceUntilIdle()

            receiveChannel.close()

            connectionJobBinary.complete()
            connectionJobJson.complete()
            advanceUntilIdle()
            stopJob.join()
            capturedJobs.joinAll()
        }

        fun success() = runWithTestObj {
            do_success(firstRequestId, this)
        }


        private suspend fun do_success(successfulRequestId: Int, scope: TestScope) {
            val response = scope.async { nestedTestObj.makeRequestOrThrow(getLoginDataServiceRequest, GetLoginDataServiceV2Response::parseFrom) }
            val element = buildSuccessfulResponse(successfulRequestId)
            receiveChannel.send(element)
            val realResponse = response.await()
            assertEquals(getLoginDataServiceV2Response(1), realResponse)
            when (mode) {
                JSON -> {
                    val extRequestJson = PragmaSerializationUtils.toJsonString(
                        ExternalRequestJson.newBuilder()
                            .setRequestId(successfulRequestId)
                            .setType(shortProtoClassName)
                            .setPayload(jsonMapper.readValue<Struct>(PragmaSerializationUtils.toJsonString(getLoginDataServiceRequest)))
                            .build()
                    )
                    coVerify { webSocketJson.send(match { frame -> frame.fin && frame.data.contentEquals(extRequestJson.toByteArray()) }) }
                }

                OCTET_STREAM -> {
                    val extRequest = externalRequest(successfulRequestId, type = responseIntType - 1, payload = getLoginDataServiceRequest.toByteString())
                    val extRequestBytes = extRequest.toByteArray()

                    coVerify { webSocketBinary.send(match { frame -> frame.fin && frame.data.contentEquals(extRequestBytes) }) }
                }
            }
        }

        fun error() = runWithTestObj {
            var receivedError = false
            val requestJob = launch {
                try {
                    nestedTestObj.makeRequestOrThrow(getLoginDataServiceRequest, GetLoginDataServiceV2Response::parseFrom)
                } catch (e: IllegalStateException) {
                    receivedError = true
                }
            }
            val sendJob = launch {
                receiveChannel.send(buildErrorResponse(firstRequestId))
            }
            sendJob.join()
            requestJob.join()
            assertTrue(receivedError, "${java.time.LocalDateTime.now()}")
        }

        fun cancellationErrorLeavesConnectionIntact() = runWithTestObj {
            var request1Error : Exception? = null
            var request2Error : Exception? = null
            var scopeCaught = false

            try {
                coroutineScope {
                    launch {
                        try {
                            nestedTestObj.makeRequestOrThrow(getLoginDataServiceV2Request(firstRequestId), GetLoginDataServiceV2Response::parseFrom)
                        } catch (e: IllegalStateException) {
                            request1Error = e
                            throw ArithmeticException(e.message)
                        }
                    }
                    launch {
                        try {
                            val secondRequestId = 1
                            nestedTestObj.makeRequestOrThrow(getLoginDataServiceV2Request(secondRequestId), GetLoginDataServiceV2Response::parseFrom)
                        } catch (e: Exception) {
                            request2Error = e
                            throw ArithmeticException(e.message)
                        }
                    }
                    val sendJob = launch {
                        receiveChannel.send(buildErrorResponse(firstRequestId))
                    }
                    sendJob.join()

                }
            } catch (e: java.lang.ArithmeticException) {
                scopeCaught = true
            }
            assertTrue(scopeCaught)
            assertEquals(IllegalStateException::class, request1Error!!::class)
            assertTrue(request2Error is CancellationException)

            val thirdRequestId = 2
            do_success(thirdRequestId, this)
        }

        abstract fun buildSuccessfulResponse(correspondingRequestId : Int): Frame
        abstract fun buildErrorResponse(correspondingRequestId : Int): Frame
        abstract fun buildNotification(): Frame

        fun notification() = runWithTestObj {
            nestedTestObj.addNotificationChannelFor(SessionTerminatedForServiceV1Notification::class)
            val sendNotificationJob = launch {
                receiveChannel.send(buildNotification())
            }
            sendNotificationJob.join()
            advanceUntilIdle()

            val result = async {
                nestedTestObj.waitForNextNotificationWithTimeout(SessionTerminatedForServiceV1Notification::class, 1000)
            }

            assertEquals(SessionTerminationReason.USURPED, result.await().reason)
            nestedTestObj.removeNotificationChannelFor(SessionTerminatedForServiceV1Notification::class)
        }
    }

    inner class JsonMakeRequestOrThrow : MakeRequestOrThrow(JSON) {
        private fun buildFrame(data: ByteArray): Frame = Frame.Text(true, data)
        override fun buildSuccessfulResponse(correspondingRequestId: Int): Frame {
            val successfulPayload = externalResponseJson(
                correspondingRequestId,
                type = protoShortName(GetLoginDataServiceV2Response::class.java),
                payload = jsonMapper.readValue(jsonMapper.writeValueAsString(getLoginDataServiceV2Response(1))),
            )

            val successfulResponse = jsonMapper.writeValueAsString(externalMessageJson(1, response = successfulPayload)).toByteArray()

            return buildFrame(successfulResponse)
        }

        override fun buildErrorResponse(correspondingRequestId: Int): Frame {
            val errorPayload = ServiceErrorFacade.internalError(protoShortName, correspondingRequestId)
            val errorResponse = jsonMapper.writeValueAsString(externalMessageJson(1, serviceError = errorPayload)).toByteArray()
            return buildFrame(errorResponse)
        }

        override fun buildNotification(): Frame {
            val payload = ExternalNotificationJson.newBuilder()
                .setType(protoShortName(SessionTerminatedForServiceV1Notification::class.java))
                .setPayload(jsonMapper.readValue<Struct>(jsonMapper.writeValueAsString(notification)))
                .build()
            return buildFrame(jsonMapper.writeValueAsString(externalMessageJson(1, notification = payload)).toByteArray())
        }
    }

    inner class BinaryMakeRequestOrThrow : MakeRequestOrThrow(OCTET_STREAM) {
        private val externalNotification =
            ExternalNotification.newBuilder().setType(notificationIntType).setPayload(notification.toByteString()).build()
        private val notificationMessage = externalMessage(1, notification = externalNotification).toByteArray()

        private fun buildFrame(data: ByteArray): Frame = Frame.Binary(true, data)

        override fun buildSuccessfulResponse(correspondingRequestId : Int): Frame {
            val successfulPayload =
                externalResponse(correspondingRequestId, type = responseType, payload = getLoginDataServiceV2Response(1).toByteString())
            val successfulResponse = externalMessage(1, response = successfulPayload).toByteArray()

            return buildFrame(successfulResponse)
        }

        override fun buildErrorResponse(correspondingRequestId : Int): Frame {
            val errorPayload = ServiceErrorFacade.internalError(protoShortName, correspondingRequestId)
            val errorResponse = externalMessage(1, serviceError = errorPayload).toByteArray()
            return buildFrame(errorResponse)
        }

        override fun buildNotification(): Frame {
            return buildFrame(notificationMessage)
        }
    }
    @Nested
    inner class TransportModeTests {
        private val jsonVerifier = JsonMakeRequestOrThrow()
        private val binaryVerifier = BinaryMakeRequestOrThrow()

        @ParameterizedTest
        @EnumSource(value = TransportMode::class)
        fun success(transportMode: TransportMode) {
            when (transportMode) {
                JSON -> jsonVerifier.success()
                OCTET_STREAM -> binaryVerifier.success()
            }
        }

        @ParameterizedTest
        @EnumSource(value = TransportMode::class)
        fun error(transportMode: TransportMode) {
            when (transportMode) {
                JSON -> jsonVerifier.error()
                OCTET_STREAM -> binaryVerifier.error()
            }
        }

        @ParameterizedTest
        @EnumSource(value = TransportMode::class)
        fun cancellationErrorLeavesConnectionIntact(transportMode: TransportMode) {
            when (transportMode) {
                JSON -> jsonVerifier.cancellationErrorLeavesConnectionIntact()
                OCTET_STREAM -> binaryVerifier.cancellationErrorLeavesConnectionIntact()
            }
        }

        @ParameterizedTest
        @EnumSource(value = TransportMode::class)
        fun notification(transportMode: TransportMode) {
            when (transportMode) {
                JSON -> jsonVerifier.notification()
                OCTET_STREAM -> binaryVerifier.notification()
            }
        }
    }

    @Nested
    inner class MessageHandlerTests {
        private val requestId = anyInt()
        private val requestType = JumpData.getIntForType(GetLoginDataServiceV2Request::class)
        private val responseType = JumpData.getIntForType(GetLoginDataServiceV2Response::class)
        private val extRequest = externalRequest(requestId, type = requestType, payload = getLoginDataServiceV2Request(1).toByteString())
        private val loginDataServiceV2Response = getLoginDataServiceV2Response(1)
        private val successfulPayload = ExternalResponse.newBuilder()
            .setRequestId(extRequest.requestId)
            .setType(responseType)
            .setPayload(loginDataServiceV2Response.toByteString())
            .build()
        val protoShortName = "GetLoginDataServiceV2Request"
        private val errorPayload = ServiceErrorFacade.internalError(protoShortName, requestId)
        private val successfulResponse = externalMessage(1, response = successfulPayload)
        private val errorResponse = externalMessage(1, serviceError = errorPayload)

        @Test
        fun errorHandler() {
            val messageHandlerTestObj = ErrorHandler(errorResponse.serviceError)
            val result = messageHandlerTestObj.respond(GetLoginDataServiceV2Response::parseFrom)
            val expectedResponse = Failure<Response, ServiceError>(errorResponse.serviceError)
            @Suppress("AssertBetweenInconvertibleTypes")
            assertEquals(expectedResponse, result)
            assertEquals(requestId, messageHandlerTestObj.requestId)
        }

        @Test
        fun responseHandler() {
            val messageHandlerTestObj = ResponseHandler(successfulResponse.response, serverTypesData)

            every { serverTypesData.getClassFromIntType(responseType) } returns GetLoginDataServiceV2Response::class.java

            val result = messageHandlerTestObj.respond(GetLoginDataServiceV2Response::parseFrom)
            val expectedResponse = Success<GetLoginDataServiceV2Response, ServiceError>(loginDataServiceV2Response)
            assertEquals(expectedResponse, result)
            assertEquals(requestId, messageHandlerTestObj.requestId)
        }
    }
}
