package pragma.gateway

import io.ktor.client.plugins.HttpClientPlugin.*
import io.ktor.client.plugins.websocket.*
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.header
import io.ktor.http.HttpHeaders
import io.ktor.server.application.*
import io.ktor.server.auth.Authentication
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.testing.*
import io.ktor.server.websocket.DefaultWebSocketServerSession
import io.ktor.server.websocket.WebSockets
import io.ktor.websocket.*
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import java.util.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.ResourceLock
import pragma.PragmaCoreTestFactory
import pragma.metrics.MetricsNodeService
import pragma.services.NodeServicesContainer
import pragma.services.WebServerMetrics
import pragma.settings.BackendType
import pragma.utils.Resources

@ExperimentalCoroutinesApi
@ResourceLock(Resources.KTOR_TEST)
internal class BackendGatewayNodeServiceTest {
    private val pragmaNode = PragmaCoreTestFactory.pragmaNode()
    private val webServerMetrics = mockk<WebServerMetrics>()
    private val metricsNodeService = mockk<MetricsNodeService>(relaxed = true)
    private val nodeServiceContainer = mockk<NodeServicesContainer>().apply { every { get(MetricsNodeService::class)} returns metricsNodeService }
    private val testObj = BackendGatewayNodeService(pragmaNode, mockk(), webServerMetrics).apply { nodeServicesContainer = nodeServiceContainer }

    @Test
    fun `given config expects same values returned`() {
        val config = BackendGatewayConfig.getFor(BackendType.GAME)
        config.port = 42

        runBlocking { testObj.onConfigChanged(config) }

        assertEquals(config.port, testObj.getBindPort())
    }

    @Test
    fun `name is always set`() {
        assertEquals("BackendGateway", testObj.gatewayName)
    }

    @Nested
    inner class WebSocket {
        private val nodeId = PragmaCoreTestFactory.nodeId(7)
        private val backendSession: BackendSession = mockk(relaxed = true)

        @BeforeEach
        fun setup() {
            every { pragmaNode.backendSessionFactory.getOrCreateSession(nodeId) } returns backendSession
        }

        @Test
        fun `application has right components installed`() {
            testApplication {
                application {
                    testObj.internalBuildModules(this)
                    assertThrows(MissingApplicationPluginException::class.java) { plugin(CORS) } // As example of some feature not installed
                    assertNotNull(plugin(WebSockets))
                    assertNotNull(plugin(Authentication))
                }
            }
        }

        @Test
        fun `application routes websockets to backend session runner`() {
            val authToken = Base64.getUrlEncoder().encodeToString("$nodeId:".toByteArray())

            testApplication {
                val client = createClient {
                    this.install(io.ktor.client.plugins.websocket.WebSockets)
                }
                application {
                    testObj.internalBuildModules(this)
                }

                val setup: HttpRequestBuilder.() -> Unit = {
                    header(HttpHeaders.Authorization, "Basic $authToken")
                }
                client.webSocket("/v1/rpc", setup) {
                    close()
                }

                // timeout used here to ensure that the websocket conversation has existed long
                // enough for the routing calls to have been executed
                coVerify(timeout = 2000) { backendSession.run(any()) }
            }
        }

        @Test
        fun `routeWebSocketRequests uses a DefaultWebSocketServerSession`() {
            val defaultWebSocketServerSession = mockk<DefaultWebSocketServerSession>()

            runBlocking {
                testObj.routeWebSocketRequest(nodeId, defaultWebSocketServerSession)
            }

            coVerify { backendSession.run(defaultWebSocketServerSession) }
        }
    }
}
