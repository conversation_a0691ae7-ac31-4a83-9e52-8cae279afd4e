package pragma.databases

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.mockk.MockKStubScope
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.sql.Connection
import java.sql.PreparedStatement
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.test.TestResult
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.slf4j.Logger
import pragma.config.EncryptedString

object DatabaseTestFactory {
    private fun rows(count: Int): List<Boolean> = MutableList(count + 1) { it < count }
    infix fun <B> MockKStubScope<Boolean, B>.returnsManyRows(count: Int) = returnsMany(rows(count))

    fun makeDatabaseConfigResource(
        i: Int,
        databaseConfig: DatabaseConfig,
        databaseConnectionComponentFactory: DatabaseConnectionComponentFactory,
        logger: Logger = mockk(relaxUnitFun = true)
    ) = DatabaseConfigResource(databaseConfig, i, databaseConnectionComponentFactory, logger)

    fun mockDatabaseConfigResource(i: Int) = mockk<DatabaseConfigResource> {
        every { index } returns i - 1
        every { configIndex } returns i
    }

    fun mockupDatabaseConnectionAndPieces(i: Int, databaseConfig: TestableDatabaseConfig, sqlString: String): DatabaseConnectionBits {
        val mockedPreparedStatement = mockk<PreparedStatement>(relaxed = true)

        return mockupDatabaseConnectionAndPieces(i, databaseConfig, mapOf(sqlString to mockedPreparedStatement))
    }

    fun mockupDatabaseConnectionAndPieces(
        i: Int,
        databaseConfig: TestableDatabaseConfig,
        preparedStatementMap: Map<String, PreparedStatement>
    ): DatabaseConnectionBits {
        databaseConfig.addHostPortSchema(i)
        val mockedConnection = mockk<Connection>(relaxUnitFun = true) {
            preparedStatementMap.forEach {
                every { prepareStatement(it.key) } returns it.value
            }
        }
        val dataSource = mockk<HikariDataSource> {
            every { connection } returns mockedConnection
        }
        return DatabaseConnectionBits(i, dataSource, mockedConnection, preparedStatementMap)
    }

    fun mockupDatabaseConnectionAndPieces(
        i: Int,
        preparedStatementMap: Map<String, PreparedStatement>
    ): DatabaseConnectionBits {
        val mockedConnection = mockk<Connection>(relaxUnitFun = true) {
            preparedStatementMap.forEach {
                every { prepareStatement(it.key) } returns it.value
            }
        }
        val dataSource = mockk<HikariDataSource> {
            every { connection } returns mockedConnection
        }
        return DatabaseConnectionBits(i, dataSource, mockedConnection, preparedStatementMap)
    }

    private fun DatabaseConfig.addCredentialsBase() {
        username = "superuser"
        password = EncryptedString("password")
    }

    fun UnpartitionedDatabaseConfig.addCredentials(newHostPortSchema: String) {
        addCredentialsBase()
        hostPortSchema = newHostPortSchema
    }
    fun PartitionedDatabaseConfig.addCredentials(vararg hostPortSchema: String) {
        addCredentialsBase()
        var index = 1
        val pairs = hostPortSchema.map { hps -> "${index++}" to hps }.toTypedArray()
        hostPortSchemas = configMapOf(*pairs)
    }

    fun trainDatabaseConnectionComponentFactoryToReturnCorrectDataSource(
        mockDatabaseConnectionComponentFactory: DatabaseConnectionComponentFactory,
        dataSources: List<HikariDataSource>,
        databaseConfig: DatabaseConfig
    ) {
        val hikariConfigSlot = slot<HikariConfig>()
        every { mockDatabaseConnectionComponentFactory.connectionPoolToWaitForSchemaCreation(capture(hikariConfigSlot)) } answers {
            val jdbcUrl = hikariConfigSlot.captured.jdbcUrl
            var ret: HikariDataSource? = null
            for (resourceIndex in 1..dataSources.count()) {
                val hostPortSchema = databaseConfig.hostPortSchemas["$resourceIndex"]!!
                if (jdbcUrl.indexOf(hostPortSchema) >= 0) {
                    ret = dataSources[resourceIndex - 1]
                }
            }
            if (ret == null) {
                throw Exception("unrecognized $jdbcUrl cannot find matching dataSource")
            }
            ret
        }
    }

    fun trainDatabaseConnectionComponentFactoryToReturnARealContext(databaseConnectionComponentFactory: DatabaseConnectionComponentFactory) {
        val context = Job()
        every { databaseConnectionComponentFactory.contextCreator(any()) } returns context
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    fun trainConnectionFactoryWithTestContext(
        connectionFactory: DatabaseConnectionComponentFactory,
        maxPoolSize: Int,
        testBlock: suspend TestScope.() -> Unit
    ): TestResult = runTest {
        every {
            connectionFactory.contextCreator(maxPoolSize)
        } returns coroutineContext
        testBlock()
    }
}

data class DatabaseConnectionBits(
    val resourceIndex: Int,
    val dataSource: HikariDataSource,
    val connection: Connection,
    val preparedStatementsMap: Map<String, PreparedStatement>
) {

}