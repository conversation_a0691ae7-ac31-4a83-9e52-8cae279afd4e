package pragma.config

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1
import kotlin.reflect.jvm.jvmName
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import pragma.PragmaCoreTestFactory.anyString
import pragma.PragmaNode
import pragma.content.ContentDataNodeService
import pragma.plugins.ConfigurablePlugin
import pragma.plugins.PluginCollection
import pragma.plugins.PragmaPlugin
import pragma.plugins.PragmaPluginCollection
import pragma.plugins.PragmaServicePlugin
import pragma.services.NodeService
import pragma.services.PragmaService
import pragma.services.Service
import pragma.settings.BackendMode
import pragma.settings.BackendType
import pragma.settings.Decrypter
import pragma.settings.PassthroughDecrypter
import pragma.settings.PragmaSpeciesImpl
import pragma.utils.PragmaSerializationUtils.yamlMapper
import pragma.utils.ReflectionsUtil
import pragma.utils.StringCapturingTerminalProxy

internal class PragmaConfigProviderTest {
    private val reflectionsUtil = mockk<ReflectionsUtil>(relaxUnitFun = true)
    private val allPotentialServiceClasses =
        mutableSetOf<KClass<out Service>>(PragmaConfigProviderTestableNodeService::class, ConfigManagerImplTest.TestService::class)
    private val pragmaSpecies = PragmaSpeciesImpl(BackendMode.DEVELOPMENT, BackendType.GAME)
    private val logger = mockk<Logger>(relaxUnitFun = true)
    private val decrypter = mockk<Decrypter>()
    private val configReflectionUtils = mockk<ConfigReflectionUtils>(relaxed = true)

    private val testObj = PragmaConfigProvider(decrypter, allPotentialServiceClasses, reflectionsUtil, pragmaSpecies, configReflectionUtils)
    private val gatewayList = listOf<KClass<out NodeService>>(PragmaConfigProviderTestableNodeService::class)

    @Test
    fun loadClasses() {
        every { reflectionsUtil.getSubTypesOf(ServiceConfig::class.java) } returns
            setOf(PragmaConfigProviderTestableConfigObject::class.java)
        every { reflectionsUtil.getSubTypesOf(Service::class.java) } returns
            setOf(PragmaConfigProviderTestableNodeService::class.java, WrongTypeTestableNodeService::class.java)
        testObj.loadClasses()

        assertEquals(
            PragmaConfigProviderTestableConfigObject::class.java,
            testObj.serviceConfigClassesByName[PragmaConfigProviderTestableConfigObject::class.simpleName]
        )
        assertTrue(
            Pair(
                PragmaConfigProviderTestableNodeService::class.java,
                PragmaConfigProviderTestableNodeService::testPluginProperty
            )
                in testObj.pluginFields
        )
        assertTrue(
            Pair(
                PragmaConfigProviderTestableNodeService::class.java,
                PragmaConfigProviderTestableNodeService::testPluginCollection
            )
                in testObj.pluginFields
        )
        assertFalse(
            Pair(
                PragmaConfigProviderTestableNodeService::class.java,
                PragmaConfigProviderTestableNodeService::notAPlugin
            )
                in testObj.pluginFields
        )
        assertFalse(
            Pair(
                WrongTypeTestableNodeService::class.java,
                WrongTypeTestableNodeService::testPluginProperty
            )
                in testObj.pluginFields
        )
    }

    @Test
    fun loadClassesLoadsServiceExtensions() {
        every { reflectionsUtil.getSubTypesOf(ServiceConfig::class.java) } returns setOf()
        every { reflectionsUtil.getSubTypesOf(Service::class.java) } returns setOf()
        every { configReflectionUtils.loadServiceExtensionConfigHandlerClasses() } returns setOf(TestServiceExtensionConfigHandler::class)
        testObj.loadClasses()
        assertEquals(testObj.serviceExtensionConfigHandlerClasses.single(), TestServiceExtensionConfigHandler::class)
    }

//    Disabled because the integration tests start pragma node, and we cannot hide the
//    duplicate TestPluginConfig.PragmaConfigProviderTestableConfigObject from pragmaNode
//    @Test
//    fun loadClassesDuplicateNames() {
//        every { reflectionsUtil.getSubTypesOf(ServiceConfig::class.java) } returns
//            setOf(PragmaConfigProviderTestableConfigObject::class.java,
//                  TestPluginConfig.PragmaConfigProviderTestableConfigObject::class.java)
//        val error = assertThrows<java.lang.IllegalStateException> { testObj.loadClasses() }
//        assertEquals("Conflicting config class name: pragma.config.TestPluginConfig.PragmaConfigProviderTestableConfigObject and " +
//            "pragma.config.PragmaConfigProviderTest.PragmaConfigProviderTestableConfigObject", error.message)
//    }

    @Suppress("UNCHECKED_CAST")
    @Test
    fun resolveServiceConfigs() {
        val pragmaConfig = PragmaConfig.getForSpecies(pragmaSpecies)
        val configYaml = """
            game:
                serviceConfigs:
                    ExtraService:
                    PragmaConfigProviderTestableConfigObject:
                    pragma.config.PragmaConfigProviderTest.PragmaConfigProviderTestableConfigObject:
        """.trimIndent()
        val node = yamlConfigContext(configYaml)
        val configNodes = mutableListOf(node)
        allPotentialServiceClasses.add(PragmaConfigProviderTestableNodeService::class)
        allPotentialServiceClasses.add(ConfigManagerImplTest.TestService::class)
        val testableConfigName = PragmaConfigProviderTestableConfigObject::class.simpleName!!
        testObj.serviceConfigClassesByName[testableConfigName] = PragmaConfigProviderTestableConfigObject::class.java
        val testConfigName = ConfigManagerImplTest.TestConfig::class.simpleName!!
        testObj.serviceConfigClassesByName[testConfigName] = ConfigManagerImplTest.TestConfig::class.java
        val forReflectionUtilMapTraining = PragmaConfigProviderTestableNodeService.forReflectionUtilMapTraining()
        forReflectionUtilMapTraining["TestConfig"] =
            mutableSetOf(ConfigManagerImplTest.TestService::class.java as Class<out ConfigHandler<ServiceConfig<*>>>)
        every { reflectionsUtil.handlerClassesByServiceConfigClassSimpleName } returns forReflectionUtilMapTraining

        testObj.resolveServiceConfigs(pragmaConfig, allPotentialServiceClasses, configNodes)

        assertTrue(testableConfigName in pragmaConfig.serviceConfigs, "there should be a map from TestableService to TestableConfigObject")
        assertEquals(pragmaConfig, pragmaConfig.serviceConfigs[testableConfigName]?.parent)
        assertTrue(testConfigName in pragmaConfig.serviceConfigs, "this should exist even though it isn't in the yml")
        assertEquals(pragmaConfig, pragmaConfig.serviceConfigs[testConfigName]?.parent)
        assertEquals(true, node.hasProblems)
        assertEquals(
            "Config source 'filename.yml', has unused service configs: 'ExtraService', 'PragmaConfigProviderTestableConfigObject'",
            node.problemMessage
        )
    }

    @Test
    fun resolvePluginConfigs() {
        val pluginConfigKey = "PragmaConfigProviderTestableNodeService.pluginForTest"
        val testPluginClassName = TestPlugin::class.qualifiedName
        val expectedConfigValue = "someConfigValue"
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        class: "$testPluginClassName"
                        config:
                            someVal: "$expectedConfigValue"
       """.trimIndent()
        trainReflectionAndAddPluginField(TestPlugin::class, PragmaConfigProviderTestableNodeService::testPluginProperty)

        val pragmaConfig = testObj.get(listOf(yamlConfigContext(configYaml)), gatewayList)
        val pluginClassAndConfig = pragmaConfig.pluginConfigs[pluginConfigKey] as PluginClassAndConfig
        assertEquals(testPluginClassName, pluginClassAndConfig.`class`)
        assertEquals(pragmaConfig, pluginClassAndConfig.parent)
        assertEquals(expectedConfigValue, (pluginClassAndConfig.config as TestPluginConfig).someVal)
    }

    @Test
    fun resolveServiceExtensionConfigs() {
        val serviceExtension = "TestServiceExtensionConfigHandler"
        val expectedConfigValue = 185L
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
                serviceExtensionConfigs:
                    $serviceExtension:
                        myLongValue: $expectedConfigValue
       """.trimIndent()
        // why are these needed, see below comment
        every { reflectionsUtil.handlerClassesByServiceConfigClassSimpleName } returns mutableMapOf()
        every { reflectionsUtil.serviceWithOrderedNodeServiceDependencies(PragmaConfigProviderTestableNodeService::class) } returns
            ArrayDeque(listOf(PragmaConfigProviderTestableNodeService::class))

        // the existing tests seem wrong and this seems wrong, this class is doing too much at too many levels
        testObj.serviceExtensionConfigHandlerClasses = setOf(TestServiceExtensionConfigHandler::class)
        val pragmaConfig = testObj.get(listOf(yamlConfigContext(configYaml)), gatewayList)
        val classAndConfig = pragmaConfig.serviceExtensionConfigs[TestServiceExtensionConfigHandler::class.qualifiedName!!] as ServiceExtensionConfig
        assertEquals(pragmaConfig, classAndConfig.parent)
        assertEquals(expectedConfigValue, (classAndConfig as SomeExtensionConfig).myLongValue)
    }

    @Test
    fun resolvePluginConfigsWhenNotInYml() {
        val pluginConfigKey = "PragmaConfigProviderTestableNodeService.pluginForTest"
        val testPluginClassName = TestPlugin::class.qualifiedName
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
       """.trimIndent()
        trainReflectionAndAddPluginField(TestPlugin::class, PragmaConfigProviderTestableNodeService::testPluginProperty)

        val pragmaConfig = testObj.get(listOf(yamlConfigContext(configYaml)), gatewayList)
        val pluginClassAndConfig = pragmaConfig.pluginConfigs[pluginConfigKey] as PluginClassAndConfig
        assertEquals(testPluginClassName, pluginClassAndConfig.`class`)
        assertEquals(pragmaConfig, pluginClassAndConfig.parent)
    }

    @Test
    fun resolvePluginConfigsErrorsWhenPluginIsWrongType() {
        val pluginConfigKey = "PragmaConfigProviderTestableNodeService.pluginForTest"
        val testPluginClassName = TestPluginInherits1::class.qualifiedName
        val expectedConfigValue = "someConfigValue"
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        class: "$testPluginClassName"
                        config:
                            someVal: "$expectedConfigValue"
       """.trimIndent()
        trainReflectionAndAddPluginField(TestPlugin::class, PragmaConfigProviderTestableNodeService::testPluginProperty)

        @Suppress("UNCHECKED_CAST")
        every {
            reflectionsUtil.getImplementationKClass(
                TestPlugin::class,
                TestPluginInherits1::class.jvmName
            )
        } returns TestPluginInherits1::class as KClass<out TestPlugin>
        val nodeContext = yamlConfigContext(configYaml)
        testObj.get(listOf(nodeContext), gatewayList)
        assertEquals(
            "Config source 'filename.yml', Found unexpected type: pragma.config.TestPluginInherits1 while setting plugin: PragmaConfigProviderTestableNodeService.testPluginProperty expected subclass of: pragma.config.TestPlugin.",
            nodeContext.problemMessage
        )
    }

    @Test
    fun resolvePluginConfigsMergesCorrectlyWhenDifferentPluginClass() {
        val pluginConfigKey = "PragmaConfigProviderTestableNodeService.pluginForTestInherits"
        val plugin1ClassName = TestPluginInherits1::class.qualifiedName
        val plugin2ClassName = TestPluginInherits2::class.qualifiedName
        val expectedConfigValue = "someConfigValue"
        val expectedConfigValueUnique = "someConfigValueUnique"
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        class: "$plugin1ClassName"
                        config:
                            someVal: "overridden below"
                            anotherVal: "ignored because different class"
       """.trimIndent()
        val configYaml2 = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        class: "$plugin2ClassName"
                        config:
                            someVal: "$expectedConfigValue"
                            uniqueVal: "$expectedConfigValueUnique"
       """.trimIndent()

        trainReflectionAndAddPluginField(TestPluginInherits1::class, PragmaConfigProviderTestableNodeService::testPluginInheritsProperty)
        trainReflectionAndAddPluginField(TestPluginInherits2::class, PragmaConfigProviderTestableNodeService::testPluginInheritsProperty)

        every {
            reflectionsUtil.getImplementationKClass(
                TestPluginInheritsInterface::class,
                plugin2ClassName!!
            )
        } returns TestPluginInherits2::class
        every {
            reflectionsUtil.getImplementationKClass(
                TestPluginInheritsInterface::class,
                plugin1ClassName!!
            )
        } returns TestPluginInherits1::class

        val nodeContext1 = yamlConfigContext(configYaml)
        val nodeContext2 = yamlConfigContext(configYaml2)
        val pragmaConfig = testObj.get(listOf(nodeContext1, nodeContext2), gatewayList)
        val pluginClassAndConfig = pragmaConfig.pluginConfigs[pluginConfigKey] as PluginClassAndConfig
        assertEquals(plugin2ClassName, pluginClassAndConfig.`class`)
        val anotherTestPluginConfig = pluginClassAndConfig.config as TestPluginInherits2Config
        assertEquals(expectedConfigValue, anotherTestPluginConfig.someVal)
        assertEquals(expectedConfigValueUnique, anotherTestPluginConfig.uniqueVal)

        assertFalse(nodeContext1.hasMajorProblems)
        assertTrue(nodeContext1.hasProblems)
        assertFalse(nodeContext2.hasProblems)
    }

    @Test
    fun resolvePluginConfigsNoticesUnconfigurableCollectionWithConfig() {
        val pluginConfigKey = "PragmaConfigProviderTestableNodeService.pluginCollectionSansConfigForTest"
        val pluginClass = TestPluginSansConfig::class
        val testPluginClassName = pluginClass.qualifiedName
        val pluginOneKey = "SomeHelpfulKeyForFirstPlugin"
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        plugins:
                            $pluginOneKey:
                                class: "$testPluginClassName"
                                config:
                                    extraValue: "value"
       """.trimIndent()

        trainReflectionAndAddPluginField(pluginClass, PragmaConfigProviderTestableNodeService::testPluginSansConfigCollection)

        val nodeContext = yamlConfigContext(configYaml)
        testObj.get(listOf(nodeContext), gatewayList)
        val expectedMessage =
            "Config source 'filename.yml', has config provided for plugin 'PragmaConfigProviderTestableNodeService.pluginCollectionSansConfigForTest' but plugin is not configurable"
        assertEquals(nodeContext.problemMessage, expectedMessage)
        assertEquals(nodeContext.hasMajorProblems, false)
        assertEquals(nodeContext.hasProblems, true)
    }

    @Test
    fun resolvePluginConfigsWorksIfEmptyConfigProvidedForUnconfigurableCollection() {
        val pluginConfigKey = "PragmaConfigProviderTestableNodeService.pluginCollectionSansConfigForTest"
        val testPluginClassName = TestPluginSansConfig::class.qualifiedName
        val pluginOneKey = "SomeHelpfulKeyForFirstPlugin"
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        plugins:
                            $pluginOneKey:
                                class: "$testPluginClassName"
                                config:
       """.trimIndent()
        trainReflectionAndAddPluginField(
            TestPluginSansConfig::class,
            PragmaConfigProviderTestableNodeService::testPluginSansConfigCollection
        )

        val pragmaConfig = testObj.get(listOf(yamlConfigContext(configYaml)), gatewayList)
        val pluginClassAndConfig = pragmaConfig.pluginConfigs[pluginConfigKey] as PluginCollectionConfigs
        assertEquals(1, pluginClassAndConfig.plugins.size)
        val pluginConfigs = pluginClassAndConfig.plugins[pluginOneKey]!!
        assertEquals(testPluginClassName, pluginConfigs.`class`)
        verify(exactly = 0) {
            logger.warn(any())
        }
    }

    @Test
    fun resolvePluginCollectionConfigs() {
        val pluginConfigKey = "PragmaConfigProviderTestableNodeService.pluginCollectionForTest"
        val pluginClass = TestPlugin::class
        val testPluginClassName = pluginClass.qualifiedName
        val expectedConfigValue = anyString()
        val expectedAnotherValue = anyString()
        val pluginOneKey = "SomeHelpfulKeyForFirstPlugin"
        val configYaml = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        plugins:
                            $pluginOneKey:
                                config:
                                    someVal: "$expectedConfigValue"
                                    anotherVal: "This value should be overwritten below"
       """.trimIndent()
        val configYaml2 = """
            game:
                serviceConfigs:
                pluginConfigs:
                    $pluginConfigKey:
                        plugins:
                            $pluginOneKey:
                                class: "$testPluginClassName"
                                config:
                                    anotherVal: $expectedAnotherValue
       """.trimIndent()
        trainReflectionAndAddPluginField(pluginClass, PragmaConfigProviderTestableNodeService::testPluginCollection, mutableMapOf())

        val pragmaConfig = testObj.get(listOf(yamlConfigContext(configYaml), yamlConfigContext(configYaml2)), gatewayList)

        val pluginClassAndConfig = pragmaConfig.pluginConfigs[pluginConfigKey] as PluginCollectionConfigs
        assertEquals(1, pluginClassAndConfig.plugins.size)
        val pluginConfigs = pluginClassAndConfig.plugins[pluginOneKey]!!
        assertEquals(testPluginClassName, pluginConfigs.`class`)
        val testPluginConfig = pluginConfigs.config as TestPluginConfig
        assertEquals(expectedConfigValue, testPluginConfig.someVal)
        assertEquals(expectedAnotherValue, testPluginConfig.anotherVal)
        verify(exactly = 0) {
            logger.warn(any())
        }
    }

    private fun yamlConfigContext(configYaml: String): YamlConfigContext {
        return YamlConfigContext(FakeSource(content = configYaml), pragmaSpecies, yamlMapper, decrypter)
    }

    @Test
    fun loadGatewayConfigs() {
        testObj.serviceConfigClassesByName[PragmaConfigProviderTestableConfigObject::class.simpleName!!] =
            PragmaConfigProviderTestableConfigObject::class.java
        val configStrings = """
            |game:
            |  serviceConfigs:
            |    PragmaConfigProviderTestableConfigObject:
            |      aSecondValue: "a value for the second field"
        """.trimMargin()
        val gateways = listOf<KClass<out NodeService>>(PragmaConfigProviderTestableNodeService::class)
        trainReflectionAndAddPluginField(TestPlugin::class, PragmaConfigProviderTestableNodeService::testPluginCollection)

        val pragmaConfig = testObj.get(listOf(yamlConfigContext(configStrings)), gateways)

        assertTrue(
            PragmaConfigProviderTestableConfigObject::class.simpleName in pragmaConfig.serviceConfigs,
            "PragmaConfigProviderTestableConfigObject should have been added to the list"
        )
        assertEquals(
            "a value for the second field",
            (pragmaConfig.serviceConfigs[PragmaConfigProviderTestableConfigObject::class.simpleName]!! as PragmaConfigProviderTestableConfigObject).aSecondValue
        )
    }

    @Test
    fun loadCoreConfig() {
        val configYaml = """
            game:
              core:
                logging:
                  rootLoggerLevel: "OFF"
""".trimIndent()
        val handlerClasses = PragmaConfigProviderTestableNodeService.forReflectionUtilMapTraining()
        every { reflectionsUtil.handlerClassesByServiceConfigClassSimpleName } returns handlerClasses
        every { reflectionsUtil.serviceWithOrderedNodeServiceDependencies(PragmaConfigProviderTestableNodeService::class) } returns
            ArrayDeque(listOf(PragmaConfigProviderTestableNodeService::class))

        val pragmaConfig = testObj.get(listOf(yamlConfigContext(configYaml)), gatewayList)

        val coreConfig = pragmaConfig.core
        assertEquals(pragmaConfig, coreConfig.parent)
        assertEquals(LogLevel.OFF, coreConfig.logging.rootLoggerLevel)
    }

        @Suppress("UNCHECKED_CAST")
    @Test
    fun printAnnotated() {
        val forReflectionUtilMapTraining = PragmaConfigProviderTestableNodeService.forReflectionUtilMapTraining()
        forReflectionUtilMapTraining["TestConfig"] =
            mutableSetOf(ConfigManagerImplTest.TestService::class.java as Class<out ConfigHandler<ServiceConfig<*>>>)

        every { reflectionsUtil.handlerClassesByServiceConfigClassSimpleName } returns forReflectionUtilMapTraining
        every { reflectionsUtil.getSubTypesOf(ServiceConfig::class.java) } returns setOf(
            ConfigManagerImplTest.TestConfig::class.java,
            PragmaConfigProviderTestableConfigObject::class.java
        )
        every { reflectionsUtil.getSubTypesOf(Service::class.java) } returns allPotentialServiceClasses.map { it.java }.toSet()
        every {
            reflectionsUtil.getImplementationKClass(
                TestPluginInheritsInterface::class,
                TestPluginInherits1::class.jvmName
            )
        } returns TestPluginInherits1::class
        every {
            reflectionsUtil.getImplementationKClass(
                TestPluginInheritsInterface::class,
                TestPluginInherits2::class.jvmName
            )
        } returns TestPluginInherits2::class
        every { reflectionsUtil.getImplementationKClass(TestPlugin::class, TestPlugin::class.jvmName) } returns TestPlugin::class
        every {
            reflectionsUtil.getImplementationKClass(
                TestPluginSansConfig::class,
                TestPluginSansConfig::class.jvmName
            )
        } returns TestPluginSansConfig::class

        val output = StringCapturingTerminalProxy()
        val config1 = StringStringProvider(
            "config1.yml", """
            |game:
            |  core:
            |    logging:
            |      rootLoggerLevel: "OFF"
            |  serviceConfigs:
            |    PragmaConfigProviderTestableConfigObject:
            |      aValue: "a string too far"
            |      aSecondValue: "a second string"
        """.trimMargin()
        )
        val config2 = StringStringProvider(
            "config2.yml", """
            |game:
            |  core:
            |    logging:
            |      rootLoggerLevel: "INFO"
            |  serviceConfigs:
            |    PragmaConfigProviderTestableConfigObject:
            |      aValue: "a string string time ago"
            |    TestConfig:
            |      numberValue: 11201
        """.trimMargin()
        )
        val configs = ConfigStringProviders(listOf(config1, config2), PassthroughDecrypter)

        testObj.printAnnotated(output, configs.getYamlConfigContexts(pragmaSpecies, decrypter))

        val newExpected = """
            |# all config elements come from defaults config except where marked as from other
            |game:
            |  core:
            |    nodeName: ""
            |    logging:
            |#      rootLoggerLevel: "INFO"
            |#      rootLoggerLevel: "OFF"  # from config1.yml
            |      rootLoggerLevel: "INFO"  # from config2.yml
            |      logFormat: PLACEHOLDER
            |      consoleLoggerEnabled: true
            |      fileLoggerEnabled: true
            |      fileLoggerCurrentFilename: "/opt/pragma/platform-logs/platform.log"
            |      loggers:
            |        com.zaxxer.hikari: "WARN"
            |        liquibase: "WARN"
            |        org.reflections.Reflections: "ERROR"
            |        ktor.application: "WARN"
            |        pragma.config.EmptyPluginConfig: "OFF"
            |      logLayout: "TEXT"
            |    portalUrl: ""
            |    enableHistogramMetrics: true
            |    enableKtorMetrics: true
            |    metricsDistributionExpiryDurationSeconds: 15
            |    startupTimeoutMillis: 60000
            |    shutdownTimeoutMillis: 120000
            |    nettyPendingTasksCheckPeriodMillis: 1500
            |    allowWorkInProgressAccess: false
            |    enableVersionWithBuildSlug: false
            |    serviceRpcTimeoutMillis: 30000
            |  serviceConfigs:
            |    TestConfig:
            |#      numberValue: 42
            |      numberValue: 11201  # from config2.yml
            |    PragmaConfigProviderTestableConfigObject:
            |#      aValue: "default"
            |#      aValue: "a string too far"  # from config1.yml
            |      aValue: "a string string time ago"  # from config2.yml
            |      aSecondValue: "a second string"  # from config1.yml
            |  pluginConfigs:
            |    PragmaConfigProviderTestableNodeService.pluginCollectionForTest:
            |    PragmaConfigProviderTestableNodeService.pluginForTestInherits:
            |      class: "pragma.config.TestPluginInherits1"
            |      config:
            |        someVal: "SomeDefaultValue"
            |        uniqueVal: ""
            |    PragmaConfigProviderTestableNodeService.pluginForTest:
            |      class: "pragma.config.TestPlugin"
            |      config:
            |        someVal: "SomeDefaultValue"
            |        anotherVal: ""
            |    PragmaConfigProviderTestableNodeService.pluginSansConfigForTest:
            |      class: "pragma.config.TestPluginSansConfig"
            |      config:
            |    PragmaConfigProviderTestableNodeService.pluginCollectionSansConfigForTest:
            |
            |
        """.trimMargin()
        val pattern = Regex(" +logFormat:.*\n")
        val valueStrippedLogFormat = output.capturedOutput.replace(pattern, "      logFormat: PLACEHOLDER\n")
        assertEquals(newExpected, valueStrippedLogFormat)
    }

    private fun <T> trainReflectionAndAddPluginField(
        pluginClass: KClass<*>,
        pluginProperty: KProperty1<PragmaConfigProviderTestableNodeService, T>,
        handlerClasses: MutableMap<String, MutableSet<Class<out ConfigHandler<ServiceConfig<*>>>>> = PragmaConfigProviderTestableNodeService.forReflectionUtilMapTraining()
    ) {
        every { reflectionsUtil.handlerClassesByServiceConfigClassSimpleName } returns handlerClasses
        every { reflectionsUtil.getImplementationKClass(pluginClass, pluginClass.jvmName) } returns pluginClass
        every { reflectionsUtil.serviceWithOrderedNodeServiceDependencies(PragmaConfigProviderTestableNodeService::class) } returns
            ArrayDeque(listOf(PragmaConfigProviderTestableNodeService::class))

        testObj.pluginFields.add(PragmaConfigProviderTestableNodeService::class.java to pluginProperty)
    }

    class TestPluginCollection : PluginCollection<TestPlugin>()

    @PragmaService(backendTypes = [BackendType.GAME], dependencies = [])
    class PragmaConfigProviderTestableNodeService(pragmaNode: PragmaNode) : NodeService(pragmaNode),
                                                                            ConfigHandler<PragmaConfigProviderTestableConfigObject> {
        companion object {
            fun forReflectionUtilMapTraining(): MutableMap<String, MutableSet<Class<out ConfigHandler<ServiceConfig<*>>>>> {
                @Suppress("UNCHECKED_CAST")
                val configHandlerClass = PragmaConfigProviderTestableNodeService::class.java as Class<out ConfigHandler<ServiceConfig<*>>>
                val pairs = PragmaConfigProviderTestableConfigObject::class.simpleName!! to mutableSetOf(configHandlerClass)
                return mutableMapOf(pairs)
            }
        }

        override suspend fun onConfigChanged(serviceConfig: PragmaConfigProviderTestableConfigObject) {}

        @Suppress("unused")
        @PragmaPlugin("pragma.config.TestPluginInherits1", "pluginForTestInherits")
        lateinit var testPluginInheritsProperty: TestPluginInheritsInterface

        @Suppress("unused")
        @PragmaPlugin("pragma.config.TestPlugin", "pluginForTest")
        lateinit var testPluginProperty: TestPlugin

        @Suppress("unused")
        @PragmaPluginCollection("pragma.config.TestPlugin", "pluginCollectionForTest")
        val testPluginCollection = TestPluginCollection()

        @Suppress("unused")
        @PragmaPlugin("pragma.config.TestPluginSansConfig", "pluginSansConfigForTest")
        lateinit var testPluginSansConfig: TestPluginSansConfig

        @Suppress("unused")
        @PragmaPluginCollection("pragma.config.TestPluginSansConfig", "pluginCollectionSansConfigForTest")
        val testPluginSansConfigCollection = TestPluginSansConfigCollection()

        @Suppress("unused")
        lateinit var notAPlugin: TestPlugin
    }

    @PragmaService(backendTypes = [BackendType.SOCIAL], dependencies = [])
    class WrongTypeTestableNodeService(pragmaNode: PragmaNode) : NodeService(pragmaNode),
                                                                 ConfigHandler<PragmaConfigProviderTestableConfigObject> {
        override suspend fun onConfigChanged(serviceConfig: PragmaConfigProviderTestableConfigObject) {}

        @Suppress("unused")
        @PragmaPlugin("pragma.config.TestPlugin", "pluginForTest")
        lateinit var testPluginProperty: TestPlugin
    }

    class PragmaConfigProviderTestableConfigObject private constructor(type: BackendType) :
        ServiceConfig<PragmaConfigProviderTestableConfigObject>(type) {
        override val description = "a testable config object"
        private var aValue by types.string("description of a value", 5)
        var aSecondValue by types.string("second value to see how merge works", 0)

        init {
            aValue = "default"
        }

        companion object : ConfigBackendModeFactory<PragmaConfigProviderTestableConfigObject> {
            override fun getFor(type: BackendType): PragmaConfigProviderTestableConfigObject {
                return PragmaConfigProviderTestableConfigObject(type)
            }
        }
    }
}

internal class TestPluginConfig(type: BackendType) : PluginConfig<TestPluginConfig>(type) {
    override val description: String = "Some Description For Test"

    @Suppress("unused")
    var someVal by types.string("some thing your plugin needs")

    @Suppress("unused")
    var anotherVal by types.string("another thing your plugin needs")

    companion object : ConfigBackendModeFactory<TestPluginConfig> {
        override fun getFor(type: BackendType): TestPluginConfig {
            return TestPluginConfig(type).apply {
                someVal = "SomeDefaultValue"
                anotherVal = ""
            }
        }
    }
}

internal open class TestPlugin(
    override val service: Service,
    override val contentDataNodeService: ContentDataNodeService
) : ConfigurablePlugin<TestPluginConfig> {
    override suspend fun onConfigChanged(config: TestPluginConfig) {
    }
}

internal class TestPluginSansConfigCollection : PluginCollection<TestPluginSansConfig>()

internal open class TestPluginSansConfig(
    val service: Service,
    val contentDataNodeService: ContentDataNodeService
) : PragmaServicePlugin

internal interface TestPluginInheritsInterface

internal class TestPluginInherits1Config(type: BackendType) : PluginConfig<TestPluginInherits1Config>(type) {
    override val description: String = "Some Description For Test"

    @Suppress("unused")
    var someVal by types.string("some thing your plugin needs")

    @Suppress("unused")
    var uniqueVal by types.string("another thing your plugin needs")

    companion object : ConfigBackendModeFactory<TestPluginInherits1Config> {
        override fun getFor(type: BackendType): TestPluginInherits1Config {
            return TestPluginInherits1Config(type).apply {
                someVal = "SomeDefaultValue"
                uniqueVal = ""
            }
        }
    }
}

internal open class TestPluginInherits1(
    override val service: Service,
    override val contentDataNodeService: ContentDataNodeService
) : TestPluginInheritsInterface, ConfigurablePlugin<TestPluginInherits1Config> {
    override suspend fun onConfigChanged(config: TestPluginInherits1Config) {
    }
}

internal class TestPluginInherits2Config(type: BackendType) : PluginConfig<TestPluginInherits2Config>(type) {
    override val description: String = "Some Description For Test"

    @Suppress("unused")
    var someVal by types.string("some thing your plugin needs")

    @Suppress("unused")
    var uniqueVal by types.string("another thing your plugin needs")

    companion object : ConfigBackendModeFactory<TestPluginInherits2Config> {
        override fun getFor(type: BackendType): TestPluginInherits2Config {
            return TestPluginInherits2Config(type).apply {
                someVal = "SomeDefaultValue"
                uniqueVal = ""
            }
        }
    }
}

internal open class TestPluginInherits2(
    override val service: Service,
    override val contentDataNodeService: ContentDataNodeService
) : TestPluginInheritsInterface, ConfigurablePlugin<TestPluginInherits2Config> {
    override suspend fun onConfigChanged(config: TestPluginInherits2Config) {
    }
}

internal class StringStringProvider(override val name: String, private val theString: String) : StringProvider {
    override fun provideString(): String = theString
}


internal class SomeExtensionConfig(type: BackendType): ServiceExtensionConfig<SomeExtensionConfig>(type) {
    override val description = "Some test service extension config"

    var myLongValue by types.long("some number thing")

    companion object : ConfigBackendModeFactory<SomeExtensionConfig> {
        override fun getFor(type: BackendType): SomeExtensionConfig {
            return SomeExtensionConfig(type).apply {
                myLongValue = 7L
            }
        }
    }
}

internal class TestServiceExtensionConfigHandler: ServiceExtensionConfigHandler<SomeExtensionConfig> {
    override fun onConfigChanged(serviceExtensionConfig: SomeExtensionConfig) {
    }
}
