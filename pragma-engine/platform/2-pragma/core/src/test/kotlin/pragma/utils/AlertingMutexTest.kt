package pragma.utils

import io.mockk.impl.annotations.OverrideMockKs
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import org.hamcrest.CoreMatchers.containsString
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.slf4j.Logger
import pragma.utils.TimeProxy.Companion.minutesToMillis
import pragma.utils.TimeProxy.Companion.secondsToMillis

@OptIn(ExperimentalCoroutinesApi::class)
@ExtendWith(MockKExtension::class)
class AlertingMutexTest {
    val name = "someName"
    val logger = mockk<Logger>(relaxUnitFun = true)

    @OverrideMockKs
    lateinit var testObj : AlertingMutex

    @Test
    fun construction() {
        val temp : Mutex = testObj
        assertNotNull(temp, "Should be an instance of Mutex")
    }

    @Test
    fun isLocked() = runTest {
        assertFalse(testObj.isLocked)
        testObj.lock()
        assertTrue(testObj.isLocked)
        testObj.unlock()
        assertFalse(testObj.isLocked)
    }

    @Test
    fun logsMessageAfterTimeoutOnce() {
        val functionName = ::logsMessageAfterTimeoutOnce.name
        runTest {
            testObj.lock()
            launch { testObj.lock() }
            advanceTimeBy(1.minutesToMillis)
            verify(exactly = 0) { logger.warn(any(), any<Throwable>()) }
            advanceTimeBy(1)
            val message = mutableListOf<String>()
            val exceptionSlot = mutableListOf<Exception>()
            verify { logger.warn(capture(message), capture(exceptionSlot)) }
            assertThat(message.last(), containsString("60000"))
            assertThat(exceptionSlot.last().stackTraceToString(), containsString(functionName))
            advanceTimeBy(10.secondsToMillis)
            verify(exactly = 1) { logger.warn(capture(message), capture(exceptionSlot)) }
            testObj.unlock()
        }
    }

    @Test
    fun lockProxiesOwner() = runTest {
        testObj.lock(this)
        assertTrue(testObj.holdsLock(this))
        val that = Any()
        assertFalse(testObj.holdsLock(that))
    }

    @Test
    fun tryLockProxiesToInnerMutex() = runTest {
        assertTrue(testObj.tryLock(this))
        assertTrue(testObj.holdsLock(this))
        assertFalse(testObj.tryLock(null))
        assertThrows<IllegalStateException> {
            testObj.unlock(Any())
        }
        assertTrue(testObj.holdsLock(this))
        testObj.unlock(this)
        assertFalse(testObj.holdsLock(this))
    }

    @Test
    fun canPassInitialLockedBooleanToInnerMutex() {
        var testObj = AlertingMutex("canPassInitialLockedBooleanToInnerMutex", true)
        assertTrue(testObj.isLocked)

        testObj = AlertingMutex("canPassInitialLockedBooleanToInnerMutex2", false)
        assertFalse(testObj.isLocked)
    }

    @Test
    fun lockedDefaultsToFalseOnConstruction() {
        val testObj = AlertingMutex("lockedDefaultsToFalseOnConstruction")
        assertFalse(testObj.isLocked)
    }

    /*
    Informative test to compare construction performance between AlertingMutex and Mutex
    Last run: 2023-08-31
    Loops: 10        Construction: alerting mutex time? 39700      regular mutex? 27401      ratio? 1
    Loops: 100       Construction: alerting mutex time? 83200      regular mutex? 26800      ratio? 3
    Loops: 1000      Construction: alerting mutex time? 557099     regular mutex? 300699     ratio? 1
    Loops: 10000     Construction: alerting mutex time? 521800     regular mutex? 290900     ratio? 1
    Loops: 100000    Construction: alerting mutex time? 2963299    regular mutex? 2665100    ratio? 1
    Loops: 1000000   Construction: alerting mutex time? 33330100   regular mutex? 7973000    ratio? 4
    Loops: 10000000  Construction: alerting mutex time? 510828500  regular mutex? 82580301   ratio? 6
    Loops: 100000000 Construction: alerting mutex time? 2925183700 regular mutex? 857553199  ratio? 3
     */
//    @ParameterizedTest
//    @ValueSource(ints = [10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000])
//    fun comparingConstruction(numLoops:Int) = runBlocking {
//        System.gc()
//        val startTime = System.nanoTime()
//        for (i in 0..numLoops) {
//            Mutex()
//        }
//        val firstStop = System.nanoTime()
//        System.gc()
//        val secondStart = System.nanoTime()
//
//        for (i in 0..numLoops) {
//            AlertingMutex()
//        }
//        val endTime = System.nanoTime()
//        val ratio = (endTime - secondStart) / (firstStop - startTime)
//        println("Loops: ${numLoops.toString().padEnd(9, ' ' )} Construction: alerting mutex time? ${(endTime - secondStart).toString().padEnd(10, ' ' )} regular mutex? ${(firstStop - startTime).toString().padEnd(10, ' ' )} ratio? $ratio")
//        System.gc()
//    }

    /*
    Informative test to compare lock()/unlock() performance between AlertingMutex and Mutex
    Last run: 2023-08-31
    Loops: 10        Locking/Unlocking: alerting mutex time? 23200      regular mutex? 319200     ratio? 0
    Loops: 100       Locking/Unlocking: alerting mutex time? 96199      regular mutex? 130500     ratio? 0
    Loops: 1000      Locking/Unlocking: alerting mutex time? 542700     regular mutex? 766001     ratio? 0
    Loops: 10000     Locking/Unlocking: alerting mutex time? 1124200    regular mutex? 983299     ratio? 1
    Loops: 100000    Locking/Unlocking: alerting mutex time? 4343901    regular mutex? 6720899    ratio? 0
    Loops: 1000000   Locking/Unlocking: alerting mutex time? 30246200   regular mutex? 25206401   ratio? 1
     */
//    @ParameterizedTest
//    @ValueSource(ints = [10, 100, 1000, 10000, 100000, 1000000])
//    fun comparingLockUnlock(numLoops:Int) = runBlocking {
//        val startTime = System.nanoTime()
//        val mutex = Mutex()
//        for (i in 0..numLoops) {
//            mutex.lock()
//            mutex.unlock()
//        }
//        val middleTime = System.nanoTime()
//
//        val secondStart = System.nanoTime()
//        val amutex = AlertingMutex()
//        for (i in 0..numLoops) {
//            amutex.lock()
//            amutex.unlock()
//        }
//        val endTime = System.nanoTime()
//        val ratio = (endTime - secondStart) / (middleTime - startTime)
//        println("Loops: ${numLoops.toString().padEnd(9, ' ')} Locking/Unlocking: alerting mutex time? ${(endTime - secondStart).toString().padEnd(10, ' ')} regular mutex? ${(middleTime - startTime).toString().padEnd(10, ' ')} ratio? $ratio")
//    }

    /*
    Informative test to compare lock()/unlock() performance between AlertingMutex and Mutex
    Last run: 2023-09-01
    Loops: 10        Locking/Unlocking: alerting mutex time? 24331500   regular mutex? 18697500   ratio? 1
    Loops: 100       Locking/Unlocking: alerting mutex time? 35562900   regular mutex? 11833300   ratio? 3
    Loops: 1000      Locking/Unlocking: alerting mutex time? 105448400  regular mutex? 16245100   ratio? 6
    Loops: 10000     Locking/Unlocking: alerting mutex time? 871232100  regular mutex? 145008700  ratio? 6
    Loops: 100000    Locking/Unlocking: alerting mutex time? 8426856300 regular mutex? 1202216800 ratio? 7
    Loops: 1000000   Locking/Unlocking: alerting mutex time? 83557461000 regular mutex? 11693408800 ratio? 7
     */
//    @ParameterizedTest
//    @ValueSource(ints = [10, 100, 1000, 10000, 100000, 1000000])
//    fun comparingLockConflict(numLoops: Int) = runBlocking {
//        val offMain = newFixedThreadPoolContext(1, "pool")
//        val startTime = System.nanoTime()
//        withContext(offMain) {
//            val mutex = Mutex()
//            for (i in 0..numLoops) {
//                mutex.lock()
//                launch { mutex.lock();mutex.unlock() }
//                yield()
//                mutex.unlock()
//            }
//        }
//        val middleTime = System.nanoTime()
//
//        val secondStart = System.nanoTime()
//        withContext(offMain) {
//            val amutex = AlertingMutex()
//            for (i in 0..numLoops) {
//                amutex.lock()
//                launch { amutex.lock();amutex.unlock() }
//                yield()
//                amutex.unlock()
//            }
//        }
//        val endTime = System.nanoTime()
//        val ratio = (endTime - secondStart) / (middleTime - startTime)
//        println("Loops: ${numLoops.toString().padEnd(9, ' ')} Locking/Unlocking: alerting mutex time? ${(endTime - secondStart).toString().padEnd(10, ' ')} regular mutex? ${(middleTime - startTime).toString().padEnd(10, ' ')} ratio? $ratio")
//    }
}
