package pragma.databases

import com.mysql.cj.jdbc.Driver
import java.sql.Connection
import java.sql.PreparedStatement
import java.sql.Statement
import java.util.UUID
import kotlin.coroutines.CoroutineContext
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import pragma.PragmaException
import pragma.PragmaResult
import pragma.PragmaResult.Failure
import pragma.PragmaResult.Success
import pragma.WorkInProgress
import pragma.utils.JumpHash

enum class DatabaseDriver(val driverClassName: String, val port: Int) {
    MYSQLDB(Driver::class.qualifiedName!!, 3306)
}

class DatabaseExecutor(
    private val jumpHashResources: List<DatabaseConfigResource>,
    private val logger: Logger = LoggerFactory.getLogger(DatabaseExecutor::class.java)
) {
    private val jumpHash = JumpHash()

    companion object {
        internal const val MYSQLDB = "mysql"
        const val UNEXPECTED_ERROR = "unexpected error"
    }

    fun getResources(): List<DatabaseConfigResource> {
        return jumpHashResources
    }

    private fun getResourceBasedOn(propertyToConsistentHash: UUID): DatabaseConfigResource {
        return jumpHashResources[jumpHash.consistent(propertyToConsistentHash, jumpHashResources.size)]
    }

    suspend fun <T> transact(propertyToConsistentHash: UUID, block: suspend (Connection) -> T): T {
        val resource = getResourceBasedOn(propertyToConsistentHash)
        return resource.transact(block)
    }

    private suspend fun <T> executeOnConnectionWithResult(
        connection: Connection,
        context: CoroutineContext,
        sql: String,
        block: suspend (PreparedStatement) -> T
    ): PragmaResult<T, String> = withContext(context) {
        try {
            connection.prepareStatement(sql).use { statement ->
                Success(block(statement))
            }
        } catch (e: PragmaException) {
            logger.error("Pragma Exception from DB", e)
            throw e
        } catch (e: Throwable) {
            logger.error("DB Error", e)
            Failure(UNEXPECTED_ERROR)
        } finally {
            connection.close()
        }
    }

    suspend fun <T> runCommand(propertyToConsistentHash: UUID, block: (Connection) -> T): T {
        val resource = getResourceBasedOn(propertyToConsistentHash)
        return resource.runCommand(block)
    }

    suspend fun <T> executeOnAllWithResult(sql: String, block: suspend (PreparedStatement) -> T): List<PragmaResult<T, String>> {
        val results = coroutineScope {
            getResources().map { resource ->
                async {
                    resource.getConnection().use { connection ->
                        when (val result = executeOnConnectionWithResult(connection, resource.coroutineContext, sql, block)) {
                            is Failure -> Failure("error during ExecuteOnAll on ${resource.hostPortSchema}: ${result.failure}")
                            is Success -> result
                        }
                    }
                }
            }
        }
        return results.awaitAll()
    }

    private class BatchInfo<T>(
        private val dbResource: DatabaseConfigResource,
        val sql: String,
        val propertyToConsistentHashGetter: (input: T) -> Any,
        val statementSetter: (T, PreparedStatement) -> Unit
    ) {
        private var lastExecutedRow = 0
        private var toExecuteCount = 0
        private val rows = mutableListOf<T>()

        private val failedIds = mutableListOf<Any>()

        fun add(row: T) {
            rows.add(row)
        }

        private fun getRowOfExecutedBatch(index: Int): T {
            return rows[index + lastExecutedRow]
        }

        private fun executeBatch(statement: PreparedStatement) {
            val dbResult = statement.executeBatch()
            dbResult.forEachIndexed { index, resultVal ->
                if (resultVal < 0 && resultVal != Statement.SUCCESS_NO_INFO) {
                    val failedRow = getRowOfExecutedBatch(index)
                    val idValue = propertyToConsistentHashGetter(failedRow)
                    failedIds.add(idValue)
                }
            }
            lastExecutedRow += toExecuteCount
            toExecuteCount = 0
        }
        suspend fun processRows(
            batchSize: Int,
            context: CoroutineContext
        ): MutableList<Any> = withContext(context) {
            dbResource.getConnection().use { connection ->
                connection.prepareStatement(sql).use { statement ->
                    rows.chunked(batchSize).forEach { chunked ->
                        chunked.forEach { input ->
                            toExecuteCount += 1
                            statementSetter(input, statement)
                            statement.addBatch()
                        }
                        executeBatch(statement)
                    }
                }
            }
            return@withContext failedIds
        }
    }

    @WorkInProgress
    suspend fun <T> executeBatch(
        sql: String,
        inputCollection: List<T>,
        batchSize: Int = 500,
        statementSetter: (T, PreparedStatement) -> Unit,
        propertyToConsistentHashGetter: (input: T) -> UUID
    ): PragmaResult<BatchSuccess, BatchFailures> {
        val resourceToRows = getResources().associateWith { resource ->
            BatchInfo(resource, sql, propertyToConsistentHashGetter, statementSetter)
        }
        for (value in inputCollection) {
            val dbShard = getResourceBasedOn(propertyToConsistentHashGetter(value))
            resourceToRows[dbShard]!!.add(value)
        }

        val failedIdsLists = coroutineScope {
            resourceToRows.map { (resource, batchInfo) ->
                async {
                    batchInfo.processRows(batchSize, resource.coroutineContext)
                }
            }
        }
        val errorList: MutableList<Any> = mutableListOf()
        failedIdsLists.awaitAll().map { errorList.addAll(it) }

        return if (errorList.isEmpty()) {
            Success(BatchSuccess())
        } else {
            Failure(BatchFailures(errorList))
        }
    }

    fun resourceCount(): Int {
        return getResources().count()
    }
}

class BatchSuccess {
    override fun equals(other: Any?): Boolean {
        return other?.javaClass?.simpleName.equals(BatchSuccess::class.simpleName)
    }

    override fun hashCode(): Int {
        return javaClass.hashCode()
    }
}

data class BatchFailures(val batchFailures: MutableList<Any>)
