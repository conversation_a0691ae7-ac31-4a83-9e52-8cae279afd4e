package pragma.session

import com.google.protobuf.GeneratedMessageV3
import java.util.UUID
import org.slf4j.Logger
import pragma.ExternalNotification
import pragma.Fixed128
import pragma.rpc.Notification
import pragma.OperatorSession
import pragma.PartnerSession
import pragma.PlayerSession
import pragma.PragmaError
import pragma.PragmaException
import pragma.PragmaSession
import pragma.PragmaSession.SessionCase.OPERATOR_SESSION
import pragma.PragmaSession.SessionCase.PARTNER_SESSION
import pragma.PragmaSession.SessionCase.PLAYER_SESSION
import pragma.PragmaSession.SessionCase.SERVICE_SESSION
import pragma.PragmaSession.SessionCase.SESSION_NOT_SET
import pragma.ServiceSession
import pragma.SessionTerminationReason
import pragma.metrics.MetricsManager
import pragma.rpcs.InternalNotificationManager
import pragma.rpcs.JumpData
import pragma.rpcs.SessionType
import pragma.utils.PragmaRoutingException
import pragma.utils.toFixed128
import pragma.utils.toPragmaSession

interface PragmaSessionWrapper {
    companion object {
        fun create(session: PragmaSession) = when(session.sessionCase!!) {
            PLAYER_SESSION -> PlayerPragmaSessionWrapper(session.playerSession)
            PARTNER_SESSION -> PartnerPragmaSessionWrapper(session.partnerSession)
            OPERATOR_SESSION -> OperatorPragmaSessionWrapper(session.operatorSession)
            SERVICE_SESSION -> ServicePragmaSessionWrapper(session.serviceSession)
            SESSION_NOT_SET -> error("Session not set in PragmaSession. If the session type is new, please add relevant class(es)")
        }
    }

    fun recordLogin(metricsManager: MetricsManager)
    fun recordLogout(metricsManager: MetricsManager)
    suspend fun sessionChangedNotification(attributesToSet: Map<Int, UUID>, attributesToRemove: Collection<Int>): ExternalNotification
    fun clearAttributes()
    fun createTerminateNotifications(reason: SessionTerminationReason, logger: Logger): Pair<ExternalNotification, Notification>
    fun logStopConnection(logger: Logger, connection: SessionContext.ComparableConnection)
    suspend fun notifyOfSoftReconnect(notificationManager: InternalNotificationManager)
    suspend fun notifyOfSoftDisconnect(notificationManager: InternalNotificationManager)
    val pragmaSession : PragmaSession
    fun isCorrectSession(type: SessionType): Boolean

    val pragmaId: Fixed128
    val typeName: String
}

abstract class PragmaSessionWrapperBase(private val sessionType: SessionType) : PragmaSessionWrapper {
    override fun isCorrectSession(type: SessionType) = (sessionType == type)

    override fun recordLogin(metricsManager: MetricsManager) {}

    override fun recordLogout(metricsManager: MetricsManager) {}

    override suspend fun sessionChangedNotification(attributesToSet: Map<Int, UUID>, attributesToRemove: Collection<Int>): ExternalNotification {
        error("Cannot mutate attribute on non-Player session")
    }

    override fun clearAttributes() {}

    override fun createTerminateNotifications(reason: SessionTerminationReason, logger: Logger): Pair<ExternalNotification, Notification> {
        error("Terminate can only be called on player and partner sessions that use websockets")
    }

    override fun logStopConnection(logger: Logger, connection: SessionContext.ComparableConnection) {}

    override suspend fun notifyOfSoftReconnect(notificationManager: InternalNotificationManager) {
        throw PragmaException(PragmaError.SessionService_SessionNotFound, "PlayerSession required to soft-reconnect")
    }

    override suspend fun notifyOfSoftDisconnect(notificationManager: InternalNotificationManager) {
        throw PragmaException(PragmaError.SessionService_SessionNotFound, "PlayerSession required to soft-disconnect")
    }

    protected fun buildExternalNotification(notification: GeneratedMessageV3): ExternalNotification {
        return ExternalNotification.newBuilder()
            .setPayload(notification.toByteString())
            .setType(JumpData.getIntForType(notification::class.java))
            .build()
    }

    override val typeName = sessionType.name
}

class PlayerPragmaSessionWrapper(private var session: PlayerSession) : PragmaSessionWrapperBase(SessionType.PLAYER) {
    override fun recordLogin(metricsManager: MetricsManager) {
        metricsManager.metricsCounter("pragma.session.player.login").increment()
    }

    override fun recordLogout(metricsManager: MetricsManager) {
        metricsManager.metricsCounter("pragma.session.player.logout").increment()
    }

    override suspend fun sessionChangedNotification(attributesToSet: Map<Int, UUID>, attributesToRemove: Collection<Int>): ExternalNotification {
        val newSessionBuilder = session.toBuilder()
        newSessionBuilder.version = session.version + 1
        val attributesToSetAsFixed128 = attributesToSet.mapValues { it.value.toFixed128() }
        newSessionBuilder.putAllAttributes(attributesToSetAsFixed128)
        attributesToRemove.forEach {
            newSessionBuilder.removeAttributes(it)
        }
        val newPlayerSession = newSessionBuilder.build()
        session = newPlayerSession

        return buildExternalNotification(PlayerSessionRpc.SessionChangedV1Notification.newBuilder()
                .setNewVersion(session.version)
                .putAllAttributesSet(attributesToSetAsFixed128)
                .addAllAttributesRemoved(attributesToRemove)
                .build()
        )
    }

    override fun clearAttributes() {
        val playerSession = session.toBuilder()
        playerSession.clearAttributes()
        playerSession.version = playerSession.version + 1
        session = playerSession.build()
    }

    override fun createTerminateNotifications(
        reason: SessionTerminationReason,
        logger: Logger
    ): Pair<ExternalNotification, Notification> {
        val playerSession = session
        logger.debug("terminating player session for pragmaSessionKey {}, reason: {}", playerSession.sessionKey, reason)
        val externalNotification = PlayerSessionRpc.SessionTerminatedV1Notification.newBuilder()
            .setPlayerSession(playerSession)
            .setReason(reason)
            .build()
        val internalNotification = PlayerSessionRpc.SessionTerminatedForServiceV1Notification.newBuilder()
            .setPlayerSession(playerSession)
            .setReason(reason)
            .build()
        return Pair(buildExternalNotification(externalNotification), internalNotification as Notification)
    }

    override fun logStopConnection(logger: Logger, connection: SessionContext.ComparableConnection) {
        logger.debug("stopping player connection for pragmaSessionKey {}, connection {}", session.sessionKey, connection)
    }

    override suspend fun notifyOfSoftReconnect(notificationManager: InternalNotificationManager) {
        notificationManager.notifyInternal(
            PlayerSessionRpc.SessionSoftReconnectedV1Notification.newBuilder().setPlayerSession(session).build()
        )
    }

    override suspend fun notifyOfSoftDisconnect(notificationManager: InternalNotificationManager) {
        notificationManager.notifyInternal(
            PlayerSessionRpc.SessionSoftDisconnectedV1Notification.newBuilder().setPlayerSession(session).build()
        )
    }

    override val pragmaSession get() = session.toPragmaSession()
    override val pragmaId get() = session.pragmaId!!
}

class PartnerPragmaSessionWrapper(private var session: PartnerSession) : PragmaSessionWrapperBase(SessionType.PARTNER) {
    override fun recordLogin(metricsManager: MetricsManager) {
        metricsManager.metricsCounter("pragma.session.partner.login").increment()
    }

    override fun recordLogout(metricsManager: MetricsManager) {
        metricsManager.metricsCounter("pragma.session.partner.logout").increment()
    }

    override fun createTerminateNotifications(
        reason: SessionTerminationReason,
        logger: Logger
    ): Pair<ExternalNotification, Notification> {
        logger.debug("terminating partner session for pragmaId {}, reason: {}", session.pragmaId, reason)
        val externalNotification = PartnerSessionRpc.PartnerSessionTerminatedV1Notification.newBuilder()
            .setPartnerSession(session)
            .setReason(reason)
            .build()
        val internalNotification = PartnerSessionRpc.PartnerSessionTerminatedForServiceV1Notification.newBuilder()
            .setPartnerSession(session)
            .setReason(reason)
            .build()
        return Pair(buildExternalNotification(externalNotification), internalNotification)
    }

    override fun logStopConnection(logger: Logger, connection: SessionContext.ComparableConnection) {
        logger.debug("stopping partner connection for pragmaId {}, connection {}", session.pragmaId, connection)
    }

    override val pragmaSession get() = session.toPragmaSession()
    override val pragmaId get() = session.pragmaId!!
}

class OperatorPragmaSessionWrapper(private var session: OperatorSession) : PragmaSessionWrapperBase(SessionType.OPERATOR) {
    override val pragmaSession get() = session.toPragmaSession()
    override val pragmaId get() = session.pragmaId!!
}

class ServicePragmaSessionWrapper(private var session: ServiceSession) : PragmaSessionWrapperBase(SessionType.SERVICE) {
    override val pragmaSession get() = session.toPragmaSession()
    override val pragmaId get() = throw PragmaRoutingException("routing SESSION_PRAGMA_ID can only be done on session with pragma_id, found $session")
}
