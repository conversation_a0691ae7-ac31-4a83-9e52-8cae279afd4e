package pragma.config
import kotlin.reflect.KClass
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import pragma.ConfigBroadcaster
import pragma.jobs.BackgroundManager
import pragma.services.NodeService
import pragma.services.Service
import pragma.settings.Decrypter
import pragma.settings.PragmaSpecies
import pragma.utils.ReflectionsUtil

interface ConfigManager {
    val pragmaConfig: PragmaConfig
    fun configFor(serviceConfigClass: String): ConfigObject<*>
}

class ConfigManagerImpl(
    private val configStringProviders: ConfigStringProviders,
    val pragmaSpecies: PragmaSpecies,
    val backgroundManager: BackgroundManager,
    private val pragmaConfigProvider: PragmaConfigProvider,
    private val gatewayList: Collection<KClass<out NodeService>>,
    val reflectionsUtil: ReflectionsUtil = ReflectionsUtil.defaultInstance,
    private val configBroadcaster: ConfigBroadcaster = ConfigBroadcaster(),
    private val logger: Logger = LoggerFactory.getLogger(ConfigManager::class.java)
) : ConfigManager {
    override lateinit var pragmaConfig: PragmaConfig
    val decrypter: Decrypter get() = pragmaConfigProvider.decrypter

    suspend fun addPluginListener(listener: Service) = configBroadcaster.addPluginListener(listener)
    suspend fun addServiceListener(listener: Service) {
        val config = configFor(reflectionsUtil.serviceConfigClassByHandlerClass[listener::class]!!.simpleName!!)
        configBroadcaster.addServiceListener(listener, config)
    }

    suspend fun run() {
        load()
        backgroundManager.runForever(::reloadConfig, 15000)
    }

    suspend fun load() {
        loadClasses()
        reloadConfig()
    }

    internal suspend fun reloadConfig() {
        if (configStringProviders.loadConfigStringProviders()) {
            logger.info("Reading config files")
            setConfig(readPragmaConfig())
        }
    }

    suspend fun addCoreListener(listener: CoreConfigHandler) = configBroadcaster.addCoreListener(listener, pragmaConfig.core)

    //this is intentionally private to prevent changing configs while a server is running
    private suspend fun setConfig(newConfig: PragmaConfig) {
        validate(newConfig)
        pragmaConfig = newConfig
        configBroadcaster.broadcast(newConfig)
    }

    private fun readPragmaConfig(): PragmaConfig {
        return pragmaConfigProvider.get(
            configStringProviders.getYamlConfigContexts(pragmaSpecies, decrypter),
            gatewayList
        )
    }

    override fun configFor(serviceConfigClass: String): ConfigObject<*> {
        return pragmaConfig.serviceConfigs[serviceConfigClass] ?: error("no config for $serviceConfigClass")
    }

    private fun loadClasses() {
        pragmaConfigProvider.loadClasses()
    }

    fun providedConfigs(): String {
        return configStringProviders.providedConfigs()
    }

    fun validate(newConfig: PragmaConfig) {
        val configValidationErrors = newConfig.getValidationErrors()

        val nodes = configStringProviders.getYamlConfigContexts(pragmaSpecies, decrypter)

        if (nodes.any { it.hasProblems } || configValidationErrors.isNotEmpty()) {
            val resolvedConfigMessage = "Config=${newConfig.toObjectNode().toPrettyString()}"
            val configFileProblems = nodes.filter { it.hasProblems }.joinToString(";${System.lineSeparator()}") { it.problemMessage }
            val invalidConfigFieldsProblems = if (configValidationErrors.isEmpty()) "" else configValidationErrors.joinToString("${System.lineSeparator()}\t", "Resolved config problems:${System.lineSeparator()}\t")

            val messages = listOf(resolvedConfigMessage, configFileProblems, invalidConfigFieldsProblems).filter { it.isNotEmpty() }.joinToString("${System.lineSeparator()}")
            if (nodes.any { it.hasMajorProblems } || configValidationErrors.isNotEmpty())
                error(messages)
            else {
                logger.warn(messages)
            }
        }
    }
}
