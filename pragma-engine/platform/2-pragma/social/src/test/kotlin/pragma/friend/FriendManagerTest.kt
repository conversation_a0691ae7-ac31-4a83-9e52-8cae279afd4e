package pragma.friend

import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import pragma.AccountTestFactory.gameIdentityList
import pragma.AccountTestFactory.playerIdUuid
import pragma.AccountTestFactory.socialIdUuid
import pragma.AccountTestFactory.socialIdentityProto
import pragma.PragmaCoreTestFactory.anyUuid
import pragma.PragmaError
import pragma.PragmaException
import pragma.PragmaResult
import pragma.ProtoTestFactory
import pragma.ProtoTestFactory.gameId
import pragma.ServiceError
import pragma.account.AccountCommon
import pragma.account.AccountCommonTestFactory
import pragma.account.SocialIdentity
import pragma.auth.PragmaDisplayName
import pragma.friend.FriendResultStatus.InviteeDisabledInvites
import pragma.friend.FriendResultStatus.InviteeReceivedInvitesFull
import pragma.friend.FriendRpc.FriendListUpdateV1Notification
import pragma.friend.FriendTestFactory.addFriendsUpdateRequest
import pragma.friend.FriendTestFactory.addReceivedBlockUpdateServiceRequest
import pragma.friend.FriendTestFactory.addReceivedInvitesUpdateRequest
import pragma.friend.FriendTestFactory.addReceivedInvitesUpdateServiceRequest
import pragma.friend.FriendTestFactory.addReceivedUnblockUpdateServiceRequest
import pragma.friend.FriendTestFactory.addSentInvitesUpdateRequest
import pragma.friend.FriendTestFactory.friendListUpdateRequest
import pragma.friend.FriendTestFactory.friendListUpdateV1Notification
import pragma.friend.FriendTestFactory.friendOverview
import pragma.friend.FriendTestFactory.friendRemoveUpdateNotification
import pragma.friend.FriendTestFactory.friendSetAcceptIncomingInvitesUpdateNotification
import pragma.friend.FriendTestFactory.presence
import pragma.friend.FriendTestFactory.removeFriendServiceRequest
import pragma.friend.FriendTestFactory.removeReceivedBlockServiceRequest
import pragma.friend.FriendTestFactory.removeReceivedInviteUpdateRequest
import pragma.friend.FriendTestFactory.removeSentInviteServiceRequest
import pragma.friend.FriendTestFactory.sentInviteRemoveUpdateNotification
import pragma.friend.dao.FriendDaoNodeService
import pragma.gameshardcache.GameShardCacheNodeService
import pragma.logging.NullLogger
import pragma.presence.ExtRichPresence
import pragma.presence.ExtRichPresenceBackendRequest
import pragma.presence.ExtRichPresencePlayerRequest
import pragma.presence.PresencePlugin
import pragma.rpcs.PragmaFailure
import pragma.services.Service
import pragma.settings.BackendType
import pragma.utils.SocialId
import pragma.utils.TimeProxy
import pragma.utils.UUIDProxy
import pragma.utils.coAssertThrows
import pragma.utils.coAssertThrowsPragmaError
import pragma.utils.toFixed128
import pragma.utils.toUUID

@OptIn(ExperimentalCoroutinesApi::class)
internal class FriendManagerTest {
    private val service: Service = mockk(relaxed = true)
    private val identityCache: IdentityCache = mockk()
    private val friendDao: FriendDaoNodeService = mockk()
    private val friendCache: FriendCache = FriendCache().apply { init(service, friendDao, identityCache) }
    private val friendClient: FriendClient = mockk(relaxUnitFun = true)
    private val gameIdentities = gameIdentityList(123)
    private val uuidProxy: UUIDProxy = mockk(relaxed = true)

    private val gameShardCacheNodeService: GameShardCacheNodeService = mockk()
    private val presenceCache: PresenceCache = PresenceCache(uuidProxy = uuidProxy).apply { init(service, gameShardCacheNodeService) }

    private val inviterOffset = 1
    private val inviterOverview = friendOverview(inviterOffset)
    private val inviterSocialId = inviterOverview.socialId.toUUID()
    private val inviterSocialIdentity = SocialIdentity(socialIdentityProto(inviterOffset, gameIdentities = gameIdentities))

    private val inviteeOffset = 2
    private val inviteeOverview = friendOverview(inviteeOffset)
    private val invitee = AccountCommonTestFactory.pragmaAccountOverview(inviteeOffset)
    private val inviteeDisplayName = PragmaDisplayName(inviteeOverview.displayName)
    private val inviteeSocialId = invitee.pragmaSocialId
    private val inviteeSocialIdentity = SocialIdentity(socialIdentityProto(inviteeOffset, gameIdentities = gameIdentities))

    private lateinit var config: FriendServiceConfig
    private val timeProxy: TimeProxy = mockk(relaxUnitFun = true)
    private var incrementingCurrentTimeMillis: Long = 0

    private var expectedVersion = 0L
    private var expectedPresenceId = anyUuid()

    private val ext: ExtRichPresence = ExtRichPresence.getDefaultInstance()
    private val presencePlugin: PresencePlugin = mockk(relaxUnitFun = true)

    private val gameTitleId = gameId(1)

    private lateinit var testObj: FriendManager

    @BeforeEach
    fun setUp() {
        testObj = FriendManager(logger = NullLogger())
        testObj.init(service, identityCache, friendCache, friendClient, presenceCache, presencePlugin)
        config = FriendServiceConfig.getFor(BackendType.SOCIAL)
        testObj.onConfigChanged(config)

        every { gameShardCacheNodeService.getGameTitleId(any()) } returns gameTitleId.toUUID()

        coEvery { timeProxy.currentEpochMillis() } coAnswers { ++incrementingCurrentTimeMillis }

        coEvery { friendDao.getFriendList(any()) } coAnswers { FriendList(firstArg(), timeProxy = timeProxy) }
        coJustRun { friendDao.writeFriendListUpdates(any(), any()) }
        coEvery { identityCache.hasCachedIdentity(any()) } returns false

        coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Success(
            FriendRpc.FriendListUpdateServiceV1Response.newBuilder().setResponse(FriendResultStatus.Ok.name).build()
        )

        every { uuidProxy.randomUUID() } returns expectedPresenceId

        expectedVersion = 0
    }

    @Test
    fun `player should receive a notification when they receive an invite`() = runTest {
        coEvery { identityCache.getSocialIdentity(inviterSocialId) } returns inviterSocialIdentity
        testObj.registerForNotifications(inviteeSocialId)

        withVersionIncrement {
            testObj.friendListUpdate(
                inviteeSocialId,
                FriendListUpdateRequest(addReceivedInvitesUpdateRequest(inviterOffset, 1, gameIdentities))
            )
        }

        val friendList = testObj.getFriends(inviteeSocialId)
        assertTrue(friendList.wasInviteReceivedFrom(inviterSocialId))

        val expectedNotification =
            FriendTestFactory.friendInviteReceivedNotification(inviterOffset, gameIdentities, version = expectedVersion)

        coVerify { service.notifyPlayers(listOf(inviteeSocialId), expectedNotification) }
    }

    @Test
    fun `denying incoming friend invites shows up on the friend list and friend list updates`() = runTest {
        val result = withVersionIncrementResult { testObj.setAcceptIncomingFriendInvites(invitee.pragmaSocialId, false) }
        assertEquals(friendSetAcceptIncomingInvitesUpdateNotification(false, expectedVersion, gameIdentities), result.updates)

        val friendList = testObj.getFriends(inviteeSocialId)
        assertFalse(friendList.acceptNewFriendInvites)
    }

    @Test
    fun `deregisterForNotification notifies friends of players`() = runTest {
        val friendsUpdateRequest = FriendListUpdateRequest(addFriendsUpdateRequest(2))
        trainIdentityCache(1, 5)
        withVersionIncrement { testObj.friendListUpdate(inviterSocialId, friendsUpdateRequest) }
        friendsUpdateRequest.addFriends.forEach {
            testObj.registerForNotifications(it.socialId.toUUID())
        }

        testObj.deregisterForNotifications(inviterSocialId)

        val notification = FriendRpc.PresenceRemoveV1Notification.newBuilder().setSocialId(inviterSocialId.toFixed128()).build()
        coVerify { service.notifyPlayers(friendsUpdateRequest.addFriends.map { it.socialId.toUUID() }, notification) }
    }

    @Nested
    inner class SendInvite {
        @Test
        fun `sendInvite with social id should notify an invitee when they are sent an invite and update the cache of the inviter`() =
            runTest {
                trainAccountLookupBySocialId(inviteeSocialId, inviteeSocialIdentity)
                trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)

                val friendList = testObj.getFriends(inviterSocialId)
                assertFalse(friendList.wasInviteSentTo(inviteeSocialId))

                val result = withVersionIncrementResult { testObj.sendInvite(inviterSocialId, inviteeSocialId) }

                val expectedUpdates = FriendTestFactory.friendInviteSentNotification(
                    inviteeOffset,
                    gameIdentities,
                    version = expectedVersion)
                assertEquals(expectedUpdates, result.updates)
                assertEquals(FriendResultStatus.Ok, result.status)
                assertTrue(friendList.wasInviteSentTo(inviteeSocialId))
                coVerify {
                    friendClient.updateFriendList(
                        addReceivedInvitesUpdateServiceRequest(
                            inviteeOffset,
                            inviterOffset,
                            gameIdentities
                        )
                    )
                }
            }

        @Test
        fun `sendInvite should notify an invitee when they are sent an invite and update the cache of the inviter`() = runTest {
            trainAccountLookupByDisplayName(inviteeDisplayName, inviteeSocialIdentity)
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)

            val friendList = testObj.getFriends(inviterSocialId)
            assertFalse(friendList.wasInviteSentTo(inviteeSocialId))

            val result = withVersionIncrementResult { testObj.sendInvite(inviterSocialId, inviteeDisplayName) }

            val expectedUpdates = FriendTestFactory.friendInviteSentNotification(inviteeOffset, gameIdentities, version = expectedVersion)
            assertEquals(expectedUpdates, result.updates)
            assertEquals(FriendResultStatus.Ok, result.status)
            assertTrue(friendList.wasInviteSentTo(inviteeSocialId))
            coVerify {
                friendClient.updateFriendList(addReceivedInvitesUpdateServiceRequest(inviteeOffset, inviterOffset, gameIdentities))
            }

        }

        @Test
        fun `sendInvite with social id should respond with InviteeNotFound if passed an unrecognized invitee`() = runTest {
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)
            trainAccountLookupBySocialId(inviteeSocialId, null)

            val result = testObj.sendInvite(inviterSocialId, inviteeSocialId)

            assertEquals(FriendResultStatus.SocialIdentityNotFound, result.status)
            assertEquals(expectedVersion, result.version)
            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }
        }

        @Test
        fun `sendInvite should throw an exception if the inviter social id is not found`() = runTest {
            trainAccountLookupBySocialId(inviterSocialId, null)
            trainAccountLookupBySocialId(inviteeSocialId, inviteeSocialIdentity)

            coAssertThrowsPragmaError(PragmaError.FriendService_SocialIdentityNotFound) {
                testObj.sendInvite(inviterSocialId, inviteeDisplayName)
            }

            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }
        }

        @Test
        fun `sendInvite should respond with InviteeAlreadyFriend if invitee already a friend`() = runTest {
            trainAccountLookupByDisplayName(inviteeDisplayName, inviteeSocialIdentity)
            coEvery { identityCache.getSocialIdentity(inviteeSocialId) } returns inviteeSocialIdentity
            coEvery { identityCache.getSocialIdentity(inviterSocialId) } returns inviterSocialIdentity

            testObj.friendListUpdate(inviterSocialId, FriendListUpdateRequest(addFriendsUpdateRequest(inviteeOffset, count = 1)))
            val result = withVersionIncrementResult { testObj.sendInvite(inviterSocialId, inviteeDisplayName) }

            assertEquals(FriendResultStatus.InviteeAlreadyFriend, result.status)
            assertEquals(expectedVersion, result.version)
            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }
        }

        @Test
        fun `sendInvite should respond with the received error from friendListUpdate if it is not successful`() = runTest {
            trainAccountLookupByDisplayName(inviteeDisplayName, inviteeSocialIdentity)
            coEvery { identityCache.getSocialIdentity(inviteeSocialId) } returns inviteeSocialIdentity
            coEvery { identityCache.getSocialIdentity(inviterSocialId) } returns inviterSocialIdentity
            coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder().setResponse(FriendResultStatus.FriendListFull.name).build()
            )

            testObj.sendInvite(inviterSocialId, inviteeDisplayName)
            config.maxPendingSentInvites = 1

            val result = testObj.sendInvite(inviterSocialId, inviteeDisplayName)

            assertEquals(FriendResultStatus.FriendListFull, result.status)
            assertEquals(expectedVersion, result.version)
        }

        @Test
        fun `sendInvite returns SentInvitesFull when max pending sent invites exceeded, and does not send additional invites`() = runTest {
            val invitee1SocialId = socialIdUuid(5)
            val invitee2SocialId = socialIdUuid(6)
            trainAccountLookupBySocialId(inviteeSocialId, inviteeSocialIdentity)
            trainAccountLookupBySocialId(invitee1SocialId, SocialIdentity(socialIdentityProto(5)))
            trainAccountLookupBySocialId(invitee2SocialId, SocialIdentity(socialIdentityProto(6)))
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)

            withVersionIncrementResult { testObj.sendInvite(inviterSocialId, invitee1SocialId) }
            withVersionIncrementResult { testObj.sendInvite(inviterSocialId, invitee2SocialId) }

            // We clear mocks here so we don't count the updateFriendList calls that happen during sendInvite
            clearMocks(friendClient)

            config.maxPendingSentInvites = 1
            val result = withVersionIncrementResult { withVersionIncrementResult { testObj.sendInvite(inviterSocialId, inviteeSocialId) } }

            assertEquals(FriendResultStatus.SentInvitesFull, result.status)

            coVerify(exactly = 0) { service.notifyPlayers(listOf(inviterSocialId), any()) }
            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }

            val friendList = testObj.getFriends(inviterSocialId)

            assertTrue(friendList.wasInviteSentTo(invitee1SocialId))
            assertTrue(friendList.wasInviteSentTo(invitee2SocialId))
            assertFalse(friendList.wasInviteSentTo(inviteeSocialId))
        }

        @Test
        fun `sendInvite should respond with InviteeReceivedInvitesFull if invitee's received invites list is full`() = runTest {
            config.maxPendingReceivedInvites = 1

            trainAccountLookupByDisplayName(inviteeDisplayName, inviteeSocialIdentity)
            val firstInviter = AccountCommonTestFactory.pragmaAccountOverview(99)
            coEvery { identityCache.getSocialIdentity(firstInviter.pragmaSocialId) } returns SocialIdentity(
                socialIdentityProto(
                    99,
                    gameIdentities = gameIdentities
                )
            )

            val firstResult = testObj.sendInvite(firstInviter.pragmaSocialId, inviteeDisplayName)
            assertEquals(FriendResultStatus.Ok, firstResult.status)

            coEvery {
                friendClient.updateFriendList(any())
            } returns
                PragmaResult.Success(
                    FriendRpc.FriendListUpdateServiceV1Response.newBuilder()
                        .setResponse(InviteeReceivedInvitesFull.name)
                        .build()
                )

            coEvery { identityCache.getSocialIdentity(inviterSocialId) } returns inviterSocialIdentity
            val secondResult = testObj.sendInvite(inviterSocialId, inviteeDisplayName)
            assertEquals(InviteeReceivedInvitesFull, secondResult.status)
            assertEquals(expectedVersion, secondResult.version)
        }

        @Test
        fun `sendInvite should respond with BlockedByInvitee if invitee has blocked the inviter`() = runTest {
            trainAccountLookupBySocialId(inviteeSocialId, inviteeSocialIdentity)
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)
            val friendList = testObj.getFriends(inviterSocialId)
            friendList.blockReceived(FriendOverview(inviteeOverview))

            val result = testObj.sendInvite(inviterSocialId, inviteeSocialId)

            assertEquals(FriendResultStatus.BlockedByInvitee, result.status)
            assertEquals(expectedVersion, result.version)
            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }
            assertFalse(testObj.getFriends(inviterSocialId).wasInviteSentTo(inviteeSocialId))
            coVerify(exactly = 0) { service.notifyPlayers(listOf(inviterSocialId), any()) }
        }

        @Test
        fun `sendInvite should respond with InviteeIsBlocked if inviter has blocked the invitee`() = runTest {
            trainAccountLookupBySocialId(inviteeSocialId, inviteeSocialIdentity)
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)
            val friendList = testObj.getFriends(inviterSocialId)
            friendList.block(FriendOverview(inviteeOverview))

            val result = testObj.sendInvite(inviterSocialId, inviteeSocialId)

            assertEquals(FriendResultStatus.InviteeIsBlocked, result.status)
            assertEquals(expectedVersion, result.version)
            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }
            assertFalse(testObj.getFriends(inviterSocialId).wasInviteSentTo(inviteeSocialId))
            coVerify(exactly = 0) { service.notifyPlayers(listOf(inviterSocialId), any()) }
        }

        @Test
        fun `sendInvite should respond with SelfOperationNotAllowed if inviter invites themself`() = runTest {
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)

            val result = testObj.sendInvite(inviterSocialId, inviterSocialId)

            assertEquals(FriendResultStatus.SelfOperationNotAllowed, result.status)
            assertEquals(expectedVersion, result.version)
            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }
            assertFalse(testObj.getFriends(inviterSocialId).wasInviteSentTo(inviteeSocialId))
            coVerify(exactly = 0) { service.notifyPlayers(listOf(inviterSocialId), any()) }
        }
    }

    @Nested
    inner class CancelInvite {
        @Test
        fun `cancelInvite removes the specified invite and notifies both players`() = runTest {
            val friends = testObj.getFriends(inviterSocialId)
            friends.inviteSent(FriendOverview(friendOverview(inviteeOffset)))
            assertTrue(friends.wasInviteSentTo(inviteeSocialId))

            val result = withVersionIncrementResult { testObj.cancelInvite(inviterSocialId, inviteeSocialId) }

            val expectedInviteRemoved = sentInviteRemoveUpdateNotification(
                inviteeOffset,
                expectedVersion,
                gameIdentities = gameIdentities
            )
            assertEquals(FriendResultStatus.Ok, result.status)
            assertEquals(expectedInviteRemoved, result.updates)
            coVerify { friendClient.updateFriendList(removeReceivedInviteUpdateRequest(inviteeSocialId, inviterSocialId)) }

            val inviterFriends = testObj.getFriends(inviterSocialId)

            assertFalse(inviterFriends.wasInviteSentTo(inviteeSocialId))
        }

        @Test
        fun `cancelInvite returns InviteNotFound when the requested invited player is not found`() = runTest {
            val result = testObj.cancelInvite(inviterSocialId, inviteeSocialId)

            assertEquals(FriendResultStatus.InviteNotFound, result.status)
            assertEquals(expectedVersion, result.version)
        }

        @Test
        fun `cancelInvite does not update friend list if updateFriendList returns expected error`() = runTest {
            val friends = testObj.getFriends(inviterSocialId)
            friends.inviteSent(FriendOverview(friendOverview(inviteeOffset)))

            val expectedFriendResultStatus = FriendResultStatus.InviteNotFound
            coEvery {
                friendClient.updateFriendList(removeReceivedInviteUpdateRequest(inviteeSocialId, inviterSocialId))
            } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder().setResponse(expectedFriendResultStatus.name).build()
            )

            val actualResult = testObj.cancelInvite(inviterSocialId, inviteeSocialId)

            assertEquals(expectedFriendResultStatus, actualResult.status)
            assertEquals(expectedVersion, actualResult.version)
            assertTrue(friends.wasInviteSentTo(inviteeSocialId))
        }

        @Test
        fun `cancelInvite does not update friend list if updateFriendList returns an unexpected error`() = runTest {
            val friends = testObj.getFriends(inviterSocialId)
            friends.inviteSent(FriendOverview(friendOverview(inviteeOffset)))

            coEvery {
                friendClient.updateFriendList(removeReceivedInviteUpdateRequest(inviteeSocialId, inviterSocialId))
            } returns PragmaResult.Failure(PragmaFailure(ServiceError.getDefaultInstance()))

            coAssertThrowsPragmaError(PragmaError.FriendService_InternalError) {
                testObj.cancelInvite(inviterSocialId, inviteeSocialId)
            }

            assertTrue(friends.wasInviteSentTo(inviteeSocialId))
            coVerify(exactly = 0) { service.notifyPlayers(any(), any()) }
        }
    }

    @Nested
    inner class RespondToInvite {
        @Test
        fun `respondToInvite adds a player as a friend when an invite is present and accepted`() = runTest {
            trainCacheAndAddInvite()

            val inviteePresence =
                presence(inviteeOffset, expectedPresenceId.toFixed128(), gameIdentities.first().pragmaPlayerId, gameTitleId, gameIdentities.first().gameShardId)
            val inviterPresence =
                presence(inviterOffset, expectedPresenceId.toFixed128(), gameIdentities.first().pragmaPlayerId, gameTitleId, gameIdentities.first().gameShardId)

            testObj.setPresence(
                inviteeSocialId,
                inviteePresence.gameShardId.toUUID(),
                BasicPresence.valueOf(inviteePresence.basicPresence),
                ExtRichPresencePlayerRequest.getDefaultInstance(),
                null,
                false
            )
            testObj.setPresence(
                inviterSocialId,
                inviterPresence.gameShardId.toUUID(),
                BasicPresence.valueOf(inviterPresence.basicPresence),
                ExtRichPresencePlayerRequest.getDefaultInstance(),
                null,
                false
            )

            val request = FriendTestFactory.inviteAcceptedServiceRequest(inviterOffset, inviteeOffset, gameIdentities, inviteePresence)

            coEvery { friendClient.updateFriendList(request) } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder()
                    .setResponse(FriendResultStatus.Ok.name)
                    .setFriend(CachedFriend(inviterSocialIdentity).toProto(listOf(inviterPresence)))
                    .build()
            )

            val result = withVersionIncrementResult { testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, true) }
            assertEquals(FriendResultStatus.Ok, result.status)

            coVerify { friendClient.updateFriendList(request) }

            val inviteeFriendAddedUpdates = FriendTestFactory.friendAddedNotification(
                inviterOffset,
                gameIdentities,
                gameTitleId,
                version = expectedVersion,
                presence = inviterPresence
            )
            assertEquals(inviteeFriendAddedUpdates, result.updates)

            val duplicateAttempt = testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, true)
            assertEquals(FriendResultStatus.InviteNotFound, duplicateAttempt.status)

            val friendList = testObj.getFriends(inviteeSocialId)
            assertTrue(friendList.isFriend(inviterSocialId))
        }

        @Test
        fun `respondToInvite does not add a player as a friend when an invite is rejected`() = runTest {
            trainCacheAndAddInvite()

            val result = withVersionIncrementResult { testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, false) }
            assertEquals(FriendResultStatus.Ok, result.status)

            val friendRequestRejectedUpdate =
                FriendTestFactory.friendRequestRejectedNotification(inviterOffset, gameIdentities, version = expectedVersion)
            assertEquals(friendRequestRejectedUpdate, result.updates)

            val request = removeSentInviteServiceRequest(inviterOffset, inviteeOffset)
            coVerify { friendClient.updateFriendList(request) }

            val friendList = testObj.getFriends(inviteeSocialId)
            assertFalse(friendList.isFriend(inviterSocialId))
        }

        @Test
        fun `respondToInvite returns InviteNotFound if no invite was present for a given inviter`() = runTest {
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)
            trainAccountLookupBySocialId(inviteeSocialId, inviteeSocialIdentity)

            val result = testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, true)

            assertEquals(FriendResultStatus.InviteNotFound, result.status)
            assertEquals(expectedVersion, result.version)

            coVerify(exactly = 0) { service.notifyPlayers(any(), any()) }

        }

        @Test
        fun `respondToInvite should return SocialIdentityNotFound if the inviter no longer (or never) exists`() = runTest {
            trainAccountLookupBySocialId(inviterSocialId, SocialIdentity(socialIdentityProto(inviterOffset)))
            testObj.registerForNotifications(invitee.pragmaSocialId)

            withVersionIncrement {
                testObj.friendListUpdate(inviteeSocialId, FriendListUpdateRequest(addReceivedInvitesUpdateRequest(inviterOffset, count = 1)))
            }

            trainAccountLookupBySocialId(inviteeSocialId, inviteeSocialIdentity)
            trainAccountLookupBySocialId(inviterSocialId, null)

            val result = testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, true)

            assertEquals(FriendResultStatus.SocialIdentityNotFound, result.status)
            assertEquals(expectedVersion, result.version)

            // expects one call from the inviteReceived invocation, but the one in respondToInvite must be skipped.
            coVerify(exactly = 1) { service.notifyPlayers(listOf(invitee.pragmaSocialId), any()) }
        }

        @Test
        fun `respondToInvite should throw if the invitee no longer (or never) exists`() = runTest {
            trainAccountLookupBySocialId(inviteeSocialId, null)
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)

            coAssertThrowsPragmaError(PragmaError.FriendService_SocialIdentityNotFound) {
                testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, true)
            }
        }

        @Test
        fun `respondToInvite responds with FriendListFull when friend list is full and does not save`() = runTest {
            coEvery { identityCache.getSocialIdentity(inviterSocialId) } returns inviterSocialIdentity
            coEvery { identityCache.getSocialIdentity(inviteeSocialId) } returns inviteeSocialIdentity

            config.maxFriendListSize = 0

            withVersionIncrement {
                trainAccountLookupBySocialId(inviterSocialId, SocialIdentity(socialIdentityProto(inviterOffset)))
            }

            testObj.friendListUpdate(inviteeSocialId, FriendListUpdateRequest(addReceivedInvitesUpdateRequest(inviterOffset, count = 1)))

            val result = testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, true)
            assertEquals(FriendResultStatus.FriendListFull, result.status)
            assertEquals(expectedVersion, result.version)

            val friendList = testObj.getFriends(inviteeSocialId)
            assertFalse(friendList.isFriend(inviterSocialId))
        }

        @Test
        fun `respondToInvite returns a FriendResponse not OK if the updateFriendList does not succeed`() = runTest {
            trainCacheAndAddInvite()
            val expectedFriendResultStatusError = FriendResultStatus.FriendListFull
            coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder()
                    .setResponse(expectedFriendResultStatusError.name)
                    .build()
            )

            val result = testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, false)

            assertEquals(expectedFriendResultStatusError, result.status)
            assertEquals(expectedVersion, result.version)
            val friendList = testObj.getFriends(inviteeSocialId)
            assertFalse(friendList.isFriend(inviterSocialId))
            coVerify(exactly = 0) { service.notifyPlayers(any(), any()) }
        }

        @Test
        fun `respondToInvite throws an error if the updateFriendList has an unexpected error`() = runTest {
            trainCacheAndAddInvite()
            coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Failure(PragmaFailure(ServiceError.getDefaultInstance()))

            coAssertThrowsPragmaError(PragmaError.FriendService_InternalError) {
                withVersionIncrementResult { testObj.respondToInvite(invitee.pragmaSocialId, inviterSocialId, false) }
            }

            val friendList = testObj.getFriends(inviteeSocialId)
            assertFalse(friendList.isFriend(inviterSocialId))
            coVerify(exactly = 0) { service.notifyPlayers(any(), any()) }
        }
    }

    @Nested
    inner class Block {
        private val blockerOffset = 1
        private val blocker = friendOverview(blockerOffset)
        private val blockerSocialId = blocker.socialId.toUUID()
        private val blockerSocialIdentity = SocialIdentity(socialIdentityProto(blockerOffset, gameIdentities = gameIdentities))

        private val blockedOffset = 2

        //private val blockedProto = friendOverview(blockedOffset)
        private val blocked = AccountCommonTestFactory.pragmaAccountOverview(blockedOffset)

        //private val blockedDisplayName = PragmaDisplayName(blockedProto.displayName)
        private val blockedSocialId = blocked.pragmaSocialId
        private val blockedSocialIdentity = SocialIdentity(socialIdentityProto(blockedOffset, gameIdentities = gameIdentities))

        @Test
        fun `block should update the cache of the blocker`() = runTest {
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)
            trainAccountLookupBySocialId(blockerSocialId, blockerSocialIdentity)

            var friendList = testObj.getFriends(blockerSocialId)
            assertFalse(friendList.wasBlockSentTo(blockedSocialId))

            val result = withVersionIncrementResult { testObj.block(blockerSocialId, blockedSocialId) }
            assertEquals(FriendResultStatus.Ok, result.status)
            val removedAndBlockedUpdates = FriendTestFactory.friendListUpdate(
                blockedOffset = blockedOffset,
                blockedCount = 1,
                gameIdentities = gameIdentities,
                version = expectedVersion
            ).build()
            assertEquals(removedAndBlockedUpdates, result.updates)

            assertTrue(friendList.wasBlockSentTo(blockedSocialId))

            coVerify { friendClient.updateFriendList(addReceivedBlockUpdateServiceRequest(blockedOffset, blockerOffset, gameIdentities)) }

            friendList = testObj.getFriends(blockerSocialId)
            assertTrue(friendList.wasBlockSentTo(blockedSocialId))
            assertEquals(1, friendList.blockedList.size)
        }

        @Test
        fun `block should respond with InviteeNotFound if passed an unrecognized invitee`() = runTest {
            trainAccountLookupBySocialId(blockedSocialId, null)
            trainAccountLookupBySocialId(blockerSocialId, blockerSocialIdentity)

            val result = testObj.block(blockerSocialId, blockedSocialId)

            assertEquals(FriendResultStatus.SocialIdentityNotFound, result.status)
            assertEquals(expectedVersion, result.version)

            coVerify(exactly = 0) { friendClient.updateFriendList(any()) }
        }

        @Test
        fun `block should throw if passed an unrecognized inviter`() = runTest {
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)
            trainAccountLookupBySocialId(blockerSocialId, null)

            coAssertThrowsPragmaError(PragmaError.FriendService_SocialIdentityNotFound) {
                testObj.block(blockerSocialId, blockedSocialId)
            }
        }

        @Test
        fun `block should respond with the given service RPC FriendResponse if it is not Ok`() = runTest {
            coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder().setResponse(FriendResultStatus.FriendListFull.name).build()
            )
            trainAccountLookupBySocialId(blockerSocialId, blockerSocialIdentity)
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)

            val result = testObj.block(blockerSocialId, blockedSocialId)

            coVerify { friendClient.updateFriendList(any()) }
            assertEquals(FriendResultStatus.FriendListFull, result.status)
        }

        @Test
        fun `block should succeed if the account is already blocked`() = runTest {
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)
            trainAccountLookupBySocialId(blockerSocialId, blockerSocialIdentity)

            var friendList = testObj.getFriends(blockerSocialId)
            assertFalse(friendList.wasBlockSentTo(blockedSocialId))

            val result = withVersionIncrementResult { testObj.block(blockerSocialId, blockedSocialId) }
            assertEquals(FriendResultStatus.Ok, result.status)
            assertTrue(friendList.wasBlockSentTo(blockedSocialId))

            val blockAgainResult = withVersionIncrementResult { testObj.block(blockerSocialId, blockedSocialId) }
            assertEquals(FriendResultStatus.Ok, blockAgainResult.status)

            coVerify(exactly = 1) {
                friendClient.updateFriendList(
                    addReceivedBlockUpdateServiceRequest(
                        blockedOffset,
                        blockerOffset,
                        gameIdentities
                    )
                )
            }

            friendList = testObj.getFriends(blockerSocialId)
            assertTrue(friendList.wasBlockSentTo(blockedSocialId))
        }

        @Test
        fun `block should unfriend and notify the blocker`() = runTest {
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)
            trainAccountLookupBySocialId(blockerSocialId, blockerSocialIdentity)

            val friendsUpdateRequest = FriendListUpdateRequest(addFriendsUpdateRequest(blockedOffset, 1, gameIdentities))
            var friendList = testObj.getFriends(blockerSocialId)

            withVersionIncrement { testObj.friendListUpdate(blockerSocialId, friendsUpdateRequest) }
            assertEquals(1, friendList.size())

            val result = withVersionIncrementResult { testObj.block(blockerSocialId, blockedSocialId) }
            val removedAndBlockedUpdates = FriendTestFactory.friendListUpdate(
                friendsRemovedOffset = blockedOffset,
                friendsRemovedCount = 1,
                blockedOffset = blockedOffset,
                blockedCount = 1,
                gameIdentities = gameIdentities,
                version = expectedVersion
            ).build()
            assertEquals(removedAndBlockedUpdates, result.updates)
            assertEquals(FriendResultStatus.Ok, result.status)

            friendList = testObj.getFriends(blockerSocialId)
            assertTrue(friendList.wasBlockSentTo(blockedSocialId))
            assertFalse(friendList.isFriend(blockedSocialId))

            val blockedFriends = testObj.getFriends(blockedSocialId)
            assertFalse(blockedFriends.isFriend(blockerSocialId))

            coVerify { friendClient.updateFriendList(addReceivedBlockUpdateServiceRequest(blockedOffset, blockerOffset, gameIdentities)) }

        }

        @Test
        fun `block should throw with the received error from friendListUpdate if it is not successful`() = runTest {
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)
            trainAccountLookupBySocialId(blockerSocialId, blockerSocialIdentity)
            coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Failure(PragmaFailure(ServiceError.getDefaultInstance()))

            coAssertThrows(PragmaException::class) { testObj.block(blockerSocialId, blockedSocialId) }
        }

        @Test
        fun `block should respond with SelfOperationNotAllowed if blocker block themself`() = runTest {
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)
            trainAccountLookupBySocialId(blockerSocialId, blockerSocialIdentity)

            val result = testObj.block(blockerSocialId, blockerSocialId)

            assertEquals(FriendResultStatus.SelfOperationNotAllowed, result.status)
            assertEquals(expectedVersion, result.version)
        }
    }

    @Nested
    inner class Unblock {
        private val unblockerOffset = 1
        private val unblocker = friendOverview(unblockerOffset)
        private val unblockerSocialId = unblocker.socialId.toUUID()
        private val unblockerSocialIdentity = SocialIdentity(socialIdentityProto(unblockerOffset, gameIdentities = gameIdentities))

        private val unblockedOffset = 2
        private val unblocked = AccountCommonTestFactory.pragmaAccountOverview(unblockedOffset)
        private val unblockedSocialId = unblocked.pragmaSocialId
        private val unblockedSocialIdentity = SocialIdentity(socialIdentityProto(unblockedOffset, gameIdentities = gameIdentities))

        @Test
        fun `updates the cache of the user invoking it`() = runTest {
            trainAccountLookupBySocialId(unblockerSocialId, unblockerSocialIdentity)
            trainAccountLookupBySocialId(unblockedSocialId, unblockedSocialIdentity)

            val friendList = testObj.getFriends(unblockerSocialId)

            val blockResult = withVersionIncrementResult { testObj.block(unblockerSocialId, unblockedSocialId) }
            assertEquals(FriendResultStatus.Ok, blockResult.status)
            assertTrue(friendList.wasBlockSentTo(unblockedSocialId))

            val unblockResult = withVersionIncrementResult { testObj.unblock(unblockerSocialId, unblockedSocialId) }
            val unblockedUpdates = FriendTestFactory.friendListUpdate(
                unblockedOffset = unblockedOffset,
                unblockedCount = 1,
                gameIdentities = gameIdentities,
                version = expectedVersion
            ).build()
            assertEquals(unblockedUpdates, unblockResult.updates)
            assertEquals(FriendResultStatus.Ok, unblockResult.status)
            assertFalse(friendList.wasBlockSentTo(unblockedSocialId))
            assertEquals(0, friendList.blockedList.size)

            coVerify {
                friendClient.updateFriendList(
                    addReceivedUnblockUpdateServiceRequest(
                        unblockedOffset,
                        unblockerOffset,
                        gameIdentities
                    )
                )
            }
        }

        @Test
        fun `unblocking an account that blocked you does not revoke the received block`() = runTest {
            trainAccountLookupBySocialId(unblockerSocialId, unblockerSocialIdentity)
            trainAccountLookupBySocialId(unblockedSocialId, unblockedSocialIdentity)

            val friendList = testObj.getFriends(unblockerSocialId)

            testObj.friendListUpdate(unblockerSocialId, FriendListUpdateRequest(friendListUpdateRequest(addReceivedBlockCount = 1, addReceivedBlockOffset = unblockedOffset)))
            testObj.block(unblockerSocialId, unblockedSocialId)
            assertTrue(friendList.wasBlockSentTo(unblockedSocialId))
            assertTrue(friendList.wasBlockReceivedFrom(unblockedSocialId))

            testObj.unblock(unblockerSocialId, unblockedSocialId)
            assertFalse(friendList.wasBlockSentTo(unblockedSocialId))
            assertTrue(friendList.wasBlockReceivedFrom(unblockedSocialId))
        }

        @Test
        fun `responds with the given service RPC FriendResponse if it is not Ok`() = runTest {
            coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder().setResponse(FriendResultStatus.FriendListFull.name).build()
            )
            trainAccountLookupBySocialId(unblockerSocialId, unblockerSocialIdentity)
            trainAccountLookupBySocialId(unblockedSocialId, unblockedSocialIdentity)

            val result = testObj.unblock(unblockerSocialId, unblockedSocialId)

            coVerify { friendClient.updateFriendList(any()) }
            assertEquals(FriendResultStatus.FriendListFull, result.status)
            assertEquals(expectedVersion, result.version)
        }

        @Test
        fun `throws a FriendService_SocialIdentityNotFound if the unblocker identity is not found `() = runTest {
            trainAccountLookupBySocialId(unblockerSocialId, null)
            trainAccountLookupBySocialId(unblockedSocialId, unblockedSocialIdentity)

            coAssertThrowsPragmaError(PragmaError.FriendService_SocialIdentityNotFound) {
                testObj.unblock(unblockerSocialId, unblockedSocialId)
            }
        }

        @Test
        fun `returns a SocialIdentityNotFound error if the unblocked identity is not found `() = runTest {
            trainAccountLookupBySocialId(unblockerSocialId, unblockerSocialIdentity)
            trainAccountLookupBySocialId(unblockedSocialId, null)

            val result = testObj.unblock(unblockerSocialId, unblockedSocialId)

            assertEquals(FriendResultStatus.SocialIdentityNotFound, result.status)
            assertEquals(expectedVersion, result.version)
        }
    }

    @Nested
    inner class FriendListUpdate {
        @Test
        fun `friendListUpdate sends appropriate notifications and updates the players cache during friend and invite add or removal`() =
            runTest {
                val friendOffset = 2
                val friendsCount = 2

                val inviteePresence =
                    presence(inviteeOffset, gameIdentities.first().pragmaPlayerId, gameTitleId, gameIdentities.first().gameShardId)
                val inviterPresence =
                    presence(inviterOffset, gameIdentities.first().pragmaPlayerId, gameTitleId, gameIdentities.first().gameShardId)

                trainIdentityCache(friendOffset, friendsCount)
                testObj.setPresence(
                    inviteeSocialId,
                    inviteePresence.gameShardId.toUUID(),
                    BasicPresence.valueOf(inviteePresence.basicPresence),
                    ExtRichPresencePlayerRequest.getDefaultInstance(),
                    null,
                    false
                )
                trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)
                testObj.setPresence(
                    inviterSocialId,
                    inviterPresence.gameShardId.toUUID(),
                    BasicPresence.valueOf(inviterPresence.basicPresence),
                    ExtRichPresencePlayerRequest.getDefaultInstance(),
                    null,
                    false
                )

                val friendsUpdateRequest =
                    FriendListUpdateRequest(addFriendsUpdateRequest(friendOffset, friendsCount, gameIdentities, inviteePresence))

                testObj.registerForNotifications(inviterSocialId)

                val friends = testObj.getFriends(inviterSocialId)

                // add friends
                withVersionIncrement { testObj.friendListUpdate(inviterSocialId, friendsUpdateRequest) }
                assertEquals(friendsCount, friends.size())

                val friendsAddedNotification = FriendTestFactory.friendsAddedNotification(
                    friendOffset,
                    friendsCount,
                    gameIdentities,
                    version = expectedVersion,
                    presence = inviteePresence
                )
                coVerify { service.notifyPlayers(listOf(inviterSocialId), friendsAddedNotification) }

                //add sentInvites
                val sentInvitesOffset = friendOffset + friends.size()
                val sentInvitesCount = 3
                val sentInvitesFriendUpdateRequest =
                    FriendListUpdateRequest(addSentInvitesUpdateRequest(sentInvitesOffset, sentInvitesCount, gameIdentities))
                trainIdentityCache(sentInvitesOffset, sentInvitesCount)

                withVersionIncrement { testObj.friendListUpdate(inviterSocialId, sentInvitesFriendUpdateRequest) }
                assertEquals(sentInvitesCount, friends.sentInvitesSize())

                val sentInvitesNotification =
                    FriendTestFactory.sentInvitesNotification(
                        sentInvitesOffset,
                        sentInvitesCount,
                        gameIdentities,
                        version = expectedVersion
                    )
                coVerify { service.notifyPlayers(listOf(inviterSocialId), sentInvitesNotification) }

                //add receivedInvites
                val receivedInvitesOffset = sentInvitesOffset + friends.sentInvitesSize()
                val receivedInvitesCount = 4
                trainIdentityCache(receivedInvitesOffset, receivedInvitesCount)

                withVersionIncrement {
                    testObj.friendListUpdate(
                        inviterSocialId,
                        FriendListUpdateRequest(
                            addReceivedInvitesUpdateRequest(
                                receivedInvitesOffset,
                                receivedInvitesCount,
                                gameIdentities
                            )
                        )
                    )
                }
                assertEquals(receivedInvitesCount, friends.receivedInvitesSize())

                val receivedInvitesNotification =
                    FriendTestFactory.receivedInvitesNotification(
                        receivedInvitesOffset,
                        receivedInvitesCount,
                        gameIdentities,
                        version = expectedVersion
                    )
                coVerify { service.notifyPlayers(listOf(inviterSocialId), receivedInvitesNotification) }

                //all the removes
                val inviterIdentities = MutableList(friendsCount) { SocialIdentity(socialIdentityProto(friendOffset + it)) }
                inviterIdentities.addAll(List(sentInvitesCount) { SocialIdentity(socialIdentityProto(sentInvitesOffset + it)) })
                inviterIdentities.addAll(List(receivedInvitesCount) { SocialIdentity(socialIdentityProto(receivedInvitesOffset + it)) })
                inviterIdentities.forEach { inviter ->
                    coEvery {
                        identityCache.getSocialIdentity(inviterSocialId)
                    } returns inviter
                }
                withVersionIncrement {
                    testObj.friendListUpdate(
                        inviterSocialId, FriendListUpdateRequest(
                            friendListUpdateRequest(
                                removeFriendsOffset = friendOffset,
                                removeFriendsCount = friendsCount,
                                removeSentInvitesOffset = sentInvitesOffset,
                                removeSentInvitesCount = sentInvitesCount,
                                removeReceivedInvitesCount = receivedInvitesOffset,
                                removeReceivedInvitesOffset = receivedInvitesCount
                            )
                        )
                    )
                }
                val removedUpdates = friendListUpdateV1Notification(
                    FriendTestFactory.friendListUpdate(
                        friendsRemovedOffset = friendOffset,
                        friendsRemovedCount = friendsCount,
                        sentInvitesRemovedOffset = sentInvitesOffset,
                        sentInvitesRemovedCount = sentInvitesCount,
                        receivedInvitesRemovedCount = receivedInvitesOffset,
                        receivedInvitesRemovedOffset = receivedInvitesCount,
                        gameIdentities = gameIdentities,
                        version = expectedVersion
                    )
                )
                coVerify { service.notifyPlayers(listOf(inviterSocialId), removedUpdates) }

                assertEquals(0, friends.size())
                assertEquals(0, friends.sentInvitesSize())
                assertEquals(0, friends.receivedInvitesSize())
            }

        @Test
        fun `friendListUpdate removes friend and blocks account in cache, sends notification of removal only`() = runTest {
            val blockerOffset = 1
            val blockedOffset = 2

            val blocker = AccountCommonTestFactory.pragmaAccountOverview(blockerOffset)
            val blockerSocialId = blocker.pragmaSocialId

            val blocked = AccountCommonTestFactory.pragmaAccountOverview(blockedOffset)
            val blockedSocialId = blocked.pragmaSocialId
            val blockedSocialIdentity = SocialIdentity(socialIdentityProto(blockedOffset, gameIdentities = gameIdentities))

            trainIdentityCache(blockedOffset, 1)
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)

            testObj.registerForNotifications(blockedSocialId)

            val friendList = testObj.getFriends(blockedSocialId)
            val addFriendUpdateRequest = FriendListUpdateRequest(addFriendsUpdateRequest(blockerOffset, 1, gameIdentities))
            withVersionIncrement { testObj.friendListUpdate(blockedSocialId, addFriendUpdateRequest) }

            assertTrue(friendList.isFriend(blockerSocialId))
            assertTrue(friendList.blockedList.isEmpty())

            val blockedUpdateRequest =
                FriendListUpdateRequest(addReceivedBlockUpdateServiceRequest(blockedOffset, blockerOffset, gameIdentities).updates)
            withVersionIncrement { testObj.friendListUpdate(blockedSocialId, blockedUpdateRequest) }

            assertFalse(friendList.isFriend(blockerSocialId))
            assertTrue(friendList.blockedList.isEmpty())

            val removedUpdates = friendListUpdateV1Notification(
                FriendTestFactory.friendListUpdate(
                    friendsRemovedOffset = blockerOffset,
                    friendsRemovedCount = 1,
                    gameIdentities = gameIdentities,
                    version = expectedVersion
                )
            )

            coVerify { service.notifyPlayers(listOf(blockedSocialId), removedUpdates) }
        }

        @Test
        fun `friendListUpdate notifies with version even when updates are empty`() = runTest {
            val blockerOffset = 1
            val blockedOffset = 2

            val blocked = AccountCommonTestFactory.pragmaAccountOverview(blockedOffset)
            val blockedSocialId = blocked.pragmaSocialId
            val blockedSocialIdentity = SocialIdentity(socialIdentityProto(blockedOffset, gameIdentities = gameIdentities))

            trainIdentityCache(blockedOffset, 1)
            trainAccountLookupBySocialId(blockedSocialId, blockedSocialIdentity)

            testObj.registerForNotifications(blockedSocialId)

            val blockedUpdateRequest =
                FriendListUpdateRequest(addReceivedBlockUpdateServiceRequest(blockedOffset, blockerOffset, gameIdentities).updates)
            withVersionIncrement { testObj.friendListUpdate(blockedSocialId, blockedUpdateRequest) }

            val updates = FriendCommon.FriendListUpdates.newBuilder().setVersion(expectedVersion).setAcceptNewFriendInvites(true)
            coVerify {
                service.notifyPlayers(
                    listOf(blockedSocialId),
                    FriendListUpdateV1Notification.newBuilder().setUpdates(updates).build()
                )
            }
        }

        @Test
        fun `friendListUpdate does not remove sent invites if we cannot add anymore friends passed the limit`() = runTest {
            val friendOffset = 2
            val friendsCount = 399
            val friendsUpdateRequest = FriendListUpdateRequest(addFriendsUpdateRequest(friendOffset, friendsCount))
            trainIdentityCache(friendOffset, friendsCount)

            withVersionIncrement { testObj.friendListUpdate(inviterSocialId, friendsUpdateRequest) }
            assertEquals(friendsCount, testObj.getFriends(inviterSocialId).size())

            val badOffset = 500
            val badCount = 2
            val badFriendAddRequest = FriendListUpdateRequest(addFriendsUpdateRequest(badOffset, 2))
            trainIdentityCache(badOffset, badCount)

            var response: FriendResultStatus? = null
            withVersionIncrement { response = testObj.friendListUpdate(inviterSocialId, badFriendAddRequest) }

            assertEquals(FriendResultStatus.FriendListFull, response)
            assertEquals(friendsCount, testObj.getFriends(inviterSocialId).size())
        }

        @Test
        fun `friendListUpdate does not send a notification to an offline friend`() = runTest {
            val friendOffset = 2
            val friendsCount = 2
            val friendsUpdateRequest = FriendListUpdateRequest(addFriendsUpdateRequest(friendOffset, friendsCount))
            trainIdentityCache(friendOffset, friendsCount)

            withVersionIncrement { testObj.friendListUpdate(inviterSocialId, friendsUpdateRequest) }

            coVerify(exactly = 0) { service.notifyPlayers(any(), any()) }
        }

        @Test
        fun `friendListUpdate returns InviteeReceivedInvitesFull if the received invite list has gone over the limit`() = runTest {
            config.maxPendingReceivedInvites = 1
            val friends = testObj.getFriends(inviterSocialId)
            friends.inviteReceived(FriendOverview(friendOverview(inviteeOffset)))

            val friendsUpdateRequest = FriendListUpdateRequest(friendListUpdateRequest(addReceivedInvitesCount = 1))
            val response = testObj.friendListUpdate(inviterSocialId, friendsUpdateRequest)

            assertEquals(InviteeReceivedInvitesFull, response)
        }

        @Test
        fun `friendListUpdate returns InviteeDisabledInvites if the friend has disabled invites`() = runTest {
            val friends = testObj.getFriends(inviterSocialId)
            friends.setAcceptFriendInvites(false)

            val friendsUpdateRequest = FriendListUpdateRequest(friendListUpdateRequest(addReceivedInvitesCount = 1))
            val response = testObj.friendListUpdate(inviterSocialId, friendsUpdateRequest)

            assertEquals(InviteeDisabledInvites, response)
        }

        @Test
        fun `friendListUpdate should not respond with InviteeReceivedInvitesFull if invitee's received invites list is full when removing a received invite`() = runTest {
            config.maxPendingReceivedInvites = 1

            val friendToRemoveOffset = 3
            val cachedFriendToRemove = CachedFriend(SocialIdentity(socialIdentityProto(friendToRemoveOffset)))
            val friends = testObj.getFriends(inviterSocialId)
            friends.inviteReceived(FriendOverview(friendOverview(inviteeOffset)))
            friends.set(cachedFriendToRemove)

            val result = testObj.friendListUpdate(inviterSocialId, FriendListUpdateRequest(friendListUpdateRequest(
                1,
                removeReceivedInvitesCount = 1,
                removeReceivedInvitesOffset = 3)))

            assertEquals(FriendResultStatus.Ok, result)
        }

        @Test
        fun `friendListUpdate should not respond with InviteeReceivedInvitesFull if invitee's received invites list is full`() = runTest {
            config.maxPendingReceivedInvites = 1

            val friendToRemoveOffset = 3
            val cachedFriendToRemove = CachedFriend(SocialIdentity(socialIdentityProto(friendToRemoveOffset)))
            val friends = testObj.getFriends(inviterSocialId)
            friends.inviteReceived(FriendOverview(friendOverview(inviteeOffset)))
            friends.set(cachedFriendToRemove)

            val result = testObj.friendListUpdate(inviterSocialId, FriendListUpdateRequest(friendListUpdateRequest(
                1,
                removeFriendsCount = 1,
                removeFriendsOffset = 3)))

            assertEquals(FriendResultStatus.Ok, result)
        }
    }

    @Nested
    inner class RemoveFriend {
        @Test
        fun `removeFriend removes the specified friend and notifies both players`() = runTest {
            val cachedFriendToRemove = CachedFriend(SocialIdentity(socialIdentityProto(inviteeOffset)))
            val friends = testObj.getFriends(inviterSocialId)
            friends.set(cachedFriendToRemove)

            val result = withVersionIncrementResult { testObj.removeFriend(inviterSocialId, inviteeSocialId) }

            val expectedFriendRemoved = friendRemoveUpdateNotification(
                inviteeOffset,
                expectedVersion,
                gameIdentities = gameIdentities
            )
            assertEquals(FriendResultStatus.Ok, result.status)
            assertEquals(expectedFriendRemoved, result.updates)
            coVerify { friendClient.updateFriendList(removeFriendServiceRequest(inviterOffset, inviteeOffset)) }

            val inviterFriends = testObj.getFriends(inviterSocialId)

            assertFalse(inviterFriends.isFriend(inviteeSocialId))
        }

        @Test
        fun `removeFriend returns FriendNotFound when the requested friend is not found`() = runTest {
            val result = testObj.removeFriend(inviterSocialId, inviteeSocialId)

            assertEquals(FriendResultStatus.FriendNotFound, result.status)
            assertEquals(expectedVersion, result.version)
        }

        @Test
        fun `removeFriend does not update friend list if updateFriendList returns expected error`() = runTest {
            val cachedFriendToRemove = CachedFriend(SocialIdentity(socialIdentityProto(inviteeOffset)))
            val friends = testObj.getFriends(inviterSocialId)
            friends.set(cachedFriendToRemove)

            val expectedFriendResultStatus = FriendResultStatus.FriendNotFound
            coEvery {
                friendClient.updateFriendList(removeFriendServiceRequest(inviterOffset, inviteeOffset))
            } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder().setResponse(expectedFriendResultStatus.name).build()
            )

            val actualResult = testObj.removeFriend(inviterSocialId, inviteeSocialId)

            assertEquals(expectedFriendResultStatus, actualResult.status)
            assertEquals(expectedVersion, actualResult.version)
            assertTrue(friends.cachedFriends.contains(cachedFriendToRemove))
        }

        @Test
        fun `removeFriend does not update friend list if updateFriendList returns an unexpected error`() = runTest {
            val cachedFriendToRemove = CachedFriend(SocialIdentity(socialIdentityProto(inviteeOffset)))
            val friends = testObj.getFriends(inviterSocialId)
            friends.set(cachedFriendToRemove)

            coEvery {
                friendClient.updateFriendList(removeFriendServiceRequest(inviterOffset, inviteeOffset))
            } returns PragmaResult.Failure(PragmaFailure(ServiceError.getDefaultInstance()))

            coAssertThrowsPragmaError(PragmaError.FriendService_InternalError) {
                testObj.removeFriend(inviterSocialId, inviteeSocialId)
            }

            assertTrue(friends.cachedFriends.contains(cachedFriendToRemove))
            coVerify(exactly = 0) { service.notifyPlayers(any(), any()) }
        }
    }

    @Nested
    inner class DeleteFriendData {
        @Test
        fun `deleteFriendData removes friend data on both sides of the friend relationship`() = runTest {
            coEvery { identityCache.getSocialIdentity(inviterSocialId) } returns inviterSocialIdentity
            val listWithFriends = FriendList(inviterSocialId)
            listWithFriends.inviteSent(FriendOverview(inviteeOverview))
            listWithFriends.set(CachedFriend(SocialIdentity(socialIdentityProto(3))))
            listWithFriends.inviteReceived(FriendOverview(friendOverview(4)))
            listWithFriends.block(FriendOverview(friendOverview(5)))
            coEvery { friendDao.getFriendList(inviterSocialId) } returns listWithFriends

            testObj.deleteAllFriendData(inviterSocialId)

            coVerify {
                friendDao.writeFriendListUpdates(
                    inviterSocialId, FriendListUpdates(
                        internalFriendsRemoved = mutableListOf(socialIdUuid(3)),
                        internalSentInvitesRemoved = mutableListOf(socialIdUuid(2)),
                        internalReceivedInvitesRemoved = mutableListOf(socialIdUuid(4)),
                        internalUnblocksAdded = mutableListOf(socialIdUuid(5)),
                        internalBlockDeleteOperations = mutableListOf(socialIdUuid(5))
                    )
                )
            }

            coVerify { friendClient.updateFriendList(removeSentInviteServiceRequest(4, inviterOffset)) }
            coVerify { friendClient.updateFriendList(removeFriendServiceRequest(inviterOffset, 3)) }
            coVerify { friendClient.updateFriendList(removeReceivedInviteUpdateRequest(inviteeSocialId, inviterSocialId)) }
            coVerify {
                friendClient.updateFriendList(
                    removeReceivedBlockServiceRequest(
                        5,
                        inviterOffset,
                        inviterSocialIdentity.gameIdentities.map { it.toProto() })
                )
            }
        }

        @Test
        fun `deleteFriendData returns an error when updateFriendList call fails`() = runTest {
            val listWithFriends = FriendList(inviterSocialId)
            listWithFriends.inviteSent(FriendOverview(inviteeOverview))
            listWithFriends.set(CachedFriend(SocialIdentity(socialIdentityProto(3))))
            val failedUpdateFriendOverview = FriendOverview(friendOverview(4))
            val successfulUpdateFriendOverview = FriendOverview(friendOverview(5))
            listWithFriends.inviteReceived(failedUpdateFriendOverview)
            listWithFriends.inviteReceived(successfulUpdateFriendOverview)

            coEvery { friendDao.getFriendList(inviterSocialId) } returns listWithFriends
            coEvery { friendClient.updateFriendList(any()) } returnsMany listOf(
                PragmaResult.Success(
                    FriendRpc.FriendListUpdateServiceV1Response.newBuilder()
                        .setResponse(FriendResultStatus.Ok.name)
                        .build()
                ),
                PragmaResult.Failure(PragmaFailure(ServiceError.getDefaultInstance())),
                PragmaResult.Success(
                    FriendRpc.FriendListUpdateServiceV1Response.newBuilder()
                        .setResponse(FriendResultStatus.Ok.name)
                        .build()
                ),
                PragmaResult.Success(
                    FriendRpc.FriendListUpdateServiceV1Response.newBuilder()
                        .setResponse(FriendResultStatus.Ok.name)
                        .build()
                )
            )

            val result = testObj.deleteAllFriendData(inviterSocialId)

            coVerify {
                friendDao.writeFriendListUpdates(
                    inviterSocialId, FriendListUpdates(
                        internalFriendsRemoved = mutableListOf(socialIdUuid(3)),
                        internalSentInvitesRemoved = mutableListOf(socialIdUuid(2)),
                        internalReceivedInvitesRemoved = mutableListOf(successfulUpdateFriendOverview.socialId)
                    )
                )
            }
            coVerify { friendClient.updateFriendList(removeSentInviteServiceRequest(4, inviterOffset)) }
            coVerify { friendClient.updateFriendList(removeFriendServiceRequest(inviterOffset, 3)) }
            coVerify { friendClient.updateFriendList(removeReceivedInviteUpdateRequest(inviteeSocialId, inviterSocialId)) }
            assertEquals(FriendResultStatus.FriendUpdateFailed, result)
        }
    }

    @Nested
    inner class GetFriend {
        @Test
        fun `getFriend includes presences`() = runTest {
            trainAccountLookupBySocialId(inviterSocialId, inviterSocialIdentity)
            testObj.setPresence(
                inviterSocialId,
                gameIdentities.first().gameShardId.toUUID(),
                BasicPresence.Online,
                ExtRichPresencePlayerRequest.getDefaultInstance(),
                null,
                false
            )

            val friend = testObj.getFriend(inviterSocialId)

            assertEquals(inviterSocialId, friend.socialId.toUUID())
            assertEquals(BasicPresence.Online.name, friend.presencesList.first().basicPresence)
            assertEquals(gameIdentities.first().gameShardId, friend.presencesList.first().gameShardId)
        }

        @Test
        fun `getFriend throws an exception for an unknown social id`() = runTest {
            trainAccountLookupBySocialId(inviterSocialId, null)

            coAssertThrowsPragmaError(PragmaError.FriendService_SocialIdentityNotFound) { testObj.getFriend(inviterSocialId) }
        }
    }

    @Nested
    inner class Presence {

        private val socialId = socialIdUuid(1)
        private val playerId = playerIdUuid(2)

        private val gameShardId = ProtoTestFactory.gameShardId(1).toUUID()
        private val socialIdentity = SocialIdentity(
            socialIdentityProto(
                1,
                gameIdentities = listOf(
                    AccountCommon.GameIdentity.newBuilder()
                        .setGameShardId(gameShardId.toFixed128())
                        .setPragmaPlayerId(playerId.toFixed128())
                        .build()
                )
            )
        )

        private val richPresence = ExtRichPresence.getDefaultInstance()
        private val extPlayerRequest = ExtRichPresencePlayerRequest.getDefaultInstance()
        private val extBackendRequest = ExtRichPresenceBackendRequest.getDefaultInstance()

        @BeforeEach
        fun setUp() {
            testObj.init(service, identityCache, friendCache, friendClient, presenceCache, presencePlugin)

            coEvery { presencePlugin.updateRichPresenceFromPlayer(any(), any(), any(), any()) } returns ext
            coEvery { presencePlugin.updateRichPresenceFromBackend(any(), any(), any(), any()) } returns ext

            coEvery { identityCache.getSocialIdentity(socialId) } returns socialIdentity

            coEvery { friendClient.updateFriendList(any()) } returns PragmaResult.Success(
                FriendRpc.FriendListUpdateServiceV1Response.newBuilder().setResponse(FriendResultStatus.Ok.name).build()
            )
            coEvery { friendClient.getPresencesAndRegisterOnline(any(), any(), any()) } returns PragmaResult.Success(
                FriendTestFactory.getPresencesAndRegisterOnlineServiceV1Response(1)
            )
        }

        @Test
        fun `setPresence populates presence data for the provided game shard`() = runTest {
            val request = FriendTestFactory.setPresenceV1Request(1)
            val result = testObj.setPresence(
                socialId,
                request.gameShardId.toUUID(),
                BasicPresence.valueOf(request.basicPresence),
                request.requestExt,
                null,
                false
            )

            assertEquals(FriendResultStatus.Ok, result.first)
            assertEquals(request.basicPresence, result.second!!.basicPresence)

            assertNotNull(presenceCache.get(socialId))
        }

        @Test
        fun `setPresence returns GameIdentityNotFound if game shard id not known to pragma`() = runTest {
            val request = FriendTestFactory.setPresenceV1Request(1)

            val responseAndCachedPresence = testObj.setPresence(
                socialId,
                request.gameShardId.toUUID(),
                BasicPresence.valueOf(request.basicPresence),
                request.requestExt,
                null,
                false
            )

            trainAccountLookupBySocialId(socialId, SocialIdentity(socialIdentityProto(2)))

            val result = testObj.setPresence(
                socialId,
                request.gameShardId.toUUID(),
                BasicPresence.Away,
                request.requestExt,
                null,
                false
            )

            assertEquals(FriendResultStatus.GameIdentityNotFound, result.first)
            assertEquals(responseAndCachedPresence.second, result.second)
        }

        @Test
        fun `setPresence notifies all friends and the requesting player of new presence info`() = runTest {
            val updateRequest = addFriendsUpdateRequest(1)
            updateRequest.addFriendsList.forEach {
                val socialId = it.socialId.toUUID()
                coEvery { identityCache.getSocialIdentity(socialId) } returns socialIdentity
                testObj.registerForNotifications(socialId)
            }
            testObj.friendListUpdate(socialId, FriendListUpdateRequest(updateRequest))



            val request = FriendTestFactory.setPresenceV1Request(1, gameShardId = ProtoTestFactory.gameShardId(1))
            testObj.setPresence(
                socialId,
                request.gameShardId.toUUID(),
                BasicPresence.valueOf(request.basicPresence),
                request.requestExt,
                null,
                false
            )

            coVerify {
                service.notifyPlayers(
                    testObj.getFriends(socialId).cachedFriends.map { it.socialId },
                    expectedFriendUpdateNotification(socialId, playerId)
                )
            }

            coVerify {
                service.notifyPlayers(
                    listOf(socialId),
                    FriendRpc.PresenceUpdateV1Notification.newBuilder()
                        .setNewPresence(PresenceData(
                            expectedPresenceId,
                            socialId,
                            playerId,
                            gameShardId,
                            gameTitleId.toUUID(),
                            BasicPresence.Online,
                            richPresence
                        ).also { it.setVersion(1) }.toProto())
                        .build()
                )
            }
        }

        @Test
        fun `setPresence does not notify a user that is not marked as online`() = runTest {
            val updateRequest = addFriendsUpdateRequest(1)
            updateRequest.addFriendsList.forEach {
                coEvery { identityCache.getSocialIdentity(it.socialId.toUUID()) } returns socialIdentity
            }
            testObj.friendListUpdate(socialId, FriendListUpdateRequest(updateRequest))

            val request = FriendTestFactory.setPresenceV1Request(1, gameShardId = ProtoTestFactory.gameShardId(1))
            testObj.setPresence(
                socialId,
                request.gameShardId.toUUID(),
                BasicPresence.valueOf(request.basicPresence),
                request.requestExt,
                null,
                false
            )

            coVerify(exactly = 0) {
                service.notifyPlayers(any(), expectedFriendUpdateNotification(socialId, playerId))
            }
        }

        @Test
        fun `deregisterForNotifications prevents notifications being set to player`() = runTest {
            val updateRequest = addFriendsUpdateRequest(1)
            updateRequest.addFriendsList.forEach {
                coEvery { identityCache.getSocialIdentity(it.socialId.toUUID()) } returns socialIdentity
                testObj.registerForNotifications(socialId)
            }
            testObj.friendListUpdate(socialId, FriendListUpdateRequest(updateRequest))

            val request = FriendTestFactory.setPresenceV1Request(1, gameShardId = ProtoTestFactory.gameShardId(1))
            updateRequest.addFriendsList.forEach { testObj.deregisterForNotifications(it.socialId.toUUID()) }
            testObj.setPresence(
                socialId,
                request.gameShardId.toUUID(),
                BasicPresence.valueOf(request.basicPresence),
                request.requestExt,
                null,
                false
            )

            coVerify(exactly = 0) {
                service.notifyPlayers(any(), expectedFriendUpdateNotification(socialId, playerId))
            }
        }

        @Test
        fun `setPresence returns PresenceRequestNotFound when both request exts are null`() = runTest {
            val request = FriendTestFactory.setPresenceV1Request(1)

            val responseAndCachedPresence = testObj.setPresence(
                socialId,
                request.gameShardId.toUUID(),
                BasicPresence.valueOf(request.basicPresence),
                request.requestExt,
                null,
                false
            )

            val result = testObj.setPresence(socialId, gameShardId, BasicPresence.Away, null, null, true)

            assertEquals(FriendResultStatus.PresenceRequestNotFound, result.first)
            assertEquals(responseAndCachedPresence.second, result.second)
        }

        @Test
        fun `setPresence does not invoke plugin if invoke update rich presence is false`() = runTest {
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, extPlayerRequest, null, false)
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, null, extBackendRequest, false)

            coVerify(exactly = 0) { presencePlugin.updateRichPresenceFromPlayer(socialId, gameShardId, any(), extPlayerRequest) }
            coVerify(exactly = 0) { presencePlugin.updateRichPresenceFromBackend(socialId, gameShardId, any(), extBackendRequest) }
        }

        @Test
        fun `setPresence invokes plugin if invoke update rich presence is true`() = runTest {
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, extPlayerRequest, null, true)
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, null, extBackendRequest, true)

            coVerify { presencePlugin.updateRichPresenceFromPlayer(socialId, gameShardId, any(), extPlayerRequest) }
            coVerify { presencePlugin.updateRichPresenceFromBackend(socialId, gameShardId, any(), extBackendRequest) }
        }

        @Test
        fun `setPresence does not notify requesting player if instructed not to`() = runTest {
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, extPlayerRequest, null,
                invokeUpdateRichPresence = false,
                notifyRequestingPlayer = false
            )

            coVerify(exactly = 0) { service.notifyPlayers(listOf(socialId), any()) }
        }

        @Test
        fun `setPresence does not update presence or notify if plugin call throws`() = runTest {
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, extPlayerRequest, null, true)
            clearMocks(service)

            val expectedPresence = testObj.getPresences(listOf(socialId)).first().presences[gameShardId]

            coEvery { presencePlugin.updateRichPresenceFromPlayer(any(), any(), any(), any()) } throws Exception("could not validate")

            val newRichRequest = ExtRichPresencePlayerRequest.newBuilder().build()
            val friendResponseAndPresence = testObj.setPresence(socialId, gameShardId, BasicPresence.Away, newRichRequest, null, true)

            val newPresence = testObj.getPresences(listOf(socialId)).first().presences[gameShardId]

            assertEquals(expectedPresence, newPresence)
            assertEquals(expectedPresence?.basic, newPresence?.basic)
            assertSame(expectedPresence?.rich, newPresence?.rich)
            coVerify(exactly = 0) { service.notifyPlayers(any(), any()) }
            assertEquals(FriendResultStatus.PresenceUpdateFailed.name, friendResponseAndPresence.first.name)
            assertEquals(BasicPresence.Online.name, friendResponseAndPresence.second!!.basicPresence)
        }

        @Test
        fun `getPresences returns a list of all presence data`() = runTest {
            val request = FriendTestFactory.getPresencesAndRegisterOnlineServiceV1Request(1)

            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, extPlayerRequest, null, false)

            val presences = testObj.getPresences(request.socialIdsList.map { it.toUUID() })

            assertEquals(socialId, presences.first().socialId)
        }

        @Test
        fun `setPresence does not stomp existing rich presence if invokeUpdateRichPresence is set to false`() = runTest {
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, extPlayerRequest, null, true)

            val cachedRichOriginal = testObj.getPresences(listOf(socialId)).first().presences[gameShardId]?.rich

            coEvery { presencePlugin.updateRichPresenceFromPlayer(any(), any(), any(), any()) } returns ExtRichPresence.newBuilder().build()

            val newRichRequest = ExtRichPresencePlayerRequest.newBuilder().build()
            testObj.setPresence(socialId, gameShardId, BasicPresence.Online, newRichRequest, null, false)

            val cachedRich = testObj.getPresences(listOf(socialId)).first().presences[gameShardId]?.rich

            assertSame(cachedRich, cachedRichOriginal)
        }

        @Test
        fun `getFriendsWithPresence fetches presences from other friend services`() = runTest {
            val response1 = PragmaResult.Success<FriendRpc.GetPresencesAndRegisterOnlineServiceV1Response, PragmaFailure>(
                FriendTestFactory.getPresencesAndRegisterOnlineServiceV1Response(
                    1
                )
            )
            coEvery {
                friendClient.getPresencesAndRegisterOnline(any(), any(), 0)
            } returns response1

            val response2 = PragmaResult.Success<FriendRpc.GetPresencesAndRegisterOnlineServiceV1Response, PragmaFailure>(
                FriendTestFactory.getPresencesAndRegisterOnlineServiceV1Response(
                    4
                )
            )
            coEvery {
                friendClient.getPresencesAndRegisterOnline(any(), any(), 1)
            } returns response2

            val response3 = PragmaResult.Success<FriendRpc.GetPresencesAndRegisterOnlineServiceV1Response, PragmaFailure>(
                FriendTestFactory.getPresencesAndRegisterOnlineServiceV1Response(
                    7
                )
            )
            coEvery {
                friendClient.getPresencesAndRegisterOnline(any(), any(), 2)
            } returns response3

            for (i in 1..9) {
                coEvery {
                    identityCache.getSocialIdentity(socialIdUuid(i))
                } returns SocialIdentity(socialIdentityProto(i))
            }

            testObj.friendListUpdate(socialId, FriendListUpdateRequest(friendListUpdateRequest(1, 9)))
            val allExpectedPresences =
                (response1.result.presencesForSocialIdsList + response2.result.presencesForSocialIdsList + response3.result.presencesForSocialIdsList)
            val actualResponse = testObj.getFriendsWithPresence(socialId, 3)

            assertEquals(allExpectedPresences.size, actualResponse.friendsList.size)
            allExpectedPresences.forEach { expectedPresence ->
                val friend = actualResponse.friendsList.find { expectedPresence.socialId == it.socialId }!!
                assertEquals(expectedPresence.presencesList, friend.presencesList)
            }
        }

        private fun expectedFriendUpdateNotification(socialId: UUID, playerId: UUID): FriendRpc.FriendUpdateV1Notification {
            return FriendRpc.FriendUpdateV1Notification.newBuilder()
                .setUpdatedPresence(
                    PresenceData(
                        expectedPresenceId,
                        socialId,
                        playerId,
                        gameShardId,
                        gameTitleId.toUUID(),
                        BasicPresence.Online,
                        richPresence
                    ).also { it.setVersion(1) }.toProto()
                )
                .setSocialId(socialId.toFixed128())
                .build()
        }
    }

    private fun trainIdentityCache(offSet: Int, count: Int) {
        val socialIdentities = List(count) { SocialIdentity(socialIdentityProto(offSet + it, gameIdentities = gameIdentities)) }
        socialIdentities.forEach { socialIdentity ->
            coEvery { identityCache.getSocialIdentity(socialIdentity.pragmaSocialId) } returns socialIdentity
        }
    }

    private fun trainAccountLookupByDisplayName(
        displayName: PragmaDisplayName,
        response: SocialIdentity?,

        ) {
        coEvery { identityCache.getSocialIdentity(displayName) } returns response
    }

    private fun trainAccountLookupBySocialId(
        socialId: SocialId,
        response: SocialIdentity?,
    ) {
        coEvery { identityCache.getSocialIdentity(socialId) } returns response
    }

    private suspend fun withVersionIncrement(block: suspend () -> Unit) {
        expectedVersion++
        block()
    }

    private suspend fun withVersionIncrementResult(block: suspend () -> FriendResult): FriendResult {
        expectedVersion++
        return block()
    }

    private suspend fun trainCacheAndAddInvite() {
        coEvery { identityCache.getSocialIdentity(inviteeSocialId) } returns inviteeSocialIdentity
        coEvery { identityCache.getSocialIdentity(inviterSocialId) } returns inviterSocialIdentity

        trainAccountLookupBySocialId(inviteeSocialId, SocialIdentity(socialIdentityProto(inviteeOffset, gameIdentities = gameIdentities)))
        trainAccountLookupBySocialId(inviterSocialId, SocialIdentity(socialIdentityProto(inviterOffset, gameIdentities = gameIdentities)))

        withVersionIncrement {
            testObj.friendListUpdate(
                inviteeSocialId,
                FriendListUpdateRequest(addReceivedInvitesUpdateRequest(inviterOffset, count = 1))
            )
        }
    }
}
