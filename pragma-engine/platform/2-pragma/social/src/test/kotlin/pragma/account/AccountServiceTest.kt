package pragma.account

import com.auth0.jwt.interfaces.Payload
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import pragma.AccountTestFactory.addAccountToPlayerGroupOperatorV1Request
import pragma.AccountTestFactory.addAccountToPlayerGroupPartnerV1Request
import pragma.AccountTestFactory.addAccountToPlayerGroupServiceV1Request
import pragma.AccountTestFactory.addAccountsToPlayerGroupOperatorV1Request
import pragma.AccountTestFactory.authenticateOrCreateV2Request
import pragma.AccountTestFactory.authenticateOrCreateV2Response
import pragma.AccountTestFactory.authenticateV1Request
import pragma.AccountTestFactory.ban
import pragma.AccountTestFactory.banAccountOperatorV1Request
import pragma.AccountTestFactory.banAccountOperatorV1Response
import pragma.AccountTestFactory.banAccountPartnerV1Request
import pragma.AccountTestFactory.banAccountPartnerV1Response
import pragma.AccountTestFactory.banAccountServiceV1Request
import pragma.AccountTestFactory.banAccountServiceV1Response
import pragma.AccountTestFactory.createAccountV1Request
import pragma.AccountTestFactory.createPartnerTokenV1Request
import pragma.AccountTestFactory.createPlayerGroupOperatorV1Request
import pragma.AccountTestFactory.createPlayerGroupPartnerV1Request
import pragma.AccountTestFactory.createPlayerGroupServiceV1Request
import pragma.AccountTestFactory.createTestAccount
import pragma.AccountTestFactory.createTestUnsafeAccount
import pragma.AccountTestFactory.deleteAccountOperatorV1Request
import pragma.AccountTestFactory.deleteAccountPartnerV1Request
import pragma.AccountTestFactory.deleteAccountServiceV1Request
import pragma.AccountTestFactory.deleteIdentityProviderAccountOperatorV1Request
import pragma.AccountTestFactory.deleteIdentityProviderAccountPartnerV1Request
import pragma.AccountTestFactory.deleteIdentityProviderAccountServiceV1Request
import pragma.AccountTestFactory.discordRedirectV1Request
import pragma.AccountTestFactory.displayName
import pragma.AccountTestFactory.editPlayerGroupOperatorV1Request
import pragma.AccountTestFactory.editPlayerGroupPartnerV1Request
import pragma.AccountTestFactory.editPlayerGroupServiceV1Request
import pragma.AccountTestFactory.gameToken
import pragma.AccountTestFactory.getBansBySocialIdOperatorV1Request
import pragma.AccountTestFactory.getBansBySocialIdOperatorV1Response
import pragma.AccountTestFactory.getBansBySocialIdPartnerV1Request
import pragma.AccountTestFactory.getBansBySocialIdPartnerV1Response
import pragma.AccountTestFactory.getBansBySocialIdServiceV1Request
import pragma.AccountTestFactory.getBansBySocialIdServiceV1Response
import pragma.AccountTestFactory.getBansV1Request
import pragma.AccountTestFactory.getBansV1Response
import pragma.AccountTestFactory.getDisplayNameForPlayerIdPartnerV2Request
import pragma.AccountTestFactory.getDisplayNameForPragmaPlayerIdV1Request
import pragma.AccountTestFactory.getPersonalDataSectionsServiceV1Request
import pragma.AccountTestFactory.getPlayerCountBySearchFilterOperatorV1Request
import pragma.AccountTestFactory.getPlayerIdentitiesByProviderAccountIdsV1Request
import pragma.AccountTestFactory.getPlayerIdentitiesByProviderAccountIdsV1Response
import pragma.AccountTestFactory.getPlayerIdentitiesOperatorV1Request
import pragma.AccountTestFactory.getPlayerIdentitiesPartnerV1Request
import pragma.AccountTestFactory.getPlayerIdentitiesServiceV1Request
import pragma.AccountTestFactory.getPlayerIdentitiesV1Request
import pragma.AccountTestFactory.getPlayerIdentitiesV1Response
import pragma.AccountTestFactory.getPragmaAccountOverviewsOperatorV1Request
import pragma.AccountTestFactory.getPragmaAccountOverviewsPartnerV1Request
import pragma.AccountTestFactory.getPragmaAccountOverviewsServiceV1Request
import pragma.AccountTestFactory.getPragmaPlayerIdForDisplayNameV1Request
import pragma.AccountTestFactory.getPragmaPlayerOverviewsOperatorV1Request
import pragma.AccountTestFactory.getPublicIdProviderInfoServiceV1Request
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsOperatorV1Request
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsOperatorV1Response
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsPartnerV1Request
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsPartnerV1Response
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsServiceV1Request
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsServiceV1Response
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsV1Request
import pragma.AccountTestFactory.getSocialIdentitiesByProviderAccountIdsV1Response
import pragma.AccountTestFactory.getSocialIdentitiesOperatorV1Request
import pragma.AccountTestFactory.getSocialIdentitiesPartnerV1Request
import pragma.AccountTestFactory.getSocialIdentitiesServiceV1Request
import pragma.AccountTestFactory.getSocialIdentitiesV1Request
import pragma.AccountTestFactory.getSocialIdentitiesV1Response
import pragma.AccountTestFactory.googleRedirectV1Request
import pragma.AccountTestFactory.linkIdentityProviderAccountPartnerV1Request
import pragma.AccountTestFactory.linkIdentityProviderAccountServiceV1Request
import pragma.AccountTestFactory.linkIdentityProviderAccountV2Request
import pragma.AccountTestFactory.oAuthRedirectV1Request
import pragma.AccountTestFactory.playerGroup
import pragma.AccountTestFactory.playerGroupId
import pragma.AccountTestFactory.playerIdentity
import pragma.AccountTestFactory.pragmaAccountOverview
import pragma.AccountTestFactory.publicIdProviderInfo
import pragma.AccountTestFactory.publicSocialIdentity
import pragma.AccountTestFactory.removeAccountFromPlayerGroupOperatorV1Request
import pragma.AccountTestFactory.removeAccountFromPlayerGroupPartnerV1Request
import pragma.AccountTestFactory.removeAccountFromPlayerGroupServiceV1Request
import pragma.AccountTestFactory.removePlayerGroupOperatorV1Request
import pragma.AccountTestFactory.removePlayerGroupPartnerV1Request
import pragma.AccountTestFactory.removePlayerGroupServiceV1Request
import pragma.AccountTestFactory.revokeBanAccountOperatorV1Request
import pragma.AccountTestFactory.revokeBanAccountOperatorV1Response
import pragma.AccountTestFactory.revokeBanAccountPartnerV1Request
import pragma.AccountTestFactory.revokeBanAccountPartnerV1Response
import pragma.AccountTestFactory.revokeBanAccountServiceV1Request
import pragma.AccountTestFactory.revokeBanAccountServiceV1Response
import pragma.AccountTestFactory.socialIdentityProto
import pragma.AccountTestFactory.socialIdentityWithPersonal
import pragma.AccountTestFactory.socialToken
import pragma.AccountTestFactory.twitchRedirectV1Request
import pragma.AccountTestFactory.unlinkIdentityProviderAccountV2
import pragma.AccountTestFactory.updateDisplayNamePartnerV1Request
import pragma.AccountTestFactory.updateDisplayNameServiceV1Request
import pragma.AccountTestFactory.updateDisplayNameV1Request
import pragma.AccountTestFactory.updateEmailV1Request
import pragma.AccountTestFactory.verifyEmailV1Request
import pragma.AccountTestFactory.viewActiveIdProvidersOperatorV1Request
import pragma.AccountTestFactory.viewPlayerGroupOperatorV1Request
import pragma.AccountTestFactory.viewPlayerGroupPartnerV1Request
import pragma.AccountTestFactory.viewPlayerGroupServiceV1Request
import pragma.AccountTestFactory.viewPlayerGroupsBySocialIdOperatorV1Request
import pragma.AccountTestFactory.viewPlayerGroupsBySocialIdOperatorV1Response
import pragma.AccountTestFactory.viewPlayerGroupsOperatorV1Request
import pragma.AccountTestFactory.viewPlayerGroupsPartnerV1Request
import pragma.AccountTestFactory.viewPlayerGroupsServiceV1Request
import pragma.AccountTestFactory.viewSocialIdentitiesV1Request
import pragma.AccountTestFactory.viewSocialIdentitiesV1Response
import pragma.AccountTestFactory.viewSocialIdentityPartnerV2Request
import pragma.AccountTestFactory.viewSocialIdentityPartnerV2Response
import pragma.AccountTestFactory.viewSocialIdentityServiceV1Request
import pragma.AccountTestFactory.viewSocialIdentityServiceV1Response
import pragma.AccountTestFactory.viewSocialIdentityV1Request
import pragma.AccountTestFactory.viewSocialIdentityV1Response
import pragma.ApplicationErrorException
import pragma.DataRightsTestFactory.personalDataReportSections
import pragma.Fixed128
import pragma.PragmaCoreTestFactory.anyInt
import pragma.PragmaCoreTestFactory.anyString
import pragma.PragmaCoreTestFactory.anyUuid
import pragma.PragmaCoreTestFactory.pragmaNode
import pragma.PragmaOptions
import pragma.PragmaResult.Success
import pragma.ProtoTestFactory.gameShardId
import pragma.ProtoTestFactory.serviceInstanceId
import pragma.SessionTestFactory.operatorSession
import pragma.SessionTestFactory.partnerSession
import pragma.SessionTestFactory.serviceSession
import pragma.SessionTestFactory.socialPlayerSession
import pragma.ValidationFailedApplicationError
import pragma.account.AccountRpc.AuthenticateOrCreateV2Response
import pragma.account.AccountRpc.BanAccountServiceV1Request
import pragma.account.AccountRpc.BanAccountServiceV1Response
import pragma.account.AccountRpc.DeleteIdentityProviderAccountOperatorV1Response
import pragma.account.AccountRpc.DeleteIdentityProviderAccountPartnerV1Response
import pragma.account.AccountRpc.DeleteIdentityProviderAccountServiceV1Response
import pragma.account.AccountRpc.IdProvider
import pragma.account.AccountRpc.UnlinkIdentityProviderAccountV2Response
import pragma.account.commands.AccountLifecycleCommand
import pragma.auth.IdProviderAccount
import pragma.auth.PragmaDisplayName
import pragma.auth.PrivateJwtClaims
import pragma.auth.TokenDecoderNodeService
import pragma.auth.TokenSignerNodeService
import pragma.auth.getStringClaim
import pragma.datarights.AccountSectionsBuilder
import pragma.datarights.PersonalDataReportSection
import pragma.gamemanagement.GameDaoNodeService
import pragma.gamemanagement.LimitedAccessEventScheduler
import pragma.gameshardcache.GameShardCacheNodeService
import pragma.rpcs.PragmaRPC
import pragma.rpcs.RoutingMethod
import pragma.rpcs.SessionType
import pragma.services.NodeServicesContainer
import pragma.settings.BackendType
import pragma.typeInfoAndSerializedMessage
import pragma.utils.RoutingUtils
import pragma.utils.TimeProxy
import pragma.utils.TimeProxy.Companion.secondsToMillis
import pragma.utils.buildNotFoundApplicationError
import pragma.utils.buildPragmaPlayerIdNotFoundApplicationError
import pragma.utils.coAssertThrowsApplicationErrorException
import pragma.utils.paginationListOf
import pragma.utils.toFixed128
import pragma.utils.toIdProviderIntType
import pragma.utils.toPragmaSession
import pragma.utils.toUUID

@ExperimentalCoroutinesApi
internal class AccountServiceTest {
    private val pragmaNode = pragmaNode()
    private val serviceInstanceId = serviceInstanceId(1)
    private val routingUtils: RoutingUtils = mockk(relaxUnitFun = true)
    private val accountLifecycleCommand: AccountLifecycleCommand = mockk(relaxUnitFun = true)
    private val playerGroupProxy: PlayerGroupProxy = mockk(relaxUnitFun = true)
    private val accountTagProxy: AccountTagProxy = mockk(relaxUnitFun = true)
    private val accountRegistrar: AccountRegistrar = mockk(relaxUnitFun = true)
    private val limitedAccessEventsScheduler: LimitedAccessEventScheduler = mockk(relaxUnitFun = true)
    private val limitedAccessEvents: LimitedAccessEvents = mockk(relaxUnitFun = true)
    private val accountSectionsBuilder: AccountSectionsBuilder = mockk(relaxUnitFun = true)
    private val playerBehaviorCustodian: PlayerBehaviorCustodian = mockk(relaxUnitFun = true)
    private val publicIdentityConverter: PublicIdentityConverter = mockk(relaxUnitFun = true)
    private val testObj = AccountService(
        pragmaNode,
        serviceInstanceId,
        routingUtils,
        accountLifecycleCommand,
        playerGroupProxy,
        accountTagProxy,
        accountRegistrar,
        limitedAccessEventsScheduler,
        limitedAccessEvents,
        accountSectionsBuilder,
        playerBehaviorCustodian,
        publicIdentityConverter,
    )

    private val accountDaoNodeService: AccountDaoNodeService = mockk(relaxUnitFun = true)
    private val gameDaoNodeService: GameDaoNodeService = mockk(relaxUnitFun = true)
    private val unsafeIdentityDaoNodeService: UnsafeIdentityDaoNodeService = mockk(relaxUnitFun = true)
    private val tokenSignerNodeService: TokenSignerNodeService = mockk(relaxUnitFun = true)
    private val tokenDecoderNodeService: TokenDecoderNodeService = mockk(relaxUnitFun = true)
    private val gameShardCacheNodeService: GameShardCacheNodeService = mockk(relaxUnitFun = true)
    private val discordIdentityProvider: DiscordIdentityProviderPlugin = mockk(relaxUnitFun = true)
    private val twitchIdentityProvider: TwitchIdentityProviderPlugin = mockk(relaxUnitFun = true)
    private val googleIdentityProvider: GoogleIdentityProviderPlugin = mockk(relaxUnitFun = true)
    private val auth0IdentityProvider: Auth0IdentityProviderPlugin = mockk(relaxUnitFun = true)
    private val steamIdentityProvider: SteamIdentityProviderPlugin = mockk(relaxUnitFun = true)
    private val emailSenderPlugin: EmailSenderPlugin = mockk(relaxUnitFun = true)
    private val accountPlugin: AccountPlugin = mockk(relaxUnitFun = true)
    private val bulkActionPlugin: BulkActionPlugin = mockk(relaxUnitFun = true)

    private val socialPlayerSession = socialPlayerSession()
    private val serviceSession = serviceSession()
    private val operatorSession = operatorSession()
    private val partnerSession = partnerSession()
    private val gameShardId = gameShardId(7).toUUID()
    private val getPlayerIdentitiesByProviderAccountIdsV1Request =
        getPlayerIdentitiesByProviderAccountIdsV1Request(1, 2, gameShardId = gameShardId.toFixed128())
    private val playerIdentity1 = PlayerIdentity(playerIdentity(1))
    private val playerIdentity2 = PlayerIdentity(playerIdentity(2))

    private val testConfig = AccountServiceConfig.getFor(BackendType.GAME).apply {
        gameManagementPollingFrequencyInMillis = 10.secondsToMillis
        verificationEmailEndpoint = "http://127.0.0.1:11000/v1/account/verifyemailv1"
    }

    @BeforeEach
    fun beforeEach() = runTest {
        testObj.onConfigChanged(testConfig)

        testObj.nodeServicesContainer = NodeServicesContainer(
            mapOf(
                AccountDaoNodeService::class to accountDaoNodeService,
                GameDaoNodeService::class to gameDaoNodeService,
                UnsafeIdentityDaoNodeService::class to unsafeIdentityDaoNodeService,
                TokenSignerNodeService::class to tokenSignerNodeService,
                TokenDecoderNodeService::class to tokenDecoderNodeService,
                GameShardCacheNodeService::class to gameShardCacheNodeService,
            )
        )
        testObj.emailSenderPlugin = emailSenderPlugin
        testObj.accountPlugin = accountPlugin
        testObj.bulkActionPlugin = bulkActionPlugin
        testObj.logger = mockk(relaxUnitFun = true)

        testObj.run()
    }

    @Test
    fun `run calls init on dependencies`() = runTest {
        coVerify { unsafeIdentityDaoNodeService.init(any()) }
        coVerify { accountDaoNodeService.init(any()) }
        coVerify { accountLifecycleCommand.init(accountDaoNodeService, accountTagProxy, playerGroupProxy, any()) }
        coVerify { playerGroupProxy.init(accountDaoNodeService) }
        coVerify { accountTagProxy.init(accountDaoNodeService) }
        coVerify { limitedAccessEventsScheduler.init(gameDaoNodeService) }
        coVerify { limitedAccessEvents.init(any(), limitedAccessEventsScheduler, testConfig.gameManagementPollingFrequencyInMillis) }
        coVerify {
            accountRegistrar.init(
                testObj,
                accountDaoNodeService,
                tokenSignerNodeService,
                any(),
                emailSenderPlugin,
                accountPlugin,
                tokenDecoderNodeService,
                any(),
                gameShardCacheNodeService,
                unsafeIdentityDaoNodeService,
                accountLifecycleCommand,
                any<TimeProxy>()
            )
        }
        coVerify { accountSectionsBuilder.init(accountDaoNodeService) }
        coVerify {
            playerBehaviorCustodian.init(
                testObj,
                accountDaoNodeService,
                gameShardCacheNodeService
            )
        }
        coVerify { publicIdentityConverter.init(testObj.identityProviders) }
    }

    @Test
    fun `onConfigChanged calls the proper onConfigChanged functions on dependencies`() = runTest {
        coVerify { accountRegistrar.onConfigChanged(testConfig) }
    }

    @Nested
    inner class TokenEndpoints {
        @Test
        fun `authenticateOrCreateRequestV2 auths game and social`() = runTest {
            val authenticateOrCreateResponse = SessionTokensResult(SessionTokens(anyString(), anyString()))
            coEvery { accountRegistrar.authenticateOrCreate(any()) } returns authenticateOrCreateResponse

            val request = authenticateOrCreateV2Request(1)
            val result = testObj.authenticateOrCreateV2(serviceSession, request)

            assertEquals(authenticateOrCreateResponse.toAuthenticateOrCreateV2Response(), result)
            coVerify { accountRegistrar.authenticateOrCreate(AuthenticateOrCreateRequest(request)) }
        }

        @Test
        fun `authenticateOrCreateRequestV2 only auths social if no game shard id on request`() = runTest {
            val onlySocialAuthenticateOrCreateResponse = SessionTokensResult(SessionTokens(anyString(), anyString()))
            coEvery { accountRegistrar.authenticateOrCreateSocial(any()) } returns onlySocialAuthenticateOrCreateResponse

            val request = authenticateOrCreateV2Request(1).toBuilder().clearGameShardId().build()
            val result = testObj.authenticateOrCreateV2(serviceSession, request)

            assertEquals(onlySocialAuthenticateOrCreateResponse.toAuthenticateOrCreateV2SocialResponse(), result)
            coVerify { accountRegistrar.authenticateOrCreateSocial(AuthSocialRequest(request)) }
        }

        @Test
        fun `authenticateV1 auths social`() = runTest {
            val socialTokenResult = SessionTokensResult(SessionTokens(anyString(), anyString()))
            coEvery { accountRegistrar.authenticateOnlySocial(any()) } returns socialTokenResult

            val request = authenticateV1Request(1)
            val result = testObj.authenticateV1(serviceSession, request)

            assertEquals(socialTokenResult.toAuthenticateV1SocialResponse(), result)
            coVerify { accountRegistrar.authenticateOnlySocial(AuthSocialRequest(request)) }
        }

        @Test
        fun `createAccountV1 calls account registrar`() = runTest {
            val socialTokenResult = SessionTokensResult(SessionTokens(anyString(), anyString()))
            coEvery { accountRegistrar.createAccountSocial(any()) } returns socialTokenResult

            val request = createAccountV1Request(1)
            val result = testObj.createAccountV1(serviceSession, request)

            assertEquals(socialTokenResult.toCreateAccountV1SocialResponse(), result)
            coVerify { accountRegistrar.createAccountSocial(AuthSocialRequest(request)) }
        }

        @Test
        fun `createPartnerTokenV1 creates a partner social and game token`() = runTest {
            val gameToken = gameToken(1)
            every { tokenSignerNodeService.createPartnerGameToken(gameShardId) } returns gameToken

            val socialToken = socialToken(1)
            every { tokenSignerNodeService.createPartnerSocialToken() } returns socialToken

            val request = createPartnerTokenV1Request(1, gameShardId.toFixed128())
            val result = testObj.createPartnerTokenV1(operatorSession, request)

            assertEquals(gameToken, result.pragmaPartnerGameToken)
            assertEquals(socialToken, result.pragmaPartnerSocialToken)
        }

        @Test
        fun `createPartnerTokenV1 throws if game shard does not exist`() = runTest {
            val request = createPartnerTokenV1Request(1)
            val badGameShardId = request.gameShardId.toUUID()
            val expectedApplicationError = buildNotFoundApplicationError(badGameShardId.toString(), "")
            every { gameShardCacheNodeService.throwIfGameShardDoesNotExist(badGameShardId) } throws ApplicationErrorException(
                expectedApplicationError
            )

            coAssertThrowsApplicationErrorException(expectedApplicationError) {
                testObj.createPartnerTokenV1(operatorSession, request)
            }
        }
    }

    @Nested
    inner class SocialIdentityEndpoints {
        @Test
        fun `viewSocialIdentitiesV1 returns correct response`() {
            val socialIdentitiesWithPersonal = mutableListOf(
                SocialIdentityWithPersonal(socialIdentityWithPersonal(1)),
                SocialIdentityWithPersonal(socialIdentityWithPersonal(2)),
                SocialIdentityWithPersonal(socialIdentityWithPersonal(3))
            )
            coEvery {
                accountDaoNodeService.getSocialIdentitiesWithPersonals(any())
            } returns socialIdentitiesWithPersonal

            val request = viewSocialIdentitiesV1Request()
            val response = runBlocking { testObj.viewSocialIdentitiesV1(operatorSession, request) }

            val expected = viewSocialIdentitiesV1Response(socialIdentitiesWithPersonal.map { it.toProto() })
            assertEquals(expected, response)
            coVerify { accountDaoNodeService.getSocialIdentitiesWithPersonals(1000) }
        }

        @Test
        fun `viewSocialIdentityV1 returns a specific social identity`() = runTest {
            val expected = viewSocialIdentityV1Response(1)
            val request = viewSocialIdentityV1Request(1)
            coEvery {
                accountDaoNodeService.getAllPragmaSocialIdentityWithPersonal(request.pragmaSocialId.toUUID())
            } returns SocialIdentityWithPersonal(expected.socialIdentity)

            val response = testObj.viewSocialIdentityV1(operatorSession, request)

            assertEquals(expected, response)
        }

        @Test
        fun `viewSocialIdentityServiceV1 returns a specific social identity`() = runTest {
            val expected = viewSocialIdentityServiceV1Response(1)
            coEvery {
                accountRegistrar.getPragmaSocialIdentityWithPersonal(any())
            } returns SocialIdentityWithPersonal(expected.socialIdentity)

            val request = viewSocialIdentityServiceV1Request(1)
            val response = testObj.viewSocialIdentityServiceV1(serviceSession, request)

            assertEquals(expected, response)
            coVerify { accountRegistrar.getPragmaSocialIdentityWithPersonal(request.pragmaSocialId.toUUID()) }
        }

        @Test
        fun `viewSocialIdentityPartnerV2 returns a specific social identity`() = runTest {
            val expected = viewSocialIdentityPartnerV2Response(1)
            coEvery {
                accountRegistrar.getPragmaSocialIdentityWithPersonal(any())
            } returns SocialIdentityWithPersonal(expected.socialIdentity)

            val request = viewSocialIdentityPartnerV2Request(1)
            val response = testObj.viewSocialIdentityPartnerV2(partnerSession, request)

            assertEquals(expected, response)
            coVerify { accountRegistrar.getPragmaSocialIdentityWithPersonal(request.pragmaSocialId.toUUID()) }
        }
    }

    @Nested
    inner class DisplayNameEndpoints {
        @Test
        fun `getPragmaPlayerIdForDisplayNameV1 returns player id`() = runTest {
            coEvery {
                accountDaoNodeService.getPragmaPlayerIdForDisplayName(any(), any())
            } returns playerIdentity1.pragmaPlayerId

            val pragmaPlayerIdRequest = getPragmaPlayerIdForDisplayNameV1Request(
                1, gameShardId = gameShardId.toFixed128()
            )
            val result = testObj.getPragmaPlayerIdForDisplayNameV1(socialPlayerSession, pragmaPlayerIdRequest)

            assertEquals(playerIdentity1.pragmaPlayerId.toFixed128(), result.pragmaPlayerId)
            val expectedDisplayName = PragmaDisplayName(
                displayName(1, pragmaPlayerIdRequest.displayName, pragmaPlayerIdRequest.discriminator)
            )
            coVerify { accountDaoNodeService.getPragmaPlayerIdForDisplayName(expectedDisplayName, gameShardId) }
        }

        @Test
        fun `getPragmaPlayerIdForDisplayNameV1 throws application error when display name not found`() = runTest {
            val pragmaPlayerIdRequest = getPragmaPlayerIdForDisplayNameV1Request(
                1, gameShardId = gameShardId.toFixed128()
            )
            val missingPragmaDisplayName = PragmaDisplayName(pragmaPlayerIdRequest.displayName, pragmaPlayerIdRequest.discriminator)
            coEvery {
                accountDaoNodeService.getPragmaPlayerIdForDisplayName(missingPragmaDisplayName, gameShardId)
            } returns null

            coAssertThrowsApplicationErrorException(
                buildNotFoundApplicationError(
                    missingPragmaDisplayName.fullDisplayName(),
                    "No playerId found for displayName ${missingPragmaDisplayName.fullDisplayName()}"
                )
            ) {
                testObj.getPragmaPlayerIdForDisplayNameV1(socialPlayerSession, pragmaPlayerIdRequest)
            }
        }

        @Test
        fun `getDisplayNameForPragmaPlayerIdV1 returns display name object`() = runTest {
            val playerId = playerIdentity1.pragmaPlayerId
            val displayName = playerIdentity1.pragmaDisplayName
            coEvery { accountDaoNodeService.getPlayerDisplayNameForPragmaPlayerId(playerId, gameShardId) } returns displayName

            val displayNameRequest = getDisplayNameForPragmaPlayerIdV1Request(1, playerId.toFixed128(), gameShardId.toFixed128())

            val result = testObj.getDisplayNameForPragmaPlayerIdV1(socialPlayerSession, displayNameRequest)

            assertEquals(displayName.displayName, result.displayName)
            assertEquals(displayName.discriminator, result.discriminator)
        }

        @Test
        fun `getDisplayNameForPragmaPlayerIdV1 throws application error when player id not found`() = runTest {
            val missingPlayerId = playerIdentity1.pragmaPlayerId
            coEvery { accountDaoNodeService.getPlayerDisplayNameForPragmaPlayerId(missingPlayerId, gameShardId) } returns null

            val displayNameRequest = getDisplayNameForPragmaPlayerIdV1Request(
                1, missingPlayerId.toFixed128(), gameShardId.toFixed128()
            )

            coAssertThrowsApplicationErrorException(buildPragmaPlayerIdNotFoundApplicationError(missingPlayerId, gameShardId)) {
                testObj.getDisplayNameForPragmaPlayerIdV1(socialPlayerSession, displayNameRequest)
            }
        }

        @Test
        fun `getDisplayNameForPlayerIdPartnerV2 returns display name object`() = runTest {
            val playerId = playerIdentity1.pragmaPlayerId
            val displayName = playerIdentity1.pragmaDisplayName
            coEvery { accountDaoNodeService.getPlayerDisplayNameForPragmaPlayerId(any(), any()) } returns displayName

            val displayNameRequest = getDisplayNameForPlayerIdPartnerV2Request(
                1, playerId.toFixed128(), gameShardId.toFixed128()
            )
            val displayNameActual = testObj.getDisplayNameForPlayerIdPartnerV2(partnerSession, displayNameRequest)

            assertEquals(displayName.displayName, displayNameActual.displayName.displayName)
            assertEquals(displayName.discriminator, displayNameActual.displayName.discriminator)
        }

        @Test
        fun `getDisplayNameForPlayerIdPartnerV2 throws application error when player id not found`() = runTest {
            val missingPlayerId = playerIdentity1.pragmaPlayerId
            coEvery { accountDaoNodeService.getPlayerDisplayNameForPragmaPlayerId(missingPlayerId, gameShardId) } returns null
            val displayNameRequest = getDisplayNameForPlayerIdPartnerV2Request(
                1, missingPlayerId.toFixed128(), gameShardId.toFixed128()
            )

            coAssertThrowsApplicationErrorException(buildPragmaPlayerIdNotFoundApplicationError(missingPlayerId, gameShardId)) {
                testObj.getDisplayNameForPlayerIdPartnerV2(partnerSession, displayNameRequest)
            }
        }
    }

    @Nested
    inner class GetPlayerIdentities {
        @Test
        fun `getPlayerIdentitiesV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::getPlayerIdentitiesV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.PLAYER, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `getPlayerIdentitiesV1 calls accountRegistrar with appropriate request information`() = runTest {
            val request = getPlayerIdentitiesV1Request(1)
            val playerIdentity1 = PlayerIdentity(playerIdentity(1))
            val playerIdentity2 = PlayerIdentity(playerIdentity(2))
            val playerIdentities = GetPlayerIdentitiesResponse(listOf(playerIdentity1, playerIdentity2))
            val publicPlayerIdentity1 =
                PlayerIdentity(playerIdentity(1, idProviderList = playerIdentity1.providerAccounts.take(1).map { it.toProto() }))
            val publicPlayerIdentity2 =
                PlayerIdentity(playerIdentity(2, idProviderList = playerIdentity2.providerAccounts.take(2).map { it.toProto() }))
            coEvery { publicIdentityConverter.convertToPublicPlayerIdentity(playerIdentity1) } returns publicPlayerIdentity1
            coEvery { publicIdentityConverter.convertToPublicPlayerIdentity(playerIdentity2) } returns publicPlayerIdentity2
            coEvery {
                accountRegistrar.getPlayerIdentitiesForPlayerIds(
                    request.playerIdsList.map { it.toUUID() },
                    request.gameShardId.toUUID(),
                    false
                )
            } returns playerIdentities

            val actualResponse = testObj.getPlayerIdentitiesV1(socialPlayerSession, request)

            val expectedPublicPlayerIdentities = listOf(publicPlayerIdentity1, publicPlayerIdentity2).map { it.toProto() }
            val expectedResponse = getPlayerIdentitiesV1Response(1, expectedPublicPlayerIdentities)
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getPlayerIdentitiesPartnerV1 calls accountRegistrar with appropriate request information`() = runTest {
            val expectedIdentities =
                GetPlayerIdentitiesResponse(listOf(PlayerIdentity(playerIdentity(1)), PlayerIdentity(playerIdentity(2))))
            val request = getPlayerIdentitiesPartnerV1Request(1, shouldIncludeInactive = true)
            coEvery {
                accountRegistrar.getPlayerIdentitiesForPlayerIds(
                    request.playerIdsList.map { it.toUUID() },
                    request.gameShardId.toUUID(),
                    request.includeInactiveIdProviders,
                )
            } returns expectedIdentities

            val actualIdentities = testObj.getPlayerIdentitiesPartnerV1(partnerSession, request)

            assertEquals(expectedIdentities.toPartnerProto(), actualIdentities)
        }

        @Test
        fun `getPlayerIdentitiesServiceV1 calls accountRegistrar with appropriate request information`() = runTest {
            val expectedIdentities =
                GetPlayerIdentitiesResponse(listOf(PlayerIdentity(playerIdentity(1)), PlayerIdentity(playerIdentity(2))))
            val request = getPlayerIdentitiesServiceV1Request(1)
            coEvery {
                accountRegistrar.getPlayerIdentitiesForPlayerIds(
                    request.playerIdsList.map { it.toUUID() },
                    request.gameShardId.toUUID(),
                    request.includeInactiveIdProviders,
                )
            } returns expectedIdentities

            val actualIdentities = testObj.getPlayerIdentitiesServiceV1(serviceSession, request)

            assertEquals(expectedIdentities.toServiceProto(), actualIdentities)
        }

        @Test
        fun `getPlayerIdentitiesOperatorV1 calls accountRegistrar with appropriate request information`() = runTest {
            val expectedIdentities =
                GetPlayerIdentitiesResponse(listOf(PlayerIdentity(playerIdentity(1)), PlayerIdentity(playerIdentity(2))))
            val request = getPlayerIdentitiesOperatorV1Request(1)
            coEvery { accountRegistrar.getPlayerIdentitiesForPlayerIds(
                request.playerIdsList.map { it.toUUID() },
                request.gameShardId.toUUID(),
                request.includeInactiveIdProviders,
            ) } returns expectedIdentities


            val actualIdentities = testObj.getPlayerIdentitiesOperatorV1(operatorSession, request)

            assertEquals(expectedIdentities.toOperatorProto(), actualIdentities)
        }

        @Test
        fun `getSocialIdentitiesV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::getSocialIdentitiesV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.PLAYER, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `getSocialIdentitiesV1 calls accountDaoNodeService with appropriate request information`() = runTest {
            val request = getSocialIdentitiesV1Request(1)
            val socialIdentity1 = SocialIdentity(socialIdentityProto(1))
            val socialIdentity2 = SocialIdentity(socialIdentityProto(2))
            val socialIdentities = listOf(socialIdentity1, socialIdentity2)
            val publicSocialIdentity1 = PublicSocialIdentity(
                publicSocialIdentity(
                    1,
                    idProviderAccounts = socialIdentity1.providerAccounts.take(1).map { it.toProto() })
            )
            val publicSocialIdentity2 = PublicSocialIdentity(
                publicSocialIdentity(
                    2,
                    idProviderAccounts = socialIdentity2.providerAccounts.take(2).map { it.toProto() })
            )
            coEvery { accountDaoNodeService.getSocialIdentitiesForPragmaSocialIds(request.socialIdsList.map { it.toUUID() }) } returns GetSocialIdentitiesResponse(
                socialIdentities
            )
            coEvery { publicIdentityConverter.convertToPublicSocialIdentity(socialIdentity1) } returns publicSocialIdentity1
            coEvery { publicIdentityConverter.convertToPublicSocialIdentity(socialIdentity2) } returns publicSocialIdentity2

            val actualResponse = testObj.getSocialIdentitiesV1(socialPlayerSession, request)

            val expectedPublicSocialIdentities = listOf(publicSocialIdentity1, publicSocialIdentity2).map { it.toProto() }
            val expectedResponse = getSocialIdentitiesV1Response(1, expectedPublicSocialIdentities)
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getSocialIdentitiesPartnerV1 calls accountDaoNodeService  with appropriate request information`() = runTest {
            val expectedIdentities =
                GetSocialIdentitiesResponse(listOf(SocialIdentity(socialIdentityProto(1)), SocialIdentity(socialIdentityProto(2))))
            coEvery { accountDaoNodeService.getSocialIdentitiesForPragmaSocialIds(any()) } returns expectedIdentities

            val request = getSocialIdentitiesPartnerV1Request(1)
            val actualIdentities = testObj.getSocialIdentitiesPartnerV1(partnerSession, request)

            assertEquals(expectedIdentities.toPartnerProto(), actualIdentities)
            coVerify {
                accountDaoNodeService.getSocialIdentitiesForPragmaSocialIds(request.socialIdsList.map { it.toUUID() })
            }
        }

        @Test
        fun `getSocialIdentitiesServiceV1 calls accountDaoNodeService with appropriate request information`() = runTest {
            val expectedIdentities =
                GetSocialIdentitiesResponse(listOf(SocialIdentity(socialIdentityProto(1)), SocialIdentity(socialIdentityProto(2))))
            coEvery { accountDaoNodeService.getSocialIdentitiesForPragmaSocialIds(any()) } returns expectedIdentities

            val request = getSocialIdentitiesServiceV1Request(1)
            val actualIdentities = testObj.getSocialIdentitiesServiceV1(serviceSession, request)

            assertEquals(expectedIdentities.toServiceProto(), actualIdentities)
            coVerify {
                accountDaoNodeService.getSocialIdentitiesForPragmaSocialIds(request.socialIdsList.map { it.toUUID() })
            }
        }

        @Test
        fun `getSocialIdentitiesOperatorV1 calls accountDaoNodeService with appropriate request information`() = runTest {
            val expectedIdentities =
                GetSocialIdentitiesResponse(listOf(SocialIdentity(socialIdentityProto(1)), SocialIdentity(socialIdentityProto(2))))
            coEvery { accountDaoNodeService.getSocialIdentitiesForPragmaSocialIds(any()) } returns expectedIdentities

            val request = getSocialIdentitiesOperatorV1Request(1)
            val actualIdentities = testObj.getSocialIdentitiesOperatorV1(operatorSession, request)

            assertEquals(expectedIdentities.toOperatorProto(), actualIdentities)
            coVerify {
                accountDaoNodeService.getSocialIdentitiesForPragmaSocialIds(request.socialIdsList.map { it.toUUID() })
            }
        }
    }

    @Nested
    inner class AccountOverviewEndpoints {
        private val expectedOverview = AccountCommonTestFactory.pragmaAccountOverview(1)

        @Test
        fun `getPlayerCountBySearchFilterOperatorV1 returns a count of matched players`() = runTest {
            coEvery { accountDaoNodeService.getPragmaAccountCountByFilter(any()) } returns 1

            val playerCountRequest = getPlayerCountBySearchFilterOperatorV1Request(1)
            val result = testObj.getPlayerCountBySearchFilterOperatorV1(operatorSession, playerCountRequest)

            assertEquals(1, result.playerCount)

            coVerify {
                accountDaoNodeService.getPragmaAccountCountByFilter(
                    PlayerSearchFilter(playerCountRequest.filter)
                )
            }
        }

        @Test
        fun `getPragmaAccountOverviewsOperatorV1Request returns a list of matched players`() = runTest {
            coEvery { accountDaoNodeService.getPragmaAccountOverviewsByFilter(any(), any(), any()) } returns paginationListOf(
                expectedOverview,
                totalCount = 0
            )

            val accountOverviewsRequest = getPragmaAccountOverviewsOperatorV1Request(1)
            val result = testObj.getPragmaAccountOverviewsOperatorV1(operatorSession, accountOverviewsRequest)

            assertEquals(0, result.count)
            assertEquals(expectedOverview.toProto(), result.accountsList.single())

            coVerify {
                accountDaoNodeService.getPragmaAccountOverviewsByFilter(
                    PlayerSearchFilter(accountOverviewsRequest.filter),
                    accountOverviewsRequest.pageSize * accountOverviewsRequest.pageIndex,
                    accountOverviewsRequest.pageSize
                )
            }
        }

        @Test
        fun `getPragmaAccountOverviewsPartnerV1Request returns a list of matched players`() = runTest {
            coEvery { accountDaoNodeService.getPragmaAccountOverviewsByFilter(any(), any(), any()) } returns paginationListOf(
                expectedOverview,
                totalCount = 1
            )

            val accountOverviewsRequest = getPragmaAccountOverviewsPartnerV1Request(1)
            val result = testObj.getPragmaAccountOverviewsPartnerV1(partnerSession, accountOverviewsRequest)

            assertEquals(1, result.count)
            assertEquals(expectedOverview.toProto(), result.accountsList.single())
            coVerify {
                accountDaoNodeService.getPragmaAccountOverviewsByFilter(
                    PlayerSearchFilter(accountOverviewsRequest.filter),
                    accountOverviewsRequest.pageSize * accountOverviewsRequest.pageIndex,
                    accountOverviewsRequest.pageSize
                )
            }
        }

        @Test
        fun `getPragmaAccountOverviewsServiceV1Request returns a list of matched players`() = runTest {
            coEvery { accountDaoNodeService.getPragmaAccountOverviewsByFilter(any(), any(), any()) } returns paginationListOf(
                expectedOverview,
                totalCount = 1
            )

            val accountOverviewsRequest = getPragmaAccountOverviewsServiceV1Request(1)
            val result = testObj.getPragmaAccountOverviewsServiceV1(serviceSession, accountOverviewsRequest)

            assertEquals(1, result.count)
            assertEquals(expectedOverview.toProto(), result.accountsList.single())
            coVerify {
                accountDaoNodeService.getPragmaAccountOverviewsByFilter(
                    PlayerSearchFilter(accountOverviewsRequest.filter),
                    accountOverviewsRequest.pageSize * accountOverviewsRequest.pageIndex,
                    accountOverviewsRequest.pageSize
                )
            }
        }

        @Test
        fun `getPragmaPlayerOverviewsOperatorV1Request returns a list of matched players`() = runTest {
            val expected = AccountCommonTestFactory.pragmaPlayerOverview(1)
            coEvery {
                accountDaoNodeService.getPragmaPlayerOverviewsByFilter(
                    any(),
                    any(),
                    any(),
                    any()
                )
            } returns paginationListOf(expected, totalCount = 1)

            val playerOverviewsRequest = getPragmaPlayerOverviewsOperatorV1Request(1)
            val result = testObj.getPragmaPlayerOverviewsOperatorV1(operatorSession, playerOverviewsRequest)

            assertEquals(1, result.count)
            assertEquals(expected.toProto(), result.playersList.single())
            coVerify {
                accountDaoNodeService.getPragmaPlayerOverviewsByFilter(
                    PlayerSearchFilter(playerOverviewsRequest.filter),
                    playerOverviewsRequest.gameShardId.toUUID(),
                    playerOverviewsRequest.pageSize * playerOverviewsRequest.pageIndex,
                    playerOverviewsRequest.pageSize,
                )
            }
        }
    }

    @Nested
    inner class CreateTestAccounts {
        @Test
        fun `createTestAccountsOperatorV2 adds accounts with display names`() = runTest {
            val gameShardId = 1.toFixed128()
            val newAccounts = listOf(createTestAccount(21), createTestAccount(22))
            val createUnsafeAccounts = newAccounts.map { it.unsafeAccount }
            coEvery {
                unsafeIdentityDaoNodeService.insertOrIgnoreAccounts(createUnsafeAccounts)
            } returns Pair(mutableListOf(createUnsafeAccounts[0].id, createUnsafeAccounts[1].id), mutableListOf())

            createUnsafeAccounts.forEach {
                val authenticateOrCreateRequest = AccountRpc.AuthenticateOrCreateV2Request.newBuilder()
                    .setSessionType(PragmaOptions.PragmaSessionType.PLAYER)
                    .setGameShardId(gameShardId)
                    .setProviderId(IdProvider.UNSAFE.name)
                    .setProviderToken("{\"accountId\":\"${it.id}\",\"displayName\":\"${it.displayName}\"}")
                    .build()
                coEvery {
                    pragmaNode.requestRpcV2(
                        testObj.serviceSession.toPragmaSession(),
                        authenticateOrCreateRequest,
                    )
                } returns Success(
                    typeInfoAndSerializedMessage(
                        1,
                        AuthenticateOrCreateV2Response.getDefaultInstance(),
                        AuthenticateOrCreateV2Response::parseFrom
                    )
                )
            }

            val createAccountRequest = AccountRpc.CreateTestAccountsOperatorV2Request.newBuilder()
                .addAllNewAccounts(newAccounts)
                .setGameShardId(gameShardId)
                .build()
            val response = testObj.createTestAccountsOperatorV2(operatorSession, createAccountRequest)

            assertEquals(createUnsafeAccounts.map { it.id }, response.addedAccountIdsList)
            assertEquals(listOf<String>(), response.failedToAddAccountIdsList)
        }

        @Test
        fun `createTestAccountsOperatorV2 uses default display names if none provided`() = runTest {
            val gameShardId = 1.toFixed128()
            val newAccounts = listOf(createTestAccount(21), createTestAccount(22))
            val createUnsafeAccounts = newAccounts.map { it.unsafeAccount }
            coEvery {
                unsafeIdentityDaoNodeService.insertOrIgnoreAccounts(createUnsafeAccounts)
            } returns Pair(mutableListOf(createUnsafeAccounts[0].id, createUnsafeAccounts[1].id), mutableListOf())

            createUnsafeAccounts.forEach {
                val authenticateOrCreateRequest = AccountRpc.AuthenticateOrCreateV2Request.newBuilder()
                    .setSessionType(PragmaOptions.PragmaSessionType.PLAYER)
                    .setGameShardId(gameShardId)
                    .setProviderId(IdProvider.UNSAFE.name)
                    .setProviderToken("{\"accountId\":\"${it.id}\",\"displayName\":\"${it.id}\"}")
                    .build()
                coEvery {
                    pragmaNode.requestRpcV2(
                        testObj.serviceSession.toPragmaSession(),
                        authenticateOrCreateRequest,
                    )
                } returns Success(
                    typeInfoAndSerializedMessage(
                        1,
                        AuthenticateOrCreateV2Response.getDefaultInstance(),
                        AuthenticateOrCreateV2Response::parseFrom
                    )
                )
            }

            val createAccountRequest = AccountRpc.CreateTestAccountsOperatorV2Request.newBuilder()
                .addAllNewAccounts(newAccounts)
                .setGameShardId(gameShardId)
                .build()
            val response = testObj.createTestAccountsOperatorV2(operatorSession, createAccountRequest)

            assertEquals(createUnsafeAccounts.map { it.id }, response.addedAccountIdsList)
            assertEquals(listOf<String>(), response.failedToAddAccountIdsList)
        }

        @Test
        fun `createTestAccountsOperatorV2 respects is_banned attribute`() = runTest {
            val gameShardId = 1.toFixed128()
            val newAccounts = listOf(createTestAccount(21, isBanned = true), createTestAccount(22), createTestAccount(23, isBanned = true))
            val createUnsafeAccounts = newAccounts.map { it.unsafeAccount }
            coEvery {
                unsafeIdentityDaoNodeService.insertOrIgnoreAccounts(createUnsafeAccounts)
            } returns Pair(createUnsafeAccounts.map { it.id }.toMutableList(), mutableListOf())

            val expectedBannedSocialIds = mutableListOf<UUID>()
            newAccounts.forEachIndexed { i, account ->
                val socialId = anyUuid()
                if (account.pragmaAccount.isBanned) {
                    expectedBannedSocialIds.add(socialId)
                }

                val socialToken = socialToken(i)
                val payload = mockk<Payload>()
                every { tokenDecoderNodeService.decodeAndVerifyJwt(socialToken) } returns payload
                every { payload.getStringClaim(PrivateJwtClaims.pragmaSocialId) } returns socialId.toString()

                val authenticateOrCreateRequest = AccountRpc.AuthenticateOrCreateV2Request.newBuilder()
                    .setSessionType(PragmaOptions.PragmaSessionType.PLAYER)
                    .setGameShardId(gameShardId)
                    .setProviderId(IdProvider.UNSAFE.name)
                    .setProviderToken("{\"accountId\":\"${account.unsafeAccount.id}\",\"displayName\":\"${account.unsafeAccount.id}\"}")
                    .build()
                coEvery {
                    pragmaNode.requestRpcV2(
                        testObj.serviceSession.toPragmaSession(),
                        authenticateOrCreateRequest,
                    )
                } returns Success(
                    typeInfoAndSerializedMessage(
                        1,
                        authenticateOrCreateV2Response(i),
                        AuthenticateOrCreateV2Response::parseFrom
                    )
                )

                coEvery {
                    pragmaNode.requestRpcV2(
                        testObj.serviceSession.toPragmaSession(),
                        createBanRequestFrom(socialId),
                    )
                } returns Success(typeInfoAndSerializedMessage(1, banAccountServiceV1Response(1), BanAccountServiceV1Response::parseFrom))
            }

            val createAccountRequest = AccountRpc.CreateTestAccountsOperatorV2Request.newBuilder()
                .addAllNewAccounts(newAccounts)
                .setGameShardId(gameShardId)
                .build()
            val response = testObj.createTestAccountsOperatorV2(operatorSession, createAccountRequest)

            assertEquals(createUnsafeAccounts.map { it.id }, response.addedAccountIdsList)
            assertEquals(listOf<String>(), response.failedToAddAccountIdsList)
            assertEquals(2, expectedBannedSocialIds.size)
            coVerify(exactly = expectedBannedSocialIds.size) {
                pragmaNode.requestRpcV2(
                    testObj.serviceSession.toPragmaSession(),
                    ofType<BanAccountServiceV1Request>()
                )
            }
            expectedBannedSocialIds.forEach { socialId ->
                coVerify {
                    pragmaNode.requestRpcV2(
                        testObj.serviceSession.toPragmaSession(),
                        createBanRequestFrom(socialId),
                    )
                }
            }
        }

        private fun createBanRequestFrom(socialId: UUID) = banAccountServiceV1Request(
            1,
            socialId.toFixed128(),
            gameShardId = gameShardId(0),
            durationInMillis = Long.MAX_VALUE,
            comment = "ban created by createTestAccountsOperatorV2"
        )
    }

    @Test
    fun `createTestUnsafeAccountsOnlyOperatorV1Request creates unsafe accounts`() = runTest {
        val addedUnsafeAccounts = listOf(createTestUnsafeAccount(23), createTestUnsafeAccount(24))
        val failedUnsafeAccounts = listOf(createTestUnsafeAccount(25))
        val newAccounts = listOf(
            *addedUnsafeAccounts.toTypedArray(),
            *failedUnsafeAccounts.toTypedArray()
        )
        coEvery {
            unsafeIdentityDaoNodeService.insertOrIgnoreAccounts(newAccounts)
        } returns Pair(addedUnsafeAccounts.map { it.id }, failedUnsafeAccounts.map { it.id })

        val createAccountRequest = AccountRpc.CreateTestUnsafeAccountsOnlyOperatorV1Request.newBuilder()
            .addAllNewAccounts(newAccounts)
            .build()
        val response = testObj.createTestUnsafeAccountsOnlyOperatorV1(operatorSession, createAccountRequest)

        assertEquals(addedUnsafeAccounts.map { it.id }, response.addedAccountIdsList)
        assertEquals(failedUnsafeAccounts.map { it.id }, response.failedToAddAccountIdsList)
        coVerify(exactly = 0) {
            pragmaNode.requestRpcV2(any(), any())
        }
    }

    @Nested
    inner class AccountLinkingEndpoints {
        @Test
        fun `linkIdentityProviderAccountPartnerV1 calls account registrar with appropriate request information`() = runTest {
            val request = linkIdentityProviderAccountPartnerV1Request()
            val sessionTokens = SessionTokens(socialToken(1), gameToken(1))
            val accountRegistrarResponse = SessionTokensResult(sessionTokens)
            coEvery { accountRegistrar.federate(LinkIdentityProviderAccountRequest(request)) } returns accountRegistrarResponse

            val response = testObj.linkIdentityProviderAccountPartnerV1(partnerSession, request)

            assertEquals(accountRegistrarResponse.toLinkIdentityProviderAccountPartnerProtoV1(), response)
            coVerify { accountRegistrar.federate(LinkIdentityProviderAccountRequest(request)) }
        }

        @Test
        fun `linkIdentityProviderAccountServiceV1 calls account registrar with appropriate request information`() = runTest {
            val request = linkIdentityProviderAccountServiceV1Request(1)
            val sessionTokens = SessionTokens(socialToken(1), gameToken(1))
            val accountRegistrarResponse = SessionTokensResult(sessionTokens)
            coEvery { accountRegistrar.federate(LinkIdentityProviderAccountRequest(request)) } returns accountRegistrarResponse

            val response = testObj.linkIdentityProviderAccountServiceV1(serviceSession, request)

            assertEquals(accountRegistrarResponse.toLinkIdentityProviderAccountServiceProtoV1(), response)
            coVerify { accountRegistrar.federate(LinkIdentityProviderAccountRequest(request)) }
        }

        @Test
        fun `linkIdentityProviderAccountV2 calls account registrar with appropriate request information`() = runTest {
            val request = linkIdentityProviderAccountV2Request(IdProvider.CUSTOM_VALUE)
            val sessionTokens = SessionTokens("", "")
            val accountRegistrarResponse = SessionTokensResult(sessionTokens)
            coEvery {
                accountRegistrar.federateWithExistingSocial(
                    socialPlayerSession,
                    IdProviderRequest(request.newProvider)
                )
            } returns accountRegistrarResponse

            val response = testObj.linkIdentityProviderAccountV2(socialPlayerSession, request)

            assertEquals(accountRegistrarResponse.toLinkIdentityProviderAccountV2(), response)
            coVerify { accountRegistrar.federateWithExistingSocial(socialPlayerSession, IdProviderRequest(request.newProvider)) }
        }

        @Test
        fun `deleteIdentityProviderAccountOperatorV1 calls account registrar with appropriate request information`() = runTest {
            val request = deleteIdentityProviderAccountOperatorV1Request(1, socialPlayerSession.socialId, IdProvider.CUSTOM_VALUE)
            val socialId = request.pragmaSocialId.toUUID()
            val idProvider = request.idProvider.toIdProviderIntType()
            coJustRun { accountRegistrar.deleteIdProviderAccount(socialId, idProvider) }

            val response = testObj.deleteIdentityProviderAccountOperatorV1(operatorSession, request)

            val expectedResponse = DeleteIdentityProviderAccountOperatorV1Response.getDefaultInstance()
            assertEquals(expectedResponse, response)
            coVerify { accountRegistrar.deleteIdProviderAccount(socialId, idProvider) }
        }

        @Test
        fun `deleteIdentityProviderAccountPartnerV1 calls account registrar with appropriate request information`() = runTest {
            val request = deleteIdentityProviderAccountPartnerV1Request(1, socialPlayerSession.socialId, IdProvider.CUSTOM_VALUE)
            val socialId = request.pragmaSocialId.toUUID()
            val idProvider = request.idProvider.toIdProviderIntType()
            coJustRun { accountRegistrar.deleteIdProviderAccount(socialId, idProvider) }

            val response = testObj.deleteIdentityProviderAccountPartnerV1(partnerSession, request)

            val expectedResponse = DeleteIdentityProviderAccountPartnerV1Response.getDefaultInstance()
            assertEquals(expectedResponse, response)
            coVerify { accountRegistrar.deleteIdProviderAccount(socialId, idProvider) }
        }

        @Test
        fun `deleteIdentityProviderAccountServiceV1 calls account registrar with appropriate request information`() = runTest {
            val request = deleteIdentityProviderAccountServiceV1Request(1, socialPlayerSession.socialId, IdProvider.CUSTOM_VALUE)
            val socialId = request.pragmaSocialId.toUUID()
            val idProvider = request.idProvider.toIdProviderIntType()
            coJustRun { accountRegistrar.deleteIdProviderAccount(socialId, idProvider) }

            val response = testObj.deleteIdentityProviderAccountServiceV1(serviceSession, request)

            val expectedResponse = DeleteIdentityProviderAccountServiceV1Response.getDefaultInstance()
            assertEquals(expectedResponse, response)
            coVerify { accountRegistrar.deleteIdProviderAccount(socialId, idProvider) }
        }

        @Test
        fun `unlinkIdentityProviderAccountV2 calls proper objects with proper information`() = runTest{
            val request = unlinkIdentityProviderAccountV2(IdProvider.CUSTOM_VALUE)
            coJustRun{ accountRegistrar.unlinkIdProviderAccount(socialPlayerSession.socialId.toUUID(), request.idProvider.toIdProviderIntType()) }

            val response = testObj.unlinkIdentityProviderAccountV2(socialPlayerSession, request)

            assertEquals(UnlinkIdentityProviderAccountV2Response.getDefaultInstance(), response)
            coVerify { accountRegistrar.unlinkIdProviderAccount(socialPlayerSession.socialId.toUUID(), request.idProvider.toIdProviderIntType()) }
        }

        @Test
        fun `unlinkIdentityProviderAccountV2 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::unlinkIdentityProviderAccountV2.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.PLAYER, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }


    }

    @Nested
    inner class DiscordEndpoints {
        private val discordAccessToken = DiscordAccessToken(anyString(), anyString(), anyInt(), anyString(), anyString())

        @BeforeEach
        fun setup() {
            testObj.identityProviders.add(IdProvider.DISCORD_VALUE, discordIdentityProvider)
        }

        @Test
        fun `discordRedirect calls to discord id provider for access token`() = runTest {
            coEvery {
                discordIdentityProvider.accessToken(any(), any(), any())
            } returns discordAccessToken

            val request = discordRedirectV1Request(1)
            val response = testObj.discordRedirectV1(serviceSession, request)

            assertEquals(discordAccessToken.accessToken, response.token.accessToken)
            assertEquals(discordAccessToken.expiresIn, response.token.expiresIn)
            assertEquals(discordAccessToken.refreshToken, response.token.refreshToken)
            assertEquals(discordAccessToken.scope, response.token.scope)
            assertEquals(discordAccessToken.tokenType, response.token.tokenType)
            coVerify { discordIdentityProvider.accessToken(request.code, request.redirectUri, request.codeVerifier) }
        }
    }

    @Nested
    inner class TwitchEndpoints {
        private val twitchAccessToken =
            TwitchAccessToken(anyString(), anyString(), anyInt(), anyString(), listOf(anyString(), anyString()))

        @BeforeEach
        fun setup() {
            testObj.identityProviders.add(IdProvider.TWITCH_VALUE, twitchIdentityProvider)
        }

        @Test
        fun `twitchRedirect calls to twitch id provider for access token`() = runTest {
            coEvery { twitchIdentityProvider.accessToken(any(), any()) } returns twitchAccessToken

            val request = twitchRedirectV1Request()
            val response = testObj.twitchRedirectV1(serviceSession, request)

            assertEquals(twitchAccessToken.toProto(), response.token)
            coVerify { twitchIdentityProvider.accessToken(request.code, request.redirectUri) }
        }
    }

    @Nested
    inner class GoogleEndpoints {
        private val googleAccessToken = OAuthAccessToken(anyString(), anyString(), anyInt(), anyString(), anyString())

        @BeforeEach
        fun setup() {
            testObj.identityProviders.add(IdProvider.GOOGLE_VALUE, googleIdentityProvider)
        }

        @Test
        fun `googleRedirect calls to google id provider for access token`() = runTest {
            coEvery { googleIdentityProvider.accessToken(any(), any()) } returns googleAccessToken

            val request = googleRedirectV1Request()
            val response = testObj.googleRedirectV1(serviceSession, request)

            assertEquals(googleAccessToken.toProto(), response.token)
            coVerify { googleIdentityProvider.accessToken(request.code, request.redirectUri) }
        }
    }

    @Nested
    inner class OAuthEndpoints {
        private val auth0AccessToken = OAuthAccessToken(anyString(), anyString(), anyInt(), anyString(), anyString())

        @BeforeEach
        fun setup() {
            testObj.identityProviders.add(IdProvider.AUTH0_VALUE, auth0IdentityProvider)
            testObj.identityProviders.add(IdProvider.STEAM_VALUE, steamIdentityProvider)
        }

        @Test
        fun `oAuthRedirect calls to given OAuth id provider for access token`() = runTest {
            coEvery { auth0IdentityProvider.accessToken(any(), any()) } returns auth0AccessToken

            val request = oAuthRedirectV1Request(IdProvider.AUTH0.name)
            val response = testObj.oAuthRedirectV1(serviceSession, request)

            assertEquals(auth0AccessToken.toProto(), response.token)
            coVerify { auth0IdentityProvider.accessToken(request.code, request.redirectUri) }
        }

        @Test
        fun `oAuthRedirect throws application error when given id provider is not an OAuth provider`() = runTest {
            val request = oAuthRedirectV1Request(IdProvider.STEAM.name)
            val expectedApplicationError = ValidationFailedApplicationError.newBuilder()
                .setField(IdProviderAccount::idProvider.name)
                .setReason("Given id provider ${IdProvider.STEAM.name} is not an OAuth provider.")
                .build()

            coAssertThrowsApplicationErrorException(expectedApplicationError) {
                testObj.oAuthRedirectV1(serviceSession, request)
            }
        }
    }

    @Nested
    inner class PlayerGroupEndpoints {
        @Test
        fun `editPlayerGroupOperatorV1 calls viewPlayerGroup and returns the response`() = runTest {
            val expectedGroup = mockk<PlayerGroup>(relaxed = true)
            coEvery { playerGroupProxy.editPlayerGroupOperator(any(), any(), any()) } returns expectedGroup

            val request = editPlayerGroupOperatorV1Request(1)
            val result = testObj.editPlayerGroupOperatorV1(operatorSession, request)

            val expectedProto = AccountRpc.EditPlayerGroupOperatorV1Response.newBuilder()
                .setPlayerGroup(expectedGroup.toProto())
                .build()
            assertEquals(expectedProto, result)
            coVerify {
                playerGroupProxy.editPlayerGroupOperator(
                    request.playerGroupId.toUUID(),
                    request.name,
                    request.description
                )
            }
        }
        @Test
        fun `editPlayerGroupPartnerV1 calls viewPlayerGroup and returns the response`() = runTest {
            val expectedGroup = mockk<PlayerGroup>(relaxed = true)
            coEvery { playerGroupProxy.editPlayerGroupOperator(any(), any(), any()) } returns expectedGroup

            val request = editPlayerGroupPartnerV1Request(1)
            val result = testObj.editPlayerGroupPartnerV1(partnerSession, request)

            val expectedProto = AccountRpc.EditPlayerGroupPartnerV1Response.newBuilder()
                .setPlayerGroup(expectedGroup.toProto())
                .build()
            assertEquals(expectedProto, result)
            coVerify {
                playerGroupProxy.editPlayerGroupOperator(
                    request.playerGroupId.toUUID(),
                    request.name,
                    request.description
                )
            }
        }
        @Test
        fun `editPlayerGroupServiceV1 calls viewPlayerGroup and returns the response`() = runTest {
            val expectedGroup = mockk<PlayerGroup>(relaxed = true)
            coEvery { playerGroupProxy.editPlayerGroupOperator(any(), any(), any()) } returns expectedGroup

            val request = editPlayerGroupServiceV1Request(1)
            val result = testObj.editPlayerGroupServiceV1(serviceSession, request)

            val expectedProto = AccountRpc.EditPlayerGroupServiceV1Response.newBuilder()
                .setPlayerGroup(expectedGroup.toProto())
                .build()
            assertEquals(expectedProto, result)
            coVerify {
                playerGroupProxy.editPlayerGroupOperator(
                    request.playerGroupId.toUUID(),
                    request.name,
                    request.description
                )
            }
        }

        @Test
        fun `viewPlayerGroupOperatorV1 calls viewPlayerGroup and returns the response`() = runTest {
            val expectedPlayerGroup = playerGroup(1)
            val expectedPlayerGroupAccounts = listOf(pragmaAccountOverview(1))
            coEvery { playerGroupProxy.viewPlayerGroup(any()) } returns DetailedPlayerGroup(
                PlayerGroup(expectedPlayerGroup),
                expectedPlayerGroupAccounts.map { PragmaAccountOverview(it) }
            )

            val request = viewPlayerGroupOperatorV1Request(1)
            val response = testObj.viewPlayerGroupOperatorV1(operatorSession, request)

            assertEquals(response.playerGroup, expectedPlayerGroup)
            assertEquals(response.accountsList, expectedPlayerGroupAccounts)
            coVerify { playerGroupProxy.viewPlayerGroup(ViewPlayerGroupRequest(request)) }
        }

        @Test
        fun `viewPlayerGroupPartnerV1 calls viewPlayerGroup and returns the response`() = runTest {
            val expectedPlayerGroup = playerGroup(1)
            val expectedPlayerGroupAccounts = listOf(pragmaAccountOverview(1))
            coEvery { playerGroupProxy.viewPlayerGroup(any()) } returns DetailedPlayerGroup(
                PlayerGroup(expectedPlayerGroup),
                expectedPlayerGroupAccounts.map { PragmaAccountOverview(it) }
            )

            val request = viewPlayerGroupPartnerV1Request(1)
            val response = testObj.viewPlayerGroupPartnerV1(partnerSession, request)

            assertEquals(response.playerGroup, expectedPlayerGroup)
            assertEquals(response.accountsList, expectedPlayerGroupAccounts)
            coVerify { playerGroupProxy.viewPlayerGroup(ViewPlayerGroupRequest(request)) }
        }

        @Test
        fun `viewPlayerGroupServiceV1 calls viewPlayerGroup and returns the response`() = runTest {
            val expectedPlayerGroup = playerGroup(1)
            val expectedPlayerGroupAccounts = listOf(pragmaAccountOverview(1))
            coEvery { playerGroupProxy.viewPlayerGroup(any()) } returns DetailedPlayerGroup(
                PlayerGroup(expectedPlayerGroup),
                expectedPlayerGroupAccounts.map { PragmaAccountOverview(it) }
            )

            val request = viewPlayerGroupServiceV1Request(1)
            val response = testObj.viewPlayerGroupServiceV1(serviceSession, request)

            assertEquals(response.playerGroup, expectedPlayerGroup)
            assertEquals(response.accountsList, expectedPlayerGroupAccounts)
            coVerify { playerGroupProxy.viewPlayerGroup(ViewPlayerGroupRequest(request)) }
        }

        @Test
        fun `viewPlayerGroupsPartnerV1 calls viewPlayerGroups and returns the response`() = runTest {
            val expectedPlayerGroup = playerGroup(1)
            coEvery { playerGroupProxy.viewPlayerGroups() } returns listOf(PlayerGroup(expectedPlayerGroup))

            val request = viewPlayerGroupsPartnerV1Request()
            val response = testObj.viewPlayerGroupsPartnerV1(partnerSession, request)

            assertEquals(response.playerGroupsList.size, 1)
            assertEquals(response.playerGroupsList, listOf(expectedPlayerGroup))
            coVerify { playerGroupProxy.viewPlayerGroups() }
        }

        @Test
        fun `viewPlayerGroupsOperatorV1 calls viewPlayerGroups and returns the response`() = runTest {
            val expectedPlayerGroup = playerGroup(1)
            coEvery { playerGroupProxy.viewPlayerGroups() } returns listOf(PlayerGroup(expectedPlayerGroup))

            val request = viewPlayerGroupsOperatorV1Request()
            val response = testObj.viewPlayerGroupsOperatorV1(operatorSession, request)

            assertEquals(response.playerGroupsList.size, 1)
            assertEquals(response.playerGroupsList, listOf(expectedPlayerGroup))
            coVerify { playerGroupProxy.viewPlayerGroups() }
        }

        @Test
        fun `viewPlayerGroupsServiceV1 calls viewPlayerGroups and returns the response`() = runTest {
            val expectedPlayerGroup = playerGroup(1)
            coEvery { playerGroupProxy.viewPlayerGroups() } returns listOf(PlayerGroup(expectedPlayerGroup))

            val request = viewPlayerGroupsServiceV1Request()
            val response = testObj.viewPlayerGroupsServiceV1(serviceSession, request)

            assertEquals(response.playerGroupsList.size, 1)
            assertEquals(response.playerGroupsList, listOf(expectedPlayerGroup))
            coVerify { playerGroupProxy.viewPlayerGroups() }
        }

        @Test
        fun `viewPlayerGroupsBySocialIdOperatorV1 returns the list of player groups the user is in`() = runTest {
            val expected = viewPlayerGroupsBySocialIdOperatorV1Response(1)
            coEvery {
                playerGroupProxy.viewPlayerGroupsBySocialId(any())
            } returns expected.playerGroupIdsList.map { it.toUUID() }

            val request = viewPlayerGroupsBySocialIdOperatorV1Request(1)
            val actual = testObj.viewPlayerGroupsBySocialIdOperatorV1(operatorSession, request)

            assertEquals(expected, actual)
            coVerify { playerGroupProxy.viewPlayerGroupsBySocialId(request.pragmaSocialId.toUUID()) }
        }

        @Test
        fun `createPlayerGroupOperator calls createPlayerGroup and returns created PlayerGroup`() = runTest {
            val expectedPlayerGroup: AccountCommon.PlayerGroup = playerGroup(1)
            coEvery { playerGroupProxy.createPlayerGroup(any()) } returns PlayerGroup(expectedPlayerGroup)

            val request = createPlayerGroupOperatorV1Request(1)
            val response = testObj.createPlayerGroupOperatorV1(operatorSession, request)

            assertEquals(request.name, response.playerGroup.name)
            assertEquals(request.description, response.playerGroup.description)
            assertNotEquals(Fixed128.getDefaultInstance(), response.playerGroup.playerGroupId)
            coVerify { playerGroupProxy.createPlayerGroup(CreatePlayerGroupRequest(request)) }
        }

        @Test
        fun `createPlayerGroupPartner calls createPlayerGroup and returns created PlayerGroup`() = runTest {
            val expectedPlayerGroup: AccountCommon.PlayerGroup = playerGroup(1)
            coEvery { playerGroupProxy.createPlayerGroup(any()) } returns PlayerGroup(expectedPlayerGroup)

            val request = createPlayerGroupPartnerV1Request(1)
            val response = testObj.createPlayerGroupPartnerV1(partnerSession, request)

            assertEquals(request.name, response.playerGroup.name)
            assertEquals(request.description, response.playerGroup.description)
            assertNotEquals(Fixed128.getDefaultInstance(), response.playerGroup.playerGroupId)
            coVerify { playerGroupProxy.createPlayerGroup(CreatePlayerGroupRequest(request)) }
        }

        @Test
        fun `createPlayerGroupService calls createPlayerGroup and returns created PlayerGroup`() = runTest {
            val expectedPlayerGroup: AccountCommon.PlayerGroup = playerGroup(1)
            coEvery { playerGroupProxy.createPlayerGroup(any()) } returns PlayerGroup(expectedPlayerGroup)

            val request = createPlayerGroupServiceV1Request(1)
            val response = testObj.createPlayerGroupServiceV1(serviceSession, request)

            assertEquals(request.name, response.playerGroup.name)
            assertEquals(request.description, response.playerGroup.description)
            assertNotEquals(Fixed128.getDefaultInstance(), response.playerGroup.playerGroupId)
            coVerify { playerGroupProxy.createPlayerGroup(CreatePlayerGroupRequest(request)) }
        }

        @Test
        fun `addAccountToPlayerGroupOperator adds social id to a specified player group and gets the user's player groups`() =
            runTest {
                val playerGroupId = playerGroupId(1)
                coEvery { playerGroupProxy.viewPlayerGroupsBySocialId(any()) } returns listOf(playerGroupId.toUUID())

                val request = addAccountToPlayerGroupOperatorV1Request(1)
                val response = testObj.addAccountToPlayerGroupOperatorV1(operatorSession, request)

                val expected = AccountRpc.AddAccountToPlayerGroupOperatorV1Response.newBuilder()
                    .addPlayerGroupIds(playerGroupId)
                    .build()
                assertEquals(expected, response)
                coVerify { playerGroupProxy.addAccountsToPlayerGroup(AddAccountsToPlayerGroup(request)) }
                coVerify { playerGroupProxy.viewPlayerGroupsBySocialId(request.pragmaSocialId.toUUID()) }
            }

        @Test
        fun `addAccountsToPlayerGroupOperator adds social ids to a specified player group`() = runTest {
            val request = addAccountsToPlayerGroupOperatorV1Request(1)
            val response = testObj.addAccountsToPlayerGroupOperatorV1(operatorSession, request)

            assertEquals(request.pragmaSocialIdsList, response.pragmaSocialIdsList)
            coVerify { playerGroupProxy.addAccountsToPlayerGroup(AddAccountsToPlayerGroup(request)) }
        }

        @Test
        fun `addAccountToPlayerGroupPartner adds social id to a specified player group and gets the user's player groups`() =
            runTest {
                val playerGroupId = playerGroupId(1)
                coEvery { playerGroupProxy.viewPlayerGroupsBySocialId(any()) } returns listOf(playerGroupId.toUUID())

                val request = addAccountToPlayerGroupPartnerV1Request(1)
                val response = testObj.addAccountToPlayerGroupPartnerV1(partnerSession, request)

                val expected = AccountRpc.AddAccountToPlayerGroupPartnerV1Response.newBuilder()
                    .addPlayerGroupIds(playerGroupId)
                    .build()
                assertEquals(expected, response)
                coVerify { playerGroupProxy.addAccountsToPlayerGroup(AddAccountsToPlayerGroup(request)) }
                coVerify { playerGroupProxy.viewPlayerGroupsBySocialId(request.pragmaSocialId.toUUID()) }
            }

        @Test
        fun `addAccountToPlayerGroupService adds social id to a specified player group and gets the user's player groups`() =
            runTest {
                val playerGroupId = playerGroupId(1)
                coEvery { playerGroupProxy.viewPlayerGroupsBySocialId(any()) } returns listOf(playerGroupId.toUUID())

                val request = addAccountToPlayerGroupServiceV1Request(1)
                val response = testObj.addAccountToPlayerGroupServiceV1(serviceSession, request)

                val expected = AccountRpc.AddAccountToPlayerGroupServiceV1Response.newBuilder()
                    .addPlayerGroupIds(playerGroupId)
                    .build()
                assertEquals(expected, response)
                coVerify { playerGroupProxy.addAccountsToPlayerGroup(AddAccountsToPlayerGroup(request)) }
                coVerify { playerGroupProxy.viewPlayerGroupsBySocialId(request.pragmaSocialId.toUUID()) }
            }

        @Test
        fun `removeAccountFromPlayerGroupOperator removes social id from specified player group`() = runTest {
            val someOtherPlayerGroupId = playerGroupId(2).toUUID()
            coEvery { playerGroupProxy.removeAccountFromPlayerGroup(any()) } returns listOf(someOtherPlayerGroupId)

            val request = removeAccountFromPlayerGroupOperatorV1Request(1)
            val actual = testObj.removeAccountFromPlayerGroupOperatorV1(operatorSession, request)

            val expected = AccountRpc.RemoveAccountFromPlayerGroupOperatorV1Response.newBuilder()
                .addPlayerGroupIds(someOtherPlayerGroupId.toFixed128())
                .build()
            assertEquals(expected, actual)
            coVerify { playerGroupProxy.removeAccountFromPlayerGroup(RemoveAccountFromPlayerGroupRequest(request)) }
        }

        @Test
        fun `removeAccountFromPlayerGroupPartner removes social id from specified player group`() = runTest {
            val someOtherPlayerGroupId = playerGroupId(2).toUUID()
            coEvery { playerGroupProxy.removeAccountFromPlayerGroup(any()) } returns listOf(someOtherPlayerGroupId)

            val request = removeAccountFromPlayerGroupPartnerV1Request(1)
            val actual = testObj.removeAccountFromPlayerGroupPartnerV1(partnerSession, request)

            val expected = AccountRpc.RemoveAccountFromPlayerGroupPartnerV1Response.newBuilder()
                .addPlayerGroupIds(someOtherPlayerGroupId.toFixed128())
                .build()
            assertEquals(expected, actual)
            coVerify { playerGroupProxy.removeAccountFromPlayerGroup(RemoveAccountFromPlayerGroupRequest(request)) }
        }

        @Test
        fun `removeAccountFromPlayerGroupService removes social id from specified player group`() = runTest {
            val someOtherPlayerGroupId = playerGroupId(2).toUUID()
            coEvery { playerGroupProxy.removeAccountFromPlayerGroup(any()) } returns listOf(someOtherPlayerGroupId)

            val request = removeAccountFromPlayerGroupServiceV1Request(1)
            val actual = testObj.removeAccountFromPlayerGroupServiceV1(serviceSession, request)

            val expected = AccountRpc.RemoveAccountFromPlayerGroupServiceV1Response.newBuilder()
                .addPlayerGroupIds(someOtherPlayerGroupId.toFixed128())
                .build()
            assertEquals(expected, actual)
            coVerify { playerGroupProxy.removeAccountFromPlayerGroup(RemoveAccountFromPlayerGroupRequest(request)) }
        }

        @Test
        fun `removePlayerGroupOperator calls removePlayerGroup and returns empty response`() = runTest {
            coEvery { playerGroupProxy.removePlayerGroup(any()) } returns 1

            val request = removePlayerGroupOperatorV1Request(1)
            testObj.removePlayerGroupOperatorV1(operatorSession, request)

            coVerify { playerGroupProxy.removePlayerGroup(request.playerGroupId.toUUID()) }
        }

        @Test
        fun `removePlayerGroupPartner calls removePlayerGroup and returns empty response`() = runTest {
            coEvery { playerGroupProxy.removePlayerGroup(any()) } returns 1

            val request = removePlayerGroupPartnerV1Request(1)
            testObj.removePlayerGroupPartnerV1(partnerSession, request)

            coVerify { playerGroupProxy.removePlayerGroup(request.playerGroupId.toUUID()) }
        }

        @Test
        fun `removePlayerGroupService calls removePlayerGroup and returns empty response`() = runTest {
            coEvery { playerGroupProxy.removePlayerGroup(any()) } returns 1

            val request = removePlayerGroupServiceV1Request(1)
            testObj.removePlayerGroupServiceV1(serviceSession, request)

            coVerify { playerGroupProxy.removePlayerGroup(request.playerGroupId.toUUID()) }
        }
    }

    @Nested
    inner class UpdateDisplayName {
        @Test
        fun `updateDisplayNameServiceV1 call account dao node service update display name`() = runTest {
            val expected = PragmaDisplayName(anyString(), anyString())
            coEvery { accountRegistrar.updateDisplayName(any(), any()) } returns expected

            val request = updateDisplayNameServiceV1Request(1)
            val response = testObj.updateDisplayNameServiceV1(serviceSession, request)

            assertEquals(expected.displayName, response.updatedDisplayName.displayName)
            assertEquals(expected.discriminator, response.updatedDisplayName.discriminator)
            coVerify {
                accountRegistrar.updateDisplayName(request.pragmaSocialId.toUUID(), request.requestedDisplayName)
            }
        }

        @Test
        fun `updateDisplayNamePartnerV1 updates display name`() = runTest {
            val request = updateDisplayNamePartnerV1Request(1)
            val expectedDiscriminator = anyString()
            coEvery {
                accountRegistrar.updateDisplayName(
                    request.pragmaSocialId.toUUID(),
                    request.requestedDisplayName
                )
            } returns PragmaDisplayName(request.requestedDisplayName, expectedDiscriminator)

            val response = testObj.updateDisplayNamePartnerV1(partnerSession, request)

            coVerify {
                accountRegistrar.updateDisplayName(request.pragmaSocialId.toUUID(), request.requestedDisplayName)
            }
            assertEquals(request.requestedDisplayName, response.updatedDisplayName.displayName)
            assertEquals(expectedDiscriminator, response.updatedDisplayName.discriminator)
        }
    }

    @Nested
    inner class DeleteAccount {
        @Test
        fun `deleteAccountOperatorV1 call account dao node service and registrar to delete account data`() = runTest {
            val request = deleteAccountOperatorV1Request(1)

            testObj.deleteAccountOperatorV1(operatorSession, request)

            coVerify {
                accountRegistrar.deleteAccount(request.pragmaSocialId.toUUID())
            }
        }

        @Test
        fun `deleteAccountPartnerV1 call account dao node service and registrar to delete account data`() = runTest {
            val request = deleteAccountPartnerV1Request(1)

            testObj.deleteAccountPartnerV1(partnerSession, request)

            coVerify {
                accountRegistrar.deleteAccount(request.pragmaSocialId.toUUID())
            }
        }

        @Test
        fun `deleteAccountServiceV1 call account dao node service and registrar to delete account data`() = runTest {
            val request = deleteAccountServiceV1Request(1)

            testObj.deleteAccountServiceV1(serviceSession, request)

            coVerify {
                accountRegistrar.deleteAccount(request.pragmaSocialId.toUUID())
            }
        }
    }

    @Test
    fun `getAccountV1 returns own player information`() = runTest {
        val request = AccountRpc.GetAccountV1Request.newBuilder().build()
        val socialIdentity = SocialIdentityWithPersonal(socialIdentityWithPersonal(1))
        coEvery { accountRegistrar.getPragmaSocialIdentityWithPersonal(socialPlayerSession.pragmaId.toUUID()) } returns socialIdentity
        val actual = testObj.getAccountV1(socialPlayerSession, request)

        val expected = socialIdentity.toGetAccountResponse()
        assertEquals(expected, actual)
    }

    @Nested
    inner class UpdateDisplayNameEndpoints {
        @Test
        fun `updateDisplayNameV1 calls accountRegistrar updateOwnDisplayName`() = runTest {
            val updateDisplayNameRequest = updateDisplayNameV1Request(1)
            coEvery {
                accountRegistrar.updateOwnDisplayName(
                    socialPlayerSession.socialId.toUUID(),
                    updateDisplayNameRequest.requestedDisplayName
                )
            } returns PragmaDisplayName(displayName(1))
            testObj.updateDisplayNameV1(socialPlayerSession, updateDisplayNameRequest)
            coVerify {
                accountRegistrar.updateOwnDisplayName(
                    socialPlayerSession.socialId.toUUID(),
                    updateDisplayNameRequest.requestedDisplayName
                )
            }
        }
    }

    @Nested
    inner class VerifyEmailEndpoints {
        @Test
        fun `updateEmailV1 calls accountRegistrar updateEmail`() = runTest {
            val updateEmailRequest = updateEmailV1Request(1)
            testObj.updateEmailV1(socialPlayerSession, updateEmailRequest)

            coVerify {
                accountRegistrar.updateEmail(
                    socialPlayerSession.socialId.toUUID(),
                    updateEmailRequest.emailAddress
                )
            }
        }

        @Test
        fun `verifyEmailV1 calls accountRegistrar verifyEmail`() = runTest {
            val verifyEmailRequest = verifyEmailV1Request(1)
            testObj.verifyEmailV1(serviceSession, verifyEmailRequest)

            coVerify { accountRegistrar.verifyEmail(verifyEmailRequest.pragmaEmailToken) }
        }
    }

    @Nested
    inner class GetSocialIdentitiesByProviderAccountIds {
        @Test
        fun `getSocialIdentitiesByProviderAccountIdsV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::getSocialIdentitiesByProviderAccountIdsV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.PLAYER, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.SESSION_PRAGMA_ID, rpcAnnotation.routingMethod)
        }

        @Test
        fun `getSocialIdentitiesByProviderAccountIdsV1 returns the right proto object`() = runTest {
            val socialIdentityWithBans1 = SocialIdentityWithBans(socialIdentityProto(1), listOf(ban(1), ban(2)))
            val socialIdentityWithBans2 = SocialIdentityWithBans(socialIdentityProto(2), listOf(ban(2), ban(3)))
            val socialIdentitiesWithBans = listOf(socialIdentityWithBans1, socialIdentityWithBans2)
            val socialIdentity1 = socialIdentityWithBans1.socialIdentity
            val socialIdentity2 = socialIdentityWithBans2.socialIdentity
            val publicSocialIdentity1 = PublicSocialIdentity(
                publicSocialIdentity(
                    1,
                    idProviderAccounts = socialIdentity1.providerAccounts.take(1).map { it.toProto() })
            )
            val publicSocialIdentity2 = PublicSocialIdentity(
                publicSocialIdentity(
                    2,
                    idProviderAccounts = socialIdentity2.providerAccounts.take(2).map { it.toProto() })
            )
            val request = getSocialIdentitiesByProviderAccountIdsV1Request(1)
            coEvery {
                accountDaoNodeService
                    .getSocialIdentitiesByProviderAccountId(request.providerAccountIdsList.map { ProviderAccountId(it) })
            } returns socialIdentitiesWithBans
            coEvery { publicIdentityConverter.convertToPublicSocialIdentity(socialIdentity1) } returns publicSocialIdentity1
            coEvery { publicIdentityConverter.convertToPublicSocialIdentity(socialIdentity2) } returns publicSocialIdentity2

            val actualResponse = testObj.getSocialIdentitiesByProviderAccountIdsV1(socialPlayerSession, request)

            val expectedPublicSocialIdentities = listOf(publicSocialIdentity1, publicSocialIdentity2).map { it.toProto() }
            val expectedResponse = getSocialIdentitiesByProviderAccountIdsV1Response(1, socialIdentities = expectedPublicSocialIdentities)
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getSocialIdentitiesByProviderAccountIdsOperatorV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::getSocialIdentitiesByProviderAccountIdsOperatorV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.OPERATOR, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `getSocialIdentitiesByProviderAccountIdsOperatorV1 returns the right proto object`() = runTest {
            val socialIdentityWithBans1 = SocialIdentityWithBans(socialIdentityProto(1), listOf(ban(1), ban(2)))
            val socialIdentityWithBans2 = SocialIdentityWithBans(socialIdentityProto(2), listOf(ban(2), ban(3)))
            val socialIdentitiesWithBans = listOf(socialIdentityWithBans1, socialIdentityWithBans2)
            val socialIdentity1 = socialIdentityWithBans1.socialIdentity
            val socialIdentity2 = socialIdentityWithBans2.socialIdentity
            val request = getSocialIdentitiesByProviderAccountIdsOperatorV1Request(1)
            coEvery {
                accountDaoNodeService
                    .getSocialIdentitiesByProviderAccountId(request.providerAccountIdsList.map { ProviderAccountId(it) })
            } returns socialIdentitiesWithBans

            val actualResponse = testObj.getSocialIdentitiesByProviderAccountIdsOperatorV1(operatorSession, request)

            val expectedSocialIdentities = listOf(socialIdentity1, socialIdentity2).map { it.toProto() }
            val expectedResponse = getSocialIdentitiesByProviderAccountIdsOperatorV1Response(1, socialIdentities = expectedSocialIdentities)
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getSocialIdentitiesByProviderAccountIdsPartnerV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::getSocialIdentitiesByProviderAccountIdsPartnerV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.PARTNER, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `getSocialIdentitiesByProviderAccountIdsPartnerV1 returns the right proto object`() = runTest {
            val socialIdentityWithBans1 = SocialIdentityWithBans(socialIdentityProto(1), listOf(ban(1), ban(2)))
            val socialIdentityWithBans2 = SocialIdentityWithBans(socialIdentityProto(2), listOf(ban(2), ban(3)))
            val socialIdentitiesWithBans = listOf(socialIdentityWithBans1, socialIdentityWithBans2)
            val socialIdentity1 = socialIdentityWithBans1.socialIdentity
            val socialIdentity2 = socialIdentityWithBans2.socialIdentity
            val request = getSocialIdentitiesByProviderAccountIdsPartnerV1Request(1)
            coEvery {
                accountDaoNodeService
                    .getSocialIdentitiesByProviderAccountId(request.providerAccountIdsList.map { ProviderAccountId(it) })
            } returns socialIdentitiesWithBans

            val actualResponse = testObj.getSocialIdentitiesByProviderAccountIdsPartnerV1(partnerSession, request)

            val expectedSocialIdentities = listOf(socialIdentity1, socialIdentity2).map { it.toProto() }
            val expectedResponse = getSocialIdentitiesByProviderAccountIdsPartnerV1Response(1, socialIdentities = expectedSocialIdentities)
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getSocialIdentitiesByProviderAccountIdsServiceV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::getSocialIdentitiesByProviderAccountIdsServiceV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.SERVICE, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `getSocialIdentitiesByProviderAccountIdsServiceV1 returns the right proto object`() = runTest {
            val socialIdentityWithBans1 = SocialIdentityWithBans(socialIdentityProto(1), listOf(ban(1), ban(2)))
            val socialIdentityWithBans2 = SocialIdentityWithBans(socialIdentityProto(2), listOf(ban(2), ban(3)))
            val socialIdentitiesWithBans = listOf(socialIdentityWithBans1, socialIdentityWithBans2)
            val socialIdentity1 = socialIdentityWithBans1.socialIdentity
            val socialIdentity2 = socialIdentityWithBans2.socialIdentity
            val request = getSocialIdentitiesByProviderAccountIdsServiceV1Request(1)
            coEvery {
                accountDaoNodeService
                    .getSocialIdentitiesByProviderAccountId(request.providerAccountIdsList.map { ProviderAccountId(it) })
            } returns socialIdentitiesWithBans

            val actualResponse = testObj.getSocialIdentitiesByProviderAccountIdsServiceV1(serviceSession, request)

            val expectedSocialIdentities = listOf(socialIdentity1, socialIdentity2).map { it.toProto() }
            val expectedResponse = getSocialIdentitiesByProviderAccountIdsServiceV1Response(1, socialIdentities = expectedSocialIdentities)
            assertEquals(expectedResponse, actualResponse)
        }
    }

    @Nested
    inner class GetPlayerIdentitiesByProviderAccountIds {
        @BeforeEach
        fun beforeEach() {
            coEvery {
                accountDaoNodeService.getPlayerIdentitiesByProviderAccountId(
                    gameShardId,
                    getPlayerIdentitiesByProviderAccountIdsV1Request.providerAccountIdsList.map {
                        ProviderAccountId(it)
                    })
            } returns listOf(playerIdentity1, playerIdentity2)
            coEvery { publicIdentityConverter.convertToPublicPlayerIdentity(playerIdentity1) } returns playerIdentity1
            coEvery { publicIdentityConverter.convertToPublicPlayerIdentity(playerIdentity2) } returns playerIdentity2
        }

        @Test
        fun `getPlayerIdentitiesByProviderAccountIdsV1 returns nothing when no players request`() = runTest {
            val request = AccountRpc.GetPlayerIdentitiesByProviderAccountIdsV1Request.getDefaultInstance()
            val response = testObj.getPlayerIdentitiesByProviderAccountIdsV1(socialPlayerSession, request)

            assertEquals(0, response.playerIdentitiesCount)
        }

        @Test
        fun `getPlayerIdentitiesByProviderAccountIdsV1 calls through to the account dao and public identity converter`() = runTest {
            val publicPlayerIdentity1 =
                PlayerIdentity(playerIdentity(1, idProviderList = playerIdentity1.providerAccounts.take(1).map { it.toProto() }))
            val publicPlayerIdentity2 =
                PlayerIdentity(playerIdentity(2, idProviderList = playerIdentity2.providerAccounts.take(2).map { it.toProto() }))
            coEvery { publicIdentityConverter.convertToPublicPlayerIdentity(playerIdentity1) } returns publicPlayerIdentity1
            coEvery { publicIdentityConverter.convertToPublicPlayerIdentity(playerIdentity2) } returns publicPlayerIdentity2

            val actualResponse =
                testObj.getPlayerIdentitiesByProviderAccountIdsV1(socialPlayerSession, getPlayerIdentitiesByProviderAccountIdsV1Request)

            val expectedPublicPlayerIdentities = listOf(publicPlayerIdentity1, publicPlayerIdentity2).map { it.toProto() }
            val expectedResponse = getPlayerIdentitiesByProviderAccountIdsV1Response(1, playerIdentities = expectedPublicPlayerIdentities)
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getPlayerIdentitiesByProviderAccountIdsV1 returns empty list when no player identities were found`() = runTest {
            coEvery {
                accountDaoNodeService.getPlayerIdentitiesByProviderAccountId(any(), any())
            } returns emptyList()

            val response =
                testObj.getPlayerIdentitiesByProviderAccountIdsV1(socialPlayerSession, getPlayerIdentitiesByProviderAccountIdsV1Request)

            assertEquals(0, response.playerIdentitiesCount)
        }

        @Test
        fun `getPlayerIdentitiesByProviderAccountIdsV1 returns partial list when some player has no keychain entry`() = runTest {
            coEvery {
                accountDaoNodeService.getPlayerIdentitiesByProviderAccountId(
                    gameShardId,
                    getPlayerIdentitiesByProviderAccountIdsV1Request.providerAccountIdsList.map {
                        ProviderAccountId(it)
                    })
            } returns listOf(playerIdentity1)

            val response =
                testObj.getPlayerIdentitiesByProviderAccountIdsV1(socialPlayerSession, getPlayerIdentitiesByProviderAccountIdsV1Request)

            assertEquals(1, response.playerIdentitiesCount)
        }
    }

    @Nested
    inner class ViewActiveIdProviders {
        @Test
        fun `returns a list of idProviders`() = runTest {
            val googleIdProvider = mockk<IdentityProviderPlugin>()
            every { googleIdProvider.canAuthenticate } returns true
            every { googleIdProvider.separator } returns "#"
            every { googleIdProvider.getType() } returns IdProvider.GOOGLE_VALUE
            every { googleIdProvider.getName() } returns IdProvider.GOOGLE.name
            val discordIdProvider = mockk<IdentityProviderPlugin>()
            every { discordIdProvider.canAuthenticate } returns true
            every { discordIdProvider.separator } returns "🍌"
            every { discordIdProvider.getType() } returns IdProvider.DISCORD_VALUE
            every { discordIdProvider.getName() } returns IdProvider.DISCORD.name
            val emailIdProvider = mockk<IdentityProviderPlugin>()
            every { emailIdProvider.canAuthenticate } returns false
            testObj.identityProviders.add(IdProvider.GOOGLE_VALUE, googleIdProvider)
            testObj.identityProviders.add(IdProvider.DISCORD_VALUE, discordIdProvider)
            testObj.identityProviders.add(IdProvider.EMAIL_VALUE, emailIdProvider)

            val actual = testObj.viewActiveIdProvidersOperatorV1(operatorSession, viewActiveIdProvidersOperatorV1Request())

            assertEquals(
                setOf(
                    AccountRpc.IdProviderInfo.newBuilder()
                        .setIdProvider(IdProvider.GOOGLE.name)
                        .setSeparator("#")
                        .build(),
                    AccountRpc.IdProviderInfo.newBuilder()
                        .setIdProvider(IdProvider.DISCORD.name)
                        .setSeparator("🍌")
                        .build()
                ),
                actual.idProvidersList.toSet()
            )
        }
    }

    @Test
    fun `getPublicIdProviderInfoServiceV1 returns a list of id provider information`() = runTest {
        val unsafeIdProvider = mockk<UnsafeIdentityProviderPlugin>()
        val unsafePublicConfig = mapOf(
            "showPortalLoginButton" to "true",
        )
        every { unsafeIdProvider.separator } returns "#"
        every { unsafeIdProvider.getType() } returns IdProvider.UNSAFE_VALUE
        every { unsafeIdProvider.getName() } returns IdProvider.UNSAFE.name
        every { unsafeIdProvider.getPublicInfo() } returns unsafePublicConfig
        testObj.identityProviders.add(IdProvider.UNSAFE_VALUE, unsafeIdProvider)

        val googleIdProvider = mockk<GoogleIdentityProviderPlugin>()
        val googlePublicConfig = mapOf(
            "clientId" to "goo-123-abc",
            "showPortalLoginButton" to "true",
        )
        every { googleIdProvider.separator } returns ""
        every { googleIdProvider.getType() } returns IdProvider.GOOGLE_VALUE
        every { googleIdProvider.getName() } returns IdProvider.GOOGLE.name
        every { googleIdProvider.getPublicInfo() } returns googlePublicConfig
        testObj.identityProviders.add(IdProvider.GOOGLE_VALUE, googleIdProvider)

        val discordIdProvider = mockk<IdentityProviderPlugin>()
        val discordPublicConfig = mapOf(
            "clientId" to "12345",
            "showPortalLoginButton" to "true",
        )
        every { discordIdProvider.separator } returns "#"
        every { discordIdProvider.getType() } returns IdProvider.DISCORD_VALUE
        every { discordIdProvider.getName() } returns IdProvider.DISCORD.name
        every { discordIdProvider.getPublicInfo() } returns discordPublicConfig
        testObj.identityProviders.add(IdProvider.DISCORD_VALUE, discordIdProvider)

        val serviceSession = serviceSession(1)
        val request = getPublicIdProviderInfoServiceV1Request()
        val actual = testObj.getPublicIdProviderInfoServiceV1(serviceSession, request)

        val expected = AccountRpc.GetPublicIdProviderInfoServiceV1Response.newBuilder()
            .addAllIdProviders(
                listOf(
                    publicIdProviderInfo(1, IdProvider.UNSAFE_VALUE, "#", unsafePublicConfig),
                    publicIdProviderInfo(6, IdProvider.GOOGLE_VALUE, "", googlePublicConfig),
                    publicIdProviderInfo(3, IdProvider.DISCORD_VALUE, "#", discordPublicConfig),
                )
            )
            .build()
        assertEquals(expected, actual)
    }

    @Test
    fun `getPersonalDataSectionsServiceV1 calls through to account sections builder`() = runTest {
        val request = getPersonalDataSectionsServiceV1Request(1)
        val personalDataReportSections = personalDataReportSections(1)

        coEvery { accountSectionsBuilder.buildSections(request.pragmaSocialId.toUUID()) } returns personalDataReportSections.mapValues {
            PersonalDataReportSection(
                it.value
            )
        }

        val actualResponse = testObj.getPersonalDataSectionsServiceV1(serviceSession, request)

        coVerify { accountSectionsBuilder.buildSections(request.pragmaSocialId.toUUID()) }

        assertEquals(personalDataReportSections, actualResponse.sectionsMap)
    }

    @Nested
    inner class BanAccount {
        @Test
        fun `banAccountOperatorV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::banAccountOperatorV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.OPERATOR, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `banAccountOperatorV1 calls through to the player behavior custodian with a game shard id`() = runTest {
            val request = banAccountOperatorV1Request(
                1,
                banScope = AccountCommon.BanScope.BAN_SCOPE_GAME_SHARD,
                gameShardId = gameShardId.toFixed128()
            )
            val expectedResponse = banAccountOperatorV1Response(1, gameShardId.toFixed128(), operatorSession.pragmaId)
            coEvery {
                playerBehaviorCustodian.banAccount(
                    request.pragmaSocialId.toUUID(),
                    request.banScope,
                    request.gameShardId.toUUID(),
                    request.durationInMillis,
                    request.banReason,
                    operatorSession.pragmaId.toUUID(),
                    request.comment,
                )
            } returns BanRecord(Ban(expectedResponse.banRecord.ban), operatorSession.pragmaId.toUUID(), 0.toUUID(), request.comment)

            val actualResponse = testObj.banAccountOperatorV1(operatorSession, request)

            assertEquals(expectedResponse, actualResponse)
            assertEquals(gameShardId.toFixed128(), actualResponse.banRecord.ban.gameShardId)
        }

        @Test
        fun `banAccountPartnerV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::banAccountPartnerV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.PARTNER, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `banAccountPartnerV1 calls through to the player behavior custodian with a game shard id`() = runTest {
            val request = banAccountPartnerV1Request(
                1,
                banScope = AccountCommon.BanScope.BAN_SCOPE_GAME_SHARD,
                gameShardId = gameShardId.toFixed128()
            )
            val expectedResponse = banAccountPartnerV1Response(1, gameShardId.toFixed128())
            coEvery {
                playerBehaviorCustodian.banAccount(
                    request.pragmaSocialId.toUUID(),
                    AccountCommon.BanScope.BAN_SCOPE_GAME_SHARD,
                    request.gameShardId.toUUID(),
                    request.durationInMillis,
                    request.banReason,
                    0.toUUID(),
                    request.comment,
                )
            } returns BanRecord(Ban(expectedResponse.banRecord.ban), 0.toUUID(), 0.toUUID(), request.comment)

            val actualResponse = testObj.banAccountPartnerV1(partnerSession, request)

            assertEquals(expectedResponse, actualResponse)
            assertEquals(gameShardId.toFixed128(), actualResponse.banRecord.ban.gameShardId)
        }

        @Test
        fun `banAccountServiceV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::banAccountServiceV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.SERVICE, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `banAccountServiceV1 calls through to the player behavior custodian with a game shard id`() = runTest {
            val request = banAccountServiceV1Request(
                1,
                banScope = AccountCommon.BanScope.BAN_SCOPE_GAME_SHARD,
                gameShardId = gameShardId.toFixed128()
            )
            val expectedResponse = banAccountServiceV1Response(1, gameShardId.toFixed128())
            coEvery {
                playerBehaviorCustodian.banAccount(
                    request.pragmaSocialId.toUUID(),
                    request.banScope,
                    request.gameShardId.toUUID(),
                    request.durationInMillis,
                    request.banReason,
                    0.toUUID(),
                    request.comment,
                )
            } returns BanRecord(Ban(expectedResponse.banRecord.ban), 0.toUUID(), 0.toUUID(), request.comment)

            val actualResponse = testObj.banAccountServiceV1(serviceSession, request)

            assertEquals(expectedResponse, actualResponse)
            assertEquals(gameShardId.toFixed128(), actualResponse.banRecord.ban.gameShardId)
        }
    }

    @Nested
    inner class RevokeBan {
        @Test
        fun `revokeBanAccountOperatorV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::revokeBanAccountOperatorV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.OPERATOR, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `revokeBanAccountOperatorV1 calls through to the account dao node service`() = runTest {
            val request = revokeBanAccountOperatorV1Request(1)
            val expectedResponse = revokeBanAccountOperatorV1Response()

            val actualResponse = testObj.revokeBanAccountOperatorV1(operatorSession, request)

            coVerify { playerBehaviorCustodian.revokeBan(request.banId.toUUID(), operatorSession.pragmaId.toUUID()) }
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `revokeBanAccountPartnerV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::revokeBanAccountPartnerV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.PARTNER, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `revokeBanAccountPartnerV1 calls through to the account dao node service`() = runTest {
            val request = revokeBanAccountPartnerV1Request(1)
            val expectedResponse = revokeBanAccountPartnerV1Response()

            val actualResponse = testObj.revokeBanAccountPartnerV1(partnerSession, request)

            coVerify { playerBehaviorCustodian.revokeBan(request.banId.toUUID()) }
            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `revokeBanAccountServiceV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::revokeBanAccountServiceV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.SERVICE, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `revokeBanAccountServiceV1 calls through to the account dao node service`() = runTest {
            val request = revokeBanAccountServiceV1Request(1)
            val expectedResponse = revokeBanAccountServiceV1Response()

            val actualResponse = testObj.revokeBanAccountServiceV1(serviceSession, request)

            coVerify { playerBehaviorCustodian.revokeBan(request.banId.toUUID()) }
            assertEquals(expectedResponse, actualResponse)
        }
    }

    @Nested
    inner class GetBans {
        @Test
        fun `getBansV1 calls through to the player behavior custodian`() = runTest {
            val request = getBansV1Request(1)
            val expectedResponse = getBansV1Response(1)
            coEvery {
                playerBehaviorCustodian.getBanRecords(
                    socialPlayerSession.socialId.toUUID(),
                    request.includeExpiredBans,
                    request.includeRevokedBans
                )
            } returns
                expectedResponse.bansList.map { BanRecord(Ban(it), 0.toUUID(), 0.toUUID(), "unused") }

            val actualResponse = testObj.getBansV1(socialPlayerSession, request)

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getBansBySocialIdOperatorV1 is annotated properly`() {
            val rpc = testObj::class.java.methods.first { it.name == testObj::getBansBySocialIdOperatorV1.name }
                ?: Assertions.fail("couldn't find method")
            val rpcAnnotation = rpc.getAnnotation(PragmaRPC::class.java) ?: Assertions.fail("couldn't find annotation")
            assertEquals(SessionType.OPERATOR, rpcAnnotation.sessionType)
            assertEquals(RoutingMethod.RANDOM, rpcAnnotation.routingMethod)
        }

        @Test
        fun `getBansBySocialIdOperatorV1 calls through to the player behavior custodian`() = runTest {
            val request = getBansBySocialIdOperatorV1Request(1)
            val expectedResponse = getBansBySocialIdOperatorV1Response(1)
            coEvery {
                playerBehaviorCustodian.getBanRecords(
                    request.pragmaSocialId.toUUID(),
                    request.includeExpiredBans,
                    request.includeRevokedBans
                )
            } returns
                expectedResponse.banRecordsList.map { BanRecord(it) }

            val actualResponse = testObj.getBansBySocialIdOperatorV1(operatorSession, request)

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getBansBySocialIdPartnerV1 calls through to the player behavior custodian`() = runTest {
            val request = getBansBySocialIdPartnerV1Request(1)
            val expectedResponse = getBansBySocialIdPartnerV1Response(1)
            coEvery {
                playerBehaviorCustodian.getBanRecords(
                    request.pragmaSocialId.toUUID(),
                    request.includeExpiredBans,
                    request.includeRevokedBans
                )
            } returns
                expectedResponse.banRecordsList.map { BanRecord(it) }

            val actualResponse = testObj.getBansBySocialIdPartnerV1(partnerSession, request)

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `getBansBySocialIdServiceV1 calls through to the player behavior custodian`() = runTest {
            val request = getBansBySocialIdServiceV1Request(1)
            val expectedResponse = getBansBySocialIdServiceV1Response(1)
            coEvery {
                playerBehaviorCustodian.getBanRecords(
                    request.pragmaSocialId.toUUID(),
                    request.includeExpiredBans,
                    request.includeRevokedBans
                )
            } returns
                expectedResponse.banRecordsList.map { BanRecord(it) }

            val actualResponse = testObj.getBansBySocialIdServiceV1(serviceSession, request)

            assertEquals(expectedResponse, actualResponse)
        }
    }
}
