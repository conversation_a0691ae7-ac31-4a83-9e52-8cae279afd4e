package pragma.account

import com.google.protobuf.GeneratedMessageV3
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import java.util.UUID
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import pragma.AccountTestFactory.ban
import pragma.AccountTestFactory.banRecord
import pragma.ApplicationErrorException
import pragma.PragmaCoreTestFactory.anyBoolean
import pragma.PragmaCoreTestFactory.anyLong
import pragma.PragmaCoreTestFactory.anyTimeMillis
import pragma.PragmaCoreTestFactory.anyUuid
import pragma.SessionTestFactory.blockPlayerServiceRequest
import pragma.SessionTestFactory.unblockPlayerServiceRequest
import pragma.account.AccountCommon.BanScope
import pragma.gameshardcache.GameShardCacheNodeService
import pragma.generateList
import pragma.rpc.Request
import pragma.rpcs.ParseFromFunction
import pragma.session.PlayerSessionRpc
import pragma.settings.BackendType
import pragma.utils.TimeProxy
import pragma.utils.TimeProxy.Companion.secondsToMillis
import pragma.utils.buildNotFoundApplicationError
import pragma.utils.coAssertDoesNotThrow
import pragma.utils.coAssertThrowsApplicationErrorException
import pragma.utils.toFixed128
import pragma.utils.toUUID

@OptIn(ExperimentalCoroutinesApi::class)
class PlayerBehaviorCustodianTest {
    private val accountService: AccountService = mockk<AccountService>().apply {
        coEvery { notifyPlayer(any(), any()) } just runs
        coEvery { requestRpcV2(any<Request>(), any<ParseFromFunction<GeneratedMessageV3>>()) } returns mockk()
    }

    private val banId = UUID.randomUUID()
    private val socialId = anyUuid()
    private val operatorID = anyUuid()
    private val banScope = BanScope.BAN_SCOPE_GAME_SHARD
    private val gameShardId = anyUuid()
    private val banReason = ExtBanReason.EXT_BAN_REASON_UNSPECIFIED
    private val banComment = "PlayerBehaviorCustodian ban comment"
    private val banningOperatorId = anyUuid()
    private val durationInMillis = anyLong(1.secondsToMillis, 25.secondsToMillis)
    private val ban = Ban(banId, socialId, revoked = true, 0, banScope, gameShardId)

    private val expectedBanRecord = BanRecord(
        banRecord(
            1,
            gameShardId.toFixed128(),
            banScope,
            ban = ban(1, socialId.toFixed128(), banReason = banReason),
            bannedBy = banningOperatorId.toFixed128()
        )
    )
    private val expectedAllGameShardsBanRecord = BanRecord(
        banRecord(
            2,
            gameShardId.toFixed128(),
            BanScope.BAN_SCOPE_ALL_GAME_SHARDS,
            ban = ban(2, socialId.toFixed128(), banReason = banReason),
            bannedBy = banningOperatorId.toFixed128()
        )
    )
    private val expectedFullAccountBanRecord = BanRecord(
        banRecord(
            3,
            gameShardId.toFixed128(),
            BanScope.BAN_SCOPE_FULL_ACCOUNT,
            ban = ban(3, socialId.toFixed128(), banReason = banReason),
            bannedBy = banningOperatorId.toFixed128()
        )
    )

    private val accountDaoNodeService: AccountDaoNodeService = mockk<AccountDaoNodeService>(relaxUnitFun = true).apply {
        coEvery {
            addBan(
                socialId,
                banScope,
                gameShardId,
                durationInMillis,
                banReason,
                banningOperatorId,
                banComment
            )
        } returns expectedBanRecord
        coEvery {
            addBan(
                socialId,
                BanScope.BAN_SCOPE_ALL_GAME_SHARDS,
                gameShardId,
                durationInMillis,
                banReason,
                banningOperatorId,
                banComment
            )
        } returns expectedAllGameShardsBanRecord
        coEvery {
            addBan(
                socialId,
                BanScope.BAN_SCOPE_FULL_ACCOUNT,
                gameShardId,
                durationInMillis,
                banReason,
                banningOperatorId,
                banComment
            )
        } returns expectedFullAccountBanRecord
        coEvery {
            revokeBan(banId, socialId)
        } just runs

        coEvery {
            revokeBan(banId, operatorID)
        } just runs
        coEvery {
            getBan(banId)
        } returns ban
    }
    private val gameShardCacheNodeService: GameShardCacheNodeService = mockk<GameShardCacheNodeService>().apply {
        coEvery { throwIfGameShardDoesNotExist(gameShardId) } just runs
    }

    private val logger = mockk<Logger>(relaxUnitFun = true)
    private val timeProxy = mockk<TimeProxy>().apply {
        coEvery { currentEpochMillis() } returns anyTimeMillis()
    }
    private val testObj = PlayerBehaviorCustodian(logger, timeProxy)

    @BeforeEach
    fun setUp() {
        testObj.init(accountService, accountDaoNodeService, gameShardCacheNodeService)
        testObj.onConfigChanged(AccountServiceConfig.getFor(BackendType.SOCIAL))
    }

    @Nested
    inner class BanAccount {

        @Test
        fun `banAccount calls through to the account dao node service`() = runTest {
            val allGameShardsBanScope = BanScope.BAN_SCOPE_ALL_GAME_SHARDS
            val expectedBanRecord =
                BanRecord(banRecord(1, ban = ban(1, socialId = socialId.toFixed128(), banScope = allGameShardsBanScope)))
            coEvery {
                accountDaoNodeService.addBan(
                    socialId,
                    allGameShardsBanScope,
                    0.toUUID(),
                    durationInMillis,
                    banReason,
                    banningOperatorId,
                    banComment
                )
            } returns expectedBanRecord

            val actualBanRecord = testObj.banAccount(
                socialId,
                allGameShardsBanScope,
                0.toUUID(),
                durationInMillis,
                expectedBanRecord.ban.banReason,
                banningOperatorId,
                banComment
            )

            assertEquals(expectedBanRecord, actualBanRecord)
        }

        @Test
        fun `banAccount defaults 0 duration to Long max value`() = runTest {
            coEvery {
                accountDaoNodeService.addBan(
                    socialId,
                    banScope,
                    gameShardId,
                    Long.MAX_VALUE,
                    banReason,
                    banningOperatorId,
                    banComment
                )
            } returns expectedBanRecord

            val actualBanRecord = testObj.banAccount(socialId, banScope, gameShardId, 0, banReason, banningOperatorId, banComment)

            assertEquals(expectedBanRecord, actualBanRecord)
        }

        @Test
        fun `banAccount calls through to the account dao node service with a game shard id`() = runTest {
            val actualBanRecord =
                testObj.banAccount(socialId, banScope, gameShardId, durationInMillis, banReason, banningOperatorId, banComment)

            assertEquals(expectedBanRecord, actualBanRecord)
        }

        @Test
        fun `banAccount sends a notification to banned player`() = runTest {
            testObj.banAccount(socialId, banScope, gameShardId, durationInMillis, banReason, banningOperatorId, banComment)

            val expectedBanNotification = AccountRpc.BanV1Notification.newBuilder()
                .setBan(expectedBanRecord.ban.toProto())
                .build()
            coVerify { accountService.notifyPlayer(socialId, expectedBanNotification) }
        }

        @Test
        fun `banAccount throws exception when banScope is invalid`() = runTest {
            val expectedApplicationError = AccountRpc.MissingBanScopeApplicationError.newBuilder().build()
            coAssertThrowsApplicationErrorException(expectedApplicationError) {
                testObj.banAccount(
                    socialId,
                    BanScope.BAN_SCOPE_UNUSED,
                    gameShardId,
                    durationInMillis,
                    banReason,
                    banningOperatorId,
                    banComment
                )
            }
        }

        @Test
        fun `banAccount throws exception when ban scope is game shard but game shard is invalid`() = runTest {
            val expectedApplicationError =
                buildNotFoundApplicationError(gameShardId.toString(), "Game shard with id \"$gameShardId\" not found.")
            coEvery { gameShardCacheNodeService.throwIfGameShardDoesNotExist(gameShardId) } throws ApplicationErrorException(
                expectedApplicationError
            )

            coAssertThrowsApplicationErrorException(expectedApplicationError) {
                testObj.banAccount(
                    socialId,
                    BanScope.BAN_SCOPE_GAME_SHARD,
                    gameShardId,
                    durationInMillis,
                    banReason,
                    banningOperatorId,
                    "ban comment"
                )
            }
        }

        @Test
        fun `banAccount does not throw exception when ban scope is not game shard and game shard is invalid`() = runTest {
            val invalidGameShardId = anyUuid()
            val expectedApplicationError =
                buildNotFoundApplicationError(gameShardId.toString(), "Game shard with id \"$gameShardId\" not found.")
            coEvery { gameShardCacheNodeService.throwIfGameShardDoesNotExist(invalidGameShardId) } throws ApplicationErrorException(
                expectedApplicationError
            )
            val allGameShardsBanScope = BanScope.BAN_SCOPE_ALL_GAME_SHARDS
            coEvery {
                accountDaoNodeService.addBan(
                    socialId,
                    allGameShardsBanScope,
                    invalidGameShardId,
                    durationInMillis,
                    banReason,
                    banningOperatorId,
                    banComment
                )
            } returns expectedBanRecord

            coAssertDoesNotThrow {
                testObj.banAccount(
                    socialId,
                    allGameShardsBanScope,
                    invalidGameShardId,
                    durationInMillis,
                    banReason,
                    banningOperatorId,
                    banComment
                )
            }
        }

        @Test
        fun `banAccount sends social block request with longest active ban time remaining`() = runTest {
            val activeBans = listOf(
                Ban(
                    ban(
                        2,
                        socialId = socialId.toFixed128(),
                        BanScope.BAN_SCOPE_ALL_GAME_SHARDS,
                        durationInMillis = 10.secondsToMillis,
                        startTimestampMillis = 1
                    )
                ),
                Ban(
                    ban(
                        3,
                        socialId = socialId.toFixed128(),
                        BanScope.BAN_SCOPE_GAME_SHARD,
                        durationInMillis = 4.secondsToMillis,
                        startTimestampMillis = 8.001.secondsToMillis
                    )
                ),
                Ban(
                    ban(
                        4,
                        socialId = socialId.toFixed128(),
                        BanScope.BAN_SCOPE_FULL_ACCOUNT,
                        durationInMillis = 3.secondsToMillis,
                        startTimestampMillis = 7.001.secondsToMillis
                    )
                ),
                Ban(
                    ban(
                        5,
                        socialId = socialId.toFixed128(),
                        BanScope.BAN_SCOPE_FULL_ACCOUNT,
                        durationInMillis = 4.secondsToMillis,
                        startTimestampMillis = 7.001.secondsToMillis
                    )
                )
            )
            val currentTime = 9001L
            val blockRequest = blockPlayerServiceRequest(1, socialId.toFixed128(), 2.secondsToMillis)
            coEvery { timeProxy.currentEpochMillis() } returns currentTime
            coEvery {
                accountDaoNodeService.addBan(
                    socialId,
                    BanScope.BAN_SCOPE_FULL_ACCOUNT,
                    gameShardId,
                    1.secondsToMillis,
                    banReason,
                    banningOperatorId,
                    banComment
                )
            } returns expectedFullAccountBanRecord
            coEvery { accountDaoNodeService.getActiveBans(socialId) } returns activeBans

            testObj.banAccount(
                socialId,
                BanScope.BAN_SCOPE_FULL_ACCOUNT,
                gameShardId,
                1.secondsToMillis,
                banReason,
                banningOperatorId,
                banComment
            )

            coVerify { accountService.requestRpcV2(blockRequest, PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom) }
        }

        @Test
        fun `banAccount does not send social block if not full account ban`() = runTest {
            val blockRequest = blockPlayerServiceRequest(1, socialId.toFixed128(), durationInMillis)
            testObj.banAccount(
                socialId,
                BanScope.BAN_SCOPE_ALL_GAME_SHARDS,
                gameShardId,
                durationInMillis,
                banReason,
                banningOperatorId,
                banComment
            )

            coVerify(exactly = 0) {
                accountService.requestRpcV2(
                    blockRequest,
                    PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom
                )
            }

            testObj.banAccount(
                socialId,
                BanScope.BAN_SCOPE_GAME_SHARD,
                gameShardId,
                durationInMillis,
                banReason,
                banningOperatorId,
                banComment
            )

            coVerify(exactly = 0) {
                accountService.requestRpcV2(
                    blockRequest,
                    PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom
                )
            }
        }

        @Test
        fun `banAccount does not send social block if disabled by config`() = runTest {
            val blockRequest = blockPlayerServiceRequest(1, socialId.toFixed128(), durationInMillis)
            coEvery {
                accountService.requestRpcV2(blockRequest, PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom)
            } returns mockk()
            testObj.onConfigChanged(AccountServiceConfig.getFor(BackendType.SOCIAL).apply {
                blockSessionOnBan = false
            })

            testObj.banAccount(
                socialId,
                BanScope.BAN_SCOPE_FULL_ACCOUNT,
                gameShardId,
                durationInMillis,
                banReason,
                banningOperatorId,
                banComment
            )

            coVerify(exactly = 0) {
                accountService.requestRpcV2(
                    blockRequest,
                    PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom
                )
            }
            verify { logger.debug("Ban added to account {} but social session block is disabled via config.", socialId) }
        }

        @Test
        fun `banAccount does not send social block request if no active bans`() = runTest {
            coEvery {
                accountDaoNodeService.addBan(
                    socialId,
                    BanScope.BAN_SCOPE_FULL_ACCOUNT,
                    gameShardId,
                    1.secondsToMillis,
                    banReason,
                    banningOperatorId,
                    banComment
                )
            } returns expectedFullAccountBanRecord
            coEvery { accountDaoNodeService.getActiveBans(socialId) } returns emptyList()

            testObj.banAccount(
                socialId,
                BanScope.BAN_SCOPE_FULL_ACCOUNT,
                gameShardId,
                1.secondsToMillis,
                banReason,
                banningOperatorId,
                banComment
            )

            coVerify(exactly = 0) { accountService.requestRpcV2(any(), PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom) }
        }
    }

    @Test
    fun `getBanRecords calls through to the account dao node service`() = runTest {
        val expectedBanRecords = generateList(0) { banRecord(it) }.map { BanRecord(it) }
        val socialId = anyUuid()
        val includeExpiredBans = anyBoolean()
        val includeRevokedBans = anyBoolean()
        coEvery { accountDaoNodeService.getBanRecords(socialId, includeExpiredBans, includeRevokedBans) } returns expectedBanRecords

        val actualBanRecords = testObj.getBanRecords(socialId, includeExpiredBans, includeRevokedBans)

        assertEquals(expectedBanRecords, actualBanRecords)
    }

    @Test
    fun `revokeBan calls through to the account dao node service`() = runTest {
        val banId = anyUuid()
        val revokerSocialId = anyUuid()
        coEvery { accountDaoNodeService.getBan(banId) } returns Ban(ban(1, socialId = socialId.toFixed128()))
        coEvery { accountDaoNodeService.getActiveBans(socialId) } returns emptyList()

        testObj.revokeBan(banId, revokerSocialId)

        coVerify { accountDaoNodeService.revokeBan(banId, revokerSocialId) }
    }

    @Test
    fun `revokeBan calls through to the account dao node service with system id when no revoker social id is provided`() = runTest {
        val banId = anyUuid()
        coEvery { accountDaoNodeService.getBan(banId) } returns Ban(ban(1, socialId = socialId.toFixed128()))
        coEvery { accountDaoNodeService.getActiveBans(socialId) } returns emptyList()

        testObj.revokeBan(banId)

        coVerify { accountDaoNodeService.revokeBan(banId, 0.toUUID()) }
    }

    @Test
    fun `revokeBan does not block nor unblock when ban does not exist`() = runTest {
        val banId = anyUuid()
        val revokerSocialId = anyUuid()
        coEvery { accountDaoNodeService.getBan(banId) } returns null

        testObj.revokeBan(banId, revokerSocialId)

        coVerify { logger.debug("Could not revoke ban. Ban id {} does not exist.", banId) }
        coVerify(exactly = 0) { accountService.requestRpcV2(any(), PlayerSessionRpc.UnblockPlayerSessionServiceV1Response::parseFrom) }
        coVerify(exactly = 0) { accountService.requestRpcV2(any(), PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom) }
    }

    @Test
    fun `revokeBan sends social unblock request when there are no other active bans`() = runTest {
        val unblockRequest = unblockPlayerServiceRequest(1, socialId.toFixed128())
        val banId = anyUuid()
        coEvery { accountDaoNodeService.getBan(banId) } returns Ban(ban(1, socialId = socialId.toFixed128()))
        coEvery { accountDaoNodeService.getActiveBans(socialId) } returns emptyList()


        testObj.revokeBan(banId)

        coVerify { accountService.requestRpcV2(unblockRequest, PlayerSessionRpc.UnblockPlayerSessionServiceV1Response::parseFrom) }
    }

    @Test
    fun `revokeBan sends social unblock request when there are only active game bans`() = runTest {
        val unblockRequest = unblockPlayerServiceRequest(1, socialId.toFixed128())
        val banId = anyUuid()
        val activeBans = listOf(
            Ban(
                ban(
                    2,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_GAME_SHARD,
                    durationInMillis = 10.secondsToMillis,
                    startTimestampMillis = 1
                )
            ),
            Ban(
                ban(
                    3,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_ALL_GAME_SHARDS,
                    durationInMillis = 4.secondsToMillis,
                    startTimestampMillis = 8.001.secondsToMillis
                )
            ),
        )
        coEvery { accountDaoNodeService.getBan(banId) } returns Ban(ban(1, socialId = socialId.toFixed128()))
        coEvery { accountDaoNodeService.getActiveBans(socialId) } returns activeBans

        testObj.revokeBan(banId)

        coVerify { accountService.requestRpcV2(unblockRequest, PlayerSessionRpc.UnblockPlayerSessionServiceV1Response::parseFrom) }
    }

    @Test
    fun `revokeBan blocks using the longest time remaining ban`() = runTest {
        val banId = anyUuid()
        val activeBans = listOf(
            Ban(
                ban(
                    2,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_FULL_ACCOUNT,
                    durationInMillis = 10.secondsToMillis,
                    startTimestampMillis = 1
                )
            ),
            Ban(
                ban(
                    3,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_FULL_ACCOUNT,
                    durationInMillis = 4.secondsToMillis,
                    startTimestampMillis = 8.001.secondsToMillis
                )
            ),
            Ban(
                ban(
                    4,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_FULL_ACCOUNT,
                    durationInMillis = 5.secondsToMillis,
                    startTimestampMillis = 3.001.secondsToMillis
                )
            )
        )
        val currentTime = 9001L
        val blockRequest = blockPlayerServiceRequest(1, socialId.toFixed128(), 3000)
        coEvery { timeProxy.currentEpochMillis() } returns currentTime
        coEvery { accountDaoNodeService.getBan(banId) } returns Ban(ban(1, socialId = socialId.toFixed128()))
        coEvery { accountDaoNodeService.getActiveBans(socialId) } returns activeBans

        testObj.revokeBan(banId)

        coVerify { accountService.requestRpcV2(blockRequest, PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom) }
    }

    @Test
    fun `revokeBan respects ban scope when blocking`() = runTest {
        val banId = anyUuid()
        val activeBans = listOf(
            Ban(
                ban(
                    2,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_ALL_GAME_SHARDS,
                    durationInMillis = 10.secondsToMillis,
                    startTimestampMillis = 1
                )
            ),
            Ban(
                ban(
                    3,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_GAME_SHARD,
                    durationInMillis = 4.secondsToMillis,
                    startTimestampMillis = 8.001.secondsToMillis
                )
            ),
            Ban(
                ban(
                    4,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_FULL_ACCOUNT,
                    durationInMillis = 3.secondsToMillis,
                    startTimestampMillis = 7.001.secondsToMillis
                )
            ),
            Ban(
                ban(
                    5,
                    socialId = socialId.toFixed128(),
                    BanScope.BAN_SCOPE_FULL_ACCOUNT,
                    durationInMillis = 4.secondsToMillis,
                    startTimestampMillis = 7.001.secondsToMillis
                )
            )
        )
        val currentTime = 9001L
        val blockRequest = blockPlayerServiceRequest(1, socialId.toFixed128(), 2000)
        coEvery { timeProxy.currentEpochMillis() } returns currentTime
        coEvery { accountDaoNodeService.getBan(banId) } returns Ban(ban(1, socialId = socialId.toFixed128()))
        coEvery { accountDaoNodeService.getActiveBans(socialId) } returns activeBans

        testObj.revokeBan(banId)

        coVerify { accountService.requestRpcV2(blockRequest, PlayerSessionRpc.BlockPlayerSessionServiceV1Response::parseFrom) }
    }
}
