package pragma.friend.dao

import java.sql.Connection
import java.sql.ResultSet
import java.util.*
import pragma.PragmaNode
import pragma.databases.PartitionedDaoNodeService
import pragma.databases.PartitionedDatabaseConfig
import pragma.databases.SharedDatabaseConfigNodeService
import pragma.friend.CachedFriend
import pragma.friend.DataRightsFriendSettingsRecord
import pragma.friend.FriendList
import pragma.friend.FriendListUpdates
import pragma.friend.FriendOverview
import pragma.friend.FriendSettingsRecord
import pragma.friend.FriendStatus
import pragma.friend.IdentityCache
import pragma.services.PragmaService
import pragma.settings.BackendType
import pragma.settings.DatabaseValidator
import pragma.utils.SocialId
import pragma.utils.SqlStringUtils
import pragma.utils.TimeProxy
import pragma.utils.UUIDUtils
import pragma.utils.toUUID

data class FriendDatabaseRecords(val friendRecords: List<FriendRecord>, val customSettingsRecord: FriendSettingsRecord?)

data class DataRightsFriendDatabaseRecords(val friendRecords: List<FriendRecord>, val customSettingsRecord: DataRightsFriendSettingsRecord?)

@Suppress("unused")
@PragmaService(
    backendTypes = [BackendType.SOCIAL],
    dependencies = [SharedDatabaseConfigNodeService::class]
)
class FriendDaoNodeService(
    pragmaNode: PragmaNode,
    databaseValidator: DatabaseValidator = pragmaNode.databaseValidator,
    val timeProxy: TimeProxy = TimeProxy.defaultInstance,
) : PartitionedDaoNodeService<FriendDaoConfig>(pragmaNode, databaseValidator) {
    override val nameOfPropertyToConsistentlyHashOn: String get() = "socialId"

    private lateinit var identityCache: IdentityCache

    fun init(identityCache: IdentityCache) {
        this.identityCache = identityCache
    }

    companion object {
        object Friend {
            const val tableName = "friends"
            const val socialId = "socialId"
            const val friendSocialId = "friendSocialId"
            const val status = "status"
        }
        object Setting {
            const val tableName = "settings"
            const val socialId = "socialId"
            const val acceptIncomingFriendInvites = "acceptIncomingFriendInvites"
        }

        object Time {
            const val createdTimestampMillis = "createdTimestampMs"
            const val updatedTimestampMillis = "updatedTimestampMs"
        }
    }

    override fun changelogFilepath() = "db-changelogs/friend.sql"
    override fun getDatabaseConfigFrom(serviceConfig: FriendDaoConfig): PartitionedDatabaseConfig = serviceConfig.databaseConfig

    suspend fun getFriendList(socialId: SocialId): FriendList {
        val friendList = FriendList(socialId)

        val databaseResults = transact(::getFriendList.name, propertyToConsistentHash = socialId) { connection ->
            FriendDatabaseRecords(getFriendRecords(connection, socialId), getFriendSettings(connection, socialId))
        }
        val identities = identityCache.getSocialIdentities(databaseResults.friendRecords.map { it.friendSocialId })

        friendList.setCustomSettings(databaseResults.customSettingsRecord)
        databaseResults.friendRecords.forEach { friendRecord ->
            if(!identities.containsKey(friendRecord.friendSocialId)) {
                logger.debug("getFriendList - Unknown social identity for {}", friendRecord.friendSocialId)
                return@forEach
            }
            val socialIdentity = identities[friendRecord.friendSocialId]!!

            when (friendRecord.status) {
                FriendStatus.Accepted -> {
                    friendList.set(CachedFriend(socialIdentity))
                }

                FriendStatus.InviteReceived -> {
                    friendList.inviteReceived(FriendOverview(socialIdentity))
                }

                FriendStatus.InviteSent -> {
                    friendList.inviteSent(FriendOverview(socialIdentity))
                }

                FriendStatus.BlockReceived -> {
                    friendList.blockReceived(FriendOverview(socialIdentity))
                }

                FriendStatus.BlockSent -> {
                    friendList.block(FriendOverview(socialIdentity))
                }

                FriendStatus.BothBlocked -> {
                    friendList.block(FriendOverview(socialIdentity))
                    friendList.blockReceived(FriendOverview(socialIdentity))
                }

                null -> {
                    logger.error(
                        "Unknown status value returned for social id `{}` friend id `{}` status value `{}`",
                        socialId,
                        friendRecord.friendSocialId,
                        friendRecord.statusValue,
                    )
                }
            }
        }
        return friendList
    }



    suspend fun inspectFriendRecords(socialId: SocialId): DataRightsFriendDatabaseRecords {
        return transact(::inspectFriendRecords.name, propertyToConsistentHash = socialId) { connection ->
            DataRightsFriendDatabaseRecords(getFriendRecords(connection, socialId), getFriendSettingsForDataRightsQueries(connection, socialId))
        }
    }

    private fun getFriendRecords(
        connection: Connection,
        socialId: SocialId,
    ): MutableList<FriendRecord> {
        val retrievePlayer = """
                        SELECT `${Friend.friendSocialId}`, `${Friend.status}`, `${Time.createdTimestampMillis}`, `${Time.updatedTimestampMillis}`
                        FROM `${Friend.tableName}` as `friend`
                        WHERE `friend`.`${Friend.socialId}` = ${SqlStringUtils.UNHEX_UUID}
                    """.trimIndent()
        val databaseRecordsList = mutableListOf<FriendRecord>()

        connection.prepareStatement(retrievePlayer).use { statement ->
            statement.setString(1, socialId.toString())
            val r: ResultSet = statement.executeQuery()
            while (r.next()) {
                val friendSocialId = UUIDUtils.from(r.getBinaryStream(Friend.friendSocialId))
                val statusValue = r.getInt(Friend.status)
                val createdTimestampMillis = r.getLong(Time.createdTimestampMillis)
                val updatedTimestampMillis = r.getLong(Time.updatedTimestampMillis)
                databaseRecordsList.add(FriendRecord(friendSocialId, statusValue, createdTimestampMillis, updatedTimestampMillis))
            }
            logger.debug("getFriendRecords - retrieved {} records for social_id {} ", databaseRecordsList.size, socialId)
        }
        return databaseRecordsList
    }

    private fun getFriendSettings(connection: Connection, socialId: SocialId): FriendSettingsRecord? {
        val retrievePlayer = """
                        SELECT `${Setting.acceptIncomingFriendInvites}`
                        FROM `${Setting.tableName}` as `setting`
                        WHERE `setting`.`${Setting.socialId}` = ${SqlStringUtils.UNHEX_UUID}
                    """.trimIndent()

        connection.prepareStatement(retrievePlayer).use { statement ->
            statement.setString(1, socialId.toString())
            val r: ResultSet = statement.executeQuery()
            if(r.next()) {
                val acceptFriendInvites = r.getBoolean(Setting.acceptIncomingFriendInvites)
                val friendSettingsRecord = FriendSettingsRecord(acceptFriendInvites)
                logger.debug("getFriendSettings - load custom settings for social_id {}: {}", socialId, friendSettingsRecord)
                return friendSettingsRecord
            }
            logger.debug("getFriendSettings - no custom settings for social_id {} ", socialId)
            return null
        }
    }

    private fun getFriendSettingsForDataRightsQueries(connection: Connection, socialId: SocialId): DataRightsFriendSettingsRecord? {
        val retrievePlayer = """
                        SELECT `${Setting.acceptIncomingFriendInvites}`, `${Time.createdTimestampMillis}`, `${Time.updatedTimestampMillis}`
                        FROM `${Setting.tableName}` as `setting`
                        WHERE `setting`.`${Setting.socialId}` = ${SqlStringUtils.UNHEX_UUID}
                    """.trimIndent()

        connection.prepareStatement(retrievePlayer).use { statement ->
            statement.setString(1, socialId.toString())
            val r: ResultSet = statement.executeQuery()
            if(r.next()) {
                val acceptFriendInvites = r.getBoolean(Setting.acceptIncomingFriendInvites)
                val createdTimestampMillis = r.getLong(Time.createdTimestampMillis)
                val updatedTimestampMillis = r.getLong(Time.updatedTimestampMillis)
                val friendSettingsRecord = DataRightsFriendSettingsRecord(FriendSettingsRecord(acceptFriendInvites), createdTimestampMillis, updatedTimestampMillis)
                logger.debug("getFriendSettings - load custom settings for social_id {}: {}", socialId, friendSettingsRecord)
                return friendSettingsRecord
            }
            logger.debug("getFriendSettings - no custom settings for social_id {} ", socialId)
            return null
        }
    }

    suspend fun writeFriendListUpdates(socialId: SocialId, friendListUpdates: FriendListUpdates) {
        if (friendListUpdates.isEmpty()) {
            return
        }

        return transact(::writeFriendListUpdates.name, propertyToConsistentHash = socialId) { connection ->
            friendListUpdates.sentInvitesAdded.forEach { sentInvite ->
                writeOrUpdateFriendRecord(
                    connection,
                    socialId,
                    sentInvite.account.socialId,
                    FriendStatus.InviteSent,
                    sentInvite.timestampMillis
                )
            }
            friendListUpdates.receivedInvitesAdded.forEach { receivedInvite ->
                writeOrUpdateFriendRecord(
                    connection,
                    socialId,
                    receivedInvite.account.socialId,
                    FriendStatus.InviteReceived,
                    receivedInvite.timestampMillis
                )
            }
            friendListUpdates.friendsAdded.forEach { newFriend ->
                writeOrUpdateFriendRecord(connection, socialId, newFriend.socialId.toUUID(), FriendStatus.Accepted)
            }

            val playersReceivingABlockUpdate = friendListUpdates.blockWriteOperations.map { it.blockRecord.account.socialId }
            friendListUpdates.friendsRemoved
                .filterNot { playersReceivingABlockUpdate.contains(it) }
                .forEach { removedFriend -> deleteFriendRecord(connection, socialId, removedFriend) }

            val friendsAddedMap = mutableSetOf<UUID>()
            friendListUpdates.friendsAdded.forEach { friendsAddedMap.add(it.socialId.toUUID()) }
            (friendListUpdates.sentInvitesRemoved + friendListUpdates.receivedInvitesRemoved).forEach { removedPotentialFriend ->
                if (!friendsAddedMap.contains(removedPotentialFriend)) {
                    deleteFriendRecord(connection, socialId, removedPotentialFriend)
                }
            }
            friendListUpdates.blockWriteOperations.forEach { operation ->
                writeOrUpdateFriendRecord(
                    connection,
                    socialId,
                    operation.blockRecord.account.socialId,
                    operation.friendStatus,
                    operation.blockRecord.timestampMillis
                )
            }
            friendListUpdates.blockDeleteOperations.forEach { removedSocialId ->
                deleteFriendRecord(
                    connection,
                    socialId,
                    removedSocialId
                )
            }
            updateCustomFriendSettings(
                connection,
                socialId,
                friendListUpdates.customSettings,
                friendListUpdates.forceDeleteSettings
            )
        }
    }

    private fun deleteFriendRecord(connection: Connection, socialId: SocialId, removedFriendId: UUID) {
        connection.prepareStatement(
            """
                DELETE FROM `${Friend.tableName}`
                  WHERE `${Friend.socialId}`= ${SqlStringUtils.UNHEX_UUID} 
                  AND `${Friend.friendSocialId}` = ${SqlStringUtils.UNHEX_UUID};
            """.trimIndent()
        ).use { statement ->
            statement.setString(1, socialId.toString())
            statement.setString(2, removedFriendId.toString())
            val result = statement.executeUpdate()
            logger.debug(
                "Deleted record from friends table: sql_result: {} [social_id:{} friend_social_id:{}]",
                result,
                socialId,
                removedFriendId,
            )
        }
    }

    private fun updateCustomFriendSettings(
        connection: Connection,
        socialId: SocialId,
        friendSettingsRecord: FriendSettingsRecord?,
        forceDeleteSettings: Boolean
    ) {
        if(forceDeleteSettings) {
            connection.prepareStatement(
                """
                DELETE FROM `${Setting.tableName}`
                  WHERE `${Setting.socialId}`= ${SqlStringUtils.UNHEX_UUID} 
            """.trimIndent()
            ).use { statement ->
                statement.setString(1, socialId.toString())
                val result = statement.executeUpdate()
                logger.debug(
                    "Deleting settings record from friends table: sql_result: {} [social_id:{}]",
                    result,
                    socialId,
                )
            }
        }

        if(friendSettingsRecord == null) {
            return
        }
        val timestamp = timeProxy.currentEpochMillis()
        connection.prepareStatement(
            """
                INSERT INTO `${Setting.tableName}` (
                    `${Setting.socialId}`, 
                    `${Setting.acceptIncomingFriendInvites}`,
                    `${Time.createdTimestampMillis}`, 
                    `${Time.updatedTimestampMillis}` )
                VALUES (${SqlStringUtils.UNHEX_UUID}, ?, ?, ?)
                ON DUPLICATE KEY UPDATE `${Setting.acceptIncomingFriendInvites}`=?, `${Time.updatedTimestampMillis}`=?;
            """.trimIndent()
        ).use { statement ->
            statement.setString(1, socialId.toString())
            statement.setBoolean(2, friendSettingsRecord.acceptFriendInvites)
            statement.setLong(3, timestamp)
            statement.setLong(4, timestamp)
            statement.setBoolean(5, friendSettingsRecord.acceptFriendInvites)
            statement.setLong(6, timestamp)
            val result = statement.executeUpdate()

            logger.debug(
                "Modified record in settings table: sql_result: {} [social_id:{}] new settings:{}",
                result,
                socialId,
                friendSettingsRecord
            )
        }
    }

    private fun writeOrUpdateFriendRecord(
        connection: Connection,
        socialId: SocialId,
        friendSocialId: UUID,
        status: FriendStatus,
        writeTimestamp: Long = timeProxy.currentEpochMillis(),
    ) {
        connection.prepareStatement(
            """
                INSERT INTO `${Friend.tableName}` (
                    `${Friend.socialId}`, 
                    `${Friend.friendSocialId}`, 
                    `${Friend.status}`, 
                    `${Time.createdTimestampMillis}`, 
                    `${Time.updatedTimestampMillis}` )
                VALUES (${SqlStringUtils.UNHEX_UUID}, ${SqlStringUtils.UNHEX_UUID}, ?, ?, ?)
                ON DUPLICATE KEY UPDATE `${Friend.status}`=?, `${Time.updatedTimestampMillis}`=?;
            """.trimIndent()
        ).use { statement ->
            statement.setString(1, socialId.toString())
            statement.setString(2, friendSocialId.toString())
            statement.setInt(3, status.value)
            statement.setLong(4, writeTimestamp)
            statement.setLong(5, writeTimestamp)
            statement.setInt(6, status.value)
            statement.setLong(7, writeTimestamp)
            val result = statement.executeUpdate()
            logger.debug(
                "Modified record in friends table: sql_result: {} [social_id:{} friend_social_id:{}] status:{} timestamp:{}",
                result,
                socialId,
                friendSocialId,
                status.name,
                writeTimestamp
            )
        }
    }
}
