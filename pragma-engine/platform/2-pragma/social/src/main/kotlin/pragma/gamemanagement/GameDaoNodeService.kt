package pragma.gamemanagement

import java.sql.Connection
import java.sql.PreparedStatement
import java.sql.ResultSet
import java.sql.SQLIntegrityConstraintViolationException
import java.util.UUID
import pragma.PragmaError
import pragma.PragmaException
import pragma.PragmaNode
import pragma.account.config.GameDaoConfig
import pragma.applicationError
import pragma.databases.SharedDatabaseConfigNodeService
import pragma.databases.UnpartitionedDaoNodeService
import pragma.databases.UnpartitionedDatabaseConfig
import pragma.services.PragmaService
import pragma.settings.BackendType
import pragma.settings.DatabaseValidator
import pragma.utils.SqlStringUtils
import pragma.utils.SqlStringUtils.createWhereInClauseIds
import pragma.utils.UUIDUtils
import pragma.utils.toUUID

@Suppress("SqlDialectInspection", "SameParameterValue")
@PragmaService(
    backendTypes = [BackendType.SOCIAL],
    dependencies = [SharedDatabaseConfigNodeService::class]
)
class GameDaoNodeService(pragmaNode: PragmaNode, databaseValidator: DatabaseValidator = pragmaNode.databaseValidator) :
    UnpartitionedDaoNodeService<GameDaoConfig>(pragmaNode, databaseValidator) {
    companion object {
        object Game {
            const val tableName = "game"
            const val pragmaGameId = "pragmaGameId"
            const val pragmaGameIdReadable = "pragmaGameIdReadable"
            const val name = "name"
            const val description = "description"
        }

        object ShardToGame {
            const val tableName = "shard_to_game"
            const val pragmaShardId = "pragmaShardId"
            const val pragmaShardIdReadable = "pragmaShardIdReadable"
            const val name = "name"
            const val description = "description"
            const val pragmaGameId = "pragmaGameId"
            const val accessMode = "accessMode"
        }

        object LimitedAccessEvent {
            const val tableName = "limited_access_event"
            const val limitedAccessEventId = "limitedAccessEventId"
            const val pragmaShardId = "pragmaShardId"
            const val name = "name"
            const val description = "description"
            const val startTimeUnixMs = "startTimeUnixMs"
            const val endTimeUnixMs = "endTimeUnixMs"
            const val timeZone = "timeZone"
        }

        object LimitedAccessEventToPlayerGroup {
            const val tableName = "limited_access_event_to_player_group"
            const val limitedAccessEventId = "limitedAccessEventId"
            const val playerGroupId = "playerGroupId"
        }

        object FullAccessGroups {
            const val tableName = "full_access_groups"
            const val pragmaShardId = "pragmaShardId"
            const val playerGroupId = "playerGroupId"
        }
    }

    private val gameShardsSql: String = """
        SELECT `${ShardToGame.pragmaShardId}`, `${ShardToGame.name}`, `${ShardToGame.description}`, `${ShardToGame.accessMode}`, `${ShardToGame.pragmaGameId}`
        FROM `${ShardToGame.tableName}` 
    """

    override fun changelogFilepath() = "db-changelogs/game_definition.sql"

    override fun getDatabaseConfigFrom(serviceConfig: GameDaoConfig): UnpartitionedDatabaseConfig {
        return serviceConfig.databaseConfig
    }

    override suspend fun developmentRun() {
        val pragmaGameId = 1.toUUID()
        val pragmaGameShardId = 1.toUUID()
        logger.debug("Inserting test game {} with test shard {}", pragmaGameId, pragmaGameId)

        executeSingle(::developmentRun.name) { connection ->
            val addGame = """
                INSERT IGNORE INTO `${Game.tableName}` (`${Game.pragmaGameId}`,`${Game.pragmaGameIdReadable}`,`${Game.name}`,`${Game.description}`)
                VALUES (${SqlStringUtils.UNHEX_UUID},?,?,?)
            """
            connection.prepareStatement(addGame).use { statement ->
                statement.setString(1, pragmaGameId.toString())
                statement.setString(2, pragmaGameId.toString())
                statement.setString(3, "testGame")
                statement.setString(4, "test description")
                statement.executeUpdate()
            }
        }

        executeSingle(::developmentRun.name) { connection ->
            val addGameShard = """
                INSERT IGNORE INTO `${ShardToGame.tableName}` (`${ShardToGame.pragmaShardId}`,`${ShardToGame.pragmaShardIdReadable}`,`${ShardToGame.name}`,`${ShardToGame.pragmaGameId}`) 
                VALUES (${SqlStringUtils.UNHEX_UUID},?,?,${SqlStringUtils.UNHEX_UUID})
            """
            connection.prepareStatement(addGameShard).use { statement ->
                statement.setString(1, pragmaGameShardId.toString())
                statement.setString(2, pragmaGameShardId.toString())
                statement.setString(3, "testGameShard")
                statement.setString(4, pragmaGameId.toString())
                statement.executeUpdate()
            }
        }
    }

    suspend fun create(gameName: String, gameDescription: String): UUID {
        return createForId(UUID.randomUUID(), gameName, gameDescription)
    }

    private suspend fun createForId(pragmaGameId: UUID, gameName: String, gameDescription: String): UUID {
        return transact(::create.name) { connection ->
            val sql1 = """
                INSERT INTO `${Game.tableName}` (`${Game.pragmaGameId}`,`${Game.pragmaGameIdReadable}`,`${Game.name}`,`${Game.description}`)
                VALUES (${SqlStringUtils.UNHEX_UUID},?,?,?)
            """
            connection.prepareStatement(sql1).use { statement ->
                statement.setString(1, pragmaGameId.toString())
                statement.setString(2, pragmaGameId.toString())
                statement.setString(3, gameName)
                statement.setString(4, gameDescription)
                try {
                    val r: Int = statement.executeUpdate()
                    if (r != 1) {
                        throw PragmaException(PragmaError.Database_Error, "Failed when inserting game $gameName.")
                    }
                } catch (e: SQLIntegrityConstraintViolationException) {
                    applicationError { GameManagementRpc.GameAlreadyExistsApplicationError.newBuilder().setGameName(gameName).build() }
                }
            }

            val sql2 = """
                SELECT `${Game.pragmaGameId}`
                FROM `${Game.tableName}` 
                WHERE `${Game.name}` = ?
            """
            connection.prepareStatement(sql2).use { statement ->
                statement.setString(1, gameName)
                val r: ResultSet = statement.executeQuery()
                if (r.next()) {
                    return@transact UUIDUtils.from(r.getBinaryStream(Game.pragmaGameId))
                }

                throw PragmaException(PragmaError.Database_Error, "Failed to retrieve inserted game $gameName.")
            }
        }
    }

    suspend fun createShard(gameShardName: String, gameShardDescription: String, gameId: UUID, gameShardId: UUID): UUID {
        return createShardForId(gameShardId, gameId, gameShardName, gameShardDescription)
    }

    suspend fun gameShardIdExists(gameShard: UUID): Boolean {
        return executeSingle(::gameShardIdExists.name) { connection ->
            val sql = """
                SELECT 1 
                FROM `${ShardToGame.tableName}` 
                WHERE `${ShardToGame.pragmaShardId}` = ${SqlStringUtils.UNHEX_UUID}
            """

            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, gameShard.toString())
                val r: ResultSet = statement.executeQuery()
                if (r.next()) {
                    return@executeSingle true
                }
                return@executeSingle false
            }
        }
    }

    suspend fun getGameById(pragmaGameId: UUID): GameTitle {
        return getAllGameTitles(listOf(pragmaGameId)).single()
    }

    suspend fun updateGameById(id: UUID, gameName: String, gameDescription: String): GameTitle {
        executeSingle(::updateGameById.name) { connection ->
            val sql = """
                UPDATE `${Game.tableName}` as g
                SET g.`${Game.name}` =?,
                g.`${Game.description}` =?
                WHERE g.`${Game.pragmaGameId}`=${SqlStringUtils.UNHEX_UUID}
            """

            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, gameName)
                statement.setString(2, gameDescription)
                statement.setString(3, id.toString())
                statement.executeUpdate()
            }

        }
        return getGameById(id)
    }

    suspend fun getAllGameTitles(pragmaGameIdFilter: List<UUID> = listOf()): List<GameTitle> {
        return transact(::getAllGameTitles.name) { connection ->
            return@transact getGameTitles(connection, pragmaGameIdFilter)
        }
    }

    private fun getGameTitles(connection: Connection, pragmaGameIdFilter: List<UUID>): List<GameTitle> {
        var sql = """
            SELECT `${Game.pragmaGameId}`, `${Game.name}`, `${Game.description}`
            FROM `${Game.tableName}`
        """
        if (pragmaGameIdFilter.isNotEmpty()) {
            sql += """
                WHERE `${Game.pragmaGameId}` IN ${createWhereInClauseIds(pragmaGameIdFilter)}  
            """.trimIndent()
        }
        val statement = connection.prepareStatement(sql)
        pragmaGameIdFilter.forEachIndexed { i, it ->
            statement.setString(1 + i, it.toString())
        }
        val r: ResultSet = statement.executeQuery()
        val gameTitles = mutableListOf<GameTitle>()
        while (r.next()) {
            gameTitles.add(
                GameTitle(
                    UUIDUtils.from(r.getBinaryStream(Game.pragmaGameId)),
                    r.getString(Game.name),
                    getNonNullStringFromNullableField(r, Game.description),
                    mutableListOf()
                )
            )
        }

        val gameTitlesToShards = filterGameShards(connection, pragmaGameIdFilter)
        gameTitles.forEach {
            if (gameTitlesToShards.containsKey(it.id)) {
                it.shards.addAll(gameTitlesToShards[it.id]!!)
            }
        }

        return gameTitles
    }

    // DB GUILD: This function is in place until we solve database migration to make the description field not nullable
    private fun getNonNullStringFromNullableField(result: ResultSet, fieldName: String) = result.getString(fieldName) ?: ""

    private suspend fun createShardForId(
        pragmaGameShardId: UUID,
        gameId: UUID,
        gameShardName: String,
        gameShardDescription: String,
    ): UUID {
        return transact(::createShard.name) { connection ->
            confirmValidGameId(connection, gameId)
            insertNewGameShard(connection, pragmaGameShardId, gameShardName, gameShardDescription, gameId)
            getGameShardIdByShardName(connection, gameShardName)
        }
    }

    suspend fun updateGameShardById(gameShardId: UUID, shardName: String, shardDescription: String): GameShard {
        val sql = """
            UPDATE `${ShardToGame.tableName}` as g
            SET g.`${ShardToGame.name}` =?,
            g.`${ShardToGame.description}` =?
            WHERE g.`${ShardToGame.pragmaShardId}`=${SqlStringUtils.UNHEX_UUID}
        """

        return transact(::updateGameShardById.name) { connection ->
            connection.prepareStatement(sql).use {
                it.setString(1, shardName)
                it.setString(2, shardDescription)
                it.setString(3, gameShardId.toString())
                it.executeUpdate()
            }
            return@transact getGameShard(connection, gameShardId)
        }
    }

    suspend fun setShardAccessMode(
        gameShardId: UUID,
        updatedAccessMode: GameManagementCommon.ShardAccessMode,
    ): GameShard {
        val sql = """
                UPDATE `${ShardToGame.tableName}` as g
                SET g.`${ShardToGame.accessMode}` =?
                WHERE g.`${ShardToGame.pragmaShardId}` =${SqlStringUtils.UNHEX_UUID}
            """

        return transact(::setShardAccessMode.name) { connection ->
            connection.prepareStatement(sql).use {
                it.setInt(1, updatedAccessMode.ordinal)
                it.setString(2, gameShardId.toString())
                it.executeUpdate()
            }
            return@transact getGameShard(connection, gameShardId)
        }
    }

    private fun getGameShardIdByShardName(connection: Connection, gameShardName: String): UUID {
        val sql =
            """
                SELECT `${ShardToGame.pragmaShardId}` 
                FROM `${ShardToGame.tableName}` 
                WHERE `${ShardToGame.name}` = ?
            """
        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, gameShardName)
            val r: ResultSet = statement.executeQuery()
            if (r.next()) {
                return UUIDUtils.from(r.getBinaryStream(ShardToGame.pragmaShardId))
            }
            throw PragmaException(PragmaError.Database_Error, "Failed to retrieve shard named $gameShardName")
        }
    }

    suspend fun getAllGameShards(): Map<UUID, List<GameShard>> {
        return transact(::getAllGameShards.name) { connection ->
            val statement = connection.prepareStatement(gameShardsSql)
            val r: ResultSet = statement.executeQuery()
            return@transact processShardsAndGetFullAccessGroups(connection, r)
        }
    }

    suspend fun getGameDisplayInfosByGameShardIds(gameShardIds: Set<UUID>): Map<UUID, GameDisplayInfo> {
        if (gameShardIds.isEmpty()) {
            return emptyMap()
        }

        return executeSingle(::getGameDisplayInfosByGameShardIds.name) { connection ->
            val shardNameField = "shardName"
            val titleNameField = "titleName"
            val sql =
                """
                SELECT `stg`.`${ShardToGame.pragmaShardId}`, `stg`.`${ShardToGame.name}` as `$shardNameField`, `g`.`${Game.name}` as `$titleNameField`
                FROM `${Game.tableName}` as `g`
                JOIN `${ShardToGame.tableName}` as `stg` ON `stg`.`${ShardToGame.pragmaGameId}` = `g`.`${Game.pragmaGameId}`
                WHERE `stg`.`${ShardToGame.pragmaShardId}` IN ${createWhereInClauseIds(gameShardIds)}
            """
            connection.prepareStatement(sql).use { statement ->
                gameShardIds.forEachIndexed { index, gameShardId ->
                    statement.setString(index + 1, gameShardId.toString())
                }

                val shardIdsToTitleNames = mutableMapOf<UUID, GameDisplayInfo>()
                val r: ResultSet = statement.executeQuery()
                while (r.next()) {
                    val shardId = UUIDUtils.from(r.getBinaryStream(ShardToGame.pragmaShardId))
                    val shardName = r.getString(shardNameField)
                    val titleName = r.getString(titleNameField)
                    shardIdsToTitleNames[shardId] = GameDisplayInfo(shardName, titleName)
                }
                return@executeSingle shardIdsToTitleNames
            }
        }
    }

    suspend fun getGameShardById(gameShardId: UUID): GameShard {
        return transact(::getGameShardById.name) { connection ->
            return@transact getGameShard(connection, gameShardId)
        }
    }

    private fun getGameShard(connection: Connection, gameShardId: UUID): GameShard {
        val sql = gameShardsSql + """
            WHERE `${ShardToGame.pragmaShardId}` = ${SqlStringUtils.UNHEX_UUID}
        """.trimIndent()
        val statement = connection.prepareStatement(sql)
        statement.setString(1, gameShardId.toString())
        val r: ResultSet = statement.executeQuery()
        return try {
            processShardsAndGetFullAccessGroups(connection, r).values.single().single()
        } catch (e: NoSuchElementException) {
            throw PragmaException(PragmaError.GameManagementService_GameShardInvalid, "Could not find gameShardId $gameShardId.", e)
        }
    }

    private fun filterGameShards(connection: Connection, pragmaGameIdsFilter: List<UUID>): Map<UUID, List<GameShard>> {
        var sql = gameShardsSql
        if (pragmaGameIdsFilter.isNotEmpty()) {
            sql += """
                WHERE `${ShardToGame.pragmaGameId}` IN ${createWhereInClauseIds(pragmaGameIdsFilter)}  
            """.trimIndent()
        }
        val statement = connection.prepareStatement(sql)
        pragmaGameIdsFilter.forEachIndexed { i, it ->
            statement.setString(1 + i, it.toString())
        }

        val r: ResultSet = statement.executeQuery()
        return processShardsAndGetFullAccessGroups(connection, r)
    }

    private fun processShardsAndGetFullAccessGroups(connection: Connection, r: ResultSet): Map<UUID, List<GameShard>> {
        val gameTitlesToShards = mutableMapOf<UUID, MutableList<GameShard>>()
        while (r.next()) {
            val gameId = UUIDUtils.from(r.getBinaryStream(ShardToGame.pragmaGameId))
            if (!gameTitlesToShards.containsKey(gameId)) {
                gameTitlesToShards[gameId] = mutableListOf()
            }
            val description = r.getString(ShardToGame.description) ?: ""
            gameTitlesToShards[gameId]?.add(
                GameShard(
                    UUIDUtils.from(r.getBinaryStream(ShardToGame.pragmaShardId)),
                    r.getString(ShardToGame.name),
                    description,
                    GameManagementCommon.ShardAccessMode.forNumber(r.getInt(ShardToGame.accessMode)),
                    mutableSetOf()
                )
            )
        }

        val allShards = gameTitlesToShards.values.flatten()
        val shardsToFullAccessGroups = getFullAccessPlayerGroupIds(connection, allShards.map { it.id })
        allShards.forEach {
            if (shardsToFullAccessGroups.containsKey(it.id)) {
                it.fullAccessPlayerGroupIds.addAll(shardsToFullAccessGroups[it.id]!!)
            }
        }

        return gameTitlesToShards
    }

    suspend fun createLimitedAccessEvent(details: LimitedAccessEventDetails): pragma.gamemanagement.LimitedAccessEvent {
        return transact(::createLimitedAccessEvent.name) { connection ->
            val uuid = UUID.randomUUID()
            insertLimitedAccessEvent(connection, details, uuid)
            addLimitedAccessEventPlayerGroups(connection, uuid, details.playerGroupIds)
            return@transact LimitedAccessEvent(uuid, details)
        }
    }

    suspend fun getLimitedAccessEvent(limitedAccessEventId: UUID): pragma.gamemanagement.LimitedAccessEvent {
        return transact(::getLimitedAccessEvent.name) { connection ->
            return@transact getLimitedAccessEventHelper(connection, limitedAccessEventId)
        }
    }

    suspend fun getGlobalLimitedAccessEvents(): List<pragma.gamemanagement.LimitedAccessEvent> {
        return transact(::getGlobalLimitedAccessEvents.name) { connection ->
            val events = mutableListOf<pragma.gamemanagement.LimitedAccessEvent>()
            val sql = """
               SELECT `${LimitedAccessEvent.limitedAccessEventId}`, `${LimitedAccessEvent.pragmaShardId}`, `${LimitedAccessEvent.name}`, `${LimitedAccessEvent.description}`, `${LimitedAccessEvent.startTimeUnixMs}`, `${LimitedAccessEvent.endTimeUnixMs}`, `${LimitedAccessEvent.timeZone}`
               FROM `${LimitedAccessEvent.tableName}`
            """.trimIndent()

            connection.prepareStatement(sql).use { statement ->
                return@transact loadLimitedAccessEvents(statement, events, connection)
            }
        }
    }

    suspend fun getAllLimitedAccessEvents(gameShardId: UUID): List<pragma.gamemanagement.LimitedAccessEvent> {
        return transact(::getAllLimitedAccessEvents.name) { connection ->
            val events = mutableListOf<pragma.gamemanagement.LimitedAccessEvent>()
            val sql = """
               SELECT `${LimitedAccessEvent.limitedAccessEventId}`, `${LimitedAccessEvent.pragmaShardId}`, `${LimitedAccessEvent.name}`, `${LimitedAccessEvent.description}`, `${LimitedAccessEvent.startTimeUnixMs}`, `${LimitedAccessEvent.endTimeUnixMs}`, `${LimitedAccessEvent.timeZone}`
               FROM `${LimitedAccessEvent.tableName}` 
               WHERE `${LimitedAccessEvent.pragmaShardId}` = ${SqlStringUtils.UNHEX_UUID}
            """.trimIndent()

            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, gameShardId.toString())
                return@transact loadLimitedAccessEvents(statement, events, connection)
            }
        }
    }

    private fun loadLimitedAccessEvents(
        statement: PreparedStatement,
        events: MutableList<pragma.gamemanagement.LimitedAccessEvent>,
        connection: Connection,
    ): MutableList<pragma.gamemanagement.LimitedAccessEvent> {
        val r: ResultSet = statement.executeQuery()
        while (r.next()) {
            val eventId = UUIDUtils.from(r.getBinaryStream(LimitedAccessEvent.limitedAccessEventId))
            events.add(
                LimitedAccessEvent(
                    eventId,
                    LimitedAccessEventDetails(
                        UUIDUtils.from(r.getBinaryStream(LimitedAccessEvent.pragmaShardId)),
                        r.getString(LimitedAccessEvent.name),
                        r.getString(LimitedAccessEvent.description),
                        getLimitedAccessEventToPlayerGroup(connection, eventId),
                        r.getLong(LimitedAccessEvent.startTimeUnixMs),
                        r.getLong(LimitedAccessEvent.endTimeUnixMs),
                        r.getString(LimitedAccessEvent.timeZone)
                    )
                )
            )
        }
        return events
    }

    suspend fun deleteLimitedAccessEvent(limitedAccessEventId: UUID) {
        return executeSingle(::deleteLimitedAccessEvent.name) { connection ->
            val sql = """
                DELETE FROM `${LimitedAccessEvent.tableName}`
                WHERE `${LimitedAccessEvent.limitedAccessEventId}` = ${SqlStringUtils.UNHEX_UUID}
            """
            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, limitedAccessEventId.toString())
                val rowsChanged = statement.executeUpdate()
                if (rowsChanged != 1) {
                    throw PragmaException(
                        PragmaError.Database_Error,
                        "DeleteLimitedAccessEvent was unable to delete a single entry $rowsChanged rows effected"
                    )
                }
            }
        }
    }

    private fun getLimitedAccessEventHelper(connection: Connection, limitedAccessEventId: UUID): pragma.gamemanagement.LimitedAccessEvent {
        val sql = """
           SELECT `${LimitedAccessEvent.limitedAccessEventId}`, `${LimitedAccessEvent.pragmaShardId}`, `${LimitedAccessEvent.name}`, `${LimitedAccessEvent.description}`, `${LimitedAccessEvent.startTimeUnixMs}`, `${LimitedAccessEvent.endTimeUnixMs}`, `${LimitedAccessEvent.timeZone}`
           FROM `${LimitedAccessEvent.tableName}` 
           WHERE `${LimitedAccessEvent.limitedAccessEventId}` = ${SqlStringUtils.UNHEX_UUID}
        """.trimIndent()

        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, limitedAccessEventId.toString())
            val r: ResultSet = statement.executeQuery()
            if (r.next()) {
                return LimitedAccessEvent(
                    UUIDUtils.from(r.getBinaryStream(LimitedAccessEvent.limitedAccessEventId)),
                    LimitedAccessEventDetails(
                        UUIDUtils.from(r.getBinaryStream(LimitedAccessEvent.pragmaShardId)),
                        r.getString(LimitedAccessEvent.name),
                        r.getString(LimitedAccessEvent.description),
                        getLimitedAccessEventToPlayerGroup(connection, limitedAccessEventId),
                        r.getLong(LimitedAccessEvent.startTimeUnixMs),
                        r.getLong(LimitedAccessEvent.endTimeUnixMs),
                        r.getString(LimitedAccessEvent.timeZone)
                    )
                )
            }
            throw PragmaException(
                PragmaError.GameManagementService_LimitedAccessEventNotFound,
                "Nothing found for limitedAccessEventId $limitedAccessEventId"
            )
        }
    }

    suspend fun updateLimitedAccessEvent(
        limitedAccessEventId: UUID,
        limitedAccessEventDetails: LimitedAccessEventDetails,
    ): pragma.gamemanagement.LimitedAccessEvent {
        return transact(::updateLimitedAccessEvent.name) { connection ->
            val sql = """
                UPDATE `${LimitedAccessEvent.tableName}`
                SET `${LimitedAccessEvent.pragmaShardId}` = ${SqlStringUtils.UNHEX_UUID},
                    `${LimitedAccessEvent.name}`= ?,
                    `${LimitedAccessEvent.description}` = ?,
                    `${LimitedAccessEvent.startTimeUnixMs}` = ?,
                    `${LimitedAccessEvent.endTimeUnixMs}` = ?,
                    `${LimitedAccessEvent.timeZone}` = ?
                WHERE `${LimitedAccessEvent.limitedAccessEventId}` = ${SqlStringUtils.UNHEX_UUID}
            """
            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, limitedAccessEventDetails.gameShardId.toString())
                statement.setString(2, limitedAccessEventDetails.name)
                statement.setString(3, limitedAccessEventDetails.description)
                statement.setLong(4, limitedAccessEventDetails.startUnixTimeMs)
                statement.setLong(5, limitedAccessEventDetails.endUnixTimeMs)
                statement.setString(6, limitedAccessEventDetails.timeZone)
                statement.setString(7, limitedAccessEventId.toString())

                val rowsChanged = statement.executeUpdate()
                if (rowsChanged == 0) {
                    throw PragmaException(
                        PragmaError.Database_Error,
                        "updateLimitedAccessEvent unable to update limitedAccessEventId: $limitedAccessEventId"
                    )
                }
            }

            val currentGroups = getLimitedAccessEventToPlayerGroup(connection, limitedAccessEventId)
            val groupsToAdd = getAddedGroups(limitedAccessEventDetails.playerGroupIds, currentGroups)
            val groupsToRemove = getRemovedGroups(limitedAccessEventDetails.playerGroupIds, currentGroups)

            addLimitedAccessEventPlayerGroups(connection, limitedAccessEventId, groupsToAdd)
            removeLimitedAccessEventPlayerGroups(connection, limitedAccessEventId, groupsToRemove)

            return@transact getLimitedAccessEventHelper(connection, limitedAccessEventId)
        }
    }

    private fun removeLimitedAccessEventPlayerGroups(connection: Connection, limitedAccessEventId: UUID, groupsToRemove: Set<UUID>) {
        if (groupsToRemove.isEmpty()) {
            return
        }
        val sql = """
            DELETE FROM `${LimitedAccessEventToPlayerGroup.tableName}` 
            WHERE `${LimitedAccessEventToPlayerGroup.limitedAccessEventId}` = ${SqlStringUtils.UNHEX_UUID}
            AND `${LimitedAccessEventToPlayerGroup.playerGroupId}` IN ${createWhereInClauseIds(groupsToRemove)}
        """
        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, limitedAccessEventId.toString())
            groupsToRemove.forEachIndexed { index, it ->
                statement.setString(index + 2, it.toString())
            }
            statement.executeUpdate()
        }
    }

    private fun getRemovedGroups(playerGroupIds: Set<UUID>, currentGroups: Set<UUID>): Set<UUID> {
        return currentGroups - playerGroupIds
    }

    private fun getAddedGroups(playerGroupIds: Set<UUID>, currentGroups: Set<UUID>): Set<UUID> {
        return playerGroupIds - currentGroups
    }

    private fun getLimitedAccessEventToPlayerGroup(connection: Connection, limitedAccessEventId: UUID): Set<UUID> {
        val playerGroupIds = mutableSetOf<UUID>()
        val sql = """
            SELECT `${LimitedAccessEventToPlayerGroup.playerGroupId}`
            FROM `${LimitedAccessEventToPlayerGroup.tableName}` 
            WHERE `${LimitedAccessEventToPlayerGroup.limitedAccessEventId}` = ${SqlStringUtils.UNHEX_UUID}
        """
        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, limitedAccessEventId.toString())
            val r: ResultSet = statement.executeQuery()
            while (r.next()) {
                playerGroupIds.add(UUIDUtils.from(r.getBinaryStream(LimitedAccessEventToPlayerGroup.playerGroupId)))
            }
        }
        return playerGroupIds
    }

    private fun insertLimitedAccessEvent(connection: Connection, details: LimitedAccessEventDetails, limitedAccessEventId: UUID) {
        val sql = """
           INSERT INTO `${LimitedAccessEvent.tableName}` (`${LimitedAccessEvent.limitedAccessEventId}`, `${LimitedAccessEvent.pragmaShardId}`, `${LimitedAccessEvent.name}`, `${LimitedAccessEvent.description}`, `${LimitedAccessEvent.startTimeUnixMs}`, `${LimitedAccessEvent.endTimeUnixMs}`, `${LimitedAccessEvent.timeZone}`)
           VALUE (${SqlStringUtils.UNHEX_UUID}, ${SqlStringUtils.UNHEX_UUID}, ?, ?, ?, ?, ?)
        """.trimIndent()
        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, limitedAccessEventId.toString())
            statement.setString(2, details.gameShardId.toString())
            statement.setString(3, details.name)
            statement.setString(4, details.description)
            statement.setLong(5, details.startUnixTimeMs)
            statement.setLong(6, details.endUnixTimeMs)
            statement.setString(7, details.timeZone)
            val rowsChanged = statement.executeUpdate()
            if (rowsChanged != 1) {
                throw PragmaException(PragmaError.Database_Error, "Inserting limited access event failed.")
            }
        }
    }

    private fun addLimitedAccessEventPlayerGroups(connection: Connection, limitedAccessEventId: UUID, playerGroupIds: Set<UUID>) {
        if (playerGroupIds.isEmpty()) {
            return
        }
        val sql = """
           INSERT INTO `${LimitedAccessEventToPlayerGroup.tableName}` (`${LimitedAccessEventToPlayerGroup.limitedAccessEventId}`, `${LimitedAccessEventToPlayerGroup.playerGroupId}`)
           VALUES (${SqlStringUtils.UNHEX_UUID}, ${SqlStringUtils.UNHEX_UUID})
        """.trimIndent()
        connection.prepareStatement(sql).use { statement ->
            playerGroupIds.forEach { playerGroupId ->
                statement.setString(1, limitedAccessEventId.toString())
                statement.setString(2, playerGroupId.toString())
                statement.addBatch()
            }
            val rowsChanged = statement.executeBatch()
            if (rowsChanged.size != playerGroupIds.size) {
                throw PragmaException(PragmaError.Database_Error, "Error with adding PlayerGroup(s) to LimitedAccessEvent.")
            }
        }
    }

    private fun insertNewGameShard(
        connection: Connection,
        pragmaGameShardId: UUID,
        gameShardName: String,
        gameShardDescription: String,
        gameId: UUID,
    ) {
        val sql = """
            INSERT INTO `${ShardToGame.tableName}` (`${ShardToGame.pragmaShardId}`,`${ShardToGame.pragmaShardIdReadable}`,`${ShardToGame.name}`,`${ShardToGame.description}`,`${ShardToGame.pragmaGameId}`) 
            VALUES (${SqlStringUtils.UNHEX_UUID},?,?,?,${SqlStringUtils.UNHEX_UUID})
        """
        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, pragmaGameShardId.toString())
            statement.setString(2, pragmaGameShardId.toString())
            statement.setString(3, gameShardName)
            statement.setString(4, gameShardDescription)
            statement.setString(5, gameId.toString())
            try {
                val r: Int = statement.executeUpdate()
                if (r != 1) {
                    throw PragmaException(PragmaError.Database_Error, "Failed when inserting game shard $gameShardName.")
                }
            } catch (e: SQLIntegrityConstraintViolationException) {
                throw PragmaException(
                    PragmaError.GameManagementService_DuplicateGameShardName,
                    "Game shard with name $gameShardName already exists in system.",
                    e
                )
            }
        }
    }

    private fun confirmValidGameId(connection: Connection, gameId: UUID) {
        val sql = """
            SELECT `${Game.pragmaGameId}`
            FROM `${Game.tableName}` 
            WHERE `${Game.pragmaGameId}` = ${SqlStringUtils.UNHEX_UUID}
        """
        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, gameId.toString())
            val r: ResultSet = statement.executeQuery()
            if (!r.next()) {
                throw PragmaException(PragmaError.GameManagementService_GameIdNotFound, "Game Id $gameId not found.")
            }
        }
    }

    suspend fun setFullAccessPlayerGroups(gameShardId: UUID, playerGroupIds: Set<UUID>): GameShard {
        return transact(::setFullAccessPlayerGroups.name) { connection ->
            val currentGroups = getFullAccessPlayerGroupIds(connection, listOf(gameShardId))[gameShardId] ?: emptySet()

            val groupsToAdd = getAddedGroups(playerGroupIds, currentGroups)
            val groupsToRemove = getRemovedGroups(playerGroupIds, currentGroups)

            addFullAccessPlayerGroups(connection, gameShardId, groupsToAdd)
            removeFullAccessPlayerGroups(connection, gameShardId, groupsToRemove)

            return@transact getGameShard(connection, gameShardId)
        }
    }

    private fun getFullAccessPlayerGroupIds(connection: Connection, filterPragmaShardIds: List<UUID>): Map<UUID, Set<UUID>> {
        var sql = """
            SELECT `${FullAccessGroups.pragmaShardId}`,`${FullAccessGroups.playerGroupId}`
            FROM `${FullAccessGroups.tableName}`
        """
        if (filterPragmaShardIds.isNotEmpty()) {
            sql += """
                WHERE `${FullAccessGroups.pragmaShardId}` IN ${createWhereInClauseIds(filterPragmaShardIds)}
            """.trimIndent()
        }
        connection.prepareStatement(sql).use { statement ->
            filterPragmaShardIds.forEachIndexed { i, it ->
                statement.setString(1 + i, it.toString())
            }
            val r: ResultSet = statement.executeQuery()
            val pragmaShardIdToPlayerGroupIds = mutableMapOf<UUID, MutableSet<UUID>>()
            while (r.next()) {
                val pragmaShardId = UUIDUtils.from(r.getBinaryStream(FullAccessGroups.pragmaShardId))
                val playerGroupId = UUIDUtils.from(r.getBinaryStream(FullAccessGroups.playerGroupId))
                if (!pragmaShardIdToPlayerGroupIds.containsKey(pragmaShardId)) {
                    pragmaShardIdToPlayerGroupIds[pragmaShardId] = mutableSetOf()
                }
                pragmaShardIdToPlayerGroupIds[pragmaShardId]?.add(playerGroupId)
            }
            return pragmaShardIdToPlayerGroupIds
        }
    }

    private fun addFullAccessPlayerGroups(connection: Connection, pragmaShardId: UUID, playerGroupIds: Set<UUID>) {
        if (playerGroupIds.isEmpty()) {
            return
        }
        val sql = """
           INSERT INTO `${FullAccessGroups.tableName}` (`${FullAccessGroups.pragmaShardId}`, `${FullAccessGroups.playerGroupId}`)
           VALUES (${SqlStringUtils.UNHEX_UUID}, ${SqlStringUtils.UNHEX_UUID})
        """.trimIndent()
        connection.prepareStatement(sql).use { statement ->
            playerGroupIds.forEach { playerGroupId ->
                statement.setString(1, pragmaShardId.toString())
                statement.setString(2, playerGroupId.toString())
                statement.addBatch()
            }
            val rowsChanged = statement.executeBatch()
            if (rowsChanged.size != playerGroupIds.size) {
                throw PragmaException(
                    PragmaError.Database_Error,
                    "Error with adding PlayerGroup(s) to Full Access List for shard $pragmaShardId"
                )
            }
        }
    }

    private fun removeFullAccessPlayerGroups(connection: Connection, pragmaShardId: UUID, groupsToRemove: Set<UUID>) {
        if (groupsToRemove.isEmpty()) {
            return
        }
        val sql = """
            DELETE FROM `${FullAccessGroups.tableName}` 
            WHERE `${FullAccessGroups.pragmaShardId}` = ${SqlStringUtils.UNHEX_UUID}
            AND `${FullAccessGroups.playerGroupId}` IN ${createWhereInClauseIds(groupsToRemove)}
        """
        connection.prepareStatement(sql).use { statement ->
            statement.setString(1, pragmaShardId.toString())
            groupsToRemove.forEachIndexed { index, it ->
                statement.setString(index + 2, it.toString())
            }
            statement.executeUpdate()
        }
    }
}
