package pragma.account

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.client.plugins.ResponseException
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.URLBuilder
import io.ktor.http.encodedPath
import java.util.Base64
import org.slf4j.Logger
import pragma.account.AccountRpc.IdProviderUnexpectedResponseApplicationError
import pragma.account.AccountRpc.UnauthorizedApplicationError
import pragma.account.config.MetaQuestIdProviderConfig
import pragma.applicationError
import pragma.applicationRequire
import pragma.auth.IdProviderAccount
import pragma.content.ContentDataNodeService
import pragma.http.EmptyAuthProvider
import pragma.http.TimedHttpClient
import pragma.logging.PragmaLoggerFactory
import pragma.plugins.ConfigurablePlugin
import pragma.services.Service
import pragma.utils.PragmaSerializationUtils

class MetaQuestIdentityProviderPlugin(
    override val service: Service,
    override val contentDataNodeService: ContentDataNodeService,
) : OAuthIdentityProvider<OAuthAccessToken>, ConfigurablePlugin<MetaQuestIdProviderConfig> {
    override fun getType() = AccountRpc.IdProvider.METAQUEST_VALUE
    override fun getMetricName() = "Meta"

    override var canAuthenticate: Boolean = true
    override var separator: String = ""

    override lateinit var config: MetaQuestIdProviderConfig
    private var httpClient: TimedHttpClient = TimedHttpClient("meta.outgoing.http", service)
    private var logger = PragmaLoggerFactory.getLogger(MetaQuestIdentityProviderPlugin::class)

    constructor(
        service: Service,
        contentDataNodeService: ContentDataNodeService,
        timedHttpClient: TimedHttpClient,
        logger: Logger,
    ) : this(service, contentDataNodeService) {
        this.httpClient = timedHttpClient
        this.logger = logger
    }

    companion object {
        const val BASE_URL = "https://graph.oculus.com"
        const val TOKEN_SEPARATOR = "|"
        internal val tokenEndpoint = run {
            val builder = URLBuilder(BASE_URL)
            builder.encodedPath = "sso_authorize_code"
            builder.buildString()
        }

        internal val userInfoEndpoint = run {
            val builder = URLBuilder(BASE_URL)
            builder.buildString()
        }

        internal val nonceValidateEndpoint = run {
            val builder = URLBuilder(BASE_URL)
            builder.encodedPath = "user_nonce_validate"
            builder.buildString()
        }
    }

    override suspend fun onConfigChanged(config: MetaQuestIdProviderConfig) {
        this.config = config
    }

    override suspend fun validate(providerToken: String): IdProviderAccount {
        val userResponse = if (providerToken.contains(TOKEN_SEPARATOR)) {
            val tokenParts = providerToken.split(TOKEN_SEPARATOR)
            val userId = tokenParts[0]
            val nonce = tokenParts[1]
            applicationRequire(validateNonce(userId, nonce)) {
                UnauthorizedApplicationError.newBuilder().setReason("The nonce code provided is invalid.").build()
            }
            getUserInfo(config.appCredentials.getDecryptedValue(), userId)
        } else {
            getUserInfo(providerToken)
        }

        val metaProviderAccount: MetaProviderAccount = try {
            PragmaSerializationUtils.jsonMapper.readValue(userResponse.bodyAsText())
        } catch (e: JsonProcessingException) {
            logger.debug("Could not parse Meta Quest user json response.", e)
            throwValidateExceptionWithMetric(service) {
                IdProviderUnexpectedResponseApplicationError.newBuilder().setIdProvider(getName()).build()
            }
        }


        return IdProviderAccount(
            getType(),
            metaProviderAccount.id,
            metaProviderAccount.alias,
            ""
        )
    }

    private suspend fun getUserInfo(accessToken: String, path: String = "me"): HttpResponse {
        return try {
            val userInfoUrlBuilder = URLBuilder(userInfoEndpoint)
            userInfoUrlBuilder.encodedPath = path
            userInfoUrlBuilder.parameters.append("access_token", accessToken)
            userInfoUrlBuilder.parameters.append("fields", "id,alias")
            httpClient.get(userInfoUrlBuilder.buildString(), EmptyAuthProvider)
        } catch (e: Exception) {
            logger.debug("Unexpected response from Meta Quest user endpoint.", e)
            throwValidateExceptionWithMetric(service) {
                IdProviderUnexpectedResponseApplicationError.newBuilder().setIdProvider(getName()).build()
            }
        }
    }

    private suspend fun validateNonce(userId: String, nonce: String): Boolean {
        val nonceResponse = try {
            val nonceValidateUrlBuilder = URLBuilder(nonceValidateEndpoint)
            nonceValidateUrlBuilder.parameters.append("access_token", config.appCredentials.getDecryptedValue())
            nonceValidateUrlBuilder.parameters.append("nonce", nonce)
            nonceValidateUrlBuilder.parameters.append("user_id", userId)
            httpClient.post(nonceValidateUrlBuilder.buildString(), "", EmptyAuthProvider)
        } catch (e: ResponseException) {
            logger.debug("Unexpected response from Meta Quest validate nonce endpoint.", e)
            throwValidateExceptionWithMetric(service) {
                IdProviderUnexpectedResponseApplicationError.newBuilder().setIdProvider(getName()).build()
            }
        } catch (e: Exception) {
            logger.debug("Unknown exception from Meta Quest validate nonce endpoint.", e)
            throwValidateExceptionWithMetric(service) {
                IdProviderUnexpectedResponseApplicationError.newBuilder().setIdProvider(getName()).build()
            }
        }
        val metaNonceResponse: MetaNonceResponse = try {
            PragmaSerializationUtils.jsonMapper.readValue(nonceResponse.bodyAsText())
        } catch (e: JsonProcessingException) {
            logger.debug("Could not parse Meta Quest validate nonce response.", e)
            throwValidateExceptionWithMetric(service) {
                IdProviderUnexpectedResponseApplicationError.newBuilder().setIdProvider(getName()).build()
            }
        }
        return metaNonceResponse.isValid
    }

    override fun getPublicInfo(): Map<String, String> {
        return mapOf(
            "clientId" to config.appId,
            "orgId" to config.orgId,
            "showPortalLoginButton" to config.showPortalLoginButton.toString(),
            "playerLoginEnabled" to config.playerLoginEnabled.toString(),
            "operatorLoginEnabled" to config.operatorLoginEnabled.toString(),
            "accountLinkingEnabled" to config.accountLinkingEnabled.toString(),
            "accountUnlinkingEnabled" to config.accountUnlinkingEnabled.toString(),
            "accountLinkingOneAssociationOnly" to config.accountLinkingOneAssociationOnly.toString(),
            "accountLinkingCooldownInDays" to config.accountLinkingCooldownInDays.toString(),
        )
    }

    override suspend fun accessToken(code: String, redirectUri: String, codeVerifier: String): OAuthAccessToken {
        val token = extractAuthorizationCodeAndUserId(code)
        val tokenEndpointUrlBuilder = URLBuilder(tokenEndpoint)
        tokenEndpointUrlBuilder.parameters.append("access_token", config.appCredentials.getDecryptedValue())
        tokenEndpointUrlBuilder.parameters.append("org_scoped_id", token.orgScopedId)
        tokenEndpointUrlBuilder.parameters.append("code", token.code)

        val response = httpClient.post(
            tokenEndpointUrlBuilder.buildString(),
            "",
        )
        val metaToken = PragmaSerializationUtils.jsonMapper.readValue<MetaAccessToken>(response.bodyAsText())
        return OAuthAccessToken(
            metaToken.oauthToken,
            "",
            Int.MAX_VALUE,
            metaToken.refreshToken,
            null
        )
    }

    private fun extractAuthorizationCodeAndUserId(base64EncodedJson: String): AuthorizationCodeAndUserId {
        return try {
            val decodedString = Base64.getDecoder().decode(base64EncodedJson).decodeToString()
            PragmaSerializationUtils.jsonMapper.readValue(decodedString)
        } catch (exception: IllegalArgumentException) {
            applicationError {
                UnauthorizedApplicationError.newBuilder().setReason("The authorization code provided by Meta Quest is malformed.").build()
            }
        } catch (exception: JsonParseException) {
            applicationError {
                UnauthorizedApplicationError.newBuilder().setReason("The authorization code provided by Meta Quest is malformed.").build()
            }
        }
    }

    data class AuthorizationCodeAndUserId(
        @JsonProperty("code")
        val code: String,
        @JsonProperty("org_scoped_id")
        val orgScopedId: String,
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class MetaProviderAccount(val id: String, val alias: String)

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class MetaNonceResponse(
        @JsonProperty("is_valid")
        val isValid: Boolean,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetaAccessToken(
    @JsonProperty("oauth_token")
    val oauthToken: String,
    @JsonProperty("refresh_code")
    val refreshToken: String,
)
