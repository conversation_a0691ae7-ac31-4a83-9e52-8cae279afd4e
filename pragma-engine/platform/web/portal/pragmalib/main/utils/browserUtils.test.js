import encodingUtils from './encodingUtils.js'
import browserUtils from './browserUtils.js'
import locationUtils from './locationUtils.js'

describe('Browser Utils', () => {
  it('[setCookie], [getCookie] work as intended', () => {
    const testCookie = {
      name: 'testCookie',
      value: 'testValue',
      expireDays: 2,
      domain: locationUtils.getDomainName()
    }
    browserUtils.setCookie(testCookie)

    const testData = browserUtils.getCookie('testCookie')
    expect(testData).toEqual(testCookie.value)

    browserUtils.setCookie({ ...testCookie, expireDays: -2 })
  })

  it('[setLocalStorageItemEncoded] sets a stringified item into the local storage', () => {
    const testData = { object: 2 }

    browserUtils.setLocalStorageItemEncoded('testSet', testData)
    expect(localStorage.getItem('testSet')).toEqual(encodingUtils.compress(testData))

    localStorage.removeItem('testSet')
  })

  it('[setLocalStorageItemEncoded] removes the item from localstorage if the value is null or undefined', () => {
    browserUtils.setLocalStorageItemEncoded('testSet', 'testData')
    browserUtils.setLocalStorageItemEncoded('testSet', null)

    expect(browserUtils.getLocalStorageItemDecoded('testSet')).toBeFalsy()
  })

  it('[getLocalStorageItemDecoded] gets a parsed item from the local storage', () => {
    const testData = { object: 2 }

    localStorage.setItem('testGet', encodingUtils.compress(testData))
    expect(browserUtils.getLocalStorageItemDecoded('testGet')).toEqual(testData)

    localStorage.removeItem('testGet')
  })

  it('[getLocalStorageItemDecoded] retrieves the same string set with [setLocalStorageItemEncoded]', () => {
    const testData = 'aStringValue'

    browserUtils.setLocalStorageItemEncoded('testString', testData)
    expect(browserUtils.getLocalStorageItemDecoded('testString')).toEqual(testData)

    localStorage.removeItem('testString')
  })

  it('[getLocalStorageItemDecoded] retrieves the same array set with [setLocalStorageItemEncoded]', () => {
    const testData = [1, 2, 'string', { object: 2 }]

    browserUtils.setLocalStorageItemEncoded('testArray', testData)
    expect(browserUtils.getLocalStorageItemDecoded('testArray')).toEqual(testData)

    localStorage.removeItem('testArray')
  })

  it('[getLocalStorageItemDecoded] retrieves the same number set with [setLocalStorageItemEncoded]', () => {
    const testNumber = 40

    browserUtils.setLocalStorageItemEncoded('testNumber', testNumber)
    expect(browserUtils.getLocalStorageItemDecoded('testNumber')).toEqual(testNumber)

    localStorage.removeItem('testNumber')
  })

  it('[getLocalStorageItemDecoded] retrieves the same object set with [setLocalStorageItemEncoded]', () => {
    const testData = { data: 2 }

    browserUtils.setLocalStorageItemEncoded('testObject', testData)
    expect(browserUtils.getLocalStorageItemDecoded('testObject')).toEqual(testData)

    localStorage.removeItem('testObject')
  })

  it('[setDomainCookieEncoded] sets a domain cookie', () => {
    browserUtils.setDomainCookieEncoded('$$testCookie', 'testData')

    expect(document.cookie).toBeTruthy()
    expect(document.cookie).toContain('$$testCookie')
    expect(document.cookie).toContain(encodingUtils.encodeBase64('testData'))

    browserUtils.setDomainCookieEncoded('$$testCookie', '', { expireDays: -2 })
  })

  it('[getDomainCookieDecoded] gets a cookie', () => {
    const cookieName = 'testCookie'
    const cookieData = 'cookieData'

    browserUtils.setCookie({
      name: cookieName,
      value: encodingUtils.encodeBase64(cookieData),
      expireDays: 1,
      domain: locationUtils.getDomainName()
    })
    expect(browserUtils.getDomainCookieDecoded(cookieName)).toEqual(cookieData)

    browserUtils.setDomainCookieEncoded('testCookie', '', { expireDays: -2 })
  })

  it('[setDomainCookieEncoded] sets a cookie with the specified expiration date', () => {
    const validCookie = '$$validCookie'
    const invalidCookie = '$$invalidCookie'
    const cookieData = 'cookieData'

    browserUtils.setDomainCookieEncoded(validCookie, cookieData)
    browserUtils.setDomainCookieEncoded(invalidCookie, cookieData, { expireDays: -2 })

    expect(browserUtils.getDomainCookieDecoded(validCookie)).toEqual(cookieData)
    expect(browserUtils.getDomainCookieDecoded(invalidCookie)).toBeFalsy()

    browserUtils.setDomainCookieEncoded(validCookie, '', { expireDays: -2 })
  })

  it('[setDomainCookieEncoded] sets a stringified cookie', () => {
    const cookieName = 'setDomTestCookie'
    const cookieValue = { data: 'testValue' }

    browserUtils.setDomainCookieEncoded(cookieName, cookieValue)
    expect(browserUtils.getCookie(cookieName)).toEqual(encodingUtils.encodeBase64(cookieValue))

    browserUtils.setDomainCookieEncoded(cookieName, '', { expireDays: -2 })
  })

  it('[getDomainCookieDecoded] gets a parsed cookie', () => {
    const cookieName = 'getDomTestCookie'
    const cookieValue = { data: 'testValue' }

    browserUtils.setCookie({
      name: cookieName,
      value: encodingUtils.encodeBase64(cookieValue),
      expireDays: 1,
      domain: locationUtils.getDomainName()
    })

    expect(browserUtils.getDomainCookieDecoded(cookieName)).toEqual(cookieValue)

    browserUtils.setDomainCookieEncoded(cookieName, '', { expireDays: -2 })
  })

  it('[setCookie] sets the cookie expired if the value is null or undefined', () => {
    browserUtils.setCookie({
      name: 'deleteTest',
      value: null,
      expireDays: 1,
      domain: locationUtils.getDomainName()
    })

    expect(browserUtils.getDomainCookieDecoded('deleteTest')).toBeFalsy()
  })

  it('[logError] does not log console errors in a testing environment', () => {
    const spyLogger = spyOn(console, 'error').and.returnValue(false)

    browserUtils.logError('err')
    expect(spyLogger).not.toHaveBeenCalled()

    window.suppressLogger = false
    browserUtils.logError('err')
    expect(spyLogger).toHaveBeenCalled()

    window.suppressLogger = true
  })
})
