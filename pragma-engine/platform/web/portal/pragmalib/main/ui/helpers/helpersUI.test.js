import helpersUI from './helpers'

describe('Helpers UI', () => {
  it('[filterTableColumns] filters table columns list, excluding columns within a given list', () => {
    const testData = [
      { dataIndex: 'itemName' },
      { dataIndex: 'catalogId' },
      { dataIndex: 'instanceId' },
      { dataIndex: 'ext' },
      { dataIndex: 'removeItem' }
    ]

    const filteredData = helpersUI.filterTableColumns(testData, ['itemName', 'removeItem'])
    expect(filteredData).toEqual([testData[1], testData[2], testData[3]])
  })

  it('[addPropToComponent] adds a new prop to an existing component', () => {
    const testComponent = {}
    const noPropsTestComponent = { props: {} }
    const propsTestComponent = { props: { readOnly: Boolean } }

    helpersUI.addPropToComponent(testComponent, 'isTest', Boolean)
    expect(Object.keys(testComponent.props)).toEqual(['isTest'])
    expect(testComponent.props.isTest).toBeTruthy()
    expect(typeof testComponent.props.isTest).toEqual('function')

    helpersUI.addPropToComponent(noPropsTestComponent, 'testName', String)
    expect(Object.keys(noPropsTestComponent.props)).toEqual(['testName'])
    expect(noPropsTestComponent.props.testName).toBeTruthy()
    expect(typeof noPropsTestComponent.props.testName).toEqual('function')

    helpersUI.addPropToComponent(propsTestComponent, 'isTest', Boolean)
    expect(Object.keys(propsTestComponent.props)).toEqual(['readOnly', 'isTest'])
    expect(propsTestComponent.props.readOnly).toBeTruthy()
    expect(propsTestComponent.props.isTest).toBeTruthy()
    expect(typeof propsTestComponent.props.readOnly).toEqual('function')
    expect(typeof propsTestComponent.props.isTest).toEqual('function')
  })
})
