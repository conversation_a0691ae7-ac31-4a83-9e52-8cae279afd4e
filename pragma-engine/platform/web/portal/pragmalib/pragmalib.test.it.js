// import paths are adjusted to the .build file structure!
import { pragmalibSha as currentSha } from '../generated-files/exports.js'
import savedSha from './pragmalib.sha.js'

xdescribe('Pragma Library', () => {
  it('Verifies pragma library integrity', () => {
    const isGood = currentSha === savedSha
    expect(isGood)
      .withContext(
        `\nPragma Library code has been changed. Don't forget release/integration notes!\nNew calculated SHA: ${currentSha}\n\nUpdate new sha by hand in pragmalib.sha.js or validate changes automatically with 'make portal-validate-pragmalib' or 'pragma portal update-sha'\n\n`
      )
      .toBeTrue()
  })
})
