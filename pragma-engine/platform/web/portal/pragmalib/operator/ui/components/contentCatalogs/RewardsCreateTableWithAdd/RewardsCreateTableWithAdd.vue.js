import { utils } from '../../../../../main/utils.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import PopoverWithTable from '../PopoverWithTable/PopoverWithTable.vue.js'
import { useRoute } from 'vue-router';
import * as Vue from 'vue';

const RewardsCreateTableWithAdd = {
  name: 'RewardsCreateTableWithAdd',
  components: { Icon, PopoverWithTable },
  emits: ['addEntry', 'saveEntry', 'deleteEntry'],
  props: {
    addPrefix: String,
    columns: Object,
    items: Array,
    allRewardItemsOptions: Array,
    addButtonText: {
      type: String,
      default: 'Add Item'
    },
    itemPlaceholder: {
      type: String,
      default: 'Select Item'
    },
    idKey: String,
    skipUniqueIds: Boolean,
    skipWeightValidation: Boolean,
    isCreateMode: Boolean
  },
  setup(props, { emit }) {
    const allRewardItemsIds = props.allRewardItemsOptions.map(item => item.value.id)

    const items = Vue.computed(() =>
      props.items.map(item => {
        const fullItem = props.allRewardItemsOptions.find(it => it.value.id === item[props.idKey])
        return { ...fullItem?.value, key: item.key || utils.getRandomString() }
      })
    )

    const editableData = Vue.reactive({})
    const errorMessages = Vue.reactive({})

    const addItem = () => {
      const keyToAdd = props.addPrefix + utils.getRandomString()
      const itemToAdd = { key: keyToAdd }
      editableData[keyToAdd] = itemToAdd

      emit('addEntry', itemToAdd)
    }

    const validateRow = itemToSave => {
      errorMessages[itemToSave.key] = {}

      if (!allRewardItemsIds.find(item => item == itemToSave[props.idKey]))
        errorMessages[itemToSave.key][props.idKey] = 'The id does not exist'

      if (
        !props.skipUniqueIds &&
        items.value.find(
          item => item[props.idKey] == itemToSave[props.idKey] && item.key !== itemToSave.key
        )
      )
        errorMessages[itemToSave.key][props.idKey] = 'The id already exists'

      if (!props.skipWeightValidation && !itemToSave.weight)
        errorMessages[itemToSave.key].weight = 'Required'

      if (Object.keys(errorMessages[itemToSave.key]).length == 0)
        delete errorMessages[itemToSave.key]
    }

    const save = record => {
      const itemToSave = {
        key: editableData[record.key].key,
        [props.idKey]: editableData[record.key][props.idKey]
      }

      validateRow(itemToSave)

      if (errorMessages[itemToSave.key]) return false
      delete editableData[record.key]

      emit('saveEntry', itemToSave)
    }

    const edit = record => {
      editableData[record.key] = record
    }

    const deleteItem = key => {
      emit('deleteEntry', key)
    }

    const cancel = record => {
      errorMessages[record.key] = {}
      if (record.key.includes(props.addPrefix)) emit('deleteEntry', record)
      delete editableData[record.key]
    }

    const onAutoCompleteSelect = (value, option, record) => {
      delete errorMessages[record[props.idKey]]
    }

    const rewardSlotIds = props.allRewardItemsOptions?.map(r => r.value.id).sort()
    const autocompleteRewardSlotIds = rewardSlotIds?.map(r => ({ value: r }))
    const route = useRoute()
    const rewardTableId = route.params.rewardTableId

    return {
      items,
      editableData,
      addItem,
      save,
      edit,
      deleteItem,
      cancel,
      onAutoCompleteSelect,
      autocompleteRewardSlotIds,
      slotEntriesColumns,
      errorMessages,
      isCreateMode: Boolean,
      rewardTableId
    }
  },
  template: `
    <div class="rewards-editable-table">
      <a-table
        :locale="{emptyText: 'You don\\'t have any reward slots yet'}"
        :columns="$props.columns"
        :data-source="items"
        size="small"
        :pagination="false"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex !== 'edit'">
            <div class="table-cell-inner">
              <template v-if="!editableData[record.key] || column.dataIndex != $props.idKey">
                <template v-if="column.dataIndex === 'slotEntries'">
                  <PopoverWithTable
                    :records="record.slotEntries || []"
                    :columns="slotEntriesColumns"
                    :previewFields="{main: 'rewardBagId'}"
                  />
                </template>
                <template v-else>
                  <router-link v-if="!$props.isCreateMode && column.dataIndex == 'rewardSlotId'" :to="{ name: 'EditRewardTableSlot', params: { rewardSlotId: record.id, rewardTableId: rewardTableId }}">{{ text }}</router-link>
                  <span v-else>{{ text }}</span>
                </template>
              </template>

              <a-form-item
                v-if="editableData[record.key] && column.dataIndex == $props.idKey"
                :help="(errorMessages[record.key] || {})[$props.idKey]"
                :validateStatus="(errorMessages[record.key] || {})[$props.idKey] ? 'error' : ''"
              >
                <a-auto-complete
                  v-model:value='editableData[record.key][$props.idKey]'
                  filterOption
                  :placeholder="$props.itemPlaceholder"
                  :dropdownStyle="{minWidth: '300px'}"
                  :options="autocompleteRewardSlotIds"
                  @select="(value, option) => onAutoCompleteSelect(value,option,record)"
                />
              </a-form-item>
            </div>
          </template>
          <template v-else>
            <div v-if="editableData[record.key]" class="rewards-editable-table-actions">
              <a-typography-link @click="save(record)">Save</a-typography-link>
              <a-typography-link @click="deleteItem(record)">Remove</a-typography-link>
              <a-typography-link @click="cancel(record)">Cancel</a-typography-link>
            </div>
            <div v-else class="rewards-editable-table-actions">
              <a @click="edit(record)">Edit</a>
            </div>
          </template>
        </template>
      </a-table>
      <a-button type="dashed" @click="addItem" block>
        <Icon name="plus" align="-2"/>
        {{$props.addButtonText}}
      </a-button>
    </div>
  `
}

const slotEntriesColumns = [
  {
    title: 'Reward Bag Id',
    dataIndex: 'rewardBagId',
    key: 'rewardBagId'
  },
  {
    title: 'Weight',
    dataIndex: 'weight',
    key: 'weight'
  }
]

export default RewardsCreateTableWithAdd
