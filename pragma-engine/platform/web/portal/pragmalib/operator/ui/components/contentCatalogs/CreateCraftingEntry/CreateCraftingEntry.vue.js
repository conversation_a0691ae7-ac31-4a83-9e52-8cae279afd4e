import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import TableWithAdd from '../../../../../main/ui/components/TableWithAdd/TableWithAdd.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import PageSection from '../../../../../main/ui/components/PageSection/PageSection.vue.js'
import JsonEditor from '../../../../../main/ui/components/JsonEditor/JsonEditor.vue.js'
import JsonTreeView from '../../../../../main/ui/components/JsonTreeViewer/JsonTreeViewer.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import { swappable } from '../../../../../main/ui/helpers/swappableComponent/swappable.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { utils } from '../../../../../main/utils.js'
import JsonViewTabSelector from '../../../../../main/ui/components/JsonViewTabSelector/JsonViewTabSelector.vue.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import { useRouter, useRoute } from 'vue-router';
import * as Vue from 'vue';

const ITEM_TO_ADD_KEY_PREFIX = '__to-add__'

const CreateCraftingEntry = {
  name: 'CreateCraftingEntry',
  components: {
    Icon,
    TableWithAdd,
    Table,
    PageSection,
    JsonEditor,
    JsonTreeView,
    JsonViewTabSelector,
    InfoParagraph
  },
  props: {
    entries: Object,
    stackableEntries: Array,
    instancedEntries: Array,
    readOnly: Boolean
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const currentEntryId = route.params?.entryId
    const isCreate = !currentEntryId

    const requirementsRichEditor = swappable('Content.CraftingEntry.RequirementsRich', {
      default: null,
      emits: ['onUpdate'],
      data: true,
      context: ['readOnly']
    })
    const extRichEditor = swappable('Content.CraftingEntry.ExtRich', {
      default: null,
      emits: ['onUpdate'],
      data: true,
      context: ['readOnly']
    })

    const reqView = Vue.ref(requirementsRichEditor ? 'rich' : 'tree')
    const extView = Vue.ref(extRichEditor ? 'rich' : 'tree')

    const allItemsInCatalog = [].concat(props.instancedEntries).concat(props.stackableEntries)

    const parsedEntries = props.entries
    const sourceFiles = []

    Object.keys(parsedEntries).forEach(file => {
      sourceFiles.push({ value: file, label: file })
    })

    const sourceFile = Object.keys(parsedEntries)?.find(file =>
      parsedEntries[file]?.find(entry => entry.id === currentEntryId)
    )
    const currentObject =
      parsedEntries[sourceFile]?.find(entry => entry.id === currentEntryId) || {}
    const updatedObject = {}

    if (currentObject.stackableCostByCatalogId)
      updatedObject.stackableCostByCatalogId = Object.keys(
        currentObject.stackableCostByCatalogId
      ).map(catalogId => ({
        name: helpersAPI.getCatalogNameByCatalogId(allItemsInCatalog, catalogId),
        catalogId,
        cost: currentObject.stackableCostByCatalogId[catalogId].cost,
        key: utils.getRandomString()
      }))

    if (currentObject.tags) updatedObject.tags = currentObject.tags
    if (currentObject.requirements) updatedObject.requirements = currentObject.requirements
    if (currentObject.ext) updatedObject.ext = currentObject.ext

    const entryIdErrorMessage = Vue.ref('')
    const errorMessages = Vue.reactive({})
    const itemsCost = Vue.ref(updatedObject.stackableCostByCatalogId || [])
    const formState = Vue.reactive({
      id: currentEntryId || '',
      tags: updatedObject.tags || [],
      requirements: utils.jsonStringify(updatedObject.requirements || {}, null, 2),
      ext: utils.jsonStringify(updatedObject.ext || {}, null, 2),
      source: sourceFile || sourceFiles[0].value
    })

    const formatItems = (items, valueColumn) => {
      return items.map(item => ({
        ...item,
        value: item[valueColumn],
        label: `${item.name} (${item.catalogId})`
      }))
    }

    const allCatalogItemsOptions = valueColumn => [
      {
        label: 'Stackable',
        options: formatItems(props.stackableEntries, valueColumn)
      }
    ]

    const reqUpdate = reqContent => {
      formState.requirements = reqContent
    }
    const extUpdate = extContent => {
      formState.ext = extContent
    }

    const onSubmitCreateEntry = () => {
      helpersAPI.resetErrorMessages(errorMessages)
      entryIdErrorMessage.value = ''

      if (formState.id.toString().trim().length === 0) {
        entryIdErrorMessage.value = 'Entry Id is required'
        return false
      }
      if (isCreate)
        Object.values(parsedEntries).forEach(items => {
          items.forEach(item => {
            if (formState.id.toString().trim() === item.id.toString().trim()) {
              entryIdErrorMessage.value = 'Id is already in use'
            }
          })
        })
      if (entryIdErrorMessage.value) return false
      if (Object.keys(errorMessages).length > 0) {
        toast.error('The form has errors')
        return false
      }

      const stackableCostByCatalogId = {}
      if (itemsCost.value.find(item => item.key.startsWith(ITEM_TO_ADD_KEY_PREFIX))) {
        toast.error('You have unsaved changes')
        return false
      }
      itemsCost.value.forEach(item => {
        if (!item.key.startsWith(ITEM_TO_ADD_KEY_PREFIX))
          stackableCostByCatalogId[item.catalogId] = { cost: item.cost }
      })

      let entryReq = {}
      try {
        entryReq = utils.jsonParse(formState.requirements)
      } catch (error) {
        console.log('Could not parse the requirements object')
        entryReq = {}
      }
      let entryExt = {}
      try {
        entryExt = utils.jsonParse(formState.ext)
      } catch (error) {
        console.log('Could not parse the ext object')
        entryExt = {}
      }

      const entryToCreate = {
        id: formState.id,
        stackableCostByCatalogId,
        tags: [].concat(formState.tags),
        requirements: entryReq,
        ext: entryExt
      }

      isCreate
        ? parsedEntries[formState.source].push(entryToCreate)
        : (parsedEntries[formState.source] = utils.replaceInObjArray(
            parsedEntries[formState.source],
            'id',
            entryToCreate.id,
            entryToCreate
          ))
      const srcJson = utils.jsonStringify(parsedEntries[formState.source])
      const payload = {
        contentType: 'CraftingEntries',
        filename: formState.source,
        srcJson
      }
      updateContentCatalog(payload)

      helpersAPI.goBackToPage({ name: 'CraftingEntriesEditor', router })
    }

    const addEntry = (entry, parentTitle) => {
      const itemsArray = parentTitle === 'Cost' ? itemsCost.value : receivedQuantity.value
      itemsArray.push(entry)
    }

    const saveEntry = (entry, parentTitle) => {
      const itemsArray = parentTitle === 'Cost' ? itemsCost.value : receivedQuantity.value
      const indexOfEntry = itemsArray.findIndex(item => item.key === entry.key)
      if (entry.key.includes(ITEM_TO_ADD_KEY_PREFIX)) entry.key = utils.getRandomString()

      if (indexOfEntry >= 0) itemsArray[indexOfEntry] = { ...entry }
      else itemsArray.push({ ...entry })
    }

    const deleteEntry = (key, parentTitle) => {
      const itemsArray = parentTitle === 'Cost' ? itemsCost.value : receivedQuantity.value
      const indexOfEntry = itemsArray.findIndex(item => item.key === key)
      if (indexOfEntry >= 0) itemsArray.splice(indexOfEntry, 1)
    }

    const updateReqTreeData = updatedContent => {
      formState.requirements = utils.convertObjectToFormattedString(updatedContent.json)
    }
    const updateExtTreeData = updatedContent => {
      formState.ext = utils.convertObjectToFormattedString(updatedContent.json)
    }

    const costFilter = (inputValue, option) => {
      if (['Stackable', 'Instanced'].includes(option.label)) return false

      const searchableString = utils.jsonStringify(Object.values(option)).toLowerCase()
      return searchableString.includes(inputValue.toLowerCase())
    }

    useSubscribe('submitCreateEntry', onSubmitCreateEntry)

    const readOnlyColumnsCost = columnsCost.filter(column => column.dataIndex !== 'edit')

    const tagsLabel = Vue.ref('')
    Vue.watchEffect(() => {
      tagsLabel.value = `Tags (${formState.tags.length})`
    })

    const formStateExtJson = Vue.reactive({})
    const formStateReqJson = Vue.reactive({})

    const tryParse = (rawData, target) => {
      try {
        const parsed = utils.jsonParse(rawData)
        Object.keys(parsed).forEach(key => (target[key] = parsed[key]))
      } catch (error) {}
    }
    tryParse(formState.ext, formStateExtJson)
    tryParse(formState.requirements, formStateReqJson)

    Vue.watch(
      () => formState.ext,
      () => {
        tryParse(formState.ext, formStateExtJson)
      }
    )

    Vue.watch(
      () => formState.requirements,
      () => {
        tryParse(formState.requirements, formStateReqJson)
      }
    )

    const onExtUpdate = data => {
      formState.ext = utils.convertObjectToFormattedString(data)
    }
    const onReqUpdate = data => {
      formState.requirements = utils.convertObjectToFormattedString(data)
    }

    return {
      formState,
      itemsCost,
      columnsCost,
      readOnlyColumnsCost,
      allCatalogItemsOptions,
      extUpdate,
      reqUpdate,
      errorMessages,
      entryIdErrorMessage,
      currentEntryId,
      sourceFiles,
      addEntry,
      saveEntry,
      deleteEntry,
      costFilter,
      JsonTreeView,
      reqView,
      extView,
      requirementsRichEditor,
      extRichEditor,
      formStateExtJson,
      onExtUpdate,
      formStateReqJson,
      onReqUpdate,
      updateReqTreeData,
      updateExtTreeData,
      tagsLabel
    }
  },
  template: `
    <a-form layout='vertical' :model='formState' class="create-entry">
      <a-row :gutter="32">
        <a-col :span="12">
          <a-form-item
            label='Entry Id'
            name='id'
            :help="entryIdErrorMessage"
            :validateStatus="entryIdErrorMessage != '' ? 'error' : ''"
          >
            <a-input v-model:value='formState.id' :disabled="!!currentEntryId" />
            <InfoParagraph v-if="!$props.readOnly" infoText="Once the entry is created the Id cannot be changed"/>
          </a-form-item>
        </a-col>
      </a-row>

      <TableWithAdd
        v-if="!$props.readOnly"
        title="Cost"
        addPrefix="__to-add__"
        :columns="columnsCost"
        :items="itemsCost"
        :allCatalogItemsOptions="allCatalogItemsOptions"
        :errorMessages="errorMessages"
        @addEntry="addEntry"
        @saveEntry="saveEntry"
        @deleteEntry="deleteEntry"
      />
      <PageSection v-if="$props.readOnly" title="Cost" :count="itemsCost.length">
        <Table
          :columns="readOnlyColumnsCost"
          :data="itemsCost"
          :pagination="false"
          class="view-only-table"
        />
      </PageSection>

      <a-row :gutter="32">
        <a-col :span="24" :xxl="{ span: 12 }">
          <a-form-item
            name='tags'
            :label="tagsLabel"
            class="tags-title"
          >
            <a-select
              v-model:value="formState.tags"
              :disabled="$props.readOnly"
              :token-separators="[',']"
              mode="tags"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="32">
        <a-col :span="12">
          <a-row type="flex" justify="space-between" align="top">
            <a-form-item
              label='Requirements'
              name='requirements'
              :span="12"
              style="flex-direction: row; margin: 0 0 -2px"
            ></a-form-item>

            <JsonViewTabSelector
              :currentView='reqView'
              @onChange="(newView) => reqView = newView"
              :showRich='!!requirementsRichEditor'
              :span="12"
              size='small'
            />
          </a-row>
          <div v-if="reqView === 'rich'">
            <component
              :is="requirementsRichEditor"
              :data="formStateReqJson"
              :context="{readOnly: $props.readOnly}"
              @onUpdate='onReqUpdate'
            />
          </div>
          <div v-if="reqView === 'raw'">
            <JsonEditor
              :rawData='formState.requirements'
              :readOnly='$props.readOnly'
              @onUpdate="reqUpdate"
            />
          </div>
          <div v-if="reqView === 'tree'" class="jse-theme-dark">
            <JsonTreeView
              :content="{text: formState.requirements}"
              :readOnly='$props.readOnly'
              :onChange="updateReqTreeData"
            ></JsonTreeView>
          </div>
        </a-col>

        <a-col :span="12">
          <a-row type="flex" justify="space-between" align="top">
            <a-form-item
              label='Ext'
              name='ext'
              :span="12"
              style="flex-direction: row; margin: 0 0 -2px"
            ></a-form-item>
            <JsonViewTabSelector
              :currentView='extView'
              @onChange="(newView) => extView = newView"
              :showRich='!!extRichEditor'
              :span="12"
              size='small'
            />
          </a-row>
          <div v-if="extView === 'rich'">
            <component
              :is="extRichEditor"
              :data="formStateExtJson"
              :context="{readOnly: $props.readOnly}"
              @onUpdate='onExtUpdate'
            />
          </div>
          <div v-if="extView === 'raw'">
            <JsonEditor
              :rawData='formState.ext'
              :readOnly='$props.readOnly'
              @onUpdate="extUpdate"
            />
          </div>
          <div v-if="extView === 'tree'" class="jse-theme-dark">
            <JsonTreeView
              :content="{text: formState.ext}"
              :readOnly='$props.readOnly'
              :onChange="updateExtTreeData"
            ></JsonTreeView>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="32">
        <a-col :span="12">
          <a-form-item
            label="Source"
            name="source"
          >
            <a-select
              :disabled="!!currentEntryId"
              v-model:value="formState.source"
              :options="sourceFiles"
              :defaultValue="sourceFiles[0]"
              notFoundContent=""
            >
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  `
}

const columnsCost = [
  { title: 'Name', dataIndex: 'name', key: 'name' },
  { title: 'Catalog Id', dataIndex: 'catalogId', key: 'catalogId' },
  { title: 'Cost', dataIndex: 'cost', key: 'cost' },
  { dataIndex: 'edit', key: 'edit' }
]

export default CreateCraftingEntry
