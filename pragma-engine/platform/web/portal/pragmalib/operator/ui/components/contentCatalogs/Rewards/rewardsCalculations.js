// This is not a config. It's written here for better code readability
const ONE_HUNDRED_PERCENT = 100

export const calculateRewardsChances = (rewardSlots, rewardBags, rewardTable) => {
  const chancesBySlot = calculateRewardChancesBySlot(rewardTable, rewardSlots, rewardBags)

  // If a slot does not have a reward, that means for that slot the probability of that reward is 0.
  // To easier calculate the probability of each reward across all slots,
  // we need its chance defined for each slot, even if it is 0
  const chancesBySlotComplete = completeSlotsWithMissingRewards(chancesBySlot)

  // To combine the reward probabilities from each slot,
  // we calculate the probability that a reward is NOT selected, then invert it on the next step.
  // We do it this way because the math is easier.
  const complementaryChances = getComplementaryChances(chancesBySlotComplete)

  return pragma.utils.mapObject(complementaryChances, chance => ONE_HUNDRED_PERCENT - chance)
}

const calculateRewardChancesBySlot = (rewardTable, rewardSlots, rewardBags) => {
  return rewardTable.rewardSlotIds.reduce((accSlots, slotId) => {
    const currentSlot = rewardSlots.find(slot => slot.id === slotId)
    const rewardIds = getAllUniqueRewardsInSlot(currentSlot, rewardBags)

    const rewardsChances = rewardIds.reduce((accRewards, rewardId) => {
      return {
        ...accRewards,
        [rewardId]: calculateSingleRewardChance(rewardId, currentSlot, rewardBags)
      }
    }, {})

    return { ...accSlots, [currentSlot.id]: rewardsChances }
  }, {})
}

const completeSlotsWithMissingRewards = chancesBySlot => {
  const allRewardIds = getUniqueRewardsAcrossAllSlots(chancesBySlot)

  const rewardsZeroed = allRewardIds.reduce(
    (acc, rewardId) => ({
      ...acc,
      [rewardId]: 0
    }),
    {}
  )

  const slotsFilled = pragma.utils.mapObject(chancesBySlot, slotChances => ({
    ...rewardsZeroed,
    ...slotChances
  }))

  return slotsFilled
}

const getComplementaryChances = chancesBySlot => {
  const numberOfSlots = Object.keys(chancesBySlot).length

  const totalChances = Math.pow(ONE_HUNDRED_PERCENT, numberOfSlots)

  // total possibilities that a speficic reward is NOT selected
  const rewardsComplementary = Object.values(chancesBySlot).reduce((accRewards, slotChances) => {
    for (const [rewardId, rewardChance] of Object.entries(slotChances)) {
      accRewards[rewardId] ??= 1 // because the first time this is undefined
      accRewards[rewardId] *= ONE_HUNDRED_PERCENT - rewardChance
    }
    return accRewards
  }, {})

  return pragma.utils.mapObject(
    rewardsComplementary,
    rewardChance => (rewardChance / totalChances) * 100
  )
}

const getAllUniqueRewardsInSlot = (currentSlot, rewardBags) => {
  const slotBagIds = currentSlot.slotEntries.map(slotEntry => slotEntry.rewardBagId)

  return pragma.utils.getUniqueValues(
    slotBagIds
      .map(slotBagId => {
        const theBag = rewardBags.find(bag => bag.id == slotBagId)

        return theBag.rewards.map(rew => rew.rewardId)
      })
      .flat()
  )
}

const getUniqueRewardsAcrossAllSlots = chancesBySlot =>
  pragma.utils.getUniqueValues(
    Object.values(chancesBySlot)
      .map(chance => Object.keys(chance))
      .flat()
  )

// "reward weight" = sum of (weight bag i * weight reward j) - will only calculate against the specified reward
// "total weight" = sum of (weight bag i * weight item j) - will iterate though all rewards in the bag
const calculateSingleRewardChance = (rewardId, slot, rewardBags) => {
  const slotBags = slot.slotEntries
  const bagsTotalWeight = calculateTotalBagsWeight(slotBags, rewardBags)
  const rewardWeight = calculateTotalRewardWeight(slotBags, rewardBags, rewardId)

  const rewardProbability = (rewardWeight / bagsTotalWeight) * 100

  // we have to consider chance of roll (no op probability)
  const probabilityAfterNoOpPercentage = rewardProbability - rewardProbability * slot.noOpPercentage

  return probabilityAfterNoOpPercentage
}

const calculateTotalRewardWeight = (slotBags, rewardBags, rewardId) => {
  return slotBags.reduce((acc, { weight: bagWeight, rewardBagId: bagId }) => {
    const bag = rewardBags.find(b => b.id === bagId)
    const weightOfRewardInBag = bag.rewards.find(r => r.rewardId === rewardId)?.weight || 0

    const weightOfRewardInBagAdjustedToSlot = bagWeight * weightOfRewardInBag

    return acc + weightOfRewardInBagAdjustedToSlot
  }, 0)
}

const calculateTotalBagsWeight = (slotBags, rewardBags) => {
  return slotBags.reduce((acc, { weight: bagWeight, rewardBagId: bagId }) => {
    const bag = rewardBags.find(b => b.id === bagId)

    const totalRewardsWeightInBag = bag.rewards.reduce(
      (acc, { weight: rewardWeight }) => acc + bagWeight * rewardWeight,
      0
    )

    return acc + totalRewardsWeightInBag
  }, 0)
}
