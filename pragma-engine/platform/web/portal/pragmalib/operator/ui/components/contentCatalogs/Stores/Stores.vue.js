import { utils } from '../../../../../main/utils.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import hooks from '../../../../../main/ui/hooks.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import SimpleCodeEditor from '../../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import { useRouter } from 'vue-router';

export const ENTRIES_TO_SHOW = 10

const Stores = {
  name: 'Stores',

  props: {
    entries: Object,
    readOnly: Boolean,
    hideSource: Boolean
  },

  components: { Table, CatalogViewer, Icon, SimpleCodeEditor },

  setup(props) {
    const router = useRouter()
    const { getSearch } = hooks.useSearch(router)
    const search = getSearch()

    const sourceFiles = Object.keys(props.entries)

    const tableColumns = props.hideSource
      ? utils.removeFromArray(storesColumns, 'key', 'source')
      : storesColumns

    const filteredEntries = utils.filterItemsByText(
      formatEntries(props.entries),
      search,
      tableColumns.map(c => c.dataIndex)
    )

    const updateStoreRaw = async (rawData, rawSource) => {
      const payload = {
        contentType: 'Stores',
        filename: rawSource,
        srcJson: rawData
      }

      updateContentCatalog(payload)
    }

    const deleteStore = async (storeId, file, close) => {
      const srcJson = utils.jsonStringify(utils.removeFromArray(props.entries[file], 'id', storeId))

      const payload = {
        contentType: 'Stores',
        filename: file,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    return {
      tableColumns,
      filteredEntries,
      utils,
      ENTRIES_TO_SHOW,
      confirm,
      sourceFiles,
      updateStoreRaw,
      deleteStore
    }
  },

  template: `
    <div class="stores-wrap">
      <CatalogViewer
        :rawData="$props.entries"
        :rawSources="sourceFiles"
        :allowRawEdit="!$props.readOnly"
        :rawEditOnSave="updateStoreRaw"
      >
        <template #richView>
          <Table
            emptyText="No entries have been found"
            :columns="tableColumns"
            :data="filteredEntries"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'name'">
                <router-link :to="{ name: $props.readOnly ? 'ViewStore' : 'ViewStoreEditor', params: { storeId: record.id }}">{{ text }}</router-link>
              </template>

              <template v-if="column.dataIndex === 'storeEntries'">
                <a-popover v-if="record.storeEntries?.length > 0">
                  <template #content>
                    <a-typography-title :level="5" class="store-entries-title">Id</a-typography-title>
                    <ul class="store-entries-list">
                      <li v-for="storeEntry in record.storeEntries.slice(0,ENTRIES_TO_SHOW)">{{storeEntry.id}}</li>
                      <li v-if="record.storeEntries.length > ENTRIES_TO_SHOW" class="entries-other">
                        + {{record.storeEntries.length - ENTRIES_TO_SHOW}} other{{(record.storeEntries.length - ENTRIES_TO_SHOW) > 1 ? 's' : ''}}
                      </li>
                    </ul>
                  </template>
                  {{record.storeEntries[0].id}} <span v-if="record.storeEntries.length - 1 > 0" class="entries-more">+ {{record.storeEntries.length - 1}} other{{(record.storeEntries.length - 1) > 1 ? 's' : ''}}</span>
                </a-popover>
              </template>

              <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
                <a-button size='small' ghost danger class='hide actionBtn'
                  @click='confirm.danger({
                    title: "Confirm Store Deletion",
                    content: "Are you sure you want to delete " + record.name + "?",
                    okText: "Delete",
                    onOk: close => { deleteStore(record.id, record.source, close) }
                  })'
                >
                  <Icon name='close' size="10" /><span>Delete</span>
                </a-button>
              </template>
            </template>
          </Table>
        </template>
      </CatalogViewer>
    </div>
  `
}

const storesColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    defaultSortOrder: 'ascend',
    sorter: true
  },
  {
    title: 'Id',
    dataIndex: 'id',
    key: 'id',
    sorter: true
  },
  {
    title: 'Store Entries',
    dataIndex: 'storeEntries',
    key: 'storeEntries'
  },
  {
    title: 'Source',
    dataIndex: 'source',
    key: 'source'
  },
  {
    dataIndex: 'delete',
    align: 'right'
  }
]

const formatEntries = entries => {
  return helpersAPI.concatenateContentCatalogSources(entries)
}

export default Stores
