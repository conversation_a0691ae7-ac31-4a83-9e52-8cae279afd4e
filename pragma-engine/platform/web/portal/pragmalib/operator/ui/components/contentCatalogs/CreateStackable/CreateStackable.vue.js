import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import { utils } from '../../../../../main/utils.js'
import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import SimpleCodeEditor from '../../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import * as Vue from 'vue';

const CreateStackable = {
  name: 'CreateStackable',
  components: { Icon, SimpleCodeEditor, InfoParagraph },
  props: { sourceFiles: Array, entries: Object },
  emits: ['create'],
  setup(props) {
    const newItemVisible = Vue.ref(false)

    const sourceFiles = []
    props.sourceFiles.forEach(file => sourceFiles.push({ value: file, label: file }))

    useSubscribe('createItem', () => {
      newItemVisible.value = true
    })

    const formState = Vue.reactive({
      name: '',
      catalogId: '',
      removeIfNone: false,
      tags: [],
      ext: '',
      source: sourceFiles[0].value
    })
    const errorMessages = Vue.reactive({})

    const notUniqueId = id => {
      const notUnique = Vue.ref(false)
      Object.values(props.entries).forEach(items => {
        items.forEach(item => {
          if (item.catalogId === id) notUnique.value = true
        })
      })
      return notUnique.value
    }

    const onCancel = () => {
      newItemVisible.value = false
      helpersAPI.resetErrorMessages(errorMessages)
    }
    const onSubmit = async formState => {
      helpersAPI.resetErrorMessages(errorMessages)

      if (!formState.name) errorMessages.name = 'Item name is required.'
      if (notUniqueId(formState.catalogId)) errorMessages.catalogId = 'This Id is already in use'
      if (!formState.catalogId) errorMessages.catalogId = 'Catalog Id is required'
      if (isNaN(formState.limit)) errorMessages.limit = 'Item limit must be a number'
      if (!formState.limit) errorMessages.limit = 'Item limit is required'
      if (!formState.source) errorMessages.source = 'Source file is required'
      const extParsed = Vue.ref({})
      if (formState.ext) {
        try {
          extParsed.value = utils.jsonParse(formState.ext)
        } catch (exception) {
          errorMessages.ext = 'Invalid JSON'
        }
      }

      if (Object.keys(errorMessages).length !== 0) return

      const fileContent = props.entries[formState.source]
      fileContent.push({
        catalogId: formState.catalogId,
        name: formState.name,
        limit: formState.limit,
        tags: formState.tags,
        removeIfNone: formState.removeIfNone,
        ext: extParsed.value
      })

      const srcJson = utils.jsonStringify(fileContent)

      const payload = {
        contentType: 'StackableSpecs',
        filename: formState.source,
        srcJson
      }

      updateContentCatalog(payload)
    }

    return {
      newItemVisible,
      onCancel,
      onSubmit,
      utils,
      formState,
      sourceFiles,
      errorMessages
    }
  },
  template: `
    <div>
      <a-drawer
        class="createStackable"
        @close="onCancel"
        :open="newItemVisible"
        :mask="false"
        :destroyOnClose="true"
      >
        <template #extra>
          <a-button secondary @click="onCancel">Cancel</a-button>
          <a-button type="primary" @click="onSubmit(formState)">Create</a-button>
        </template>

        <a-form :model="formState" layout="vertical" class="info-form">
          <a-form-item
            label="Name"
            name="name"
            class="required"
            :help="errorMessages.name"
            :validateStatus="errorMessages.name && 'error' || ''"
          >
            <a-input v-model:value='formState.name'/>
          </a-form-item>

          <a-form-item
            label="Catalog Id"
            name="catalogId"
            class="required"
            :help="errorMessages.catalogId"
            :validateStatus="errorMessages.catalogId && 'error' || ''"
          >
            <a-input v-model:value='formState.catalogId'/>
            <InfoParagraph infoText="Once the item has been created the Id cannot be changed"/>
          </a-form-item>

          <a-form-item
            label="Limit"
            name="limit"
            class="required"
            :help="errorMessages.limit"
            :validateStatus="errorMessages.limit && 'error' || ''"
          >
            <a-input v-model:value.number='formState.limit'/>
          </a-form-item>

          <a-form-item
            label="Tags"
            name="tags"
          >
            <a-select
              v-model:value="formState.tags"
              mode="tags"
              notFoundContent=""
            />
          </a-form-item>

          <a-form-item
            label="Remove If None"
            name="removeIfNone"
          >
          <a-radio-group v-model:value="formState.removeIfNone">
            <a-radio :value="true">True</a-radio>
            <a-radio :value="false">False</a-radio>
          </a-radio-group>
          </a-form-item>

          <a-form-item
            label="Ext"
            name="ext"
            :help="errorMessages.ext"
            :validateStatus="errorMessages.ext && 'error' || ''"
          >
            <SimpleCodeEditor
              :read_only="false"
              :hide_header="true"
              font_size="12px"
              style="width: 100%"
              :wrap_code="true"
              v-model="formState.ext"
              :languages="[['JSON', 'JSON']]"
            />
          </a-form-item>

          <a-form-item
            label="Source"
            name="source"
            class="required"
            :help="errorMessages.source"
            :validateStatus="errorMessages.source && 'error' || ''"
          >
            <a-select
              v-model:value="formState.source"
              :options="sourceFiles"
              :defaultValue="sourceFiles[0]"
              notFoundContent=""
            >
            </a-select>

          </a-form-item>

        </a-form>
      </a-drawer>
    </div>
  `
}

export default CreateStackable
