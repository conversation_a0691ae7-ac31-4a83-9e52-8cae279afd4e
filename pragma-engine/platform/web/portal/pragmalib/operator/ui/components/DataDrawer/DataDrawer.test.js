import { utils } from '../../../../main/utils.js'
import Drawer, { emptyValue, formatDrawerData } from './DataDrawer.vue.js'
import { hooks } from '../../../../main/ui/hooks.js'
import * as Vue from 'vue';
import * as VueTestUtils from '@vue/test-utils'

describe('Drawer', () => {
  it('formats drawer data based on data type', () => {
    const data = {
      id: '22asd2',
      booleanTrue: true,
      booleanFalse: false,
      numberString: '2300',
      number: 1200,
      object: { key: 'value' },
      arrayAsCode: [{ key: 'value' }],
      undefinedAsCode: undefined,
      array: ['material', 'iron'],
      emptyArray: [],
      emptyObject: {},
      emptyString: '',
      undefined: undefined
    }

    const dataFormated = formatDrawerData(data, Object.keys(data), {
      arrayAsCode: 'code',
      undefinedAsCode: 'code'
    })

    expect(dataFormated.id).toEqual({ format: 'string', value: data.id })
    expect(dataFormated.booleanTrue).toEqual({ format: 'boolean', value: data.booleanTrue })
    expect(dataFormated.booleanFalse).toEqual({ format: 'boolean', value: data.booleanFalse })
    expect(dataFormated.numberString).toEqual({ format: 'number', value: '2,300' })
    expect(dataFormated.number).toEqual({ format: 'number', value: '1,200' })
    expect(dataFormated.object).toEqual({
      format: 'code',
      value: utils.convertObjectToFormattedString(data.object)
    })
    expect(dataFormated.arrayAsCode).toEqual({
      format: 'code',
      value: utils.convertObjectToFormattedString(data.arrayAsCode)
    })
    expect(dataFormated.array).toEqual({ format: 'keywordList', value: data.array })

    expect(dataFormated.emptyArray).toEqual({ format: 'keywordList', value: emptyValue })
    expect(dataFormated.emptyObject).toEqual({ format: 'code', value: '-' })
    expect(dataFormated.emptyString).toEqual({ format: 'string', value: emptyValue })
    expect(dataFormated.undefined).toEqual({ format: undefined, value: emptyValue })
    expect(dataFormated.undefinedAsCode).toEqual({
      format: 'code',
      value: emptyValue
    })
  })

  it('includes labels for all drawerData', async () => {
    let exampleData = { neat: 'stuff', numbers: 2, requestedTimeMs: 123456 }
    let drawerKeysToLabels = { requestedTimeMs: 'Requested On' }
    const { drawerData, setDrawer } = hooks.useDrawer()
    let wrapper = VueTestUtils.mount(Drawer, { propsData: { drawerData, drawerKeysToLabels } })

    setDrawer(exampleData)
    await Vue.nextTick()

    let dataDrawer = wrapper.getComponent('.ant-drawer')
    let labelText = dataDrawer.findAll('label').map(el => el.text())

    expect(labelText).toContain('Neat')
    expect(labelText).toContain('Numbers')
    expect(labelText).toContain('Requested On')
  })

  it('hides data tabs when showViewSelector is false', async () => {
    let exampleData = { neat: 'stuff', numbers: 2, requestedTimeMs: 123456 }
    let drawerKeysToLabels = { requestedTimeMs: 'Requested On' }
    const { drawerData, setDrawer } = hooks.useDrawer()
    let wrapper = VueTestUtils.mount(Drawer, {
      propsData: { drawerData, drawerKeysToLabels, showViewSwitcher: false }
    })

    setDrawer(exampleData)
    await Vue.nextTick()

    let dataDrawer = wrapper.getComponent('.ant-drawer')
    let labelText = dataDrawer.findAll('label').map(el => el.text())

    expect(labelText.includes('Rich')).toBe(false)
    expect(labelText.includes('Tree')).toBe(false)
    expect(labelText.includes('Raw')).toBe(false)
  })
})
