import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import { utils } from '../../../../../main/utils.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import PageSection from '../../../../../main/ui/components/PageSection/PageSection.vue.js'
import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import PopoverWithTable from '../PopoverWithTable/PopoverWithTable.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import RewardsCreateTableWithAdd from '../RewardsCreateTableWithAdd/RewardsCreateTableWithAdd.vue.js'
import { calculateRewardsChances } from '../Rewards/rewardsCalculations.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import { useRoute, useRouter } from 'vue-router';
import * as Vue from 'vue';

const ADD_KEY_PREFIX = '__reward-slot-id-to-add__'

const CreateEditRewardTable = {
  name: 'CreateEditRewardTable',
  components: {
    Icon,
    RewardsCreateTableWithAdd,
    PageSection,
    PopoverWithTable,
    Table,
    InfoParagraph
  },
  props: {
    entries: Object,
    table: Object,
    rewards: Array,
    rewardSlots: Array,
    rewardBags: Array,
    readOnly: Boolean,
    isCreateMode: Boolean
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()

    const searchInput = ''

    const noRewardSlotsMessage = "You don't have any reward bag yet"
    const noSearchResultMessage = 'No Search Results'

    const rewardTableWithAddLocal = { emptyText: noRewardSlotsMessage }

    const sourceFiles = Object.keys(props.entries)

    const retrieveRewardSlotIds = () => {
      return (
        props.table?.rewardSlotIds.map(rs => {
          const rewardSlot = props.rewardSlots.find(rewardSlot => rewardSlot.id === rs)
          return {
            key: utils.getRandomString(),
            ...rewardSlot
          }
        }) || []
      )
    }

    const formState = Vue.reactive({
      name: props.table?.name || '',
      id: props.table?.id || '',
      rewardSlotIds: retrieveRewardSlotIds(),
      allRewardsSlotIds: retrieveRewardSlotIds(),
      guaranteedRewardIds: props.table?.guaranteedRewardIds || [],
      source: sourceFiles[0]
    })

    const searchRewardTableSlots = searchInput => {
      if (!searchInput) {
        rewardTableWithAddLocal.emptyText = noRewardSlotsMessage
        formState.rewardSlotIds = formState.allRewardsSlotIds
      } else {
        rewardTableWithAddLocal.emptyText = noSearchResultMessage
        formState.rewardSlotIds = formState.allRewardsSlotIds
        formState.rewardSlotIds = formState.rewardSlotIds.filter(
          rewardTableSlot =>
            rewardTableSlot.rewardSlotId?.toLowerCase().includes(searchInput.toLowerCase()) ||
            !rewardTableSlot.rewardSlotId
        )
      }
    }

    const filteredRewardViewData = Vue.computed(() => {
      let rewardViewSlots = props.rewardSlots.map(rewardSlot => {
        return {
          rewardSlotId: rewardSlot.id,
          chanceOfRoll: rewardSlot.chanceOfRoll,
          slotEntries: rewardSlot.slotEntries
        }
      })

      if (searchInput)
        rewardViewSlots = rewardViewSlots.filter(
          rewardSlot => rewardSlot.rewardSlotId?.includes(searchInput) || !rewardSlot.rewardSlotId
        )

      return rewardViewSlots
    })

    const errorMessages = Vue.reactive({})

    const notUniqueId = id => {
      let notUnique = false
      Object.values(props.entries).forEach(items => {
        items.forEach(item => {
          if (item.id === id) notUnique = true
        })
      })
      return notUnique
    }

    const onSubmitCreateEditRewardTable = () => {
      helpersAPI.resetErrorMessages(errorMessages)

      if (!formState.name) errorMessages.name = 'Table name is required.'
      if (!formState.id) errorMessages.id = 'Table id is required.'
      if (!props.table && notUniqueId(formState.id)) errorMessages.id = 'This id is already in use'
      if (!formState.source) errorMessages.source = 'Source file is required'

      if (formState.rewardSlotIds.find(entry => entry.key.startsWith(ADD_KEY_PREFIX))) {
        toast.error('You have unsaved changes')
        return
      }

      if (Object.keys(errorMessages).length !== 0) return

      const fileContent = props.entries[formState.source]

      const currentContentObject = fileContent.find(
        fileContentItem => fileContentItem.id === formState.id
      )

      const updateContentObject = {
        ...currentContentObject,
        name: formState.name,
        id: formState.id,
        rewardSlotIds: formState.rewardSlotIds.map(r => r.rewardSlotId),
        guaranteedRewardIds: formState.guaranteedRewardIds
      }

      if (currentContentObject) {
        fileContent.splice(fileContent.indexOf(currentContentObject), 1, updateContentObject)
      } else {
        fileContent.push(updateContentObject)
      }

      const srcJson = utils.jsonStringify(fileContent)

      const payload = {
        contentType: 'RewardTables',
        filename: formState.source,
        srcJson
      }

      updateContentCatalog(payload)
      helpersAPI.goBackToPage({ name: 'RewardTablesEditor', router })
    }

    useSubscribe('submitCreateEditRewardTable', onSubmitCreateEditRewardTable)

    const addEntry = entry => {
      formState.rewardSlotIds = [
        ...formState.rewardSlotIds,
        {
          ...entry,
          rewardSlotId: null
        }
      ]
    }

    const saveEntry = entry => {
      const index = formState.rewardSlotIds.findIndex(item => item.key === entry.key)
      if (index >= 0) {
        entry.key = utils.getRandomString()
        formState.rewardSlotIds[index] = entry
        formState.allRewardsSlotIds[index] = entry
      }
    }

    const deleteEntry = entry => {
      const indexOfEntry = formState.rewardSlotIds.findIndex(item => item.key === entry.key)
      if (indexOfEntry >= 0) {
        formState.rewardSlotIds.splice(indexOfEntry, 1)
      }
    }

    const rewardSlotsValues = props.rewardSlots?.map(r => ({ value: r })) || []

    const rewardIds = props.rewards.map(reward => {
      return reward.id
    })

    const rawData = Vue.computed(() => ({
      [formState.source]: {
        rewardSlotIds: formState.rewardSlotIds.map(rs => ({ rewardSlotId: rs.rewardSlotId }))
      }
    }))

    const calculatedRewards =
      props.table && calculateRewardsChances(props.rewardSlots, props.rewardBags, props.table)

    const rewardChances =
      calculatedRewards &&
      addGuaranteedRewardsToChances(
        props.table,
        props.rewards,
        buildRewardsForDisplay(calculatedRewards, props.rewards)
      )

    const rewardTableId = route.params.rewardTableId

    return {
      utils,
      formState,
      sourceFiles,
      errorMessages,
      addEntry,
      saveEntry,
      deleteEntry,
      ADD_KEY_PREFIX,
      rewardSlotsValues,
      rewardIds,
      rawData,
      searchInput,
      searchRewardTableSlots,
      filteredRewardViewData,
      rewardChancesColumns,
      slotEntriesColumns,
      rewardChances,
      rewardSlotsColumns,
      rewardSlotIdsColumnsWithEdit,
      GUARANTEED,
      rewardTableId
    }
  },
  template: `
    <div>
      <a-row :gutter="[32, 30]">
        <a-col :span="24">
          <PageSection
            title="Rewards Slots"
            :count="formState.allRewardsSlotIds.length"
          >
          <a-row :gutter="[32, 16]" :gap="24">
            <!-- TODO: As we discussed the requirements this search component will be hidden until it will be needed -->
            <!-- <a-col :span="24">
              <a-input-search
                allowClear
                class="searchTableInput"
                :defaultValue='searchInput'
                placeholder="Search In Rewards Slots"
                enter-button="Search"
                @search="searchRewardTableSlots"
              >
                <template #prefix> <Icon name="search" /> </template>
              </a-input-search>
            </a-col> -->
            <a-col :span="24">
              <RewardsCreateTableWithAdd
                v-if="!$props.readOnly"
                :addPrefix="ADD_KEY_PREFIX"
                :columns="rewardSlotIdsColumnsWithEdit"
                :items="formState.rewardSlotIds"
                :allRewardItemsOptions="rewardSlotsValues"
                idKey="rewardSlotId"
                addButtonText="Add Reward Slot"
                itemPlaceholder="Reward Slot Id"
                skipWeightValidation
                skipUniqueIds
                :isCreateMode="$props.isCreateMode"
                @addEntry="(entry) => addEntry(entry)"
                @saveEntry="(entry) => saveEntry(entry)"
                @deleteEntry="(entry) => deleteEntry(entry)"
              />
              <Table
                v-if="$props.readOnly"
                :columns="rewardSlotsColumns"
                :data="formState.rewardSlotIds"
                :pagination="false"
                size="small"
                class="view-only-table"
              >
                <template #bodyCell="{ column, text, record }">
                  <template v-if="column.dataIndex === 'slotEntries'">
                    <PopoverWithTable
                      :records="record.slotEntries || []"
                      :columns="slotEntriesColumns"
                      :previewFields="{main: 'rewardBagId'}"
                  />
                  </template>
                  <template v-else>
                  <template v-if="!$props.isCreateMode && column.dataIndex == 'rewardSlotId'">
                    <router-link :to="{ name: 'ViewRewardTableSlot', params: { rewardSlotId: record.id, rewardTableId }}">{{ text }}</router-link>
                  </template>
                  <span v-else>{{ text }}</span>
                </template>
                </template>
              </Table>
            </a-col>
          </a-row>
          </PageSection>
        </a-col>
        <a-col :span="24">
          <a-form layout='vertical' :model="formState" class="info-form">
            <PageSection title="Other Details">
              <a-row :gutter="32" class="create-entry-row">
                <a-col :span="12">
                  <a-form-item
                    label="Reward Table Id"
                    name="id"
                    class="required"
                    :help="errorMessages.id"
                    :validateStatus="errorMessages.id && 'error' || ''"
                  >
                    <a-input v-model:value='formState.id' :disabled="!!$props.table"/>
                    <InfoParagraph v-if="!$props.readOnly" style="padding-bottom: 20px; display: block" infoText="Once the reward table is created the Id cannot be changed"/>
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item
                    label="Reward Table Name"
                    name="name"
                    class="required"
                    :help="errorMessages.name"
                    :disabled="$props.readOnly"
                    :validateStatus="errorMessages.name && 'error' || ''"
                  >
                    <a-input v-model:value='formState.name' :disabled="$props.readOnly"/>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="32" class="create-entry-row">
                <a-col :span="12">
                  <a-form-item
                    label="Guaranteed Reward Ids"
                    name="guaranteedRewardIds"
                  >
                    <a-select
                      v-model:value="formState.guaranteedRewardIds"
                      mode="tags"
                      notFoundContent=""
                      :disabled="$props.readOnly"
                    >
                      <a-select-option
                        v-for="reward in rewardIds"
                        :value="reward"
                      >
                        {{reward}}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </PageSection>
          </a-form>
        </a-col>
      </a-row>
      <a-row :gutter="[32, 30]" class="create-entry-row">
        <a-col :span="24" v-if="$props.table">
          <PageSection title="Reward Chances" style="margin-top: 32px">
            <Table
              :columns="rewardChancesColumns"
              :data="rewardChances"
              :pagination="false"
              size="small"
            >
              <template #headerCell="{ column }">
                <template v-if="column.dataIndex === 'min'">
                  <span>
                    Min
                    <a-tooltip placement="top">
                      <template #title>
                        <span>Lower limit of range to grant, <br />number selected randomly.</span>
                      </template>
                      <Icon name="info-circle" style="padding: 3px 5px 2px; vertical-align: top; height: auto; color: gray"/>
                    </a-tooltip>
                  </span>
                </template>
                <template v-if="column.dataIndex === 'max'">
                  <span>
                    Max
                    <a-tooltip placement="top">
                      <template #title>
                        <span>Upper limit of range to grant, <br />number selected randomly.</span>
                      </template>
                      <Icon name="info-circle" style="padding: 3px 5px 2px; vertical-align: top; height: auto; color: gray"/>
                    </a-tooltip>
                  </span>
                </template>
              </template>
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'chance'">
                  <span>{{text === GUARANTEED ? text : text + '%'}}</span>
                </template>
              </template>
            </Table>
          </PageSection>
        </a-col>
      </a-row>
    </div>
  `
}

const rewardSlotsColumns = [
  {
    title: 'Reward Slot Id',
    dataIndex: 'rewardSlotId',
    key: 'rewardSlotId',
    defaultSortOrder: 'ascend',
    sorter: true
  },
  {
    title: 'Chance Of Roll',
    dataIndex: 'chanceOfRoll',
    key: 'chanceOfRoll'
  },
  {
    title: 'Slot Entries',
    dataIndex: 'slotEntries',
    key: 'slotEntries'
  }
]
const rewardChancesColumns = [
  {
    title: 'Reward Id',
    dataIndex: 'rewardId',
    key: 'rewardId',
    sorter: true
  },
  {
    title: 'Chance',
    dataIndex: 'chance',
    defaultSortOrder: 'descend',
    key: 'chance',
    sorter: true
  },
  {
    title: 'Min',
    dataIndex: 'min',
    key: 'min'
  },
  {
    title: 'Max',
    dataIndex: 'max',
    key: 'max'
  }
]

const slotEntriesColumns = [
  {
    title: 'Reward Bag Id',
    dataIndex: 'rewardBagId',
    key: 'rewardBagId'
  },
  {
    title: 'Weight',
    dataIndex: 'weight',
    key: 'weight'
  }
]

const rewardSlotIdsColumnsWithEdit = [...rewardSlotsColumns, { dataIndex: 'edit', key: 'edit' }]

const GUARANTEED = 'Guaranteed'
const addGuaranteedRewardsToChances = (table, rewards, chances) => {
  table.guaranteedRewardIds.forEach(r => {
    const reward = rewards.find(rew => rew.id == r)
    chances.push({
      rewardId: r,
      chance: GUARANTEED,
      min: reward?.count?.min,
      max: reward?.count?.max
    })
  })
  return chances
}

const buildRewardsForDisplay = (calculatedRewards, rewards) => {
  return Object.entries(calculatedRewards).map(([rewardId, chance]) => {
    const theReward = rewards.find(r => r.id === rewardId)
    return {
      rewardId,
      chance: chance.toFixed(2),
      min: theReward?.count?.min,
      max: theReward?.count?.max
    }
  })
}

export default CreateEditRewardTable
