import { utils } from '../../../../../main/utils.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import ItemSelector from '../ItemSelector/ItemSelector.vue.js'
import * as Vue from 'vue';

const ItemsTableWithAdd = {
  name: 'ItemsTableWithAdd',
  components: { Icon, ItemSelector },
  emits: ['addEntry', 'saveEntry', 'deleteEntry'],
  props: {
    addPrefix: String,
    columns: Object,
    items: Array,
    addButtonText: {
      type: String,
      default: 'Add Item'
    },
    itemPlaceholder: {
      type: String,
      default: 'Select Item'
    },
    idKey: String,
    skipUniqueIds: Boolean,
    instanced: Array,
    stackable: Array
  },
  setup(props, { emit }) {
    const items = Vue.computed(() =>
      props.items.map(item => ({ ...item, key: item.key || utils.getRandomString() }))
    )

    const editableData = Vue.reactive({})
    const errorMessages = Vue.reactive({})

    const addItem = () => {
      const keyToAdd = props.addPrefix + utils.getRandomString()
      const itemToAdd = { key: keyToAdd }
      editableData[keyToAdd] = itemToAdd

      emit('addEntry', itemToAdd)
    }

    const validateRow = itemToSave => {
      errorMessages[itemToSave.key] = {}

      const findStackable = props.stackable?.find(
        item => item[props.idKey] == itemToSave[props.idKey]
      )
      const findInstanced = props.instanced?.find(
        item => item[props.idKey] == itemToSave[props.idKey]
      )

      if (!findStackable && !findInstanced)
        errorMessages[itemToSave.key][props.idKey] = 'The id does not exist'

      if (
        !props.skipUniqueIds &&
        items.value.find(
          item => item[props.idKey] == itemToSave[props.idKey] && item.key !== itemToSave.key
        )
      )
        errorMessages[itemToSave.key][props.idKey] = 'The id already exists'

      if (Object.keys(errorMessages[itemToSave.key]).length == 0)
        delete errorMessages[itemToSave.key]
    }

    const tagsFocused = Vue.ref(false)

    const save = (record, count = 0) => {
      // if you are writing a tag and click save before the tag is registered,
      // antdv throws an error
      // to avoid this we wait for the blur event of the tag
      // avoind the infinite loop by only trying 5 times
      if (tagsFocused.value) {
        setTimeout(() => {
          if (count > 5) return
          save(record, count + 1)
        }, 20)
        return
      }
      const itemToSave = editableData[record.key]
      validateRow(itemToSave)

      if (errorMessages[itemToSave.key]) return false
      delete editableData[record.key]

      emit('saveEntry', itemToSave)
    }

    const edit = record => {
      editableData[record.key] = record
    }

    const deleteItem = key => {
      emit('deleteEntry', key)
    }

    const cancel = record => {
      errorMessages[record.key] = {}
      if (record.key.includes(props.addPrefix)) emit('deleteEntry', record)
      delete editableData[record.key]
    }

    const onAutoCompleteSelect = (value, option, record) => {
      delete errorMessages[record[props.idKey]]
    }

    const setTagsFocused = isFocused => {
      tagsFocused.value = isFocused
    }
    const maxLongValue = utils.MAX_LONG_VALUE

    return {
      items,
      editableData,
      addItem,
      save,
      edit,
      deleteItem,
      cancel,
      onAutoCompleteSelect,
      errorMessages,
      formatTags,
      setTagsFocused,
      maxLongValue
    }
  },
  template: `
    <div class="items-editable-table">
      <a-table
        :locale="{emptyText: 'No Data'}"
        :columns="$props.columns"
        :data-source="items"
        size="small"
        :pagination="false"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex !== 'edit'">
            <div class="table-cell-inner">
              <template v-if="!editableData[record.key]">
                <template v-if="column.dataIndex === 'tags'">
                  <a-popover>
                    <template #content>
                      <pre>{{formatTags(record.tags).tagsParsed}}</pre>
                    </template>
                    {{formatTags(record.tags).tagsString}}
                  </a-popover>
                </template>
                <span v-else>{{ text }}</span>
              </template>

              <a-form-item
                v-if="editableData[record.key] && column.dataIndex == $props.idKey"
                :help="(errorMessages[record.key] || {})[$props.idKey]"
                :validateStatus="(errorMessages[record.key] || {})[$props.idKey] ? 'error' : ''"
              >
                <item-selector
                  v-model:value='editableData[record.key][$props.idKey]'
                  :instanced="$props.instanced"
                  :stackable="$props.stackable"
                />
              </a-form-item>

              <a-form-item
                v-if="editableData[record.key] && (column.dataIndex == 'amount')"
                :help="(errorMessages[record.key] || {})[column.dataIndex]"
                :validateStatus="(errorMessages[record.key] || {})[column.dataIndex] ? 'error' : ''"
              >
                <a-input-number
                  v-model:value="editableData[record.key][column.dataIndex]"
                  min="0"
                  :max="maxLongValue"
                  :stringMode = "true"
                />
              </a-form-item>

              <a-form-item
                v-if="editableData[record.key] && (column.dataIndex == 'tags')"
                :help="(errorMessages[record.key] || {})[column.dataIndex]"
                :validateStatus="(errorMessages[record.key] || {})[column.dataIndex] ? 'error' : ''"
              >
                <a-select
                  v-model:value="editableData[record.key][column.dataIndex]"
                  @focus="setTagsFocused(true)"
                  @blur="setTagsFocused(false)"
                  mode="tags"
                  notFoundContent=""
                  style="min-width:100px;"
                />
              </a-form-item>
            </div>
          </template>
          <template v-else>
            <div v-if="editableData[record.key]" class="items-editable-table-actions">
              <a-typography-link @click="save(record)">Save</a-typography-link>
              <a-typography-link @click="deleteItem(record)">Remove</a-typography-link>
              <a-typography-link @click="cancel(record)">Cancel</a-typography-link>
            </div>
            <div v-else class="items-editable-table-actions">
              <a @click="edit(record)">Edit</a>
            </div>
          </template>
        </template>
      </a-table>
      <a-button type="dashed" @click="addItem" block>
        <Icon name="plus" align="-2"/>
        {{$props.addButtonText}}
      </a-button>
    </div>
  `
}

const formatTags = entries => {
  const tags = entries?.join(', ') || ''
  const tagsString = utils.addTextEllipsis(tags)
  const tagsParsed = utils.convertObjectToFormattedString(entries)

  return {
    tagsString,
    tagsParsed
  }
}

export default ItemsTableWithAdd
