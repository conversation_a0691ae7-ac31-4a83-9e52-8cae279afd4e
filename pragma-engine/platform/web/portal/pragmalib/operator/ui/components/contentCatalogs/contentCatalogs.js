import { variables } from './GlobalCatalogVariables.js'
import CatalogExtTableCellRender from './ExtSwappableComponents/ExtTableCellRender.vue.js'
import CraftingEntries from './CraftingEntries/CraftingEntries.vue.js'
import CreateCraftingEntry from './CreateCraftingEntry/CreateCraftingEntry.vue.js'
import CreateEditLimitedGrant from './CreateEditLimitedGrant/CreateEditLimitedGrant.vue.js'
import CreateEditReward from './CreateRewards/CreateEditReward.vue.js'
import CreateEditRewardBag from './CreateRewards/CreateEditRewardBag.vue.js'
import CreateEditRewardSlot from './CreateRewards/CreateEditRewardSlot.vue.js'
import CreateEditRewardTable from './CreateRewards/CreateEditRewardTable.vue.js'
import CreateInstanced from './CreateInstanced/CreateInstanced.vue.js'
import CreateStackable from './CreateStackable/CreateStackable.vue.js'
import CreateStoreEntry from './CreateStoreEntry/CreateStoreEntry.vue.js'
import CreateUpdateEntry from './CreateUpdateEntry/CreateUpdateEntry.vue.js'
import DefaultExtRichPreview from './ExtSwappableComponents/DefaultExtRichPreview.vue.js'
import ItemBundles from './ItemBundles/ItemBundles.vue.js'
import ItemSpecs from './ItemSpecs/ItemSpecs.vue.js'
import LimitedGrants from './LimitedGrants/LimitedGrants.vue.js'
import ListUpdateEntries from './ListUpdateEntries/ListUpdateEntries.vue.js'
import PopoverWithTable from './PopoverWithTable/PopoverWithTable.vue.js'
import RewardBags from './Rewards/RewardBags.vue.js'
import Rewards from './Rewards/Rewards.vue.js'
import RewardSlots from './Rewards/RewardSlots.vue.js'
import RewardTables from './Rewards/RewardTables.vue.js'
import StoreCreateForm from './StoreForm/StoreCreateForm.vue.js'
import StoreEditForm from './StoreForm/StoreEditForm.vue.js'
import Stores from './Stores/Stores.vue.js'
import ViewStore from './ViewStore/ViewStore.vue.js'

export default {
  CatalogExtTableCellRender,
  CraftingEntries,
  CreateCraftingEntry,
  CreateEditLimitedGrant,
  CreateEditReward,
  CreateEditRewardBag,
  CreateEditRewardSlot,
  CreateEditRewardTable,
  CreateInstanced,
  CreateStackable,
  CreateStoreEntry,
  CreateUpdateEntry,
  DefaultExtRichPreview,
  ItemBundles,
  ItemSpecs,
  LimitedGrants,
  ListUpdateEntries,
  PopoverWithTable,
  RewardBags,
  Rewards,
  RewardSlots,
  RewardTables,
  StoreCreateForm,
  StoreEditForm,
  Stores,
  variables,
  ViewStore
}
