import { calculateRewardsChances } from './rewardsCalculations.js'
import {
  generateRewardSlots,
  generateRewardBags,
  generateRewards,
  generateRewardTables
} from './rewardsFactory.js'

const { utils } = pragma

const ONE_MILLION = 1000000

const REWARDS_COUNT = 10
const BAGS_COUNT = 10
const SLOTS_COUNT = 10

describe('RewardTables', () => {
  const generatedRewards = generateRewards({ count: REWARDS_COUNT })
  const generatedRewardBags = generateRewardBags({ count: BAGS_COUNT, rewards: generatedRewards })
  const generatedRewardSlots = generateRewardSlots({
    count: SLOTS_COUNT,
    rewardBags: generatedRewardBags
  })

  const generatedRewardTables = generateRewardTables({
    count: 1,
    rewardSlots: generatedRewardSlots,
    rewards: generatedRewards
  })

  xit('[verifies if the percentage is right] through random generated content', () => {
    const tableToExtractFrom = generatedRewardTables[0]

    const slotsToArrayOfBags = tableToExtractFrom.rewardSlotIds.reduce((carry, slotId) => {
      const findSlot = generatedRewardSlots.find(slot => slot.id == slotId)
      const slotEntriesToArray = findSlot.slotEntries.reduce((finalSlotArray, slotEntry) => {
        for (let i = 0; i < slotEntry.weight; i++) {
          finalSlotArray.push(slotEntry.rewardBagId)
        }
        return finalSlotArray
      }, [])
      return {
        ...carry,
        [slotId]: {
          slotBagsArray: slotEntriesToArray,
          chanceOfRollFrom1To10: 10 - findSlot.noOpPercentage * 10
        }
      }
    }, {})

    const bagUniqueIdsInTable = utils.getUniqueValues(
      Object.values(slotsToArrayOfBags)
        .map(s => s.slotBagsArray)
        .flat()
    )

    const bagsToArrayOfRewards = bagUniqueIdsInTable.reduce((carry, bagId) => {
      const findBag = generatedRewardBags.find(bag => bag.id == bagId)
      const bagRewardsToArray = findBag.rewards.reduce((finalRewardsArray, bagReward) => {
        for (let i = 0; i < bagReward.weight; i++) {
          finalRewardsArray.push(bagReward.rewardId)
        }
        return finalRewardsArray
      }, [])

      return {
        ...carry,
        [bagId]: bagRewardsToArray
      }
    }, {})

    const rewardUniqueIdsInTable = utils.getUniqueValues(Object.values(bagsToArrayOfRewards).flat())

    const rewardsRolledCount = rewardUniqueIdsInTable.reduce(
      (carry, rewId) => ({
        ...carry,
        [rewId]: 0
      }),
      {}
    )

    const extractReward = bagId => {
      const bag = bagsToArrayOfRewards[bagId]
      return bag[utils.getRandomIntInRange(0, bag.length - 1)]
    }

    const extractBagFromSlot = slotId => {
      const slot = slotsToArrayOfBags[slotId]
      return slot.slotBagsArray[utils.getRandomIntInRange(0, slot.slotBagsArray.length - 1)]
    }

    for (let i = 0; i < ONE_MILLION; i++) {
      const rewardsExtracted = []
      tableToExtractFrom.rewardSlotIds.forEach(slotId => {
        const slotChanceOfRollFrom1To10 = slotsToArrayOfBags[slotId].chanceOfRollFrom1To10
        const shouldExtract = utils.getRandomIntInRange(0, 9) < slotChanceOfRollFrom1To10

        if (shouldExtract) {
          const extractedBagId = extractBagFromSlot(slotId)
          const extractedReward = extractReward(extractedBagId)
          rewardsExtracted.push(extractedReward)
        }
      })

      utils.getUniqueValues(rewardsExtracted).forEach(rewId => {
        rewardsRolledCount[rewId]++
      })
    }

    const chancesFromExtractions = Object.keys(rewardsRolledCount).reduce(
      (carry, rewardId) => ({
        ...carry,
        [rewardId]: (rewardsRolledCount[rewardId] / 10000).toFixed(2)
      }),
      {}
    )

    const chancesFromExctractionsSum = Object.values(chancesFromExtractions)
      .reduce((acc, currentChance) => acc + parseFloat(currentChance), 0)
      .toFixed(2)

    const calculatedChances = calculateRewardsChances(
      generatedRewardSlots,
      generatedRewardBags,
      tableToExtractFrom
    ).reduce(
      (carry, reward) => ({
        ...carry,
        [reward.rewardId]: reward.chance
      }),
      {}
    )

    const calculatedChancesSum = Object.values(calculatedChances)
      .reduce((acc, currentChance) => acc + parseFloat(currentChance), 0)
      .toFixed(2)

    console.log('DATA:', {
      calculatedChances,
      calculatedChancesSum,
      chancesFromExtractions,
      chancesFromExctractionsSum
    })

    // just to avoid warnings as we only need the log for now
    expect(true).toBeTruthy()
  })

  xit('[verifies if the percentage is right] through repeated table rolls', () => {
    const rewardHitsCount = {
      goldCoins: 0,
      resourceBone: 0,
      ingotCopper: 0,
      hideLeather: 0,
      dragonSnack: 0
    }

    const bagsData = {
      ogreRewardSpecialBag: [
        'resourceBone',
        'resourceBone',
        'resourceBone',
        'resourceBone',
        'resourceBone',
        'resourceBone',
        'resourceBone',
        'hideLeather',
        'hideLeather',
        'hideLeather'
      ],
      currencyRewardBag: [
        'goldCoins',
        'goldCoins',
        'goldCoins',
        'goldCoins',
        'goldCoins',
        'goldCoins',
        'goldCoins',
        'ingotCopper',
        'ingotCopper',
        'ingotCopper'
      ],
      petSpecialRewardBag: [
        'dragonSnack',
        'dragonSnack',
        'dragonSnack',
        'dragonSnack',
        'dragonSnack',
        'dragonSnack',
        'dragonSnack',
        'dragonSnack',
        'dragonSnack',
        'dragonSnack'
      ]
    }

    const ogreQuestSlotItems = [
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'ogreRewardSpecialBag',
      'ogreRewardSpecialBag',
      'ogreRewardSpecialBag'
    ]

    const petSlotItems = [
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'currencyRewardBag',
      'petSpecialRewardBag',
      'petSpecialRewardBag',
      'petSpecialRewardBag',
      'petSpecialRewardBag',
      'petSpecialRewardBag'
    ]

    const randomFrom0To9 = () => Math.floor(Math.random() * 10)

    const extractRewardFromBag = bagId => {
      return bagsData[bagId][randomFrom0To9()]
    }

    const chooseOgreSlotBag = () => ogreQuestSlotItems[randomFrom0To9()]
    const choosePetSlotBag = () => petSlotItems[randomFrom0To9()]

    for (let i = 0; i < 1000000; i++) {
      const shouldExtract = randomFrom0To9() < 8
      const extractFromOgre = shouldExtract ? extractRewardFromBag(chooseOgreSlotBag()) : null
      const extractFromPet = extractRewardFromBag(choosePetSlotBag())

      if (extractFromOgre === extractFromPet) {
        rewardHitsCount[extractFromOgre]++
      } else {
        if (extractFromOgre) {
          rewardHitsCount[extractFromOgre]++
        }
        rewardHitsCount[extractFromPet]++
      }
    }

    const rewardsCountFormatted = Object.keys(rewardHitsCount).reduce(
      (carry, rewardId) => ({
        ...carry,
        [rewardId]: (rewardHitsCount[rewardId] / 10000).toFixed(2)
      }),
      {}
    )

    // console.log('Chances of roll: ', rewardsCountFormatted)

    // just to avoid warnings as we only need the log for now
    expect(true).toBeTruthy()
  })
})
