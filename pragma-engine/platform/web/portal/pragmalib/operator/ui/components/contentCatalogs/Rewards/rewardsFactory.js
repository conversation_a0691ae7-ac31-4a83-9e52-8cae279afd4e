const { utils } = pragma

const UP_TO = 5

// set to null to make the values random up to [UP_TO]
const BAG_LENGTH = null
const SLOT_LENGTH = null
const NO_OP_PERCENTAGE = null

const humanCatalogIds = [
  'gold_coins',
  'ingot_copper',
  'resource_bone',
  'hide_leather',
  'dragon_snack',
  'eggs'
]

export const generateRewards = ({ count }) => {
  const rewardsGenerated = []
  for (let i = 0; i < count; i++) {
    const randomCatalogIndex = utils.getRandomIntInRange(0, humanCatalogIds.length - 1)
    const min = utils.getRandomIntInRange(0, 100)
    const max = utils.getRandomIntInRange(min, 100)
    rewardsGenerated.push({
      id: utils.getRandomString(),
      catalogId: humanCatalogIds[randomCatalogIndex],
      count: {
        min,
        max
      }
    })
  }

  return rewardsGenerated
}

export const generateRewardBags = ({ count, rewards }) => {
  const rewardBagsGenerated = []
  for (let i = 0; i < count; i++) {
    const shuffledRewards = utils.shuffleArray(rewards)
    const rewardsLength =
      BAG_LENGTH ?? utils.getRandomIntInRange(1, Math.min(rewards.length, UP_TO))
    shuffledRewards.length = rewardsLength

    const rewardsInBag = shuffledRewards.map(rew => ({
      rewardId: rew.id,
      weight: utils.getRandomIntInRange(1, 10)
    }))

    const idAndName = utils.getRandomString()
    rewardBagsGenerated.push({
      id: idAndName,
      name: idAndName,
      rewards: rewardsInBag
    })
  }
  return rewardBagsGenerated
}

export const generateRewardSlots = ({ count, rewardBags }) => {
  const rewardSlotsGenerated = []
  for (let i = 0; i < count; i++) {
    const shuffledRewardBags = utils.shuffleArray(rewardBags)
    const rewardBagsLength =
      SLOT_LENGTH ?? utils.getRandomIntInRange(1, Math.min(rewardBags.length, UP_TO))
    shuffledRewardBags.length = rewardBagsLength

    const slotEntries = shuffledRewardBags.map(bag => ({
      rewardBagId: bag.id,
      weight: utils.getRandomIntInRange(1, 10)
    }))

    const noOpPercentage = NO_OP_PERCENTAGE ?? (i > count - 3 ? utils.getRandomFloat() : 0)

    rewardSlotsGenerated.push({
      id: utils.getRandomString(),
      slotEntries,
      noOpPercentage
    })
  }

  return rewardSlotsGenerated
}

export const generateRewardTables = ({ count, rewardSlots, rewards }) => {
  const rewardTablesGenerated = []
  for (let i = 0; i < count; i++) {
    const shuffledRewardSlots = utils.shuffleArray(rewardSlots)
    const rewardSlotsLength = utils.getRandomIntInRange(1, Math.min(rewardSlots.length, UP_TO))
    shuffledRewardSlots.length = rewardSlotsLength

    const rewardSlotIds = shuffledRewardSlots.map(rewSlot => rewSlot.id)

    const shuffledRewards = utils.shuffleArray(rewards)
    const rewardsLength = utils.getRandomIntInRange(1, Math.min(rewards.length, UP_TO))
    shuffledRewards.length = rewardsLength

    const guaranteedRewardIds = shuffledRewards.map(rew => rew.id)

    const idAndName = utils.getRandomString()
    rewardTablesGenerated.push({
      id: idAndName,
      name: idAndName,
      rewardSlotIds,
      guaranteedRewardIds
    })
  }

  return rewardTablesGenerated
}
