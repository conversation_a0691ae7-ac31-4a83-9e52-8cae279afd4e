.rewards-editable-table {
  hr {
    margin: @marginXS*1px 0 @marginSM*1px 0;
    height: 1px;
    background-color: @colorBorderSecondary;
    border: none;
  }
  .rewards-editable-table-actions {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    max-width: 180px;

    a {
      display: inline-block;
      padding: @paddingXXS/2px @paddingXS*1px;
    }
  }

  table {
    th:last-child,
    td:last-child {
      width: 175px;
      min-width: 175px;
    }
    .ant-select {
      min-width: 100%;
    }
    td:first-child,
    th:first-child {
      width: 50%;
    }
    td:nth-child(2),
    th:nth-child(2) {
      width: 22%;
    }
  }
  .table-cell-inner {
    display: block;
    min-height: 30px;

    > span {
      display: inline-block;
      margin-top: @marginXXS * @scalePx;
    }
  }
  .ant-table-cell {
    vertical-align: top;

    .ant-form-item-explain > * {
      line-height: 1.2;
    }
  }
  .ant-form-item {
    margin-bottom: 0;
  }
  .ant-form-item,
  .ant-form-item-control {
    flex-flow: row wrap;

    > * {
      width: 100%;
    }
  }
  > .ant-btn {
    margin: @marginSM*1px 0px;
  }
  .table-cell-inner {
    > .ant-row.ant-form-item {
      margin-top: @marginXXS/-2px;

      .ant-form-item-explain-error {
        margin: @marginXXS*1px 0 @marginXXS*-1px;
      }
    }
  }
}