import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import { utils } from '../../../../../main/utils.js'
import { variables } from '../GlobalCatalogVariables.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import CopyableCell from '../../../../../main/ui/components/CopyableCell/CopyableCell.vue.js'
import CreateStackable from '../CreateStackable/CreateStackable.vue.js'
import CreateInstanced from '../CreateInstanced/CreateInstanced.vue.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import hooks from '../../../../../main/ui/hooks.js'
import helpersUI from '../../../../../main/ui/helpers/helpers.js'
import PopoverWithTable from '../PopoverWithTable/PopoverWithTable.vue.js'
import ItemsTableWithAdd from '../ItemsTableWithAdd/ItemsTableWithAdd.vue.js'
import { useRouter } from 'vue-router';

const META_DATA_KEY = variables.META_DATA_KEY
const ADD_KEY_PREFIX = '__item-to-add__'

const ItemBundles = {
  props: {
    entries: Object,
    stackable: Object,
    instanced: Object,
    readOnly: Boolean,
    hideSource: Boolean
  },

  components: {
    Table,
    CatalogViewer,
    Icon,
    CopyableCell,
    PopoverWithTable,
    ItemsTableWithAdd,
    CreateStackable,
    CreateInstanced
  },

  setup(props) {
    const router = useRouter()
    const { getSearch } = hooks.useSearch(router)
    const search = getSearch()

    const entries = Object.entries(props.entries).reduce((acc, [file, entriesByFile]) => {
      acc[file] = entriesByFile.map(entry => ({
        ...entry,
        instancedItems: entry.instancedItems || [],
        stackableItems: entry.stackableItems || []
      }))
      return acc
    }, {})

    const sourceFiles = Object.keys(props.entries)

    const tableColumns = props.hideSource
      ? utils.removeFromArray(itemBundleColumns, 'key', 'source')
      : itemBundleColumns

    const filteredEntries = utils.filterItemsByText(
      formatEntries(entries),
      search,
      tableColumns.map(c => c.dataIndex)
    )

    const { drawerData, setDrawer } = hooks.useDrawer()
    const drawerKeys = helpersUI
      .filterTableColumns(tableColumns, ['delete'])
      .map(column => column.dataIndex)

    const editableFields = tableColumns
      .map(column => column.dataIndex)
      .filter(key => key !== 'catalogId' && key !== 'source')

    const updateItem = async data => {
      const hasInstancedItemsKey = data.instancedItems.some(
        item => item.key && item.key.startsWith(ADD_KEY_PREFIX)
      )
      const hasStackableItemsKey = data.stackableItems.some(
        item => item.key && item.key.startsWith(ADD_KEY_PREFIX)
      )
      if (hasInstancedItemsKey || hasStackableItemsKey) {
        toast.error('You have unsaved changes')
        return false
      } else {
        const file = data.source
        delete data.source
        const srcJson = utils.jsonStringify(
          utils.replaceInObjArray(props.entries[file], 'catalogId', data.catalogId, data)
        )

        const payload = {
          contentType: 'ItemBundles',
          filename: file,
          srcJson
        }

        updateContentCatalog(payload)
      }
    }

    const updateItemRaw = async (rawData, rawSource) => {
      const payload = {
        contentType,
        filename: rawSource,
        srcJson: rawData
      }

      updateContentCatalog(payload)
    }

    const deleteItem = async (itemId, file, close) => {
      const srcJson = utils.jsonStringify(
        utils.removeFromArray(props.entries[file], 'catalogId', itemId)
      )

      const payload = {
        contentType,
        filename: file,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    const addEntry = (entry, items, setData, type) => {
      setData(type, [
        ...items,
        {
          ...entry,
          catalogId: null,
          amount: null,
          tags: null
        }
      ])
    }

    const saveEntry = (entry, items, setData, type) => {
      const entryToSave = { ...entry }
      const itemsCopy = [...items]

      const indexOfEntry = items.findIndex(
        item => item.catalogId === entryToSave.catalogId || item.key === entryToSave.key
      )
      delete entryToSave.key
      if (indexOfEntry >= 0) {
        itemsCopy[indexOfEntry] = entryToSave
      }

      setData(type, itemsCopy)
    }

    const deleteEntry = (entry, items, setData, type) => {
      const itemsCopy = [...items]
      const isAddEntry = entry.key?.includes(ADD_KEY_PREFIX)
      const indexOfEntry = itemsCopy.findIndex(item =>
        isAddEntry ? item.key === entry.key : item.catalogId === entry.catalogId
      )

      if (indexOfEntry >= 0) {
        itemsCopy.splice(indexOfEntry, 1)
        setData(type, itemsCopy)
      }
    }

    const formatFields = {
      ext: 'code',
      tags: 'keywordList'
    }

    return {
      entries,
      filteredEntries,
      tableColumns,
      setDrawer,
      drawerKeys,
      drawerData,
      sourceFiles,
      META_DATA_KEY,
      ADD_KEY_PREFIX,
      confirm,
      editableFields,
      updateItem,
      updateItemRaw,
      deleteItem,
      addEntry,
      saveEntry,
      deleteEntry,
      formatFields,
      itemColumnsWithEdit,
      itemColumns,
      instaItemColumns,
      instaColumnsWithEdit
    }
  },

  template: `
    <div class="itemBundles-wrap">
      <CatalogViewer
        :drawerData="drawerData"
        :rawData="entries"
        :rawSources="sourceFiles"
        :drawerKeys="drawerKeys"
        :drawerSlotKeys="['stackableItems', 'instancedItems']"
        drawerEditLabel="Edit Item Bundles"
        :allowRawEdit="!$props.readOnly"
        :rawEditOnSave="updateItemRaw"
        :allowRichEdit="!$props.readOnly ? editableFields : false"
        :richEditSaveSuccess="updateItem"
      >
        <template #richView>
          <Table
            emptyText="No entries have been found"
            :columns="tableColumns"
            :data="filteredEntries"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'id'">
                <a @click.prevent.stop="setDrawer(record)">{{text}}</a>
              </template>

              <template v-if="column.dataIndex === 'tags'">
                <a-popover>
                  <template #content>
                    <pre>{{record[META_DATA_KEY].tagsParsed}}</pre>
                  </template>
                  {{record[META_DATA_KEY].tagsString}}
                </a-popover>
              </template>

              <template v-if="column.dataIndex === 'stackableItems'">
                <PopoverWithTable
                  :records="record.stackableItems || []"
                  :columns="itemColumns"
                  :previewFields="{main: 'catalogId'}"
                />
              </template>

              <template v-if="column.dataIndex === 'instancedItems'">
                <PopoverWithTable
                  :records="record.instancedItems || []"
                  :columns="instaItemColumns"
                  :previewFields="{main: 'catalogId'}"
                />
              </template>

              <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
                <a-button size='small' ghost danger class='hide actionBtn'
                  @click='confirm.danger({
                    title: "Confirm Item Bundle Deletion",
                    content: "Are you sure you want to delete " + record.id + "?",
                    okText: "Delete",
                    onOk: close => { deleteItem(record.id, record.source, close) }
                  })'
                >
                  <Icon name='close' size="10" /><span>Delete</span>
                </a-button>
              </template>
            </template>
          </Table>
        </template>

        <template #stackableItems="{isEditMode, data, setData}">
          <ItemsTableWithAdd
            v-if="isEditMode"
            :addPrefix="ADD_KEY_PREFIX"
            :columns="itemColumnsWithEdit"
            :items="data.stackableItems"
            :stackable="$props.stackable"
            idKey="catalogId"
            addButtonText="Add Item"
            itemPlaceholder="Catalog Id"
            @addEntry="(entry) => addEntry(entry, data.stackableItems, setData, 'stackableItems')"
            @saveEntry="(entry) => saveEntry(entry, data.stackableItems, setData, 'stackableItems')"
            @deleteEntry="(entry) => deleteEntry(entry, data.stackableItems, setData, 'stackableItems')"
          />
          <Table
            v-if="!isEditMode"
            :columns="itemColumns"
            :data="data.stackableItems"
            :pagination="false"
            size="small"
          />
        </template>

        <template #instancedItems="{isEditMode, data, setData}">
          <ItemsTableWithAdd
            v-if="isEditMode"
            :addPrefix="ADD_KEY_PREFIX"
            :columns="instaColumnsWithEdit"
            :items="data.instancedItems"
            :instanced="$props.instanced"
            idKey="catalogId"
            addButtonText="Add Item"
            itemPlaceholder="Catalog Id"
            @addEntry="(entry) => addEntry(entry, data.instancedItems, setData, 'instancedItems')"
            @saveEntry="(entry) => saveEntry(entry, data.instancedItems, setData, 'instancedItems')"
            @deleteEntry="(entry) => deleteEntry(entry, data.instancedItems, setData, 'instancedItems')"
          />
          <Table
            v-if="!isEditMode"
            :columns="instaItemColumns"
            :data="data.instancedItems"
            :pagination="false"
            size="small"
          />
        </template>
      </CatalogViewer>
    </div>
  `
}

const itemBundleColumns = [
  {
    title: 'Id',
    dataIndex: 'id',
    key: 'id',
    defaultSortOrder: 'ascend',
    sorter: true
  },
  {
    title: 'Tags',
    dataIndex: 'tags',
    key: 'tags',
    sorter: true
  },
  {
    title: 'Stackable',
    dataIndex: 'stackableItems',
    key: 'stackableItems',
    sorter: true
  },
  {
    title: 'Instanced',
    dataIndex: 'instancedItems',
    key: 'instancedItems',
    sorter: true
  },
  {
    title: 'Source',
    dataIndex: 'source',
    key: 'source'
  },
  {
    dataIndex: 'delete',
    align: 'right'
  }
]

const itemColumns = [
  {
    title: 'Catalog Id',
    dataIndex: 'catalogId',
    key: 'catalogId'
  },
  {
    title: 'Amount',
    dataIndex: 'amount',
    key: 'amount'
  },
  {
    title: 'Tags',
    dataIndex: 'tags',
    key: 'tags'
  }
]
const instaItemColumns = [
  {
    title: 'Catalog Id',
    dataIndex: 'catalogId',
    key: 'catalogId'
  },
  {
    title: 'Tags',
    dataIndex: 'tags',
    key: 'tags'
  }
]

const itemColumnsWithEdit = [...itemColumns, { dataIndex: 'edit', key: 'edit' }]
const instaColumnsWithEdit = [...instaItemColumns, { dataIndex: 'edit', key: 'edit' }]

const formatEntries = entries => {
  return helpersAPI.concatenateContentCatalogSources(entries).map(entry => {
    const tags = entry.tags?.join(', ') || ''
    const tagsString = utils.addTextEllipsis(tags)
    const tagsParsed = utils.convertObjectToFormattedString(entry.tags)

    return {
      ...entry,
      [META_DATA_KEY]: {
        tagsString,
        tagsParsed
      }
    }
  })
}

export default ItemBundles
