import { utils } from '../../../../../main/utils.js'

const ItemSelector = {
  name: 'ItemSelector',
  props: {
    instanced: Array,
    stackable: Array
  },

  emits: ['change', 'update:modelValue'],

  setup(props, { emit }) {
    const allCatalogItemsOptions = formatCatalogItems(props.instanced, props.stackable)
    const onChange = (inputValue, item) => {
      emit('change', inputValue, item)
      emit('update:modelValue', inputValue)
    }

    return {
      allCatalogItemsOptions,
      onChange
    }
  },
  template: `
    <a-auto-complete
      :options="allCatalogItemsOptions"
      placeholder="catalogId"
      :filterOption="true"
      @change="onChange"
    />
  `
}

export const formatCatalogItems = (instanced, stackable) => {
  const allCatalogItems = []

  stackable &&
    stackable.length &&
    allCatalogItems.push({
      label: 'Stackable',
      options: stackable.map(item => ({
        ...item,
        type: 'stackable',
        value: item.catalogId,
        label: `${item.name} (${item.catalogId})`
      }))
    })

  instanced &&
    instanced.length &&
    allCatalogItems.push({
      label: 'Instanced',
      options: instanced.map(item => ({
        ...item,
        type: 'instanced',
        value: item.catalogId,
        label: `${item.name} (${item.catalogId})`
      }))
    })

  // sort all items in allCatalogItems
  allCatalogItems.forEach(
    optionGroup => (optionGroup.options = utils.sortByField(optionGroup.options, 'label'))
  )

  return allCatalogItems
}

export default ItemSelector
