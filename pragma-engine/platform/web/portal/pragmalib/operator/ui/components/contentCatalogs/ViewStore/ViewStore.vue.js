import CopyableCell from '../../../../../main/ui/components/CopyableCell/CopyableCell.vue.js'
import PageSection from '../../../../../main/ui/components/PageSection/PageSection.vue.js'
import { utils } from '../../../../../main/utils.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import { useRoute, useRouter } from 'vue-router';
import PopoverWithTable, {
  formatCosts,
  formatReceivedQuantities
} from '../PopoverWithTable/PopoverWithTable.vue.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'

const ViewStore = {
  name: 'ViewStore',
  props: {
    store: [Array, Object],
    allItems: Array,
    readOnly: Boolean
  },
  components: { CopyableCell, PageSection, PopoverWithTable, Icon, Table },
  setup(props) {
    const router = useRouter()
    const route = useRoute()

    const parsedStores = {}
    Object.keys(props.store).forEach(file => {
      parsedStores[file] = utils.jsonParse(props.store[file])
    })
    const storeId = route.params.storeId
    const storeFile = Object.keys(parsedStores)?.find(file =>
      parsedStores[file]?.find(store => store.id === storeId)
    )
    const storeObject = parsedStores[storeFile]?.find(store => store.id === storeId) || {}
    const storeEntries = storeObject?.storeEntries || []
    const entries = formatEntries(storeEntries, props.allItems)

    const searchPlaceholder = `Search in ${storeObject.name}`

    const deleteEntry = async (entryId, close) => {
      const indexOfChangedStore = parsedStores[storeFile].findIndex(store => store.id === storeId)
      parsedStores[storeFile][indexOfChangedStore].storeEntries = utils.removeFromArray(
        parsedStores[storeFile][indexOfChangedStore].storeEntries,
        'id',
        entryId
      )
      const srcJson = utils.jsonStringify(parsedStores[storeFile])

      const payload = {
        contentType: 'Stores',
        filename: storeFile,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    const defaultStateCreateEntry = () => {
      router.push({
        name: 'CreateEntry'
      })
    }

    return {
      entries,
      searchPlaceholder,
      storeId: storeObject.id,
      storeEntriesColumns,
      receivedQuantityByCatalogIdColumns,
      costByCatalogIdColumns,
      defaultStateCreateEntry,
      confirm,
      deleteEntry
    }
  },
  template: `
    <div class="view-store-wrap">
      <Table
        emptyText="You don't have any store entries yet"
        :columns="storeEntriesColumns"
        :data="entries"
        enableSearch
        :searchPlaceholder="searchPlaceholder"
        pagination="hideOnSinglePage"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'id'">
            <router-link v-if="!$props.readOnly" :to="{ name: 'EditStoreEntry', params: { storeEntryId: record.id, storeId}}">{{ text }}</router-link>
            <router-link v-if="$props.readOnly" :to="{ name: 'ViewStoreEntry', params: { storeEntryId: record.id, storeId}}">{{ text }}</router-link>
          </template>

          <template v-if="column.dataIndex === 'receivedQuantityByCatalogId'">
            <PopoverWithTable
              :records="record.receivedQuantityByCatalogIdArray"
              :columns="receivedQuantityByCatalogIdColumns"
              :previewFields="{main: 'name', extra: 'catalogId'}"
            />
          </template>

          <template v-if="column.dataIndex === 'costByCatalogId'">
            <PopoverWithTable
              :records="record.costByCatalogIdArray"
              :columns="costByCatalogIdColumns"
              :previewFields="{main: 'name', extra: 'catalogId'}"
            />
          </template>

          <template v-if="column.dataIndex === 'tags'">
            <a-popover>
              <template #content>
                <pre>{{record.tagsParsed}}</pre>
              </template>
              {{record.tagsString}}
            </a-popover>
          </template>

          <template v-if="column.dataIndex === 'requirements'">
            <a-popover>
              <template #content>
                <pre>{{record.requirementsParsed}}</pre>
              </template>
              {{record.requirementsString}}
            </a-popover>
          </template>

          <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
              <a-button size='small' ghost danger class='hide actionBtn'
                @click='confirm.danger({
                  title: "Confirm Entry Deletion",
                  content: "Are you sure you want to delete " + record.id + "?",
                  okText: "Delete",
                  onOk: close => { deleteEntry(record.id, close) }
                })'
              >
                <Icon name='close' size="10" /><span>Delete</span>
              </a-button>
            </template>
        </template>
      </Table>
      <div v-if='entries?.length == 0 && !readOnly'>
        <a-button
          class='ant-btn-secondary default-state-btn'
          @click='defaultStateCreateEntry'
        ><Icon name="plus" align="-2" />Create Entry</a-button>
      </div>
    </div>
  `
}

const storeEntriesColumns = [
  {
    title: 'Id',
    dataIndex: 'id',
    key: 'id'
  },
  {
    title: 'Received Quantity',
    dataIndex: 'receivedQuantityByCatalogId',
    key: 'receivedQuantityByCatalogId'
  },
  {
    title: 'Cost',
    dataIndex: 'costByCatalogId',
    key: 'costByCatalogId'
  },
  {
    title: 'Tags',
    dataIndex: 'tags',
    key: 'tags'
  },
  {
    title: 'Requirements',
    dataIndex: 'requirements',
    key: 'requirements'
  },
  {
    dataIndex: 'delete',
    align: 'right'
  }
]

const receivedQuantityByCatalogIdColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: 'Catalog Id',
    dataIndex: 'catalogId',
    key: 'catalogId'
  },
  {
    title: 'Quantity',
    dataIndex: 'quantity',
    key: 'quantity'
  }
]

const costByCatalogIdColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: 'Catalog Id',
    dataIndex: 'catalogId',
    key: 'catalogId'
  },
  {
    title: 'Cost',
    dataIndex: 'cost',
    key: 'cost'
  }
]

const formatEntries = (entries, allItems) => {
  return entries.map(e => {
    const tags = e.tags?.join(', ') || ''
    const tagsString = utils.addTextEllipsis(tags)
    const tagsParsed = utils.convertObjectToFormattedString(e.tags)

    const requirements = utils.jsonStringify(e.requirements) || ''
    const requirementsString = utils.addTextEllipsis(requirements)
    const requirementsParsed = utils.convertObjectToFormattedString(e.requirements)

    const receivedQuantityByCatalogIdArray = formatReceivedQuantities(
      e.receivedQuantityByCatalogId,
      allItems
    )
    const costByCatalogIdArray = formatCosts(e.costByCatalogId, allItems)

    return {
      ...e,
      tagsString,
      tagsParsed,
      requirementsString,
      requirementsParsed,
      receivedQuantityByCatalogIdArray,
      costByCatalogIdArray
    }
  })
}

export default ViewStore
