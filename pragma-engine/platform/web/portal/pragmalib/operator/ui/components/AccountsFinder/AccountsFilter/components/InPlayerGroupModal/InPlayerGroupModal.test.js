import { RPC_PATH } from '../../../../../../../main/api/rpc.js'
import InPlayerGroupModal from './InPlayerGroupModal.vue.js'
import * as VueTestUtils from '@vue/test-utils'
import AxiosMockAdapter from 'axios-mock-adapter'
import axios from 'axios'
const { store, utils, auth } = pragma
const routeName = 'ListAccounts'

const { mockedResponsesByRequestType } = pragma.test.mock.responses

describe('InPlayerGroupModal', () => {
  let mockAxios
  const protocol = 'protocol'
  const host = 'host'
  const port = 'port'
  const authToken = 'authToken'
  const endpoint = utils.buildEndpoint({ protocol: protocol, host: host, port: port })

  const viewPlayerGroupsMock =
    mockedResponsesByRequestType['AccountRpc.ViewPlayerGroupsOperatorV1Request'].data

  const mockViewPlayerGroupsOperatorV1 = () =>
    mockAxios.onPost(`${endpoint}${RPC_PATH}`).reply(200, viewPlayerGroupsMock)

  beforeEach(() => {
    mockAxios = new AxiosMockAdapter(axios)
    store.set('appInfo.socialBackend', { protocol: protocol, host: host, port: port })
    auth.setAuthData('authSocialToken', authToken)
    window.testRoute = { name: routeName, path: '/accounts/list-accounts' }
  })

  afterEach(() => {
    document.body.removeAttribute('style')
    document.body.removeAttribute('class')
  })

  it('renders without crashing', async () => {
    mockViewPlayerGroupsOperatorV1()
    VueTestUtils.mount(InPlayerGroupModal, {
      props: {
        visible: true,
        excludedGroups: pragma.test.mock.mockedAccountPlayerGroups,
        playerGroups: []
      }
    })
    const appNode = document.querySelector('#app')
    const appWrapper = new VueTestUtils.DOMWrapper(appNode)

    expect(appWrapper.text()).toContain('Currently in player group')
  })
})
