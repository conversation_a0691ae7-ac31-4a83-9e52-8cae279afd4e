import { utils } from '../../../../../main/utils.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import * as Vue from 'vue';

const RewardsTableWithAdd = {
  name: 'RewardsTableWithAdd',
  components: { Icon },
  emits: ['addEntry', 'saveEntry', 'deleteEntry', 'selectEntry'],
  props: {
    addPrefix: String,
    columns: Object,
    items: Array,
    allRewardItemsOptions: Array,
    addButtonText: {
      type: String,
      default: 'Add Item'
    },
    itemPlaceholder: {
      type: String,
      default: 'Select Item'
    },
    idKey: String,
    skipUniqueIds: Boolean,
    skipWeightValidation: Boolean,
    tableLocale: Object,
    validateRow: Function,
    currentPage: String,
    routerColumn: String,
    getRouterParams: Function,
    isCreateMode: Boolean
  },
  setup(props, { emit }) {
    const allRewardItems = props.allRewardItemsOptions.map(item => item.value)

    const items = Vue.computed(() =>
      props.items.map(item => ({ ...item, key: item.key || utils.getRandomString() }))
    )

    const editableData = Vue.reactive({})
    const errorMessages = Vue.reactive({})

    const addItem = () => {
      const keyToAdd = props.addPrefix + utils.getRandomString()
      const itemToAdd = { key: keyToAdd }
      editableData[keyToAdd] = itemToAdd

      emit('addEntry', itemToAdd)
    }

    const validateRow = itemToSave => {
      errorMessages[itemToSave.key] = {}

      if (!allRewardItems.find(item => item == itemToSave[props.idKey]))
        errorMessages[itemToSave.key][props.idKey] = 'The id does not exist'

      if (
        !props.skipUniqueIds &&
        items.value.find(
          item => item[props.idKey] == itemToSave[props.idKey] && item.key !== itemToSave.key
        )
      )
        errorMessages[itemToSave.key][props.idKey] = 'The id already exists'

      if (!props.skipWeightValidation && !itemToSave.weight)
        errorMessages[itemToSave.key].weight = 'Required'

      if (Object.keys(errorMessages[itemToSave.key]).length == 0)
        delete errorMessages[itemToSave.key]
    }

    const save = record => {
      const itemToSave = editableData[record.key]

      if (validateRow) {
        props.validateRow(errorMessages, itemToSave, props, allRewardItems, items)
      } else {
        validateRow(itemToSave)
      }

      if (errorMessages[itemToSave.key]) return false
      delete editableData[record.key]

      emit('saveEntry', itemToSave)
    }

    const edit = record => {
      editableData[record.key] = record
    }

    const deleteItem = record => {
      emit('deleteEntry', record)
    }

    const cancel = record => {
      errorMessages[record.key] = {}
      if (record.key.includes(props.addPrefix)) emit('deleteEntry', record)
      delete editableData[record.key]
    }

    const onAutoCompleteSelect = (value, option, record) => {
      delete errorMessages[record[props.idKey]]
      emit('selectEntry', value, editableData, record.key)
    }

    function getNextRewardPage(currentPage) {
      let pagesSuffix = new Map()
      pagesSuffix.set('Table', 'Slot')
      pagesSuffix.set('Slot', 'Bag')
      pagesSuffix.set('Bag', 'Reward')

      let nextPage = currentPage
      // e.q EditRewardBag + Reward
      pagesSuffix.forEach((value, key) => {
        if (currentPage.endsWith(key)) {
          nextPage = currentPage + value
        }
      })

      return nextPage
    }

    return {
      items,
      editableData,
      addItem,
      save,
      edit,
      deleteItem,
      cancel,
      onAutoCompleteSelect,
      errorMessages,
      getNextRewardPage
    }
  },
  template: `
    <div class="rewards-editable-table">
      <a-table
        :locale="$props.tableLocale"
        :columns="$props.columns"
        :data-source="items"
        size="small"
        :pagination="false"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex !== 'edit'">
            <div class="table-cell-inner">

              <template v-if="!editableData[record.key]">
                  <router-link v-if="!$props.isCreateMode && column.dataIndex == $props.routerColumn" :to="{ name: getNextRewardPage($props.currentPage), params: $props.getRouterParams(record)}">{{ text }}</router-link>
                  <span v-else>{{ text }}</span>
              </template>

              <a-form-item
                v-if="editableData[record.key] && (column.dataIndex == 'name')"
                :help="(errorMessages[record.key] || {})[column.dataIndex]"
                :validateStatus="(errorMessages[record.key] || {})[column.dataIndex] ? 'error' : ''"
              >
                <a-auto-complete
                  v-model:value='editableData[record.key][column.dataIndex]'
                  filterOption
                  :placeholder="$props.itemPlaceholder"
                  :dropdownStyle="{minWidth: '300px'}"
                  :options="$props.allRewardItemsOptions"
                  @select="(value, option) => onAutoCompleteSelect(value,option,record)"
                />
              </a-form-item>

              <a-form-item
                v-if="editableData[record.key] && column.dataIndex == $props.idKey"
                :help="(errorMessages[record.key] || {})[$props.idKey]"
                :validateStatus="(errorMessages[record.key] || {})[$props.idKey] ? 'error' : ''"
              >
                <a-auto-complete
                  v-model:value='editableData[record.key][$props.idKey]'
                  filterOption
                  :placeholder="$props.itemPlaceholder"
                  :dropdownStyle="{minWidth: '300px'}"
                  :options="$props.allRewardItemsOptions"
                  @select="(value, option) => onAutoCompleteSelect(value,option,record)"
                />
              </a-form-item>

              <a-form-item
                v-if="editableData[record.key] && (column.dataIndex == 'weight')"
                :help="(errorMessages[record.key] || {})[column.dataIndex]"
                :validateStatus="(errorMessages[record.key] || {})[column.dataIndex] ? 'error' : ''"
              >
                <a-input-number
                  v-model:value="editableData[record.key][column.dataIndex]"
                  min="0"
                  max="100"
                />
              </a-form-item>
            </div>
          </template>
          <template v-else>
            <div v-if="editableData[record.key]" class="rewards-editable-table-actions">
              <a-typography-link @click="save(record)">Save</a-typography-link>
              <a-typography-link @click="deleteItem(record)">Remove</a-typography-link>
              <a-typography-link @click="cancel(record)">Cancel</a-typography-link>
            </div>
            <div v-else class="rewards-editable-table-actions">
              <a @click="edit(record)">Edit</a>
            </div>
          </template>
        </template>
      </a-table>
      <a-button v-if="$props.addButtonText" type="dashed" @click="addItem" block>
        <Icon name="plus" align="-2"/>
        {{$props.addButtonText}}
      </a-button>
    </div>
  `
}

export default RewardsTableWithAdd
