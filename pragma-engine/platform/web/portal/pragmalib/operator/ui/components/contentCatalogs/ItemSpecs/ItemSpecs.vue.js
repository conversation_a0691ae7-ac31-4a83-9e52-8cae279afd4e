import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { swappable } from '../../../../../main/ui/helpers/swappableComponent/swappable.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import { utils } from '../../../../../main/utils.js'
import { variables } from '../GlobalCatalogVariables.js'
import CatalogExtTableCellRender from '../ExtSwappableComponents/ExtTableCellRender.vue.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import CopyableCell from '../../../../../main/ui/components/CopyableCell/CopyableCell.vue.js'
import CreateStackable from '../CreateStackable/CreateStackable.vue.js'
import CreateInstanced from '../CreateInstanced/CreateInstanced.vue.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import DefaultExtRichPreview from '../ExtSwappableComponents/DefaultExtRichPreview.vue.js'
import hooks from '../../../../../main/ui/hooks.js'
import helpersUI from '../../../../../main/ui/helpers/helpers.js'
import { useRouter } from 'vue-router';

const META_DATA_KEY = variables.META_DATA_KEY

const ItemSpecs = {
  props: {
    entries: Object,
    readOnly: Boolean,
    hideSource: Boolean,
    type: {
      validator(value) {
        return ['Stackable', 'Instanced'].includes(value)
      }
    }
  },

  components: {
    Table,
    CatalogViewer,
    Icon,
    CopyableCell,
    CreateStackable,
    CreateInstanced
  },

  setup(props) {
    const isStackable = props.type == 'Stackable'
    const contentType = isStackable ? 'StackableSpecs' : 'InstancedSpecs'
    const router = useRouter()
    const { getSearch } = hooks.useSearch(router)
    const search = getSearch()

    const codeRichEditorCustomComponent = swappable(
      isStackable ? 'Content.Stackable.ExtRich' : 'Content.Instanced.ExtRich',
      {
        default: DefaultExtRichPreview,
        emits: ['onUpdate'],
        data: true,
        context: ['readOnly', 'editDataStringified', 'allowEdit']
      }
    )

    const catalogExtTableCellRender = swappable(
      isStackable ? 'Content.Stackable.ExtTable' : 'Content.Instanced.ExtTable',
      {
        default: CatalogExtTableCellRender,
        emits: null,
        data: true,
        context: null
      }
    )

    const sourceFiles = Object.keys(props.entries)

    const tableColumns = props.hideSource
      ? utils.removeFromArray(itemSpecsColumns(isStackable), 'key', 'source')
      : itemSpecsColumns(isStackable)

    const filteredEntries = utils.filterItemsByText(
      formatEntries(props.entries),
      search,
      tableColumns.map(c => c.dataIndex)
    )

    const { drawerData, setDrawer } = hooks.useDrawer()
    const drawerKeys = helpersUI
      .filterTableColumns(tableColumns, ['delete'])
      .map(column => column.dataIndex)

    const editableFields = tableColumns
      .map(column => column.dataIndex)
      .filter(key => key !== 'catalogId' && key !== 'source')

    const updateItem = async data => {
      const file = data.source
      delete data.source
      const srcJson = utils.jsonStringify(
        utils.replaceInObjArray(props.entries[file], 'catalogId', data.catalogId, data)
      )

      const payload = {
        contentType,
        filename: file,
        srcJson
      }

      updateContentCatalog(payload)
    }

    const updateItemRaw = async (rawData, rawSource) => {
      const payload = {
        contentType,
        filename: rawSource,
        srcJson: rawData
      }

      updateContentCatalog(payload)
    }

    const deleteItem = async (itemId, file, close) => {
      const srcJson = utils.jsonStringify(
        utils.removeFromArray(props.entries[file], 'catalogId', itemId)
      )

      const payload = {
        contentType,
        filename: file,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    const formatFields = {
      ext: 'code',
      ...(isStackable && { removeIfNone: 'boolean' }),
      tags: 'keywordList'
    }

    return {
      filteredEntries,
      tableColumns,
      setDrawer,
      drawerKeys,
      drawerData,
      sourceFiles,
      META_DATA_KEY,
      confirm,
      editableFields,
      updateItem,
      updateItemRaw,
      deleteItem,
      codeRichEditorCustomComponent,
      catalogExtTableCellRender,
      formatFields,
      isStackable
    }
  },

  template: `
    <div>
      <CatalogViewer
        :rawData='$props.entries'
        :rawSources='sourceFiles'
        :drawerData="drawerData"
        :drawerKeys="drawerKeys"
        drawerEditLabel='Edit Item'
        :formatFields="formatFields"
        :allowRawEdit="!$props.readOnly"
        :rawEditOnSave="updateItemRaw"
        :allowRichEdit="!$props.readOnly ? editableFields : false"
        :richEditSaveSuccess="updateItem"
        :drawerCodeRichEditor='codeRichEditorCustomComponent'
      >
        <template #richView>
          <Table
            emptyText="No entries have been found"
            :columns="tableColumns"
            :data="filteredEntries"
          >
            <template #bodyCell="{ column, text, record, index }">
              <template v-if="column.dataIndex === 'name'">
                <a @click.prevent.stop="setDrawer(record)">{{text}}</a>
              </template>
              <template v-if="column.dataIndex === 'catalogId'">
                <CopyableCell :text="text" />
              </template>
              <template v-if="column.dataIndex === 'tags'">
                <a-popover>
                  <template #content>
                    <pre>{{record[META_DATA_KEY].tagsParsed}}</pre>
                  </template>
                  {{record[META_DATA_KEY].tagsString}}
                </a-popover>
              </template>
              <template v-if="column.dataIndex === 'limit'">
                {{record[META_DATA_KEY].limitString}}
              </template>
              <template v-if="column.dataIndex === 'removeIfNone'">
                {{record.removeIfNone}}
              </template>
              <template v-if="column.dataIndex === 'ext'">
                <component
                  :is="catalogExtTableCellRender"
                  :data="record.ext || {}"
                />
              </template>
              <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
                <a-button size='small' ghost danger class='hide actionBtn'
                  @click='confirm.danger({
                    title: "Confirm Item Deletion",
                    content: "Are you sure you want to delete " + record.name + "?",
                    okText: "Delete",
                    onOk: close => {
                        deleteItem(record.catalogId, record.source, close)
                    }
                  })'
                >
                  <Icon name='close' size="10" /><span>Delete</span>
                </a-button>
              </template>
            </template>
          </Table>
        </template>
      </CatalogViewer>

      <CreateStackable v-if="isStackable && !$props.readOnly" :entries="$props.entries" :sourceFiles="sourceFiles"/>
      <CreateInstanced v-if="!isStackable" :entries="$props.entries" :sourceFiles="sourceFiles"/>
    </div>
  `
}

const itemSpecsColumns = isStackable =>
  [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: true
    },
    {
      title: 'Catalog Id',
      dataIndex: 'catalogId',
      key: 'catalogId',
      defaultSortOrder: 'ascend',
      sorter: true
    },
    isStackable && {
      title: 'Limit',
      dataIndex: 'limit',
      key: 'limit',
      sorter: true
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags'
    },
    isStackable && {
      title: 'Remove If None',
      dataIndex: 'removeIfNone',
      key: 'removeIfNone',
      sorter: true
    },
    {
      title: 'Ext',
      dataIndex: 'ext',
      key: 'ext'
    },
    {
      title: 'Source',
      dataIndex: 'source',
      key: 'source'
    },
    {
      dataIndex: 'delete',
      align: 'right'
    }
  ].filter(o => o)

const formatEntries = entries => {
  return helpersAPI.concatenateContentCatalogSources(entries).map(entry => {
    const limitString = utils.convertNumberToFormattedString(entry.limit)

    const tags = entry.tags?.join(', ') || ''
    const tagsString = utils.addTextEllipsis(tags)
    const tagsParsed = utils.convertObjectToFormattedString(entry.tags)

    return {
      ...entry,
      [META_DATA_KEY]: {
        tagsString,
        tagsParsed,
        ...(entry.limit && { limitString })
      }
    }
  })
}

export default ItemSpecs
