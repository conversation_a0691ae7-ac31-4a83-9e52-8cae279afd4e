import EmailVerifiedModal from './EmailVerifiedModal.vue.js'
import * as Vue from 'vue';
import * as VueTestUtils from '@vue/test-utils'

describe('EmailVerifiedModal', () => {
  let wrapper
  beforeEach(() => {
    wrapper = VueTestUtils.mount(EmailVerifiedModal, {
      props: {
        visible: true
      }
    })
  })

  afterEach(() => {
    document.body.removeAttribute('style')
    document.body.removeAttribute('class')
  })

  it('renders modal', async () => {
    expect(document.querySelector('.emailVerifiedModal').innerHTML).toContain('Verification requirement')
  })

  it('emits the email verified on submit', () => {
    //click on the first radio button
    document.querySelector('.ant-radio-wrapper').click()

    //click on apply
    document.querySelector('.ant-modal .ant-btn-primary').click()

    expect(wrapper.emitted().submit[0][0].searchEmailVerified.filter).toEqual({
      searchEmailVerified: 'VERIFIED'
    })
  })

  it('emits the email unverified on submit', () => {
    //click on the first radio button
    const notVerifiedRadio = document.querySelectorAll('.ant-radio-wrapper')[1]
    notVerifiedRadio.click()

    //click on the apply button
    document.querySelector('.ant-modal .ant-btn-primary').click()

    expect(wrapper.emitted().submit[0][0].searchEmailVerified.filter).toEqual({
      searchEmailVerified: 'UNVERIFIED'
    })
  })

  it('shows error message when option is not selected', async () => {
    //click on the apply button
    document.querySelector('.ant-modal .ant-btn-primary').click()

    //emit to be empty
    expect(wrapper.emitted()).toEqual({})
    await Vue.nextTick()

    expect(document.querySelector('.ant-form-item-explain-error')).toBeTruthy()
  })
})
