import { utils } from '../../../../../main/utils.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import hooks from '../../../../../main/ui/hooks.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import SimpleCodeEditor from '../../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import CopyableCell from '../../../../../main/ui/components/CopyableCell/CopyableCell.vue.js'
import PopoverWithTable from '../PopoverWithTable/PopoverWithTable.vue.js'
import RewardsTableWithAdd from '../RewardsTableWithAdd/RewardsTableWithAdd.vue.js'
import { useRouter } from 'vue-router';

export const ENTRIES_TO_SHOW = 10
const ADD_KEY_PREFIX = '__slot_entry-to-add__'
const SLOT_ENTRIES = 'slotEntries'

const RewardSlots = {
  name: 'RewardSlots',

  props: {
    entries: Object,
    readOnly: Boolean,
    hideSource: Boolean,
    rewardTables: Array,
    rewardBags: Array
  },

  components: {
    Table,
    CatalogViewer,
    Icon,
    SimpleCodeEditor,
    CopyableCell,
    PopoverWithTable,
    RewardsTableWithAdd
  },

  setup(props) {
    const router = useRouter()
    const { getSearch } = hooks.useSearch(router)
    const search = getSearch()

    const sourceFiles = Object.keys(props.entries)

    const tableColumns = props.hideSource
      ? utils.removeFromArray(rewardSlotsColumns, 'key', 'source')
      : rewardSlotsColumns

    const filteredEntries = utils.filterItemsByText(
      formatEntries(props.entries),
      search,
      tableColumns.map(c => c.dataIndex)
    )

    const editableFields = tableColumns
      .map(column => column.dataIndex)
      .filter(key => key !== 'rewardSlotId' && key !== 'source')

    const updateRewardSlot = async data => {
      const file = data.source

      delete data.source

      data.id = data.rewardSlotId
      delete data.rewardSlotId

      // substract from 100 before dividing to avoid problems with floating point arithmetic
      data.noOpPercentage = (100 - data.chanceOfRoll) / 100
      delete data.chanceOfRoll

      const srcJson = utils.jsonStringify(
        utils.replaceInObjArray(props.entries[file], 'id', data.id, data)
      )

      const payload = {
        contentType: 'RewardSlots',
        filename: file,
        srcJson
      }

      updateContentCatalog(payload)
    }

    const addEntry = (entry, slotEntries, setData) => {
      setData(SLOT_ENTRIES, [
        ...slotEntries,
        {
          ...entry,
          rewardBagId: null,
          weight: null
        }
      ])
    }

    const saveEntry = (entry, slotEntries, setData) => {
      const entryToSave = { ...entry }
      const slotEntriesCopy = [...slotEntries]

      const indexOfEntry = slotEntries.findIndex(
        item => item.rewardBagId === entryToSave.key || item.key === entryToSave.key
      )

      delete entryToSave.key
      if (indexOfEntry >= 0) {
        slotEntriesCopy[indexOfEntry] = entryToSave
      }

      setData(SLOT_ENTRIES, slotEntriesCopy)
    }

    const deleteEntry = (entry, slotEntries, setData) => {
      const slotEntriesCopy = [...slotEntries]
      const isAddEntry = entry.key?.includes(ADD_KEY_PREFIX)
      const indexOfEntry = slotEntriesCopy.findIndex(item =>
        isAddEntry ? item.key === entry.key : item.rewardBagId === entry.rewardBagId
      )

      if (indexOfEntry >= 0) {
        slotEntriesCopy.splice(indexOfEntry, 1)
        setData(SLOT_ENTRIES, slotEntriesCopy)
      }
    }

    const updateRewardSlotRaw = async (rawData, rawSource) => {
      const payload = {
        contentType: 'RewardSlots',
        filename: rawSource,
        srcJson: rawData
      }

      updateContentCatalog(payload)
    }

    const deleteRewardSlot = async (slotId, file, close) => {
      const srcJson = utils.jsonStringify(utils.removeFromArray(props.entries[file], 'id', slotId))

      const payload = {
        contentType: 'RewardSlots',
        filename: file,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    const rewardBagIds = props.rewardBags?.map(r => r.id).sort()
    const autocompleteRewardBagIds = rewardBagIds?.map(r => ({ value: r }))

    return {
      tableColumns,
      filteredEntries,
      slotEntriesColumns,
      utils,
      ENTRIES_TO_SHOW,
      confirm,
      sourceFiles,
      updateRewardSlotRaw,
      updateRewardSlot,
      deleteRewardSlot,
      beforeDeletionWarning,
      addEntry,
      saveEntry,
      deleteEntry,
      editableFields,
      slotEntriesColumnsWithEdit,
      SLOT_ENTRIES,
      ADD_KEY_PREFIX,
      autocompleteRewardBagIds
    }
  },

  template: `
    <div class="rewards-wrap">
      <CatalogViewer
        :rawData="$props.entries"
        :rawSources="sourceFiles"
        :allowRawEdit="!$props.readOnly"
        :rawEditOnSave="updateRewardSlotRaw"
        :allowRichEdit="!$props.readOnly ? editableFields : false"
        :richEditSaveSuccess="updateRewardSlot"
        :formatFields="{chanceOfRoll: 'percentage'}"
      >
        <template #richView>
          <Table
            emptyText="No entries have been found"
            :columns="tableColumns"
            :data="filteredEntries"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'rewardSlotId'">
                <router-link :to="{ name: !$props.readOnly ? 'EditRewardSlot' : 'ViewRewardSlot', params: { rewardSlotId: record.id }}">{{ text }}</router-link>
              </template>

              <template v-if="column.dataIndex === 'slotEntries'">
                <PopoverWithTable
                  :records="record.slotEntries"
                  :columns="slotEntriesColumns"
                  :previewFields="{main: 'rewardBagId'}"
                />
              </template>

              <template v-if="column.dataIndex === 'chanceOfRoll'">
                {{text + '%'}}
              </template>

              <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
                <a-button size='small' ghost danger class='hide actionBtn'
                  @click='confirm.danger({
                    title: "Confirm Reward Deletion",
                    content: "Are you sure you want to delete " + record.id + "?" + beforeDeletionWarning(record.id, $props.rewardTables),
                    okText: "Delete",
                    onOk: close => { deleteRewardSlot(record.id, record.source, close) }
                  })'
                >
                  <Icon name='close' size="10" /><span>Delete</span>
                </a-button>
              </template>
            </template>
          </Table>
        </template>

        <template #slotEntries="{isEditMode, data, setData}">
          <RewardsTableWithAdd
            v-if="isEditMode"
            :addPrefix="ADD_KEY_PREFIX"
            :columns="slotEntriesColumnsWithEdit"
            :items="data.slotEntries"
            :allRewardItemsOptions="autocompleteRewardBagIds"
            idKey="rewardBagId"
            addButtonText="Add Reward Bag"
            itemPlaceholder="Reward Bag Id"
            @addEntry="(entry) => addEntry(entry, data.slotEntries, setData)"
            @saveEntry="(entry) => saveEntry(entry, data.slotEntries, setData)"
            @deleteEntry="(entry) => deleteEntry(entry, data.slotEntries, setData)"
          />
          <Table
            v-if="!isEditMode"
            :columns="slotEntriesColumns"
            :data="data.slotEntries"
            :pagination="false"
            size="small"
          />
        </template>
      </CatalogViewer>
    </div>
  `
}

const rewardSlotsColumns = [
  {
    title: 'Reward Slot Id',
    dataIndex: 'rewardSlotId',
    key: 'rewardSlotId',
    defaultSortOrder: 'ascend',
    sorter: true
  },
  {
    title: 'Chance Of Roll',
    dataIndex: 'chanceOfRoll',
    key: 'chanceOfRoll'
  },
  {
    title: 'Slot Entries',
    dataIndex: 'slotEntries',
    key: 'slotEntries'
  },
  {
    title: 'Source',
    dataIndex: 'source',
    key: 'source'
  },
  {
    dataIndex: 'delete',
    align: 'right'
  }
]

const slotEntriesColumns = [
  {
    title: 'Reward Bag Id',
    dataIndex: 'rewardBagId',
    key: 'rewardBagId'
  },
  {
    title: 'Weight',
    dataIndex: 'weight',
    key: 'weight'
  }
]

const slotEntriesColumnsWithEdit = [...slotEntriesColumns, { dataIndex: 'edit', key: 'edit' }]

const formatEntries = entries => {
  return helpersAPI.concatenateContentCatalogSources(entries).map(entry => ({
    ...entry,
    rewardSlotId: entry.id,
    chanceOfRoll: 100 - entry.noOpPercentage * 100
  }))
}

const beforeDeletionWarning = (id, rewardTables) => {
  const foundIn = []
  rewardTables.forEach(table => {
    if (table.rewardSlotIds.find(slot => slot === id)) foundIn.push(table.id)
  })
  return foundIn.length
    ? ` This slot is also found in ${foundIn.length > 1 ? 'tables' : 'table'}: ${foundIn.join(
        ', '
      )}`
    : ''
}

export default RewardSlots
