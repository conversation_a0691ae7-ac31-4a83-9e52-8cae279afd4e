import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import { utils } from '../../../../../main/utils.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import SimpleCodeEditor from '../../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import PageSection from '../../../../../main/ui/components/PageSection/PageSection.vue.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import PopoverWithTable from '../PopoverWithTable/PopoverWithTable.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import RewardsTableWithAdd from '../RewardsTableWithAdd/RewardsTableWithAdd.vue.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import { useRoute, useRouter } from 'vue-router';
import * as Vue from 'vue';

const ADD_KEY_PREFIX = '__reward-bag-id-to-add__'

const CreateEditRewardSlot = {
  name: 'CreateEditRewardSlot',
  components: {
    Icon,
    SimpleCodeEditor,
    PageSection,
    CatalogViewer,
    PopoverWithTable,
    RewardsTableWithAdd,
    Table,
    InfoParagraph
  },
  props: {
    entries: Object,
    table: Object,
    rewardSlots: Array,
    rewardBags: Array,
    rewardTables: Array,
    readOnly: Boolean,
    isCreateMode: Boolean,
    selectedRewardSlotId: String
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    const currentPage = route.name

    const errorMessages = Vue.reactive({})
    const sourceFiles = Object.keys(props.entries)
    const searchInput = ''

    const noRewardBagMessage = "You don't have any reward bag yet"
    const noSearchResultMessage = 'No Search Results'

    const rewardTableWithAddLocal = { emptyText: noRewardBagMessage }

    const retrieveAllRewardTableBagsOptions = () => {
      return props.rewardBags.map(rewardBag => {
        return {
          value: `${rewardBag.id} (${rewardBag.name})`
        }
      })
    }

    const formState = Vue.reactive({
      source: sourceFiles[0],
      chanceOfRoll: 100,
      slotId: '',
      rewardTableBags: [],
      allRewardTableBags: [],
      rewardBagItemsOptions: retrieveAllRewardTableBagsOptions()
    })

    const retriveRewardTableBags = props => {
      return props.rewardSlots
        ?.find(rewardSlot => rewardSlot.id === props.selectedRewardSlotId)
        ?.slotEntries?.map(slotEntry => {
          const rewardBag = props.rewardBags?.find(
            rewardBag => rewardBag.id === slotEntry.rewardBagId
          )
          return {
            name: rewardBag.name,
            rewardBagId: slotEntry.rewardBagId,
            weight: slotEntry.weight,
            key: utils.getRandomString()
          }
        })
    }

    if (!props.isCreateMode) {
      formState.rewardTableBags = retriveRewardTableBags(props)
      formState.allRewardTableBags = formState.rewardTableBags
      formState.slotId = props.selectedRewardSlotId
    }

    const validateOptionIdValue = (value, groupValue) => {
      const groupValueTrimed = groupValue.trim()
      return !!value.match(new RegExp(`^(${groupValueTrimed})\\ \\((.*)\\)$`, 'g'))
    }
    const validateOptionNameValue = (value, groupValue) => {
      const groupValueTrimed = groupValue.trim()
      return !!value.match(new RegExp(`^(.*)\\ \\((${groupValueTrimed})\\)$`, 'g'))
    }

    const validateTableRow = (
      errorMessages,
      itemToSave,
      props,
      allTableRewardItemsOptions,
      items
    ) => {
      errorMessages[itemToSave.key] = {}

      if (!itemToSave['name']) errorMessages[itemToSave.key]['name'] = 'The name does not exist'
      else {
        if (
          !allTableRewardItemsOptions.find(itemOptionValue => {
            if (!!itemToSave['name'] && itemOptionValue.includes(itemToSave['name'])) {
              return validateOptionNameValue(itemOptionValue, itemToSave['name'])
            }
            return false
          })
        ) {
          errorMessages[itemToSave.key]['name'] = 'The name is invalid'
        }
      }

      if (!itemToSave[props.idKey])
        errorMessages[itemToSave.key][props.idKey] = 'The id does not exist'
      else {
        if (
          !allTableRewardItemsOptions.find(itemOptionValue => {
            if (!!itemToSave[props.idKey] && itemOptionValue.includes(itemToSave[props.idKey])) {
              return validateOptionIdValue(itemOptionValue, itemToSave[props.idKey])
            }
            return false
          })
        ) {
          errorMessages[itemToSave.key][props.idKey] = 'The id is invalid'
        }
      }

      if (
        !props.skipUniqueIds &&
        items.value.find(
          item => item[props.idKey] == itemToSave[props.idKey] && item.key !== itemToSave.key
        )
      )
        errorMessages[itemToSave.key][props.idKey] = 'The id already exists'

      if (!props.skipWeightValidation && !itemToSave.weight)
        errorMessages[itemToSave.key].weight = 'Required'

      if (Object.keys(errorMessages[itemToSave.key]).length == 0)
        delete errorMessages[itemToSave.key]
    }

    const searchRewardTableBags = searchInput => {
      if (!searchInput) {
        rewardTableWithAddLocal.emptyText = noRewardBagMessage
        formState.rewardTableBags = formState.allRewardTableBags
      } else {
        rewardTableWithAddLocal.emptyText = noSearchResultMessage
        formState.rewardTableBags = formState.allRewardTableBags
        formState.rewardTableBags = formState.rewardTableBags.filter(
          rewardTableBag =>
            rewardTableBag.rewardBagId?.toLowerCase().includes(searchInput.toLowerCase()) ||
            !rewardTableBag.rewardBagId
        )
      }
    }

    const notUniqueId = id => {
      let notUnique = false
      Object.values(props.entries).forEach(items => {
        items.forEach(item => {
          if (item.id == id) notUnique = true
        })
      })
      return notUnique
    }

    const onSubmitCreateEditRewardSlot = () => {
      helpersAPI.resetErrorMessages(errorMessages)

      if (!formState.chanceOfRoll) errorMessages.chanceOfRoll = 'Chance of Roll is required.'
      if (!formState.slotId) errorMessages.slotId = 'Slot id is required.'

      if (!props.table && props.isCreateMode && notUniqueId(formState.slotId))
        errorMessages.slotId = 'This id is already in use'
      if (!formState.source) errorMessages.source = 'Source file is required'

      if (formState.rewardTableBags.find(entry => entry.key.startsWith(ADD_KEY_PREFIX))) {
        toast.error('You have unsaved changes')
        return
      }

      if (Object.keys(errorMessages).length !== 0) return

      const fileContent = props.entries[formState.source]

      const currentContentObject = fileContent.find(
        fileContentItem => fileContentItem.id === formState.slotId
      )

      const updateContentObject = {
        ...currentContentObject,

        id: formState.slotId,
        noOpPercentage: (100 - formState.chanceOfRoll) / 100,
        slotEntries: formState.allRewardTableBags.map(rewardTableBag => {
          return {
            rewardBagId: rewardTableBag.rewardBagId,
            weight: rewardTableBag.weight
          }
        })
      }

      if (currentContentObject) {
        fileContent.splice(fileContent.indexOf(currentContentObject), 1, updateContentObject)
      } else {
        fileContent.push(updateContentObject)
      }

      const srcJson = utils.jsonStringify(fileContent)

      const payload = {
        contentType: 'RewardSlots',
        filename: formState.source,
        srcJson
      }

      updateContentCatalog(payload)
      helpersAPI.goBackToPage({ name: 'RewardSlotsEditor', router })
    }

    useSubscribe('submitCreateEditRewardSlot', onSubmitCreateEditRewardSlot)

    const saveEntry = entry => {
      const index = formState.rewardTableBags.findIndex(item => item.key === entry.key)
      if (index >= 0) {
        entry.key = utils.getRandomString()
        formState.rewardTableBags[index] = entry
        formState.allRewardTableBags[index] = entry
      }
    }

    const deleteEntry = entry => {
      const indexOfEntry = formState.rewardTableBags.findIndex(item => item.key === entry.key)
      if (indexOfEntry >= 0) {
        formState.rewardTableBags.splice(indexOfEntry, 1)
      }
    }

    const selectEntry = (value, editableData, recordKey) => {
      const valueMatches = value.match('^(.*) \\((.*)\\)$')
      editableData[recordKey]['name'] = valueMatches[2]
      editableData[recordKey]['rewardBagId'] = valueMatches[1]
    }

    const addEntry = entry => {
      formState.rewardTableBags = [
        ...formState.rewardTableBags,
        {
          ...entry,
          name: null,
          rewardBagId: null,
          weight: null
        }
      ]
    }

    const { percentageFormatter, percentageParser } = utils

    let selectedRewardTables = []
    let usageWarning = ''
    if (!props.isCreateMode && !props.readOnly) {
      const getListOfEntitiesInWichSelectedIdExists = selectedId => {
        return props.rewardTables
          .filter(
            rewardTable =>
              rewardTable.rewardSlotIds.find(rewardSlotId => rewardSlotId == selectedId) !==
              undefined
          )
          ?.map(rewardTable => rewardTable.name)
      }

      selectedRewardTables = getListOfEntitiesInWichSelectedIdExists(props.selectedRewardSlotId)
      usageWarning = `${props.selectedRewardSlotId} is used in the the following Reward
          ${selectedRewardTables.length > 1 ? 'Tables' : 'Table'}:
          ${selectedRewardTables}.`
    }

    const getRouterParams = record => {
      return {
        rewardBagId: record.rewardBagId,
        rewardSlotId: route.params.rewardSlotId,
        rewardTableId: route.params.rewardTableId
      }
    }

    return {
      utils,
      formState,
      errorMessages,
      validateTableRow,
      addEntry,
      saveEntry,
      deleteEntry,
      selectEntry,
      ADD_KEY_PREFIX,
      searchInput,
      searchRewardTableBags,
      rewardBagsColumns,
      rewardBagsColumnsWithAdd,
      percentageFormatter,
      percentageParser,
      rewardTableWithAddLocal,
      usageWarning,
      selectedRewardTables,
      currentPage,
      getRouterParams
    }
  },
  template: `
    <div>
      <a-row :gutter="[32, 30]">
        <a-col :span="24">
          <a-alert v-if="!$props.isCreateMode && !$props.readOnly && (selectedRewardTables.length > 0)" :message="usageWarning" type="info" show-icon />
          <PageSection
            title="Rewards Bags"
            :count="formState.allRewardTableBags.length"
          >
          <a-row :gutter="[32, 16]" :gap="24">
            <!-- TODO: As we discussed the requirements this search component will be hidden until it will be needed -->
            <!-- <a-col :span="24">
              <a-input-search
                allowClear
                class="searchTableInput"
                :defaultValue='searchInput'
                placeholder="Search In Rewards Bags"
                enter-button="Search"
                @search="searchRewardTableBags"
              >
                <template #prefix> <Icon name="search" /> </template>
              </a-input-search>
            </a-col> -->
            <a-col :span="24">
              <RewardsTableWithAdd
                v-if="$props.readOnly"
                :addPrefix="ADD_KEY_PREFIX"
                :columns="rewardBagsColumns"
                :items="formState.rewardTableBags"
                :allRewardItemsOptions="formState.rewardBagItemsOptions"
                :validateRow="validateTableRow"
                :tableLocale="rewardTableWithAddLocal"
                addButtonText=""
                idKey="rewardBagId"
                :currentPage="currentPage"
                routerColumn="name"
                :getRouterParams="getRouterParams"
                @addEntry="(entry) => addEntry(entry)"
                @saveEntry="(entry) => saveEntry(entry)"
                @deleteEntry="(entry) => deleteEntry(entry)"
                @selectEntry="(entry, editableData, recordKey) => selectEntry(entry, editableData, recordKey)"
              />
              <RewardsTableWithAdd
                v-else
                :addPrefix="ADD_KEY_PREFIX"
                :columns="rewardBagsColumnsWithAdd"
                :items="formState.rewardTableBags"
                :allRewardItemsOptions="formState.rewardBagItemsOptions"
                :validateRow="validateTableRow"
                :tableLocale="rewardTableWithAddLocal"
                addButtonText="Add Reward Bag"
                idKey="rewardBagId"
                :currentPage="currentPage"
                routerColumn="name"
                :isCreateMode="$props.isCreateMode"
                :getRouterParams="getRouterParams"
                @addEntry="(entry) => addEntry(entry)"
                @saveEntry="(entry) => saveEntry(entry)"
                @deleteEntry="(entry) => deleteEntry(entry)"
                @selectEntry="(entry, editableData, recordKey) => selectEntry(entry, editableData, recordKey)"
              />
            </a-col>
          </a-row>
          </PageSection>
        </a-col>
        <a-col :span="24">
          <a-form layout='vertical' :model="formState" class="info-form">
            <PageSection title="Other Details">
              <a-row :gutter="32" class="create-entry-row">
                <a-col :span="12">
                  <a-form-item
                    label="Reward Slot Id"
                    name="slotId"
                    class="required"
                    :help="errorMessages.slotId"
                    :validateStatus="errorMessages.slotId && 'error' || ''"
                  >

                    <a-input v-if="$props.isCreateMode"  v-model:value='formState.slotId'/>
                    <a-input v-else v-model:value="$props.selectedRewardSlotId" :disabled='true'/>
                    <InfoParagraph v-if="!$props.readOnly" style="padding-bottom: 20px; display: block" infoText="Once the reward table is created the Id cannot be changed"/>
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item
                    label="Chance of Roll"
                    name="chanceOfRoll"
                    class="required"
                    :help="errorMessages.chanceOfRoll"
                    :disabled="$props.readOnly"
                    :validateStatus="errorMessages.chanceOfRoll && 'error' || ''"
                    >
                    <a-input-number
                        v-model:value="formState.chanceOfRoll"
                        default="100"
                        min="0"
                        max="100"
                        :formatter="percentageFormatter"
                        :parser="percentageParser"
                        :disabled="$props.readOnly"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </PageSection>
          </a-form>
        </a-col>
      </a-row>
    </div>
  `
}

const rewardBagsColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    defaultSortOrder: 'ascend',
    sorter: true
  },
  {
    title: 'Reward Bag Id',
    dataIndex: 'rewardBagId',
    key: 'rewardBagId'
  },
  {
    title: 'Weight',
    dataIndex: 'weight',
    key: 'weight'
  }
]

const rewardBagsColumnsWithAdd = [...rewardBagsColumns, { dataIndex: 'edit', key: 'edit' }]

export default CreateEditRewardSlot
