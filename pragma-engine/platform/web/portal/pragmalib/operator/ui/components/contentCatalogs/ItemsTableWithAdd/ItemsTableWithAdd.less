.items-editable-table {
  hr {
    margin: @marginXS*1px 0 @marginSM*1px 0;
    height: 1px;
    background-color: @colorBorderSecondary;
    border: none;
  }
  .items-editable-table-actions {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    gap: @paddingXXS*1px;

    a {
      display: inline-block;
      padding: @paddingXXS*1px;
      text-wrap: nowrap;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  table {
    td:last-child {
      width: 175px;
      min-width: 175px;
    }
    .ant-select {
      min-width: 100%;
    }
    td:first-child,
    th:first-child {
      width: 50%;
    }
    td:nth-child(2),
    th:nth-child(2) {
      width: 22%;
    }
  }
  .table-cell-inner {
    display: block;
  }
  .ant-table-cell {
    vertical-align: top;

    .ant-form-item-explain > * {
      line-height: 1.2;
    }
  }
  .ant-form-item {
    margin-bottom: 0;
  }
  .ant-form-item,
  .ant-form-item-control {
    flex-flow: row wrap;

    > * {
      width: 100%;
    }
  }
  > .ant-btn {
    margin: @marginSM*1px 0px;
  }
  .table-cell-inner {
    > .ant-row.ant-form-item {
      margin-top: @marginXXS/-2px;

      .ant-form-item-explain-error {
        margin: @marginXXS*1px 0 @marginXXS*-1px;
      }
    }
  }
}