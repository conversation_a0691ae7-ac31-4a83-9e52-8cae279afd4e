import { utils } from '../../../../../main/utils.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import hooks from '../../../../../main/ui/hooks.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import SimpleCodeEditor from '../../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import CopyableCell from '../../../../../main/ui/components/CopyableCell/CopyableCell.vue.js'
import RewardsTableWithAdd from '../RewardsTableWithAdd/RewardsTableWithAdd.vue.js'
import { calculateRewardsChances } from './rewardsCalculations.js'
import { useRouter } from 'vue-router';

export const ENTRIES_TO_SHOW = 10
const ADD_KEY_PREFIX = '__reward-slot-id-to-add__'
const REWARD_SLOT_IDS = 'rewardSlotIds'

const RewardTables = {
  name: 'RewardTables',

  props: {
    entries: Object,
    rewards: Array,
    rewardSlots: Array,
    rewardBags: Array,
    readOnly: Boolean,
    hideSource: Boolean
  },

  components: {
    Table,
    CatalogViewer,
    Icon,
    SimpleCodeEditor,
    CopyableCell,
    RewardsTableWithAdd
  },

  setup(props) {
    const router = useRouter()
    const { getSearch } = hooks.useSearch(router)
    const search = getSearch()

    const sourceFiles = Object.keys(props.entries)

    const tableColumns = props.hideSource
      ? utils.removeFromArray(rewardTablesColumns, 'key', 'source')
      : rewardTablesColumns

    const formattedEntries = formatEntries(
      props.entries,
      props.rewards,
      props.rewardSlots,
      props.rewardBags
    )

    const filteredEntries = utils.filterItemsByText(
      formattedEntries,
      search,
      tableColumns.map(c => c.dataIndex)
    )

    const editableFields = tableColumns
      .map(column => column.dataIndex)
      .filter(key => key !== 'rewardTableId' && key !== 'source')

    const updateRewardTable = async data => {
      const file = data.source

      delete data.source
      delete data.rewardChances

      data.id = data.rewardTableId
      delete data.rewardTableId

      const mappedSourcesIds = data.rewardSlotIds.map(r => r.rewardSlotId)
      data.rewardSlotIds = mappedSourcesIds

      const srcJson = utils.jsonStringify(
        utils.replaceInObjArray(props.entries[file], 'id', data.id, data)
      )

      const payload = {
        contentType: 'RewardTables',
        filename: file,
        srcJson
      }

      updateContentCatalog(payload)
    }

    const updateRewardTableRaw = async (rawData, rawSource) => {
      const payload = {
        contentType: 'RewardTables',
        filename: rawSource,
        srcJson: rawData
      }

      updateContentCatalog(payload)
    }

    const deleteRewardTable = async (tableId, file, close) => {
      const srcJson = utils.jsonStringify(utils.removeFromArray(props.entries[file], 'id', tableId))

      const payload = {
        contentType: 'RewardTables',
        filename: file,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    const rewardIds = props.rewards?.map(r => r.id).sort()
    const rewardSlotIds = props.rewardSlots?.map(r => r.id).sort()
    const autocompleteRewardSlotIds = rewardSlotIds?.map(r => ({ value: r }))

    const addEntry = (entry, rewardSlotIds, setData) => {
      setData(REWARD_SLOT_IDS, [
        ...rewardSlotIds,
        {
          ...entry,
          rewardSlotId: null
        }
      ])
    }

    const saveEntry = (entry, rewardSlotIds, setData) => {
      const entryToSave = { ...entry }
      const rewardSlotIdsCopy = [...rewardSlotIds]

      const indexOfEntry = rewardSlotIds.findIndex(
        item => item.rewardSlotId === entryToSave.key || item.key === entryToSave.key
      )

      delete entryToSave.key
      if (indexOfEntry >= 0) {
        rewardSlotIdsCopy[indexOfEntry] = entryToSave
      }

      setData(REWARD_SLOT_IDS, rewardSlotIdsCopy)
    }

    const deleteEntry = (entry, rewardSlotIds, setData) => {
      const rewardSlotIdsCopy = [...rewardSlotIds]
      const isAddEntry = entry.key?.includes(ADD_KEY_PREFIX)
      const indexOfEntry = rewardSlotIdsCopy.findIndex(item =>
        isAddEntry ? item.key === entry.key : item.rewardSlotId === entry.rewardSlotId
      )

      if (indexOfEntry >= 0) {
        rewardSlotIdsCopy.splice(indexOfEntry, 1)
        setData(REWARD_SLOT_IDS, rewardSlotIdsCopy)
      }
    }

    return {
      tableColumns,
      filteredEntries,
      utils,
      ENTRIES_TO_SHOW,
      confirm,
      sourceFiles,
      updateRewardTableRaw,
      updateRewardTable,
      deleteRewardTable,
      rewardSlotIdsColumns,
      rewardSlotIdsColumnsWithEdit,
      editableFields,
      rewardIds,
      rewardSlotIds,
      autocompleteRewardSlotIds,
      rewardChancesColumns,
      addEntry,
      saveEntry,
      deleteEntry,
      REWARD_SLOT_IDS,
      ADD_KEY_PREFIX,
      GUARANTEED
    }
  },

  template: `
    <div class="rewards-wrap">
      <CatalogViewer
        :rawData="$props.entries"
        :rawSources="sourceFiles"
        :allowRawEdit="!$props.readOnly"
        :rawEditOnSave="updateRewardTableRaw"
        :allowRichEdit="!$props.readOnly ? editableFields : false"
        :richEditSaveSuccess="updateRewardTable"
      >
        <template #richView>
          <Table
            emptyText="No entries have been found"
            :columns="tableColumns"
            :data="filteredEntries"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'name'">
                <router-link :to="{ name: !$props.readOnly ? 'EditRewardTable' : 'ViewRewardTable', params: { rewardTableId: record.id }}">{{ text }}</router-link>
              </template>

              <template v-if="column.dataIndex === 'rewardSlotIds'">
                <a-popover v-if="record.rewardSlotIds?.length > 0">
                  <template #content>
                    <a-typography-title :level="5" class="rewards-entries-title">Reward Slot Ids</a-typography-title>
                    <ul class="rewards-entries-list">
                      <li v-for="slotEntry in record.rewardSlotIds.slice(0,ENTRIES_TO_SHOW)">{{slotEntry.rewardSlotId}}</li>
                      <li v-if="record.rewardSlotIds.length > ENTRIES_TO_SHOW" class="entries-other">
                        + {{record.rewardSlotIds.length - ENTRIES_TO_SHOW}} other{{(record.rewardSlotIds.length - ENTRIES_TO_SHOW) > 1 ? 's' : ''}}
                      </li>
                    </ul>
                  </template>
                  {{record.rewardSlotIds[0].rewardSlotId}} <span v-if="record.rewardSlotIds.length - 1 > 0" class="entries-more">+ {{record.rewardSlotIds.length - 1}} other{{(record.rewardSlotIds.length - 1) > 1 ? 's' : ''}}</span>
                </a-popover>
              </template>

              <template v-if="column.dataIndex === 'guaranteedRewardIds'">
                <a-popover v-if="record.guaranteedRewardIds?.length > 0">
                  <template #content>
                    <a-typography-title :level="5" class="rewards-entries-title">Guaranteed Reward Ids</a-typography-title>
                    <ul class="rewards-entries-list">
                      <li v-for="guaranteedReward in record.guaranteedRewardIds.slice(0,ENTRIES_TO_SHOW)">{{guaranteedReward}}</li>
                      <li v-if="record.guaranteedRewardIds.length > ENTRIES_TO_SHOW" class="entries-other">
                        + {{record.guaranteedRewardIds.length - ENTRIES_TO_SHOW}} other{{(record.guaranteedRewardIds.length - ENTRIES_TO_SHOW) > 1 ? 's' : ''}}
                      </li>
                    </ul>
                  </template>
                  {{record.guaranteedRewardIds[0]}} <span v-if="record.guaranteedRewardIds.length - 1 > 0" class="entries-more">+ {{record.guaranteedRewardIds.length - 1}} other{{(record.guaranteedRewardIds.length - 1) > 1 ? 's' : ''}}</span>
                </a-popover>
              </template>

              <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
                <a-button size='small' ghost danger class='hide actionBtn'
                  @click='confirm.danger({
                    title: "Confirm Reward Deletion",
                    content: "Are you sure you want to delete " + record.id + "?",
                    okText: "Delete",
                    onOk: close => { deleteRewardTable(record.id, record.source, close) }
                  })'
                >
                  <Icon name='close' size="10" /><span>Delete</span>
                </a-button>
              </template>
            </template>
          </Table>
        </template>

        <template #rewardSlotIds="{isEditMode, data, setData}">
          <RewardsTableWithAdd
            v-if="isEditMode"
            :addPrefix="ADD_KEY_PREFIX"
            :columns="rewardSlotIdsColumnsWithEdit"
            :items="data.rewardSlotIds"
            :allRewardItemsOptions="autocompleteRewardSlotIds"
            idKey="rewardSlotId"
            addButtonText="Add Reward Slot"
            itemPlaceholder="Reward Slot Id"
            skipWeightValidation
            skipUniqueIds
            @addEntry="(entry) => addEntry(entry, data.rewardSlotIds, setData)"
            @saveEntry="(entry) => saveEntry(entry, data.rewardSlotIds, setData)"
            @deleteEntry="(entry) => deleteEntry(entry, data.rewardSlotIds, setData)"
          />
          <Table
            v-if="!isEditMode"
            :columns="rewardSlotIdsColumns"
            :data="data.rewardSlotIds"
            :pagination="false"
            size="small"
          />
        </template>

        <template #rewardChances="{data}">
          <Table
            :columns="rewardChancesColumns"
            :data="data.rewardChances"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'chance'">
                  <span>{{text === GUARANTEED ? text : text + '%'}}</span>
              </template>
            </template>
          </Table>
        </template>
      </CatalogViewer>
    </div>
  `
}

const rewardTablesColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    defaultSortOrder: 'ascend',
    sorter: true
  },
  {
    title: 'Reward Table Id',
    dataIndex: 'rewardTableId',
    key: 'rewardTableId',
    sorter: true
  },
  {
    title: 'Reward Slot Ids',
    dataIndex: 'rewardSlotIds',
    key: 'rewardSlotIds'
  },
  {
    title: 'Guaranteed Reward Ids',
    dataIndex: 'guaranteedRewardIds',
    key: 'guaranteedRewardIds'
  },
  {
    title: 'Source',
    dataIndex: 'source',
    key: 'source'
  },
  {
    dataIndex: 'delete',
    align: 'right'
  }
]

const rewardChancesColumns = [
  {
    title: 'Reward Id',
    dataIndex: 'rewardId',
    key: 'rewardId'
  },
  {
    title: 'Chance',
    dataIndex: 'chance',
    key: 'chance',
    defaultSortOrder: 'descend'
  },
  {
    title: 'Min',
    dataIndex: 'min',
    key: 'min'
  },
  {
    title: 'Max',
    dataIndex: 'max',
    key: 'max'
  }
]

const rewardSlotIdsColumns = [
  {
    title: 'Reward Slot Id',
    dataIndex: 'rewardSlotId',
    key: 'rewardSlotId'
  }
]

const rewardSlotIdsColumnsWithEdit = [...rewardSlotIdsColumns, { dataIndex: 'edit', key: 'edit' }]

const GUARANTEED = 'Guaranteed'
const addGuaranteedRewardsToChances = (table, rewards, chances) => {
  table.guaranteedRewardIds.forEach(r => {
    const reward = rewards.find(rew => rew.id == r)
    chances.push({
      rewardId: r,
      chance: GUARANTEED,
      min: reward?.count?.min,
      max: reward?.count?.max
    })
  })
  return chances
}

const buildRewardsForDisplay = (calculatedRewards, rewards) => {
  return Object.entries(calculatedRewards).map(([rewardId, chance]) => {
    const theReward = rewards.find(r => r.id === rewardId)
    return {
      rewardId,
      chance: chance.toFixed(2),
      min: theReward?.count?.min,
      max: theReward?.count?.max
    }
  })
}

const formatEntries = (entries, rewards, rewardSlots, rewardBags) => {
  return helpersAPI.concatenateContentCatalogSources(entries).map(entry => {
    const calculatedRewards = calculateRewardsChances(rewardSlots, rewardBags, entry)

    return {
      ...entry,
      rewardTableId: entry.id,
      rewardSlotIds: entry.rewardSlotIds.map(rew => ({ rewardSlotId: rew })),
      rewardChances: addGuaranteedRewardsToChances(
        entry,
        rewards,
        buildRewardsForDisplay(calculatedRewards, rewards)
      )
    }
  })
}

export default RewardTables
