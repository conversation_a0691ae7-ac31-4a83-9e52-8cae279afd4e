import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { ENTRIES_TO_SHOW } from '../Stores/Stores.vue.js'
import * as Vue from 'vue';

const PopoverWithTable = {
  name: 'PopoverWithTable',
  props: {
    records: Array,
    columns: Array,
    previewFields: Object
  },
  setup(props) {
    const recordsToShow = Vue.computed(() => props.records.slice(0, ENTRIES_TO_SHOW))
    const hasRecords = Vue.computed(() => props.records.length > 0)

    const firstEntryLabel = Vue.computed(() => {
      const previewMain =
        props.records && props.records[0] && props.records[0][props.previewFields?.main]
      const previewExtra = props.previewFields?.extra
        ? ` (${props.records[0] && props.records[0][props.previewFields?.extra]})`
        : ''
      return hasRecords ? `${previewMain} ${previewExtra}` : ''
    })

    const firstEntryPlusMoreLabel = Vue.computed(() => {
      const extraCounter = props.records.length - 1
      const suffix = extraCounter > 1 ? 's' : ''
      return `+ ${extraCounter} other${suffix}`
    })

    return {
      recordsToShow,
      hasRecords,
      firstEntryLabel,
      firstEntryPlusMoreLabel,
      ENTRIES_TO_SHOW
    }
  },
  template: `
    <a-popover>
      <template #content>
        <a-table
          :locale="{emptyText: 'No Data'}"
          :columns="$props.columns"
          :data-source="recordsToShow"
          :pagination="false"
          size="small"
        >
        </a-table>
        <span v-if="records.length > ENTRIES_TO_SHOW" class="entries-other">
          + {{records.length - ENTRIES_TO_SHOW}} more
        </span>
      </template>
      <span v-if="hasRecords">
        {{ firstEntryLabel }}
        <span v-if="records.length > 1" class='entries-more'>
          {{ firstEntryPlusMoreLabel }}
        </span>
      </span>
    </a-popover>
  `
}

export const formatCosts = (costs, allItems) => {
  if (!costs) return []
  return Object.keys(costs).map(catalogId => {
    return {
      name: helpersAPI.getCatalogNameByCatalogId(allItems, catalogId),
      catalogId: catalogId,
      cost: costs[catalogId].cost
    }
  })
}

export const formatReceivedQuantities = (quantitiesByCatalogId, allItems) => {
  return Object.keys(quantitiesByCatalogId).map(catalogId => {
    return {
      name: helpersAPI.getCatalogNameByCatalogId(allItems, catalogId),
      catalogId: catalogId,
      quantity: quantitiesByCatalogId[catalogId]
    }
  })
}

export default PopoverWithTable
