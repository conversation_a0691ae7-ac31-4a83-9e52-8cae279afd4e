import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import { utils } from '../../../../../main/utils.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import SimpleCodeEditor from '../../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import PageSection from '../../../../../main/ui/components/PageSection/PageSection.vue.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import PopoverWithTable from '../PopoverWithTable/PopoverWithTable.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import RewardsTableWithAdd from '../RewardsTableWithAdd/RewardsTableWithAdd.vue.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import { useRoute, useRouter } from 'vue-router';
import * as Vue from 'vue';

const ADD_KEY_PREFIX = '__reward-id-to-add__'

const CreateEditRewardBag = {
  name: 'CreateEditRewardBag',
  components: {
    Icon,
    SimpleCodeEditor,
    PageSection,
    CatalogViewer,
    PopoverWithTable,
    RewardsTableWithAdd,
    Table,
    InfoParagraph
  },
  props: {
    entries: Object,
    table: Object,
    rewards: Array,
    rewardBags: Array,
    rewardSlots: Array,
    readOnly: Boolean,
    isCreateMode: Boolean,
    selectedRewardBagId: String
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    const currentPage = route.name

    const errorMessages = Vue.reactive({})
    const sourceFiles = Object.keys(props.entries)
    const searchInput = ''

    const noRewardMessage = "You don't have any reward yet"
    const noSearchResultMessage = 'No Search Results'

    const rewardTableWithAddLocal = { emptyText: noRewardMessage }

    const retrieveAllRewardTableOptions = () => {
      return Array.from(
        new Set(
          props.rewards.map(reward => {
            return { value: typeof reward.id === 'string' ? reward.id : String(reward.id) }
          })
        )
      )
    }

    const formState = Vue.reactive({
      source: sourceFiles[0],
      bagId: '',
      rewardBagName: '',
      tableRewards: [],
      allTableRewards: [],
      rewardItemsOptions: retrieveAllRewardTableOptions()
    })

    const retriveTableRewards = props => {
      return props.rewardBags
        ?.find(rewardBag => rewardBag.id === props.selectedRewardBagId)
        ?.rewards.map(reward => {
          return {
            rewardId: reward.rewardId,
            weight: reward.weight,
            key: utils.getRandomString()
          }
        })
    }

    if (!props.isCreateMode) {
      formState.tableRewards = retriveTableRewards(props)
      formState.allTableRewards = formState.tableRewards
      formState.bagId = props.selectedRewardBagId
      formState.rewardBagName = props.rewardBags.find(
        rewardBag => rewardBag.id === props.selectedRewardBagId
      )?.name
    }

    const validateTableRow = (
      errorMessages,
      itemToSave,
      props,
      allTableRewardItemsOptions,
      items
    ) => {
      errorMessages[itemToSave.key] = {}

      if (
        !allTableRewardItemsOptions.find(
          allRewardItem => allRewardItem == itemToSave[props.idKey]
        ) ||
        !itemToSave[props.idKey]
      )
        errorMessages[itemToSave.key][props.idKey] = 'The id does not exist'

      if (
        !props.skipUniqueIds &&
        items.value.find(
          item => item[props.idKey] == itemToSave[props.idKey] && item.key !== itemToSave.key
        )
      )
        errorMessages[itemToSave.key][props.idKey] = 'The id already exists'

      if (!props.skipWeightValidation && !itemToSave.weight)
        errorMessages[itemToSave.key].weight = 'Required'

      if (Object.keys(errorMessages[itemToSave.key]).length == 0)
        delete errorMessages[itemToSave.key]
    }

    const searchTableRewards = searchInput => {
      if (!searchInput) {
        rewardTableWithAddLocal.emptyText = noRewardMessage
        formState.tableRewards = formState.allTableRewards
      } else {
        rewardTableWithAddLocal.emptyText = noSearchResultMessage
        formState.tableRewards = formState.allTableRewards
        formState.tableRewards = formState.tableRewards.filter(
          tableReward =>
            tableReward.rewardId?.toLowerCase().includes(searchInput.toLowerCase()) ||
            !tableReward.rewardId
        )
      }
    }

    const notUniqueId = id => {
      let notUnique = false
      Object.values(props.entries).forEach(items => {
        items.forEach(item => {
          if (item.id == id) notUnique = true
        })
      })
      return notUnique
    }

    const onSubmitCreateEditRewardBag = () => {
      helpersAPI.resetErrorMessages(errorMessages)

      if (!formState.rewardBagName) errorMessages.rewardBagName = 'Reward bag name is required.'
      if (!formState.bagId) errorMessages.bagId = 'Bag id is required.'

      if (!props.table && props.isCreateMode && notUniqueId(formState.bagId))
        errorMessages.bagId = 'This id is already in use'
      if (!formState.source) errorMessages.source = 'Source file is required'

      if (formState.tableRewards.find(entry => entry.key.startsWith(ADD_KEY_PREFIX))) {
        toast.error('You have unsaved changes')
        return
      }

      if (Object.keys(errorMessages).length !== 0) return

      const fileContent = props.entries[formState.source]

      const currentContentObject = fileContent.find(
        fileContentItem => fileContentItem.id === formState.bagId
      )

      const updateContentObject = {
        ...currentContentObject,

        id: formState.bagId,
        name: formState.rewardBagName,
        rewards: formState.allTableRewards.map(tableReward => {
          return {
            rewardId: tableReward.rewardId,
            weight: tableReward.weight
          }
        })
      }

      if (currentContentObject) {
        fileContent.splice(fileContent.indexOf(currentContentObject), 1, updateContentObject)
      } else {
        fileContent.push(updateContentObject)
      }

      const srcJson = utils.jsonStringify(fileContent)

      const payload = {
        contentType: 'RewardBags',
        filename: formState.source,
        srcJson
      }

      updateContentCatalog(payload)
      helpersAPI.goBackToPage({ name: 'RewardBagsEditor', router })
    }

    useSubscribe('submitCreateEditRewardBag', onSubmitCreateEditRewardBag)

    const saveEntry = entry => {
      const index = formState.tableRewards.findIndex(item => item.key === entry.key)
      if (index >= 0) {
        entry.key = utils.getRandomString()
        formState.tableRewards[index] = entry
        formState.allTableRewards[index] = entry
      }
    }

    const deleteEntry = entry => {
      const indexOfEntry = formState.tableRewards.findIndex(item => item.key === entry.key)
      if (indexOfEntry >= 0) {
        formState.tableRewards.splice(indexOfEntry, 1)
      }
    }

    const selectEntry = (value, editableData, recordKey) => {
      editableData[recordKey]['rewardId'] = value
    }

    const addEntry = entry => {
      formState.tableRewards = [
        ...formState.tableRewards,
        {
          ...entry,
          rewardId: null,
          weight: null
        }
      ]
    }

    const percentageFormatter = value => `${value}%`
    const percentageParser = value => value.replace('%', '')

    let selectedRewardSlots = []
    let usageWarning = ''
    if (!props.isCreateMode && !props.readOnly) {
      const getListOfEntitiesInWichSelectedIdExists = selectedId => {
        return props.rewardSlots
          .filter(rewardSlot => {
            return (
              rewardSlot.slotEntries.find(slotEntry => slotEntry.rewardBagId == selectedId) !==
              undefined
            )
          })
          ?.map(rewardSlot => rewardSlot.id)
      }

      selectedRewardSlots = getListOfEntitiesInWichSelectedIdExists(props.selectedRewardBagId)
      usageWarning = `${props.selectedRewardBagId} is used in the the following Reward
          ${selectedRewardSlots.length > 1 ? 'Slots' : 'Slot'}:
          ${selectedRewardSlots}.`
    }

    const getRouterParams = record => {
      return {
        rewardId: record.rewardId,
        rewardSlotId: route.params.rewardSlotId,
        rewardBagId: route.params.rewardBagId,
        rewardTableId: route.params.rewardTableId
      }
    }

    return {
      utils,
      formState,
      errorMessages,
      validateTableRow,
      addEntry,
      saveEntry,
      deleteEntry,
      selectEntry,
      ADD_KEY_PREFIX,
      searchInput,
      searchRewardTableBags: searchTableRewards,
      rewardsColumns,
      rewardsColumnsWithAdd,
      percentageFormatter,
      percentageParser,
      rewardTableWithAddLocal,
      usageWarning,
      selectedRewardSlots,
      currentPage,
      getRouterParams
    }
  },
  template: `
    <div>
      <a-row :gutter="[32, 30]">
        <a-col :span="24">
          <a-alert v-if="!$props.isCreateMode && !$props.readOnly && (selectedRewardSlots.length > 0)" :message="usageWarning" type="info" show-icon />
          <PageSection
            title="Rewards"
            :count="formState.allTableRewards.length"
          >
          <a-row :gutter="[32, 16]" :gap="24">
            <!-- TODO: As we discussed the requirements this search component will be hidden until it will be needed -->
            <!-- <a-col :span="24">
              <a-input-search
                allowClear
                class="searchTableInput"
                :defaultValue='searchInput'
                placeholder="Search In Rewards"
                enter-button="Search"
                @search="searchRewardTableBags"
              >
                <template #prefix> <Icon name="search" /> </template>
              </a-input-search>
            </a-col> -->
            <a-col :span="24">
              <RewardsTableWithAdd
                v-if="$props.readOnly"
                :addPrefix="ADD_KEY_PREFIX"
                :columns="rewardsColumns"
                :items="formState.tableRewards"
                :allRewardItemsOptions="formState.rewardItemsOptions"
                :validateRow="validateTableRow"
                :tableLocale="rewardTableWithAddLocal"
                addButtonText=""
                idKey="rewardId"
                :currentPage="currentPage"
                routerColumn="rewardId"
                :getRouterParams="getRouterParams"
                @addEntry="(entry) => addEntry(entry)"
                @saveEntry="(entry) => saveEntry(entry)"
                @deleteEntry="(entry) => deleteEntry(entry)"
                @selectEntry="(entry, editableData, recordKey) => selectEntry(entry, editableData, recordKey)"
              />
              <RewardsTableWithAdd
                v-else
                :addPrefix="ADD_KEY_PREFIX"
                :columns="rewardsColumnsWithAdd"
                :items="formState.tableRewards"
                :allRewardItemsOptions="formState.rewardItemsOptions"
                :validateRow="validateTableRow"
                :tableLocale="rewardTableWithAddLocal"
                addButtonText="Add Reward"
                idKey="rewardId"
                :currentPage="currentPage"
                routerColumn="rewardId"
                :getRouterParams="getRouterParams"
                :isCreateMode="$props.isCreateMode"
                @addEntry="(entry) => addEntry(entry)"
                @saveEntry="(entry) => saveEntry(entry)"
                @deleteEntry="(entry) => deleteEntry(entry)"
                @selectEntry="(entry, editableData, recordKey) => selectEntry(entry, editableData, recordKey)"
              />
            </a-col>
          </a-row>
          </PageSection>
        </a-col>
        <a-col :span="24">
          <a-form layout='vertical' :model="formState" class="info-form">
            <PageSection title="Other Details">
              <a-row :gutter="32" class="create-entry-row">
                <a-col :span="12">
                  <a-form-item
                    label="Reward Bag Id"
                    name="bagId"
                    class="required"
                    :help="errorMessages.bagId"
                    :validateStatus="errorMessages.bagId && 'error' || ''"
                  >

                    <a-input v-if="$props.isCreateMode"  v-model:value='formState.bagId'/>
                    <a-input v-else v-model:value="$props.selectedRewardBagId" :disabled='true'/>
                    <InfoParagraph v-if="!$props.readOnly" style="padding-bottom: 20px; display: block" infoText="Once the reward table is created the Id cannot be changed"/>
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item
                    label="Reward Bag Name"
                    name="rewardBagName"
                    class="required"
                    :help="errorMessages.rewardBagName"
                    :disabled="$props.readOnly"
                    :validateStatus="errorMessages.rewardBagName && 'error' || ''"
                    >
                    <a-input v-if="$props.isCreateMode"  v-model:value='formState.rewardBagName'/>
                    <a-input v-else v-model:value="formState.rewardBagName" :disabled="$props.readOnly"/>
                  </a-form-item>
                </a-col>
              </a-row>
            </PageSection>
          </a-form>
        </a-col>
      </a-row>
    </div>
  `
}

const rewardsColumns = [
  {
    title: 'Reward Id',
    dataIndex: 'rewardId',
    key: 'rewardId'
  },
  {
    title: 'Weight',
    dataIndex: 'weight',
    key: 'weight'
  }
]

const rewardsColumnsWithAdd = [...rewardsColumns, { dataIndex: 'edit', key: 'edit' }]

export default CreateEditRewardBag
