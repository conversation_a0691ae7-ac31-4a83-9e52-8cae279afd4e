import eventEmitter from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import { utils } from '../../../../../main/utils.js'
import SimpleCodeEditor from '../../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import * as Vue from 'vue';

const DefaultExtRichPreview = {
  name: 'DefaultExtRichPreview',
  components: {
    SimpleCodeEditor
  },
  props: {
    data: Object,
    context: Object
  },
  setup(props) {
    const changeViewMode = viewMode => {
      eventEmitter.emit('changeCodeDrawerViewMode', viewMode)
    }

    const codePreview = Vue.computed(() =>
      utils.addLinesEllipsis(utils.convertObjectToFormattedString(props.data))
    )

    return {
      changeViewMode,
      codePreview
    }
  },
  template: `
    <div>
      <SimpleCodeEditor
        :read_only="true"
        :hide_header="true"
        font_size="12px"
        style="width: 100%"
        :wrap_code="true"
        :value="codePreview"
        :languages="[['JSON', 'JSON']]"
      />
      <span class="extTreeRowHint">
        * to view {{context.allowEdit ? 'or edit' : ''}} click
        <a @click.prevent.stop="changeViewMode('tree')">tree</a>
        or
        <a @click.prevent.stop="changeViewMode('raw')">raw</a>
        mode
      </span>
    </div>
  `
}

export default DefaultExtRichPreview
