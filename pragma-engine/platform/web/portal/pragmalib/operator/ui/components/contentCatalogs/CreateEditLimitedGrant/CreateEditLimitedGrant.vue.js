import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import TableWithAdd from '../../../../../main/ui/components/TableWithAdd/TableWithAdd.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import PageSection from '../../../../../main/ui/components/PageSection/PageSection.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { utils } from '../../../../../main/utils.js'
import ScheduleForm from '../../ScheduleForm/ScheduleForm.vue.js'
import JsonViewTabSelector from '../../../../../main/ui/components/JsonViewTabSelector/JsonViewTabSelector.vue.js'
import JsonTreeView from '../../../../../main/ui/components/JsonTreeViewer/JsonTreeViewer.vue.js'
import JsonEditor from '../../../../../main/ui/components/JsonEditor/JsonEditor.vue.js'
import { swappable } from '../../../../../main/ui/helpers/swappableComponent/swappable.js'
import DateDisplay from '../../../../../main/ui/components/DateDisplay/DateDisplay.vue.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import { useRouter } from 'vue-router';
import * as Vue from 'vue';

const STACKABLE_TO_ADD_KEY_PREFIX = '__to-add__'
const INSTANCED_TO_ADD_KEY_PREFIX = '__instanced-to-add__'
const REWARD_TO_ADD_KEY_PREFIX = '__reward-to-add__'

const CreateEditLimitedGrant = {
  name: 'CreateEditLimitedGrant',
  components: {
    Icon,
    TableWithAdd,
    Table,
    PageSection,
    ScheduleForm,
    DateDisplay,
    JsonEditor,
    JsonTreeView,
    JsonViewTabSelector,
    InfoParagraph
  },
  props: {
    catalog: Object,
    entries: Object,
    rewardTables: Array,
    readOnly: Boolean,
    isCreateMode: Boolean,
    selectedTrackingId: String,
    stackable: Object,
    instanced: Object
  },
  setup(props) {
    const router = useRouter()
    const currentTrackingId = props.selectedTrackingId || ''

    const allStackableItems = props.catalog ? props.catalog.stackableEntries : props.stackable
    const allInstancedItems = props.catalog ? props.catalog.instancedEntries : props.instanced
    const allRewardTablesItems = props.rewardTables

    const extRichEditor = swappable('Content.LimitedGrant.ExtRich', {
      default: null,
      emits: ['onUpdate'],
      data: true,
      context: ['readOnly']
    })

    const extView = Vue.ref(extRichEditor ? 'rich' : 'tree')

    let currentGrant = {}
    const sourceFile = Object.keys(props.entries).find(file => {
      const grant = props.entries[file].find(entry => entry.trackingId === currentTrackingId)
      if (grant) {
        currentGrant = grant
        return true
      }
      return false
    })

    const stackableItems = Vue.reactive(
      formatTableData(currentGrant.stackableGrants || [], allStackableItems)
    )
    const instancedItems = Vue.reactive(
      formatTableData(currentGrant.instancedGrants || [], allInstancedItems)
    )

    const rewardTableItems = Vue.reactive(
      formatRewardTableData(currentGrant.rewardGrants || [], allRewardTablesItems)
    )

    const startTime = Vue.ref(currentGrant.startTime)
    const endTime = Vue.ref(currentGrant.endTime)

    const trackingIdErrorMessage = Vue.ref('')
    const errorMessages = Vue.reactive({})

    const onScheduleUpdate = ({ startTimestamp, endTimestamp }) => {
      startTime.value = startTimestamp
      endTime.value = endTimestamp
    }

    const formState = Vue.reactive({
      trackingId: currentTrackingId,
      stackableGrants: stackableItems,
      instancedGrants: instancedItems,
      rewardGrants: rewardTableItems,
      source: sourceFile || Object.keys(props.entries)[0],
      ext: utils.jsonStringify(currentGrant.ext || {}, null, 2)
    })

    const formatItems = (items, valueColumn) => {
      return items.map(item => ({
        ...item,
        value: item[valueColumn],
        label: `${item.name} (${item.id || item.catalogId})`
      }))
    }

    const stackableItemsOptions = valueColumn => [
      {
        label: 'Stackable',
        options: formatItems(allStackableItems, valueColumn)
      }
    ]
    const instancedItemsOptions = valueColumn => [
      {
        label: 'Instanced',
        options: formatItems(allInstancedItems, valueColumn)
      }
    ]

    const rewardTableItemsOptions = valueColumn => [
      {
        label: 'Reward Tables',
        options: formatItems(allRewardTablesItems, valueColumn)
      }
    ]

    const readOnlyColumnsStackable = columnsStackable.filter(column => column.dataIndex !== 'edit')
    const readOnlyColumnsInstanced = columnsInstanced.filter(column => column.dataIndex !== 'edit')
    const readOnlyColumnsRewardTables = columnsRewardTables.filter(
      column => column.dataIndex !== 'edit'
    )

    const onSubmitCreateLimitedGrant = () => {
      helpersAPI.resetErrorMessages(errorMessages)
      trackingIdErrorMessage.value = ''
      if (!formState.trackingId) {
        trackingIdErrorMessage.value = 'Tracking Id is required'
        return false
      }

      if (props.isCreateMode)
        Object.values(props.entries).forEach(items => {
          items.forEach(item => {
            if (formState.trackingId.toString().trim() === item.trackingId.toString().trim()) {
              trackingIdErrorMessage.value = 'Id is already in use'
            }
          })
        })

      if (trackingIdErrorMessage.value) return false

      if (Object.keys(errorMessages).length > 0) {
        toast.error('The form has errors')
        return false
      }

      let entryExt = {}
      try {
        entryExt = utils.jsonParse(formState.ext)
      } catch (error) {
        toast.error('Could not parse the ext object')
        return
      }

      const entryToCreate = {
        trackingId: formState.trackingId,
        stackableGrants: stackableItems,
        instancedGrants: instancedItems,
        rewardGrants: rewardTableItems,
        startTime: startTime.value,
        endTime: endTime.value,
        ext: entryExt
      }

      if (
        entryToCreate.stackableGrants.find(item =>
          item.key?.startsWith(STACKABLE_TO_ADD_KEY_PREFIX)
        )
      ) {
        toast.error('You have unsaved changes in stackable table')
        return false
      }
      if (
        entryToCreate.instancedGrants.find(item =>
          item.key?.startsWith(INSTANCED_TO_ADD_KEY_PREFIX)
        )
      ) {
        toast.error('You have unsaved changes in instanced table')
        return false
      }
      if (entryToCreate.rewardGrants.find(item => item.key?.startsWith(REWARD_TO_ADD_KEY_PREFIX))) {
        toast.error('You have unsaved changes in reward table')
        return false
      }

      entryToCreate.stackableGrants.forEach(grant => {
        delete grant.key
        delete grant.name
      })
      entryToCreate.instancedGrants.forEach(grant => {
        delete grant.key
        delete grant.name
      })
      entryToCreate.rewardGrants.forEach(grant => {
        grant.rewardTableId = grant.id
        delete grant.key
        delete grant.name
        delete grant.id
      })

      props.isCreateMode
        ? props.entries[formState.source].push(entryToCreate)
        : (props.entries[formState.source] = utils.replaceInObjArray(
            props.entries[formState.source],
            'trackingId',
            entryToCreate.trackingId,
            entryToCreate
          ))
      const srcJson = utils.jsonStringify(props.entries[formState.source])
      const payload = {
        contentType: 'LimitedGrants',
        filename: formState.source,
        srcJson
      }
      updateContentCatalog(payload)
      helpersAPI.goBackToPage({ name: 'LimitedGrantsEditor', router })
    }

    const validateRow = (errorMessages, itemToSave, props) => {
      errorMessages[itemToSave.key] = {}

      const isStackableItem = itemToSave.key?.includes(STACKABLE_TO_ADD_KEY_PREFIX)
      const isInstancedItem = itemToSave.key?.includes(INSTANCED_TO_ADD_KEY_PREFIX)
      const isRewardTableItem = itemToSave.key?.includes(REWARD_TO_ADD_KEY_PREFIX)

      if (!itemToSave.catalogId && !isRewardTableItem)
        errorMessages[itemToSave.key].catalogId = 'Required'
      if (!itemToSave.id && isRewardTableItem) errorMessages[itemToSave.key].id = 'Required'
      if (!itemToSave.amount && isStackableItem) errorMessages[itemToSave.key].amount = 'Required'
      if (!itemToSave.count && isRewardTableItem) errorMessages[itemToSave.key].count = 'Required'
      if (!itemToSave.name) errorMessages[itemToSave.key].name = 'Required'

      if (
        itemToSave.catalogId &&
        isStackableItem &&
        !allStackableItems.find(item => item.catalogId == itemToSave.catalogId)
      )
        errorMessages[itemToSave.key].catalogId = 'Catalog Id does not exist'

      if (
        itemToSave.catalogId &&
        isInstancedItem &&
        !allInstancedItems.find(item => item.catalogId == itemToSave.catalogId)
      )
        errorMessages[itemToSave.key].catalogId = 'Catalog Id does not exist'

      if (
        itemToSave.id &&
        isRewardTableItem &&
        !allRewardTablesItems.find(item => item.id == itemToSave.id)
      )
        errorMessages[itemToSave.key].id = 'Reward Table Id does not exist'

      if (
        itemToSave.name &&
        isStackableItem &&
        !allStackableItems.find(item => item.name == itemToSave.name)
      )
        errorMessages[itemToSave.key].name = 'Name does not exist'

      if (
        itemToSave.name &&
        isInstancedItem &&
        !allInstancedItems.find(item => item.name == itemToSave.name)
      )
        errorMessages[itemToSave.key].name = 'Name does not exist'

      if (
        itemToSave.name &&
        isRewardTableItem &&
        !allRewardTablesItems.find(item => item.name == itemToSave.name)
      )
        errorMessages[itemToSave.key].name = 'Name does not exist'

      if (Object.keys(errorMessages[itemToSave.key]).length == 0)
        delete errorMessages[itemToSave.key]
    }

    const addStackableEntry = itemToSave => {
      stackableItems.push(itemToSave)
    }
    const addInstancedEntry = itemToSave => {
      instancedItems.push(itemToSave)
    }
    const addRewardTableEntry = itemToSave => {
      rewardTableItems.push(itemToSave)
    }

    const saveEntry = itemToSave => {
      const selectedItem =
        stackableItems.find(item => item.key === itemToSave.key) ||
        instancedItems.find(item => item.key === itemToSave.key) ||
        rewardTableItems.find(item => item.key === itemToSave.key)

      if (selectedItem >= 0) {
        entry.key = utils.getRandomString()
        formState.stackableItems[selectedItem] = entry
      }
      selectedItem.key?.includes(REWARD_TO_ADD_KEY_PREFIX)
        ? (selectedItem.id = itemToSave.id)
        : (selectedItem.catalogId = itemToSave.catalogId)
      selectedItem.name = itemToSave.name
      selectedItem.key?.includes(STACKABLE_TO_ADD_KEY_PREFIX)
        ? (selectedItem.amount = itemToSave.amount)
        : ''
      selectedItem.key?.includes(REWARD_TO_ADD_KEY_PREFIX)
        ? (selectedItem.count = itemToSave.count)
        : ''

      replaceKey(selectedItem, STACKABLE_TO_ADD_KEY_PREFIX)
      replaceKey(selectedItem, INSTANCED_TO_ADD_KEY_PREFIX)
      replaceKey(selectedItem, REWARD_TO_ADD_KEY_PREFIX)
    }

    const replaceKey = (selectedItem, prefixToRemove) => {
      if (selectedItem.key && selectedItem.key.includes(prefixToRemove)) {
        selectedItem.key = selectedItem.key.replace(prefixToRemove, '')
      }
    }

    const deleteEntry = key => {
      const stackableEntryIndex = stackableItems.findIndex(item => item.key === key)
      if (stackableEntryIndex >= 0) stackableItems.splice(stackableEntryIndex, 1)
      const instancedEntryIndex = instancedItems.findIndex(item => item.key === key)
      if (instancedEntryIndex >= 0) instancedItems.splice(instancedEntryIndex, 1)
      const rewardEntryIndex = rewardTableItems.findIndex(item => item.key === key)
      if (rewardEntryIndex >= 0) rewardTableItems.splice(rewardEntryIndex, 1)
    }

    useSubscribe('submitCreateLimitedGrant', onSubmitCreateLimitedGrant)

    const formStateExtJson = Vue.reactive({})

    const tryExtParse = () => {
      try {
        const parsedExt = utils.jsonParse(formState.ext)
        Object.keys(parsedExt).forEach(key => (formStateExtJson[key] = parsedExt[key]))
      } catch (error) {}
    }
    tryExtParse()

    Vue.watch(
      () => formState.ext,
      () => {
        tryExtParse()
      }
    )
    const extUpdate = extContent => {
      formState.ext = extContent
    }

    const onExtUpdate = data => {
      formState.ext = utils.convertObjectToFormattedString(data)
    }

    const updateTreeData = updatedContent => {
      formState.ext = utils.convertObjectToFormattedString(updatedContent.json)
    }

    return {
      formState,
      stackableItems,
      instancedItems,
      rewardTableItems,
      validateRow,
      columnsStackable,
      columnsInstanced,
      columnsRewardTables,
      readOnlyColumnsStackable,
      readOnlyColumnsInstanced,
      readOnlyColumnsRewardTables,
      STACKABLE_TO_ADD_KEY_PREFIX,
      INSTANCED_TO_ADD_KEY_PREFIX,
      REWARD_TO_ADD_KEY_PREFIX,
      stackableItemsOptions,
      instancedItemsOptions,
      rewardTableItemsOptions,
      errorMessages,
      trackingIdErrorMessage,
      currentTrackingId,
      addStackableEntry,
      addInstancedEntry,
      addRewardTableEntry,
      saveEntry,
      deleteEntry,
      startTime,
      endTime,
      onScheduleUpdate,
      extView,
      extUpdate,
      extRichEditor,
      formStateExtJson,
      onExtUpdate,
      updateTreeData
    }
  },
  template: `
    <a-form layout='vertical' :model='formState' class="create-entry">
      <a-row :gutter="32" class="create-entry-row">
        <a-col :span="12">
          <a-form-item
            label='Tracking Id'
            name='trackingId'
            :help="trackingIdErrorMessage"
            :validateStatus="trackingIdErrorMessage != '' ? 'error' : ''"
          >
            <a-input v-model:value='formState.trackingId' :disabled="!!currentTrackingId" />
            <InfoParagraph v-if="!$props.readOnly" infoText="Once the grant is created the Tracking Id cannot be changed"/>
          </a-form-item>
        </a-col>
      </a-row>

      <TableWithAdd
        v-if="!$props.readOnly"
        title="Stackable"
        :addPrefix="STACKABLE_TO_ADD_KEY_PREFIX"
        addButtonText="Add Stackable"
        :columns="columnsStackable"
        :items="stackableItems"
        :allCatalogItemsOptions="stackableItemsOptions"
        :validateRow="validateRow"
        :errorMessages="errorMessages"
        @addEntry="addStackableEntry"
        @saveEntry="saveEntry"
        @deleteEntry="deleteEntry"
      />
      <PageSection v-if="$props.readOnly" title="Stackable" :count="stackableItems.length">
        <Table
          :columns="readOnlyColumnsStackable"
          :data="stackableItems"
          :pagination="false"
          class="view-only-table"
        />
      </PageSection>

      <TableWithAdd
        v-if="!$props.readOnly"
        title="Instanced"
        :addPrefix="INSTANCED_TO_ADD_KEY_PREFIX"
        addButtonText="Add Instanced"
        :columns="columnsInstanced"
        :items="instancedItems"
        :allCatalogItemsOptions="instancedItemsOptions"
        :validateRow="validateRow"
        :errorMessages="errorMessages"
        @addEntry="addInstancedEntry"
        @saveEntry="saveEntry"
        @deleteEntry="deleteEntry"
      />
      <PageSection v-if="$props.readOnly" title="Instanced" :count="instancedItems.length">
        <Table
          :columns="readOnlyColumnsInstanced"
          :data="instancedItems"
          :pagination="false"
          class="view-only-table"
        />
      </PageSection>

      <TableWithAdd
        v-if="!$props.readOnly"
        title="Reward Tables"
        :addPrefix="REWARD_TO_ADD_KEY_PREFIX"
        addButtonText="Add Reward Table"
        :columns="columnsRewardTables"
        :items="rewardTableItems"
        :allCatalogItemsOptions="rewardTableItemsOptions"
        :validateRow="validateRow"
        :errorMessages="errorMessages"
        @addEntry="addRewardTableEntry"
        @saveEntry="saveEntry"
        @deleteEntry="deleteEntry"
      />
      <PageSection v-if="$props.readOnly" title="Reward Tables" :count="rewardTableItems.length">
        <Table
          :columns="readOnlyColumnsRewardTables"
          :data="rewardTableItems"
          :pagination="false"
          class="view-only-table"
        />
      </PageSection>

      <PageSection title="Schedule">
        <ScheduleForm
          v-if="!$props.readOnly"
          @onUpdate='onScheduleUpdate'
          :startTimestamp='startTime'
          :endTimestamp='endTime'
        />
        <template v-if="$props.readOnly">
          <div style="display: flex; gap: 3rem;">
            <p>Start at: <br/> <DateDisplay :timestamp='startTime' /></p>
            <p>End at: <br/> <DateDisplay :timestamp='endTime' /></p>
          </div>
        </template>
      </PageSection>

      <PageSection title="Ext">
        <template #actions>
          <JsonViewTabSelector
            :currentView='extView'
            @onChange="(newView) => extView = newView"
            :showRich='!!extRichEditor'
            :span="12"
            size='small'
          />
        </template>

        <div v-if="extView === 'rich'">
          <component
            :is="extRichEditor"
            :data="formStateExtJson"
            :context="{readOnly: $props.readOnly}"
            @onUpdate='onExtUpdate'
          />
        </div>
        <div v-if="extView === 'raw'">
          <JsonEditor
            :rawData='formState.ext'
            :readOnly='$props.readOnly'
            @onUpdate="extUpdate"
          />
        </div>
        <div v-if="extView === 'tree'" class="jse-theme-dark">
          <JsonTreeView
            :content="{text: formState.ext}"
            :readOnly='$props.readOnly'
            :onChange="updateTreeData"
          ></JsonTreeView>
        </div>

      </PageSection>
      <br />
    </a-form>
  `
}

const formatTableData = (items, allCatalogItems) =>
  items.map(item => ({
    name: helpersAPI.getCatalogNameByCatalogId(allCatalogItems, item.catalogId),
    key: utils.getRandomString(),
    ...item
  }))

const formatRewardTableData = (items, allRewardTablesItems) =>
  items.map(item => ({
    name: helpersAPI.getCatalogNameByCatalogId(allRewardTablesItems, item.rewardTableId),
    id: item.rewardTableId,
    key: utils.getRandomString(),
    ...item
  }))

const columnsStackable = [
  { title: 'Name', dataIndex: 'name', key: 'name' },
  { title: 'Catalog Id', dataIndex: 'catalogId', key: 'catalogId' },
  { title: 'Amount', dataIndex: 'amount', key: 'amount' },
  { dataIndex: 'edit', key: 'edit' }
]
const columnsInstanced = columnsStackable.filter(column => column.key !== 'amount')

const columnsRewardTables = [
  { title: 'Name', dataIndex: 'name', key: 'name' },
  { title: 'Reward Table Id', dataIndex: 'id', key: 'id' },
  { title: 'Count', dataIndex: 'count', key: 'count' },
  { dataIndex: 'edit', key: 'edit' }
]

export default CreateEditLimitedGrant
