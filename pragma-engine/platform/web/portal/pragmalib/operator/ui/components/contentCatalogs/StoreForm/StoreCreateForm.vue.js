import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import jsonUtils from '../../../../../main/utils/jsonUtils.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import { useRouter } from 'vue-router';
import * as Vue from 'vue';

const StoreCreateForm = {
  name: 'StoreForm',
  components: { Icon, InfoParagraph },
  props: {
    entries: Object
  },
  setup(props) {
    const router = useRouter()
    const parsedEntries = {}
    Object.keys(props.entries).forEach(file => {
      parsedEntries[file] = jsonUtils.jsonParse(props.entries[file])
    })
    const sourceFiles = []
    Object.keys(props.entries).forEach(file => sourceFiles.push({ value: file, label: file }))

    const formState = Vue.reactive({
      name: '',
      id: '',
      source: sourceFiles[0].value
    })
    const errorMessages = Vue.reactive({
      name: '',
      id: ''
    })

    const onFinish = () => {
      helpersAPI.resetErrorMessages(errorMessages)
      if (formState.name.trim() === '') {
        errorMessages.name = 'Name is required'
      }

      if (formState.id.trim() === '') {
        errorMessages.id = 'Id is required'
      }
      Object.values(parsedEntries).forEach(items => {
        items.forEach(item => {
          if (formState.id.toString().trim() === item.id.toString().trim()) {
            errorMessages.id = 'Id is already in use'
            return false
          }
        })
      })

      if (Object.keys(errorMessages).length !== 0) return

      toast.success(`'${formState.name.trim()}' store has been successfully created.`)

      parsedEntries[formState.source].push({
        name: formState.name,
        id: formState.id,
        storeEntries: []
      })
      const srcJson = jsonUtils.jsonStringify(parsedEntries[formState.source])
      const payload = {
        contentType: 'Stores',
        filename: formState.source,
        srcJson
      }
      updateContentCatalog(payload, true)

      router.push({
        name: 'ViewStoreEditor',
        params: { storeId: formState.id }
      })
    }

    useSubscribe('submitStoreDetails', onFinish)

    return { formState, errorMessages, sourceFiles }
  },
  template: `
    <a-form layout='vertical' :model='formState' :wrapper-col="{span: 14}">
      <a-form-item
        label='Name'
        name='name'
        :help='errorMessages.name'
        :validateStatus="errorMessages.name && 'error' || ''"
        required
      >
        <a-input v-model:value='formState.name' :ref='(el) => el?.focus()' />
      </a-form-item>
      <a-form-item
        label='Id'
        name='id'
        :help='errorMessages.id'
        :validateStatus="errorMessages.id && 'error' || ''"
        required
      >
        <a-input v-model:value='formState.id'/>
        <InfoParagraph infoText="Once the store is created the Id cannot be changed"/>
      </a-form-item>
      <a-form-item
          label="Source"
          name="source"
          class="required"
        >
          <a-select
            v-model:value="formState.source"
            :options="sourceFiles"
            :defaultValue="sourceFiles[0]"
            notFoundContent=""
          >
          </a-select>
        </a-form-item>
    </a-form>
  `
}
export default StoreCreateForm
