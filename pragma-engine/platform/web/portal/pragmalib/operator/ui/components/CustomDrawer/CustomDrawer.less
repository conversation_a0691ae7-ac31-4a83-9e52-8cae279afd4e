.customDrawer {
  &.ant-drawer .ant-form-item + .ant-form-item {
    margin-top: @marginLG * @scalePx !important;
  }

  &.ant-drawer .ant-form-item {
    margin-bottom: 0 !important;
  }

  .data-changed-warning {
    color: @colorWarningText;
    margin-top: @marginXXS * @scalePx;
  }
  .preview-has-changed {
    border: 1px solid @colorWarningBorder !important;
    border-radius: @borderRadius * @scalePx !important;
  }

  .drawer-expand {
    position: relative;

    > .ant-btn {
      position: absolute;
      bottom: 100%;
      margin-bottom: @marginXXS * @scalePx;
      right: 0;
    }
  }

  .drawerTopRadioGroupViewMode {
    display: flex;
    margin-bottom: @marginMD * @scalePx;
    background-color: @colorBgLayout;

    .pIcon {
      vertical-align: -2px !important;
    }

    .ant-radio-button-wrapper {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }

  h1 {
    font-size: @fontSizeXL * @scalePx;
    margin-bottom: @marginMD * @scalePx;

    &:empty {
      display: none;
    }
  }

  h2 {
    font-size: @fontSize * @scalePx;
    margin-bottom: @marginSM * @scalePx;
    margin-top: @marginLG * @scalePx;
  }

  label {
    color: @colorTextSecondary;
  }

  ul {
    margin-bottom: @marginSM * @scalePx;
    text-indent: 0;
    padding: 0;
  }

  li {
    list-style-type: none;
  }
}