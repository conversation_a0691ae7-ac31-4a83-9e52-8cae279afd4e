import Icon from '../../../../main/ui/components/Icon/Icon.vue.js'
import { utils } from '../../../../main/utils.js'
import { hooks } from '../../../../main/ui/hooks.js'
import { DateDisplay } from '../../../../main/ui/components/DateDisplay/DateDisplay.vue.js'
import JsonTreeView from '../../../../main/ui/components/JsonTreeViewer/JsonTreeViewer.vue.js'
import JsonEditor from '../../../../main/ui/components/JsonEditor/JsonEditor.vue.js'
import {
  useSubscribe,
  eventEmitter
} from '../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import JsonViewTabSelector from '../../../../main/ui/components/JsonViewTabSelector/JsonViewTabSelector.vue.js'
import SimpleCodeEditor from '../../../../main/ui/components/SimpleCodeEditor/SimpleCodeEditor.vue.js'
import ItemSelector from '../contentCatalogs/ItemSelector/ItemSelector.vue.js'
import helpersUI from '../../../../main/ui/helpers/helpers.js'
import CustomDrawer from '../CustomDrawer/CustomDrawer.vue.js'
import * as Vue from 'vue';

const Drawer = {
  components: {
    SimpleCodeEditor,
    Icon,
    DateDisplay,
    JsonTreeView,
    JsonEditor,
    JsonViewTabSelector,
    ItemSelector,
    helpersUI,
    CustomDrawer
  },
  emits: ['submitSuccess', 'submitError'],
  props: {
    drawerData: Object,
    drawerKeys: Array,
    drawerKeysToLabels: {
      type: Object,
      default: {}
    },
    drawerEditLabel: {
      type: String,
      default: 'Edit'
    },
    formatFields: Object, // {[field]: 'code' | 'keywordList' | 'number' | 'textarea' | 'date' | 'false' }
    allowEdit: {
      type: [Array, Boolean],
      default: false
    },
    drawerFieldsOptions: Object,
    extRichEditor: Object,
    showViewSwitcher: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit, slots }) {
    const showDrawer = Vue.ref(false)
    const drawerDataFormatted = Vue.ref({})
    const drawerKeys = Vue.ref([])
    const editData = Vue.ref({})
    const editFields = Vue.ref([])
    const isEditMode = Vue.ref(false)
    const viewMode = Vue.ref('rich' || 'raw' || 'tree')
    const randomRichKey = Vue.ref(utils.getRandomString())
    const editRawDataParseError = Vue.ref(false)
    const editRawDataToBeParsed = Vue.ref('')
    const isEditModeChangedFromEvent = Vue.ref(false)
    const fromCancel = Vue.ref(false)

    const changeViewMode = mode => (viewMode.value = mode)

    useSubscribe('changeCodeDrawerViewMode', mode => changeViewMode(mode))
    useSubscribe('changeDataDrawerEditMode', mode => {
      isEditModeChangedFromEvent.value = true
      isEditMode.value = mode
    })

    props.drawerData &&
      Vue.watch(props.drawerData, () => {
        if (Object.keys(props.drawerData?.value).length) {
          showDrawer.value = true
          isEditMode.value = false
          drawerKeys.value = props.drawerKeys || Object.keys(props.drawerData.value)

          editFields.value = getEditFields(props.allowEdit, drawerKeys.value)

          drawerDataFormatted.value = formatDrawerData(
            props.drawerData.value,
            drawerKeys.value,
            props.formatFields
          )

          resetAndPopulateEditDrawerData()
        } else {
          showDrawer.value = false
        }
      })

    const editDataStringified = Vue.computed(() =>
      utils.convertObjectToFormattedString(editData.value)
    )

    Vue.watch(viewMode, (toView, fromView) => {
      if (toView === 'raw') editRawDataToBeParsed.value = editDataStringified.value
      if (fromView === 'raw') parseRawData()
    })

    Vue.watch(isEditMode, newMode => {
      if (isEditModeChangedFromEvent.value) {
        isEditModeChangedFromEvent.value = false
        return
      }
      eventEmitter.emit('dataDrawerEditMode', {
        isEditMode: newMode,
        data: props.drawerData.value,
        fromCancel: fromCancel.value
      })
    })

    const resetAndPopulateEditDrawerData = () => {
      editData.value = {}

      for (const key of drawerKeys.value) {
        editData.value[key] = props.drawerData.value[key]
      }

      // force refresh of rawJsonEditor on cancel
      randomRichKey.value = utils.getRandomString()
      editRawDataToBeParsed.value = editDataStringified.value
    }

    const enableEdit = () => {
      fromCancel.value = false
      isEditMode.value = true
    }

    const saveEdit = () => {
      if (viewMode.value === 'raw') parseRawData()
      if (!editRawDataParseError.value) {
        emit('submitSuccess', editData.value)
      } else {
        emit('submitError')
      }
    }

    const cancelEdit = () => {
      resetAndPopulateEditDrawerData()
      fromCancel.value = true
      isEditMode.value = false
    }

    const onMarkersChange = markers => {
      editRawDataParseError.value = !!markers.length
    }

    const applyExtCodeChanges = (key, data) => {
      const dataToApply = typeof data === 'string' ? parseData(data) : data
      editData.value[key] = dataToApply
    }

    const parseData = dataString => {
      try {
        return utils.jsonParse(dataString)
      } catch (e) {
        return { parseError: 'Invalid Data' }
      }
    }

    const parseRawData = () => {
      const parsed = parseData(editRawDataToBeParsed.value)
      if (!parsed.parseError) {
        updateJsonData(parsed)
      }
    }

    const updateJsonData = parsedData => {
      editData.value = parsedData
    }

    const updateRawData = dataToParse => {
      editRawDataToBeParsed.value = dataToParse
    }

    const setEditData = (key, value) => {
      editData.value[key] = value
    }

    const onClose = () => {
      showDrawer.value = false
    }

    const { percentageFormatter, percentageParser, MAX_LONG_VALUE } = utils

    const dataLabelLookup = key => props.drawerKeysToLabels[key] ?? utils.titleizeAndSpace(key)

    return {
      emptyValue,
      utils,
      showDrawer,
      drawerDataFormatted,
      editData,
      editDataStringified,
      editFields,
      viewMode,
      isEditMode,
      enableEdit,
      saveEdit,
      cancelEdit,
      slots,
      drawerKeys,
      dataLabelLookup,
      applyExtCodeChanges,
      updateRawData,
      updateJsonData,
      editRawDataParseError,
      parseData,
      changeViewMode,
      onMarkersChange,
      randomRichKey,
      setEditData,
      percentageFormatter,
      percentageParser,
      helpersUI,
      onClose,
      MAX_LONG_VALUE
    }
  },
  template: `
    <CustomDrawer
      v-if="showDrawer"
      :open="showDrawer"
      :onClose="onClose"
      size="large"
      :class="!isEditMode ? 'dataDrawer jse-theme-dark drawerTree' : 'dataDrawer jse-theme-dark drawerTreeEdit'"
    >
      <!-- Edit Buttons -->
      <template #extra v-if="$props.allowEdit">
        <template v-if="!isEditMode">
          <a-button type="primary" @click="enableEdit()"><Icon name="edit" /> {{ $props.drawerEditLabel }}</a-button>
        </template>
        <template v-if="isEditMode">
          <a-button secondary @click="cancelEdit">Cancel</a-button>
          <a-button type="primary" @click="saveEdit">Save</a-button>
        </template>
      </template>

      <JsonViewTabSelector
        v-if="$props.showViewSwitcher"
        class="drawerTopRadioGroupViewMode"
        :currentView='viewMode'
        @onChange="(newView) => changeViewMode(newView)"
        :showRich='true'
        :disabled="editRawDataParseError"
      />

      <a-form layout="vertical" class="info-form" v-if="viewMode == 'rich'" :key="'form' + randomRichKey">
        <a-form-item v-for="key in drawerKeys" :label="dataLabelLookup(key)">
          <!-- slot -->
          <slot v-if='slots[key]' :name="key" :isEditMode="isEditMode" :data='editData' :setData="setEditData" />

          <template v-else="slots[key]">
            <!-- tags -->
            <div v-if="drawerDataFormatted[key]?.format === 'keywordList'">
              <template v-if="!isEditMode || !editFields.includes(key)">
                <div v-if="drawerDataFormatted[key]?.value === emptyValue">
                  {{emptyValue}}
                </div>
                <div v-else>
                  <a-tag v-for="tag in drawerDataFormatted[key]?.value" :key="tag">
                    {{tag}}
                  </a-tag>
                </div>
              </template>

              <template v-if="isEditMode && editFields.includes(key)">
                <a-select
                  v-model:value="editData[key]"
                  mode="tags"
                  notFoundContent=""
                >
                  <a-select-option
                    v-for="tag in $props.drawerFieldsOptions?.[key]"
                    :value="tag"
                  >
                    {{tag}}
                  </a-select-option>
                </a-select>
              </template>
            </div>

            <!-- number -->
            <div v-else-if="drawerDataFormatted[key]?.format === 'number' && isEditMode && editFields.includes(key)">
              <a-input-number
                v-model:value='editData[key]'
                :min="0"
                :max="MAX_LONG_VALUE"
                :stringMode = "true"
              />
            </div>

            <!-- percentage -->
            <div v-else-if="drawerDataFormatted[key]?.format === 'percentage'">
              <a-input-number
                v-if="isEditMode && editFields.includes(key)"
                v-model:value='editData[key]'
                min="0"
                max="100"
                style="width:90px;"
                :formatter="percentageFormatter"
                :parser="percentageParser"
              />
              <div v-if="!isEditMode || !editFields.includes(key)">
                {{drawerDataFormatted[key]?.value}}%
              </div>
            </div>

            <!-- boolean -->
            <div v-else-if="drawerDataFormatted[key]?.format === 'boolean' && isEditMode && editFields.includes(key)">
              <a-radio-group v-model:value="editData[key]">
                <a-radio :value="true">True</a-radio>
                <a-radio :value="false">False</a-radio>
              </a-radio-group>
            </div>

            <!-- code -->
            <div v-else-if="drawerDataFormatted[key]?.format === 'code'" class="drawer-expand">
              <component
                v-if="extRichEditor"
                :key="'rich' + randomRichKey"
                :is="extRichEditor"
                :data="editData[key] || {}"
                :context="{editDataStringified, allowEdit: $props.allowEdit, readOnly: !isEditMode}"
                @onUpdate='(data) => applyExtCodeChanges(key, data)'
              />
            </div>

            <!-- textarea -->
            <div v-else-if="drawerDataFormatted[key]?.format === 'textarea'">
              <div v-if="!isEditMode || !editFields.includes(key)">
                {{drawerDataFormatted[key]?.value}}
              </div>
              <div v-if="isEditMode && editFields.includes(key)">
                <a-textarea :autoSize="{ minRows: 6, maxRows: 6 }" v-model:value='editData[key]' />
              </div>
            </div>

            <!-- itemSelector -->
            <div v-else-if="drawerDataFormatted[key]?.format === 'itemSelector'">
              <div v-if="!isEditMode || !editFields.includes(key)">
                {{helpersUI.getCatalogItemDisplayName(drawerDataFormatted[key]?.value, [...($props.drawerFieldsOptions[key].instanced || []), ...($props.drawerFieldsOptions[key].stackable || [])])}}
              </div>
              <div v-if="isEditMode && editFields.includes(key)">
                <item-selector
                  v-model:value='editData[key]'
                  :instanced="$props.drawerFieldsOptions[key].instanced"
                  :stackable="$props.drawerFieldsOptions[key].stackable"
                />
              </div>
            </div>

            <!-- date -->
            <div v-else-if="drawerDataFormatted[key]?.format === 'date'">
              <DateDisplay :timestamp='drawerDataFormatted[key]?.value'/>
            </div>

            <!-- everything else -->
            <div v-else>
              <template v-if="!isEditMode || !editFields.includes(key)">
                {{drawerDataFormatted[key]?.value}}
              </template>
              <template v-else>
                <a-input v-model:value='editData[key]' />
              </template>
            </div>
          </template>
        </a-form-item>
      </a-form>

      <div class="codeDrawer">
        <JsonTreeView
          :readOnly="!isEditMode"
          v-if="viewMode === 'tree'"
          :content="{text: editDataStringified}"
          :onChange="(changedData) => updateJsonData(changedData.json)"
        />

        <JsonEditor
          :key="randomRichKey"
          v-if="viewMode === 'raw'"
          :rawData='editDataStringified'
          :readOnly='!isEditMode'
          :style="'height: calc(100vh - 160px)'"
          @onUpdate='(changedData) => updateRawData(changedData)'
          @markersChange='onMarkersChange'
        />
      </div>
    </CustomDrawer>
  `
}

const getEditFields = (allowEdit, drawerKeys) => {
  if (allowEdit.length) {
    return allowEdit
  } else if (allowEdit === true) {
    return drawerKeys
  } else return []
}

export const formatDrawerData = (data, keys, formatFields = {}) => {
  const dataFormated = {}
  keys.forEach(key => {
    dataFormated[key] = formatValue(key, data[key], formatFields)
  })

  return dataFormated
}

const formatValue = (key, value, formatFields) => {
  const valueType = typeof value

  let format = valueType !== 'undefined' ? valueType : undefined

  // Object and Arrays
  if (valueType === 'object') {
    format = Array.isArray(value) ? 'keywordList' : 'code'
  }

  // Numbers
  if (value && valueType === 'string') {
    const parsedNumber = Number(value)
    if (!isNaN(parsedNumber)) {
      format = 'number'
    }
  }

  // Enforce given formats
  if (formatFields?.[key]) format = formatFields[key]

  // Display [emptyValue] for readonly fields with no value
  if (value === '' || value === undefined || (Array.isArray(value) && !value.length)) {
    return { format, value: emptyValue }
  }

  if (format === 'code') {
    const codeValue = utils.isEmptyObject(value)
      ? emptyValue
      : utils.convertObjectToFormattedString(value)
    return { format, value: codeValue }
  }

  if (format === 'number') {
    return { format, value: utils.convertNumberToFormattedString(value) }
  }

  return { format, value }
}

export const emptyValue = '-'

export default Drawer
