import AccountsFilter from './AccountsFilter/AccountsFilter.vue.js'
import AccountsSearch from './AccountsSearch/AccountsSearch.vue.js'

const AccountsFinder = {
  components: { AccountsFilter, AccountsSearch },
  props: {
    idProvidersList: Object,
    pageState: Object,
    tags: Array,
    playerGroups: Array
  },
  setup(props) {},
  template: `
    <div class="accountsFinder">
      <AccountsSearch :idProvidersList="$props.idProvidersList" :pageState="pageState"/>
      <AccountsFilter :idProvidersList="$props.idProvidersList" :tags='tags' :playerGroups='playerGroups' />
    </div>
  `
}

export default AccountsFinder
