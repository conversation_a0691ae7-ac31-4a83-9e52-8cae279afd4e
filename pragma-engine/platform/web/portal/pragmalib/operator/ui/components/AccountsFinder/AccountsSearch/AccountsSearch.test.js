import AccountsSearch from './AccountsSearch.vue.js'
import * as VueTestUtils from '@vue/test-utils'

describe('AccountsSearch', () => {
  let wrapper
  beforeEach(() => {
    wrapper = VueTestUtils.mount(AccountsSearch, {
      props: {
        idProvidersList: [{ idProvider: 'DISCORD', separator: '#' }]
      }
    })
  })

  it('renders without crashing', async () => {
    expect(wrapper.text()).toContain('Search By')
  })

  it('renders the correct search options', async () => {
    const searchBtn = wrapper.find('.dropdownBtn')
    await searchBtn.trigger('mouseenter')

    await pragma.test.helpers.waitForSelector('.accountsSearchDropdown')

    const dropdownHTML = wrapper.findComponent('.accountsSearchDropdown .ant-dropdown-menu').html()

    expect(dropdownHTML).toContain('Display Name')
    expect(dropdownHTML).toContain('Account Id')
    expect(dropdownHTML).toContain('Email Address')
  })

  xit('renders search input when option is selected', async () => {
    const searchBtn = wrapper.find('.dropdownBtn')
    await searchBtn.trigger('mouseenter')
    await pragma.test.helpers.waitForSelector('.accountsSearchDropdown')
    document
      .querySelector('.ant-dropdown-menu-submenu-title')
      .dispatchEvent(new Event('mouseenter'))
    await pragma.test.helpers.waitForSelector('.ant-dropdown-menu-submenu-placement-rightTop')
    expect(document.querySelector('#form_item_searchText').exists()).toBe(true)
  })
})
