import { hooks } from '../../../../../main/ui/hooks.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import { utils } from '../../../../../main/utils.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import { variables } from '../GlobalCatalogVariables.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { useRouter } from 'vue-router';

const META_DATA_KEY = variables.META_DATA_KEY

import PopoverWithTable, { formatCosts } from '../PopoverWithTable/PopoverWithTable.vue.js'
import { swappable } from '../../../../../main/ui/helpers/swappableComponent/swappable.js'
import CatalogExtTableCellRender from '../ExtSwappableComponents/ExtTableCellRender.vue.js'

const CraftingEntries = {
  props: {
    entries: Object,
    allItems: Array,
    readOnly: Boolean,
    hideSource: Boolean
  },

  components: { Table, CatalogViewer, Icon, PopoverWithTable },

  setup(props) {
    const router = useRouter()
    const { getSearch } = hooks.useSearch(router)
    const search = getSearch()

    const sourceFiles = Object.keys(props.entries)

    const tableColumns = props.hideSource
      ? utils.removeFromArray(catalogColumns, 'key', 'source')
      : catalogColumns

    const filteredEntries = utils.filterItemsByText(
      formatEntries(props.entries, props.allItems),
      search,
      tableColumns.map(c => c.dataIndex)
    )

    const editableFields = tableColumns
      .map(column => column.dataIndex)
      .filter(key => key !== 'id' && key !== 'source')

    const updateEntry = async data => {
      const file = data.source
      delete data.source
      data.ext = utils.jsonParse(data.ext)
      data.requirements = utils.jsonParse(data.requirements)
      const srcJson = utils.jsonStringify(
        utils.replaceInObjArray(props.entries[file], 'id', data.id, data)
      )

      const payload = {
        contentType: 'CraftingEntries',
        filename: file,
        srcJson
      }

      updateContentCatalog(payload)
    }

    const updateEntryRaw = async (rawData, rawSource) => {
      const payload = {
        contentType: 'CraftingEntries',
        filename: rawSource,
        srcJson: rawData
      }

      updateContentCatalog(payload)
    }

    const deleteEntry = async (entryId, file, close) => {
      const srcJson = utils.jsonStringify(utils.removeFromArray(props.entries[file], 'id', entryId))

      const payload = {
        contentType: 'CraftingEntries',
        filename: file,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    const catalogExtTableCellRender = swappable('Content.CraftingEntry.ExtTable', {
      default: CatalogExtTableCellRender,
      emits: null,
      data: true,
      context: null
    })

    const catalogRequirementsTableCellRender = swappable(
      'Content.CraftingEntry.RequirementsTable',
      {
        default: CatalogExtTableCellRender,
        emits: null,
        data: true,
        context: null
      }
    )

    return {
      filteredEntries,
      tableColumns,
      stackableCostByCatalogIdColumns,
      META_DATA_KEY,
      confirm,
      editableFields,
      updateEntry,
      updateEntryRaw,
      sourceFiles,
      deleteEntry,
      catalogExtTableCellRender,
      catalogRequirementsTableCellRender
    }
  },

  template: `
    <div>
      <CatalogViewer
        :rawData='$props.entries'
        :rawSources='sourceFiles'
        :allowRawEdit="!$props.readOnly"
        :rawEditOnSave="updateEntryRaw"
      >
        <template #richView>
          <Table
            emptyText="'No entries have been found"
            :columns="tableColumns"
            :data="filteredEntries"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'id'">
                <router-link v-if="!$props.readOnly" :to="{ name: 'EditCraftingEntry', params: { entryId: record.id }}">{{ text }}</router-link>
                <router-link v-else :to="{ name: 'ViewCraftingEntry', params: { entryId: record.id }}">{{ text }}</router-link>
              </template>
              <template v-if="column.dataIndex === 'stackableCostByCatalogId'">
                <PopoverWithTable
                  :records="record[META_DATA_KEY].stackableCostByCatalogIdArray"
                  :columns="stackableCostByCatalogIdColumns"
                  :previewFields="{main: 'name', extra: 'catalogId'}"
                />
              </template>
              <template v-if="column.dataIndex === 'requirements'">
                <component
                  :is="catalogRequirementsTableCellRender"
                  :data="record.requirements || {}"
                />
              </template>
              <template v-if="column.dataIndex === 'tags'">
                <a-popover>
                  <template #content>
                    <pre>{{record[META_DATA_KEY].tagsParsed}}</pre>
                  </template>
                  {{record[META_DATA_KEY].tagsString}}
                </a-popover>
              </template>
              <template v-if="column.dataIndex === 'ext'">
                <component
                  :is="catalogExtTableCellRender"
                  :data="record.ext || {}"
                />
              </template>
              <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
                <a-button size='small' ghost danger class='hide actionBtn'
                  @click='confirm.danger({
                    title: "Confirm Entry Deletion",
                    content: "Are you sure you want to delete " + record.id + "?",
                    okText: "Delete",
                    onOk: close => { deleteEntry(record.id, record.source, close) }
                  })'
                >
                  <Icon name='close' size="10" /><span>Delete</span>
                </a-button>
              </template>
            </template>
          </Table>
        </template>
      </CatalogViewer>
    </div>
  `
}

const catalogColumns = [
  {
    title: 'Id',
    dataIndex: 'id',
    key: 'id',
    sorter: true
  },
  {
    title: 'Cost',
    dataIndex: 'stackableCostByCatalogId',
    key: 'stackableCostByCatalogId',
    sorter: true
  },
  {
    title: 'Tags',
    dataIndex: 'tags',
    key: 'tags'
  },
  {
    title: 'Requirements',
    dataIndex: 'requirements',
    key: 'requirements'
  },
  {
    title: 'Ext',
    dataIndex: 'ext',
    key: 'ext'
  },
  {
    title: 'Source',
    dataIndex: 'source',
    key: 'source'
  },
  {
    dataIndex: 'delete',
    align: 'right'
  }
]

const stackableCostByCatalogIdColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: 'Catalog Id',
    dataIndex: 'catalogId',
    key: 'catalogId'
  },
  {
    title: 'Cost',
    dataIndex: 'cost',
    key: 'cost'
  }
]

const formatEntries = (entries, allItems) => {
  return helpersAPI.concatenateContentCatalogSources(entries).map(entry => {
    const stackableCostByCatalogIdArray = formatCosts(entry.stackableCostByCatalogId, allItems)

    const tags = entry.tags?.join(', ') || ''
    const tagsString = utils.addTextEllipsis(tags)
    const tagsParsed = utils.convertObjectToFormattedString(entry.tags)

    return {
      ...entry,
      [META_DATA_KEY]: {
        stackableCostByCatalogIdArray,
        tagsString,
        tagsParsed
      }
    }
  })
}
export default CraftingEntries
