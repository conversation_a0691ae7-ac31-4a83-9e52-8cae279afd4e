import { utils } from '../../../../../../../main/utils.js'
import * as Vue from 'vue';

const InPlayerGroupModal = {
  name: 'InPlayerGroupModal',
  props: {
    visible: Boolean,
    excludedGroups: Array,
    playerGroups: Array
  },
  emits: ['submit', 'cancel'],
  setup({ excludedGroups, playerGroups }, { emit }) {
    const formState = Vue.reactive({ excludedGroups: [], selectedGroups: [] })
    const { jsonParse, jsonStringify } = utils

    const onSubmit = () => {
      const { selectedGroups, excludedGroups } = Vue.toRaw(formState)
      const filtersIncluded =
        selectedGroups.reduce((acc, group) => {
          try {
            const parsedGroup = jsonParse(group)
            return {
              ...acc,
              [parsedGroup.playerGroupId]: {
                filter: { playerGroupIds: [parsedGroup.playerGroupId] },
                id: parsedGroup.playerGroupId,
                label: `In Group: "${parsedGroup.name}"`,
                timestamp: Date.now()
              }
            }
          } catch (error) {
            console.error(error)
            return acc
          }
        }, {}) || {}

      const filtersExcluded =
        excludedGroups.reduce((acc, group) => {
          try {
            const parsedGroup = jsonParse(group)
            return {
              ...acc,
              [parsedGroup.playerGroupId]: {
                filter: { excludedPlayerGroupIds: [parsedGroup.playerGroupId] },
                id: parsedGroup.playerGroupId,
                label: `Not In Group: "${parsedGroup.name}"`,
                timestamp: Date.now()
              }
            }
          } catch (error) {
            console.error(error)
            return acc
          }
        }, {}) || {}

      emit('submit', { ...filtersIncluded, ...filtersExcluded })
    }

    const onCancel = () => {
      emit('cancel')
    }

    const excludedGroupIds = excludedGroups ? excludedGroups.map(g => g.playerGroupId) : []
    const groups = playerGroups.filter(g => !excludedGroupIds.includes(g.playerGroupId))

    return {
      onCancel,
      onSubmit,
      formState,
      groups,
      getContainer: () => document.querySelector('#app'),
      jsonStringify
    }
  },
  template: `
    <a-modal
      class="inPlayerGroupModal"
      :maskClosable="false"
      v-model:open="$props.visible"
      :destroyOnClose="true"
      title="Groups"
      ok-text="Apply"
      @ok="onSubmit"
      @cancel="onCancel"
      :getContainer="getContainer"
    >
      <a-form form layout='vertical' :model="formState">
        <a-form-item label="Currently in player group" name="selectedGroups">
          <a-select
            allowClear
            mode="multiple"
            optionFilterProp='children'
            v-model:value="formState.selectedGroups"
          >
            <a-select-option
              v-for="(g, index) in groups"
              :key="g.playerGroupId"
              :value="jsonStringify(g)"
            >
              {{g.name}}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="Not in player group" name="excludedGroups">
          <a-select
            allowClear
            mode="multiple"
            optionFilterProp='children'
            v-model:value="formState.excludedGroups"
          >
            <a-select-option
              v-for="(g, index) in groups"
              :key="g.playerGroupId"
              :value="jsonStringify(g)"
            >
              {{g.name}}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  `
}

export default InPlayerGroupModal
