import { hooks } from '../../../../../main/ui/hooks.js'
import CatalogViewer from '../../CatalogViewer/CatalogViewer.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import { utils } from '../../../../../main/utils.js'
import confirm from '../../../../../main/ui/helpers/confirm/confirm.js'
import { variables } from '../GlobalCatalogVariables.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { useRouter } from 'vue-router';

const ENTRIES_TO_SHOW = 10
const META_DATA_KEY = variables.META_DATA_KEY

import PopoverWithTable from '../PopoverWithTable/PopoverWithTable.vue.js'
import DateDisplay from '../../../../../main/ui/components/DateDisplay/DateDisplay.vue.js'

const LimitedGrants = {
  props: {
    entries: Object,
    allItems: Array,
    rewardTables: Array,
    readOnly: Boolean,
    hideSource: Boolean
  },

  components: { Table, CatalogViewer, Icon, PopoverWithTable, DateDisplay },

  setup(props) {
    const router = useRouter()
    const { getSearch } = hooks.useSearch(router)
    const search = getSearch()

    const sourceFiles = Object.keys(props.entries)

    const tableColumns = props.hideSource
      ? utils.removeFromArray(catalogColumns, 'key', 'source')
      : catalogColumns

    const filteredEntries = utils.filterItemsByText(
      formatEntries(props.entries, props.allItems, props.rewardTables),
      search,
      tableColumns.map(c => c.dataIndex)
    )

    const editableFields = tableColumns
      .map(column => column.dataIndex)
      .filter(key => key !== 'trackingId' && key !== 'source')

    const updateEntry = async data => {
      const file = data.source
      delete data.source
      const srcJson = utils.jsonStringify(
        utils.replaceInObjArray(props.entries[file], 'trackingId', data.trackingId, data)
      )

      const payload = {
        contentType: 'LimitedGrants',
        filename: file,
        srcJson
      }

      updateContentCatalog(payload)
    }

    const updateEntryRaw = async (rawData, rawSource) => {
      const payload = {
        contentType: 'LimitedGrants',
        filename: rawSource,
        srcJson: rawData
      }

      updateContentCatalog(payload)
    }

    const deleteEntry = async (trackingId, file, close) => {
      const srcJson = utils.jsonStringify(
        utils.removeFromArray(props.entries[file], 'trackingId', trackingId)
      )

      const payload = {
        contentType: 'LimitedGrants',
        filename: file,
        srcJson
      }

      if (await updateContentCatalog(payload)) close()
    }

    return {
      filteredEntries,
      tableColumns,
      META_DATA_KEY,
      confirm,
      editableFields,
      updateEntry,
      updateEntryRaw,
      sourceFiles,
      ENTRIES_TO_SHOW,
      deleteEntry,
      stackableGrantsColumns,
      instancedGrantsColumns,
      rewardGrantsColumns
    }
  },
  template: `
    <div>
      <CatalogViewer
        :rawData='$props.entries'
        :rawSources='sourceFiles'
        :allowRawEdit="!$props.readOnly"
        :rawEditOnSave="updateEntryRaw"
      >
        <template #richView>
          <Table
            emptyText="'No entries have been found"
            :columns="tableColumns"
            :data="filteredEntries"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'trackingId'">
                <router-link v-if="!$props.readOnly" :to="{ name: 'EditLimitedGrant', params: { trackingId: record.trackingId }}">{{ text }}</router-link>
                <router-link v-else :to="{ name: 'ViewLimitedGrant', params: { trackingId: record.trackingId }}">{{ text }}</router-link>
              </template>
              <template v-if="column.dataIndex === 'stackableItems'">
                <template v-if="record.stackableGrants?.length > 0">
                  <PopoverWithTable
                    :records="record[META_DATA_KEY].stackableGrantsFormatted"
                    :columns="stackableGrantsColumns"
                    :previewFields="{main: 'name', extra: 'catalogId'}"
                  />
                </template>
              </template>
              <template v-if="column.dataIndex === 'instancedItems'">
                <template v-if="record.instancedGrants?.length > 0">
                  <PopoverWithTable
                    :records="record[META_DATA_KEY].instancedGrantsFormatted"
                    :columns="instancedGrantsColumns"
                    :previewFields="{main: 'name', extra: 'catalogId'}"
                  />
                </template>
              </template>
              <template v-if="column.dataIndex === 'rewardTables'">
                <template v-if="record.rewardGrants?.length > 0">
                  <PopoverWithTable
                    :records="record[META_DATA_KEY].rewardGrantsFormatted"
                    :columns="rewardGrantsColumns"
                    :previewFields="{main: 'name', extra: 'rewardTableId'}"
                  />
                </template>
              </template>
              <template v-if="column.dataIndex === 'startTime' && record.startTime">
                <DateDisplay :timestamp='record.startTime' />
              </template>
              <template v-if="column.dataIndex === 'endTime' && record.endTime">
                <DateDisplay :timestamp='record.endTime' />
              </template>

              <template v-if="column.dataIndex === 'delete' && !$props.readOnly">
                <a-button size='small' ghost danger class='hide actionBtn'
                  @click='confirm.danger({
                    title: "Confirm Entry Deletion",
                    content: "Are you sure you want to delete " + record.trackingId + "?",
                    okText: "Delete",
                    onOk: close => { deleteEntry(record.trackingId, record.source, close) }
                  })'
                >
                  <Icon name='close' size="10" /><span>Delete</span>
                </a-button>
              </template>
            </template>
          </Table>
        </template>
      </CatalogViewer>
    </div>
  `
}

const catalogColumns = [
  {
    title: 'Tracking Id',
    dataIndex: 'trackingId',
    key: 'trackingId',
    sorter: true
  },
  {
    title: 'Stackable Items',
    dataIndex: 'stackableItems',
    key: 'stackableItems',
    sorter: true
  },
  {
    title: 'Instanced Items',
    dataIndex: 'instancedItems',
    key: 'instancedItems'
  },
  {
    title: 'Reward Tables',
    dataIndex: 'rewardTables',
    key: 'rewardTables'
  },
  {
    title: 'Start Time',
    dataIndex: 'startTime',
    key: 'startTime'
  },
  {
    title: 'End Time',
    dataIndex: 'endTime',
    key: 'endTime'
  },
  {
    title: 'Source',
    dataIndex: 'source',
    key: 'source'
  },
  {
    dataIndex: 'delete',
    align: 'right'
  }
]

const instancedGrantsColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: 'Catalog Id',
    dataIndex: 'catalogId',
    key: 'catalogId'
  }
]

const stackableGrantsColumns = [
  ...instancedGrantsColumns,
  { title: 'Amount', dataIndex: 'amount', key: 'amount' }
]

const rewardGrantsColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: 'Reward Id',
    dataIndex: 'rewardTableId',
    key: 'rewardTableId'
  },
  {
    title: 'Count',
    dataIndex: 'count',
    key: 'count'
  }
]

const formatEntries = (entries, allItems, allRewardTablesItems) => {
  return helpersAPI.concatenateContentCatalogSources(entries).map(entry => {
    const instancedGrantsFormatted = (entry.instancedGrants || []).map(iGrant => ({
      ...iGrant,
      name: helpersAPI.getCatalogNameByCatalogId(allItems, iGrant.catalogId)
    }))

    const stackableGrantsFormatted = (entry.stackableGrants || []).map(sGrant => ({
      ...sGrant,
      name: helpersAPI.getCatalogNameByCatalogId(allItems, sGrant.catalogId)
    }))

    const rewardGrantsFormatted = (entry.rewardGrants || []).map(rewardGrant => ({
      ...rewardGrant,
      name: allRewardTablesItems.find(item => item.id === rewardGrant.rewardTableId)?.name
    }))

    return {
      ...entry,
      [META_DATA_KEY]: {
        instancedGrantsFormatted,
        stackableGrantsFormatted,
        rewardGrantsFormatted
      }
    }
  })
}
export default LimitedGrants
