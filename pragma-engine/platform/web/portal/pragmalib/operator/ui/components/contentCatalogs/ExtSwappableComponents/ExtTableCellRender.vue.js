import { utils } from '../../../../../main/utils.js'

const CatalogExtTableCellRender = {
  props: {
    data: Object,
    context: Object
  },
  setup(props) {
    const stringified = utils.jsonStringify(props.data)
    const formatted =
      stringified === '{}' ? null : utils.addTextEllipsis(stringified).replaceAll('"', '')
    const formattedPopover = utils
      .addLinesEllipsis(utils.convertObjectToFormattedString(props.data))
      .replaceAll('"', '')
    return {
      formatted,
      formattedPopover
    }
  },
  template: `
    <div>
        <a-popover>
          <template #content>
            <pre>{{formattedPopover}}</pre>
          </template>
          <pre>{{formatted}}</pre>
        </a-popover>
    </div>
  `
}

export default CatalogExtTableCellRender
