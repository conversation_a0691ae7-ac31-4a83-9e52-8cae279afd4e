import Icon from '../../../../../main/ui/components/Icon/Icon.vue.js'
import TableWithAdd from '../../../../../main/ui/components/TableWithAdd/TableWithAdd.vue.js'
import Table from '../../../../../main/ui/components/Table/Table.vue.js'
import PageSection from '../../../../../main/ui/components/PageSection/PageSection.vue.js'
import JsonEditor from '../../../../../main/ui/components/JsonEditor/JsonEditor.vue.js'
import JsonTreeView from '../../../../../main/ui/components/JsonTreeViewer/JsonTreeViewer.vue.js'
import { toast } from '../../../../../main/ui/helpers/toast/toast.js'
import { updateContentCatalog } from '../contentCatalogsHelpers.js'
import { swappable } from '../../../../../main/ui/helpers/swappableComponent/swappable.js'
import helpersAPI from '../../../../../main/api/helpersAPI.js'
import { useSubscribe } from '../../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import { utils } from '../../../../../main/utils.js'
import JsonViewTabSelector from '../../../../../main/ui/components/JsonViewTabSelector/JsonViewTabSelector.vue.js'
import InfoParagraph from '../../../../../main/ui/components/InfoParagraph/InfoParagraph.vue.js'
import { useRoute, useRouter } from 'vue-router';
import * as Vue from 'vue';

const ITEM_TO_ADD_KEY_PREFIX = '__to-add__'

const CreateUpdateEntry = {
  name: 'CreateUpdateEntry',
  components: {
    Icon,
    TableWithAdd,
    Table,
    PageSection,
    JsonEditor,
    JsonTreeView,
    JsonViewTabSelector,
    InfoParagraph
  },
  props: {
    entries: Object,
    instancedEntries: Array,
    stackableEntries: Array,
    readOnly: Boolean
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const currentEntryId = route.params?.entryId
    const isCreate = !currentEntryId

    const extRichEditor = swappable('Content.UpdateEntry.ExtRich', {
      default: null,
      emits: ['onUpdate'],
      data: true,
      context: ['readOnly']
    })

    const extView = Vue.ref(extRichEditor ? 'rich' : 'tree')

    const allItemsInCatalog = [].concat(props.instancedEntries).concat(props.stackableEntries)

    const parsedEntries = props.entries
    const sourceFiles = []

    Object.keys(parsedEntries).forEach(file => {
      sourceFiles.push({ value: file, label: file })
    })

    const sourceFile = Object.keys(parsedEntries)?.find(file =>
      parsedEntries[file]?.find(entry => entry.id === currentEntryId)
    )
    const currentObject =
      parsedEntries[sourceFile]?.find(entry => entry.id === currentEntryId) || {}
    const updatedObject = {}

    if (currentObject.costByCatalogId)
      updatedObject.costByCatalogId = Object.keys(currentObject.costByCatalogId).map(catalogId => ({
        name: helpersAPI.getCatalogNameByCatalogId(allItemsInCatalog, catalogId),
        catalogId,
        cost: currentObject.costByCatalogId[catalogId].cost,
        key: utils.getRandomString()
      }))

    if (currentObject.tags) updatedObject.tags = currentObject.tags
    if (currentObject.ext) updatedObject.ext = currentObject.ext

    const entryIdErrorMessage = Vue.ref('')
    const errorMessages = Vue.reactive({})
    const itemsCost = Vue.ref(updatedObject.costByCatalogId || [])
    const formState = Vue.reactive({
      id: currentEntryId || '',
      tags: updatedObject.tags || [],
      ext: utils.jsonStringify(updatedObject.ext || {}, null, 2),
      source: sourceFile || sourceFiles[0].value
    })

    const formatItems = (items, valueColumn) => {
      return items.map(item => ({
        ...item,
        value: item[valueColumn],
        label: `${item.name} (${item.catalogId})`
      }))
    }

    const allCatalogItemsOptions = valueColumn => [
      {
        label: 'Stackable',
        options: formatItems(props.stackableEntries, valueColumn)
      }
    ]

    const extUpdate = extContent => {
      formState.ext = extContent
    }

    const onSubmitCreateEntry = () => {
      helpersAPI.resetErrorMessages(errorMessages)
      entryIdErrorMessage.value = ''

      if (formState.id.toString().trim().length === 0) {
        entryIdErrorMessage.value = 'Entry Id is required'
        return false
      }
      if (isCreate)
        Object.values(parsedEntries).forEach(items => {
          items.forEach(item => {
            if (formState.id.toString().trim() === item.id.toString().trim()) {
              entryIdErrorMessage.value = 'Id is already in use'
            }
          })
        })
      if (entryIdErrorMessage.value) return false
      if (Object.keys(errorMessages).length > 0) {
        toast.error('The form has errors')
        return false
      }

      const costByCatalogId = {}
      if (itemsCost.value.find(item => item.key.startsWith(ITEM_TO_ADD_KEY_PREFIX))) {
        toast.error('You have unsaved changes')
        return false
      }
      itemsCost.value.forEach(item => {
        if (!item.key.startsWith(ITEM_TO_ADD_KEY_PREFIX))
          costByCatalogId[item.catalogId] = { cost: item.cost }
      })

      let entryExt = {}
      try {
        entryExt = utils.jsonParse(formState.ext)
      } catch (error) {
        console.log('Could not parse the ext object')
        entryExt = {}
      }

      const entryToCreate = {
        id: formState.id,
        costByCatalogId,
        tags: [].concat(formState.tags),
        ext: entryExt
      }

      isCreate
        ? parsedEntries[formState.source].push(entryToCreate)
        : (parsedEntries[formState.source] = utils.replaceInObjArray(
            parsedEntries[formState.source],
            'id',
            entryToCreate.id,
            entryToCreate
          ))
      const srcJson = utils.jsonStringify(parsedEntries[formState.source])
      const payload = {
        contentType: 'UpdateEntries',
        filename: formState.source,
        srcJson
      }
      updateContentCatalog(payload)

      helpersAPI.goBackToPage({ name: 'UpdateEntriesEditor', router })
    }

    const addEntry = (entry, parentTitle) => {
      const itemsArray = parentTitle === 'Cost' ? itemsCost.value : receivedQuantity.value
      itemsArray.push(entry)
    }

    const saveEntry = (entry, parentTitle) => {
      const itemsArray = parentTitle === 'Cost' ? itemsCost.value : receivedQuantity.value
      const indexOfEntry = itemsArray.findIndex(item => item.key === entry.key)
      if (entry.key.includes(ITEM_TO_ADD_KEY_PREFIX)) entry.key = utils.getRandomString()

      if (indexOfEntry >= 0) itemsArray[indexOfEntry] = { ...entry }
      else itemsArray.push({ ...entry })
    }

    const deleteEntry = (key, parentTitle) => {
      const itemsArray = parentTitle === 'Cost' ? itemsCost.value : receivedQuantity.value
      const indexOfEntry = itemsArray.findIndex(item => item.key === key)
      if (indexOfEntry >= 0) itemsArray.splice(indexOfEntry, 1)
    }

    const updateTreeData = updatedContent => {
      formState.ext = utils.convertObjectToFormattedString(updatedContent.json)
    }

    const costFilter = (inputValue, option) => {
      if (['Stackable', 'Instanced'].includes(option.label)) return false

      const searchableString = utils.jsonStringify(Object.values(option)).toLowerCase()
      return searchableString.includes(inputValue.toLowerCase())
    }

    useSubscribe('submitCreateEntry', onSubmitCreateEntry)

    const readOnlyColumnsCost = columnsCost.filter(column => column.dataIndex !== 'edit')

    const tagsLabel = Vue.ref('')
    Vue.watchEffect(() => {
      tagsLabel.value = `Tags (${formState.tags.length})`
    })

    const formStateExtJson = Vue.reactive({})

    const tryExtParse = () => {
      try {
        const parsedExt = utils.jsonParse(formState.ext)
        Object.keys(parsedExt).forEach(key => (formStateExtJson[key] = parsedExt[key]))
      } catch (error) {}
    }
    tryExtParse()

    Vue.watch(
      () => formState.ext,
      () => {
        tryExtParse()
      }
    )

    const onExtUpdate = data => {
      formState.ext = utils.convertObjectToFormattedString(data)
    }

    return {
      formState,
      itemsCost,
      columnsCost,
      readOnlyColumnsCost,
      allCatalogItemsOptions,
      extUpdate,
      errorMessages,
      entryIdErrorMessage,
      currentEntryId,
      sourceFiles,
      addEntry,
      saveEntry,
      deleteEntry,
      costFilter,
      JsonTreeView,
      extView,
      extRichEditor,
      formStateExtJson,
      onExtUpdate,
      updateTreeData,
      tagsLabel
    }
  },
  template: `
    <a-form layout='vertical' :model='formState' class="create-entry">
      <a-row :gutter="32" class="create-entry-row">
        <a-col :span="12">
          <a-form-item
            label='Entry Id'
            name='id'
            :help="entryIdErrorMessage"
            :validateStatus="entryIdErrorMessage != '' ? 'error' : ''"
          >
            <a-input v-model:value='formState.id' :disabled="!!currentEntryId" />
            <InfoParagraph v-if="!$props.readOnly" infoText="Once the entry is created the Id cannot be changed"/>
          </a-form-item>
        </a-col>
      </a-row>

      <TableWithAdd
        v-if="!$props.readOnly"
        title="Cost"
        addPrefix="__to-add__"
        :columns="columnsCost"
        :items="itemsCost"
        :allCatalogItemsOptions="allCatalogItemsOptions"
        :errorMessages="errorMessages"
        @addEntry="addEntry"
        @saveEntry="saveEntry"
        @deleteEntry="deleteEntry"
      />
      <PageSection v-if="$props.readOnly" title="Cost" :count="itemsCost.length">
        <Table
          :columns="readOnlyColumnsCost"
          :data="itemsCost"
          :pagination="false"
          class="view-only-table"
        />
      </PageSection>

      <a-row :gutter="32">
        <a-col :span="24" :xxl="{ span: 12 }">
          <a-form-item
            name='tags'
            :label="tagsLabel"
            class="tags-title"
          >
            <a-select
              v-model:value="formState.tags"
              :disabled="$props.readOnly"
              :token-separators="[',']"
              mode="tags"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="32">
        <a-col :span="12">
          <a-form-item
            label="Source"
            name="source"
          >
            <a-select
              :disabled="!!currentEntryId"
              v-model:value="formState.source"
              :options="sourceFiles"
              :defaultValue="sourceFiles[0]"
              notFoundContent=""
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-row type="flex" justify="space-between" align="top">
            <a-form-item
              label='Ext'
              name='ext'
              :span="12"
              style="flex-direction: row; margin: 0 0 -2px"
            ></a-form-item>
            <JsonViewTabSelector
              :currentView='extView'
              @onChange="(newView) => extView = newView"
              :showRich='!!extRichEditor'
              :span="12"
              size='small'
            />
          </a-row>
          <div v-if="extView === 'rich'">
            <component
              :is="extRichEditor"
              :data="formStateExtJson"
              :context="{readOnly: $props.readOnly}"
              @onUpdate='onExtUpdate'
            />
          </div>
          <div v-if="extView === 'raw'">
            <JsonEditor
              :rawData='formState.ext'
              :readOnly='$props.readOnly'
              @onUpdate="extUpdate"
            />
          </div>
          <div v-if="extView === 'tree'" class="jse-theme-dark">
            <JsonTreeView
              :content="{text: formState.ext}"
              :readOnly='$props.readOnly'
              :onChange="updateTreeData"
            ></JsonTreeView>
          </div>
        </a-col>
      </a-row>
    </a-form>
  `
}

const columnsCost = [
  { title: 'Name', dataIndex: 'name', key: 'name' },
  { title: 'Catalog Id', dataIndex: 'catalogId', key: 'catalogId' },
  { title: 'Cost', dataIndex: 'cost', key: 'cost' },
  { dataIndex: 'edit', key: 'edit' }
]

export default CreateUpdateEntry
