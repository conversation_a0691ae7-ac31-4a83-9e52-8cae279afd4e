import { rpc } from '../../../../main/api/rpc.js'
import eventEmitter from '../../../../main/ui/helpers/eventEmitter/eventEmitter.js'
import { toast } from '../../../../main/ui/helpers/toast/toast.js'

export const updateContentCatalog = async (payload, suppressToast = false) => {
  const response = await rpc.requestGameRPC(
    'InventoryRpc.UpdateRawContentSourceDataV1Request',
    payload
  )

  if (!response.ok) {
    toast.error(response.error.message)
    return false
  }

  if (!suppressToast) toast.success(`${payload.contentType} catalog has been modified.`)

  eventEmitter.emit('reloadData')
  eventEmitter.emit('contentCatalogChanged')
  return true
}
