import {
  accountFormatter,
  concatenateInstancedAndStackableItems,
  extractParsedRawContentSource,
  formatGamesByShard,
  getShardsFromGame,
  getShardsFromGames,
  getStoreFromCatalog,
  getStoreFromSource,
  getTagsFromAllStoreEntries,
  parseRawContentSource,
  sortTags
} from './formatters.js'

const { mockedAccountTags, mockedGameShardsData } = pragma.test.mock

const { mockedResponsesByRequestType } = pragma.test.mock.responses

describe('Formatters API', () => {
  it('[accountFormatter] returns formatted account data', () => {
    const accountMock =
      mockedResponsesByRequestType['AccountRpc.ViewSocialIdentityV1Request'].data.response.payload

    const formattedData = accountFormatter({
      data: accountMock
    })
    const accountData = accountMock.socialIdentity
    const socialIdentity = accountData.socialIdentity || accountData
    const pragmaDisplayName = socialIdentity.pragmaDisplayName

    expect(formattedData.discriminator).toEqual(pragmaDisplayName.discriminator)
    expect(formattedData.displayName).toEqual(pragmaDisplayName.displayName)
    expect(formattedData.createdTimestampMillis).toEqual(accountData.createdTimestampMillis)
    expect(formattedData.lastLoginTimestampMillis).toEqual(accountData.lastLoginTimestampMillis)
    expect(formattedData.pragmaPersonalId).toEqual(accountData.pragmaPersonalId)
    expect(formattedData.emailAddress).toEqual(accountData.emailAddress)
    expect(formattedData.emailVerified).toEqual(accountData.emailVerified)
    expect(formattedData.idProviderAccounts).toEqual(accountData.socialIdentity.idProviderAccounts)
    expect(formattedData.pragmaDisplayName).toEqual(pragmaDisplayName)
    expect(formattedData.pragmaPersonalId).toEqual(accountData.pragmaPersonalId)
    expect(formattedData.pragmaPlayerId).toEqual(
      accountData.socialIdentity.gameIdentities[0].pragmaPlayerId
    )
    expect(formattedData.pragmaSocialId).toEqual(accountData.socialIdentity.pragmaSocialId)
    expect(formattedData.tags).toEqual(accountData.tags)
    expect(formattedData.uniqueDisplayName).toEqual(
      `${pragmaDisplayName.displayName}#${pragmaDisplayName.discriminator}`
    )
  })

  it('[formatGamesByShard] groups games by shards', () => {
    const games = mockedGameShardsData
    const shards = formatGamesByShard({ data: { games: games } })

    expect(shards.shardId1.shardId).toEqual('shardId1')
    expect(shards.shardId1.shardName).toEqual('testGameShard1')
    expect(shards.shardId1.gameId).toEqual('gameId1')
    expect(shards.shardId1.gameName).toEqual('game1')

    expect(shards.shardId2.shardId).toEqual('shardId2')
    expect(shards.shardId2.shardName).toEqual('testGameShard2')
    expect(shards.shardId2.gameId).toEqual('gameId1')
    expect(shards.shardId2.gameName).toEqual('game1')
  })

  it('[getShardsFromGame], [getShardsFromGames] returns shards from a given game/array of games', () => {
    const viewGamesResponseMock =
      mockedResponsesByRequestType['GameManagementRpc.ViewGameTitlesV1Request'].data.response
        .payload

    const game = { game: viewGamesResponseMock.games[0] }
    const gameShards = getShardsFromGame({ data: game })

    expect(gameShards[0].id).toEqual(game.game.gameShards[0].id)
    expect(gameShards[0].gameName).toEqual(game.game.name)
    expect(gameShards[1].gameName).toEqual(game.game.name)

    const gamesShards = getShardsFromGames({ data: viewGamesResponseMock })

    expect(gamesShards[0].id).toEqual(viewGamesResponseMock.games[0].gameShards[0].id)
  })

  it('[getTagsFromAllStoreEntries] returns tags from all store entries', () => {
    const catalogOperatorResponseMock = mockedResponsesByRequestType[
      'InventoryRpc.GetCatalogOperatorV1Request'
    ]({ catalog: 'Stores' }).data.response.payload.stores.stores

    const allTags = getTagsFromAllStoreEntries(catalogOperatorResponseMock)

    expect(allTags).toContain(...catalogOperatorResponseMock[0].storeEntries[0].tags)
  })

  it('[sortTags] sorts alphabetically', () => {
    const data = {
      accountTags: mockedAccountTags
    }
    const sortedTags = sortTags({ data })

    expect(sortedTags.accountTags[0].tag).toEqual('survey_completed')
    expect(sortedTags.accountTags[0].tagId).toEqual('2')

    expect(sortedTags.accountTags[1].tag).toEqual('tos_accepted')
    expect(sortedTags.accountTags[1].tagId).toEqual('1')
  })

  it('[getStoreFromCatalog] extracts a specific store from catalog', () => {
    const catalogOperatorResponseMock = mockedResponsesByRequestType[
      'InventoryRpc.GetCatalogOperatorV1Request'
    ]({ catalog: 'Stores' }).data.response

    const catalog = catalogOperatorResponseMock.payload
    const testStore = catalog.stores.stores[0]
    const store = getStoreFromCatalog({ data: catalog, urlParams: { storeId: testStore.id } })

    expect(store).toEqual(testStore)
  })

  it('[getStoreFromSource] extract a specific store from a source file', () => {
    const rawContentSourceOperatorMock = mockedResponsesByRequestType[
      'InventoryRpc.GetRawContentSourceOperatorV1Request'
    ]({ contentType: 'Stores' }).data.response.payload

    const storeId = 'storeId1'

    const formattedData = getStoreFromSource({
      data: rawContentSourceOperatorMock,
      urlParams: { storeId }
    })

    expect(formattedData.id).toEqual(storeId)
  })

  it('[concatenateInstancedAndStackableItems] concatenates instanced and stackable items', () => {
    const catalogOperatorResponseMock = mockedResponsesByRequestType[
      'InventoryRpc.GetCatalogOperatorV1Request'
    ]({ catalog: 'Items' }).data.response

    const items = catalogOperatorResponseMock.payload

    const concatItems = concatenateInstancedAndStackableItems({ data: items })

    expect(
      concatItems.find(item => item.catalogId == items.items.instancedEntries[0].catalogId)
    ).toBeTruthy()
    expect(
      concatItems.find(item => item.catalogId == items.items.stackableEntries[0].catalogId)
    ).toBeTruthy()
  })

  it('[parseRawContentSource] parses raw content source and fills empty ext', () => {
    const rawContentSourceOperatorMock = mockedResponsesByRequestType[
      'InventoryRpc.GetRawContentSourceOperatorV1Request'
    ]({ contentType: 'StackableSpecs' }).data.response.payload

    const parsedRawContentSource = parseRawContentSource({ data: rawContentSourceOperatorMock })

    const resultParsed = parsedRawContentSource.sourceFiles['StackableSpecs.json']

    resultParsed.forEach(res => {
      expect(res.ext).toBeTruthy()
    })
  })

  it('[extractParsedRawContentSource] returns all items from multiple content sources', () => {
    const rawContentSourceOperatorMock = mockedResponsesByRequestType[
      'InventoryRpc.GetRawContentSourceOperatorV1Request'
    ]({ contentType: 'StackableSpecs' }).data.response.payload

    const extractedParsedContentSource = extractParsedRawContentSource({
      data: rawContentSourceOperatorMock
    })

    expect(extractedParsedContentSource.find(item => item.catalogId == 'fireShard')).toBeTruthy()
    expect(extractedParsedContentSource.find(item => item.catalogId == 'fireShard2')).toBeTruthy()
  })
})
