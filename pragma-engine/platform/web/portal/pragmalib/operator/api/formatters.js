import jsonUtils from '../../main/utils/jsonUtils.js'
import timeUtils from '../../main/utils/timeUtils.js'
import stringUtils from '../../main/utils/stringUtils.js'
import numberUtils from '../../main/utils/numberUtils.js'
import dayjs from 'dayjs';

export const accountFormatter = ({ data }) => formatAccount(data)

export const formatGamesByShard = ({ data }) => {
  const gamesByShardId = {}

  data.games.forEach(game => {
    game.gameShards.forEach(shard => {
      gamesByShardId[shard.id] = {
        gameId: game.id,
        gameName: game.name,
        gameDescription: game.description || '',
        shardId: shard.id,
        shardName: shard.name,
        shardDescription: shard.description
      }
    })
  })

  return gamesByShardId
}

export const getShardsFromGames = ({ data }) => shardsList(data.games)
export const getShardsFromGame = ({ data }) => shardsList([data.game])

export const sortTags = ({ data }) => {
  data.accountTags.sort((tagA, tagB) => tagA.tag.localeCompare(tagB.tag))
  return data
}

export const getTagsFromAllStoreEntries = (stores = []) => {
  const allTags = []
  stores.forEach(store => {
    ; (store.storeEntries || []).forEach(entry => {
      ; (entry.tags || []).forEach(tag => allTags.push(tag))
    })
  })

  return allTags
}

export const getStoreFromCatalog = ({ data, urlParams }) =>
  data?.stores?.stores?.find(obj => obj.id === urlParams.storeId)

export const getStoreFromSource = ({ data, urlParams }) => {
  const parsedStores = {}
  Object.keys(data.sourceFiles).forEach(file => {
    parsedStores[file] = jsonUtils.jsonParse(data.sourceFiles[file])
  })
  const storeFile = Object.keys(parsedStores)?.find(file =>
    parsedStores[file]?.find(store => store.id === urlParams.storeId)
  )
  return parsedStores[storeFile]?.find(store => store.id === urlParams.storeId) || {}
}

export const getRewardTableFromCatalog = ({ data, urlParams }) =>
  data?.find(obj => obj.id === urlParams.rewardTableId)

export const getRewardTableFromSource = ({ data, urlParams }) => {
  const rewardTableFile = Object.keys(data.sourceFiles)?.find(file =>
    data.sourceFiles[file]?.find(rewardTable => rewardTable.id === urlParams.rewardTableId)
  )
  return (
    data.sourceFiles[rewardTableFile]?.find(
      rewardTable => rewardTable.id === urlParams.rewardTableId
    ) || {}
  )
}

export const concatenateInstancedAndStackableItems = ({ data }) => {
  return data.items.instancedEntries.concat(data.items.stackableEntries)
}

export const parseRawContentSource = ({ data }) => {
  const result = { sourceFiles: {} }
  for (const file in data.sourceFiles) {
    try {
      // file contents arrive as a simple string, so we need to decode it
      result.sourceFiles[file] = fillEmptyExts(jsonUtils.jsonParse(data.sourceFiles[file]))
    } catch (error) {
      console.error('Cannot parse JSON')
    }
  }
  return result
}

export const parseRawContentSourceNoExt = ({ data }) => {
  const result = { sourceFiles: {} }
  for (const file in data.sourceFiles) {
    try {
      // file contents arrive as a simple string, so we need to decode it
      result.sourceFiles[file] = jsonUtils.jsonParse(data.sourceFiles[file])
    } catch (error) {
      console.error('Cannot parse JSON')
    }
  }
  return result
}

export const extractParsedRawContentSource = response => {
  const parsedResponse = parseRawContentSourceNoExt(response)
  const extractedItems = []
  Object.values(parsedResponse.sourceFiles).forEach(file =>
    file.forEach(item => extractedItems.push(item))
  )
  return extractedItems
}

// Unused by core portal. If neither by customers, please remove!
////////////////////////////////////////////////////////////////////////

export const accountsFormatter = ({ data }) => {
  const socialIdentities = data.socialIdentities
  return socialIdentities.map(formatAccount)
}

export const getEntryFromCatalog = ({ data, urlParams }) =>
  data?.updates?.updateEntries.find(obj => obj.id === urlParams.entryId)

////////////////////////////////////////////////////////////////////////

// Private Functions
const shardsList = games => {
  return games.flatMap(game => {
    return game.gameShards.map(shard => {
      return { ...shard, gameName: game.name, gameId: game.id }
    })
  })
}

const fillEmptyExts = data => data.map(item => ({ ...item, ext: item.ext || {} }))

const formatAccount = rawAccount => {
  const accountData = rawAccount.socialIdentity
  const socialIdentity = accountData.socialIdentity || accountData
  const pragmaDisplayName = socialIdentity.pragmaDisplayName
  return {
    ...socialIdentity,
    ...pragmaDisplayName,
    pragmaPlayerId: socialIdentity.gameIdentities[0]?.pragmaPlayerId,
    pragmaPersonalId: accountData.pragmaPersonalId,
    uniqueDisplayName: `${pragmaDisplayName.displayName}#${pragmaDisplayName.discriminator}`,
    emailAddress: accountData.emailAddress,
    emailVerified: accountData.emailVerified,
    tags: accountData.tags?.sort((tagA, tagB) => tagA.tag.localeCompare(tagB.tag)),
    createdTimestampMillis: accountData.createdTimestampMillis,
    lastLoginTimestampMillis: accountData.lastLoginTimestampMillis
  }
}

const formatBanRecord = (banRecord, shards, socialIdToDisplayName, timeProxy = timeProxy) => {
  return {
    ...banRecord,
    ...banRecord.ban,
    timeRemaining: formatTimeRemaining(banRecord.ban, timeProxy),
    endTimeInMillis: getEndTime(banRecord.ban),
    banScope: formatBanScope(banRecord.ban, shards),
    status: formatBanStatus(banRecord.ban, timeProxy),
    banReason: stringUtils.formatEnum(banRecord.ban.banReason, 'EXT_BAN_REASON_'),
    formattedBannedBy: formatSocialId(banRecord.bannedBy, socialIdToDisplayName),
    formattedRevokedBy: formatSocialId(banRecord.revokedBy, socialIdToDisplayName)
  }
}

const formatSocialId = (uuid, socialIdToDisplayName) => {
  return socialIdToDisplayName[uuid]
}

const formatBanScope = (ban, shards) => {
  if (ban.banScope === 'BAN_SCOPE_FULL_ACCOUNT') {
    return 'Account'
  } else if (ban.banScope === 'BAN_SCOPE_ALL_GAME_SHARDS') {
    return 'All Games'
  }

  const gameShard = shards.find((shard) => shard.id === ban.gameShardId)
  if (!gameShard) {
    return `${ban.gameShardId}`
  }
  return `${gameShard.gameName} - ${gameShard.name}`
}

const formatTimeRemaining = (ban, timeProxy) => {
  if (ban.durationInMillis == numberUtils.MAX_LONG) {
    return "Permanent"
  }
  const durationRemaining = getDurationRemainingInMillis(ban, timeProxy)
  if (durationRemaining <= 0) {
    return "0 days"
  }
  const startTime = dayjs(timeProxy.getCurrentTimeMillis())
  const endTime = dayjs(parseInt(ban.startTimestampMillis) + parseInt(ban.durationInMillis))

  return startTime.to(endTime, true)
}

const getEndTime = (ban) => {
  return parseInt(ban.startTimestampMillis) + parseInt(ban.durationInMillis)
}

const formatBanStatus = (ban, timeProxy) => {
  if (ban.revoked) {
    return 'Revoked'
  }

  const durationRemaining = getDurationRemainingInMillis(ban, timeProxy)
  if (durationRemaining <= 0) {
    return 'Expired'
  }

  return 'Active'
}

const getDurationRemainingInMillis = (ban, timeProxy) => {
  const currentTime = timeProxy.getCurrentTimeMillis()
  const endTime = parseInt(ban.startTimestampMillis) + parseInt(ban.durationInMillis)
  return endTime - currentTime
}

export default {
  accountsFormatter,
  accountFormatter,
  formatGamesByShard,
  getShardsFromGames,
  getShardsFromGame,
  sortTags,
  getTagsFromAllStoreEntries,
  getStoreFromCatalog,
  getStoreFromSource,
  getRewardTableFromCatalog,
  getRewardTableFromSource,
  concatenateInstancedAndStackableItems,
  parseRawContentSource,
  parseRawContentSourceNoExt,
  extractParsedRawContentSource,
  shardsList,
  formatAccount,
  formatBanRecord,
  formatBanScope,
}
