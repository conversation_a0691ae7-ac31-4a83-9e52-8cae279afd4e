// these are added using the base.js config to pragma.ext.pragmaOperator
// and only available in the operator build

import components from './ui/components/components.js'
import formatters from './api/formatters.js'
import helpers from './ui/helpers.js'

const extOperator = {}

extOperator.ui = {}
extOperator.ui.components = components
extOperator.ui.helpers = helpers

extOperator.api = {}
extOperator.api.formatters = formatters

export default extOperator
