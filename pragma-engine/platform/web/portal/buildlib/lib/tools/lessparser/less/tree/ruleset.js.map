{"version": 3, "file": "ruleset.js", "sourceRoot": "", "sources": ["../../../src/less/tree/ruleset.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,sEAAwC;AACxC,8DAAgC;AAChC,8DAAgC;AAChC,0DAA4B;AAC5B,gEAAkC;AAClC,8DAAgC;AAChC,kEAAoC;AACpC,iEAAmC;AACnC,6FAAoE;AACpE,yEAA+C;AAC/C,oEAAwC;AACxC,sDAAkC;AAElC,IAAM,OAAO,GAAG,UAAS,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,cAAc;IACpE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACnC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAEtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC,CAAA;AAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC1C,IAAI,EAAE,SAAS;IACf,SAAS,EAAE,IAAI;IAEf,aAAa,gBAAK,OAAO,IAAI,CAAC,CAAC,CAAC;IAEhC,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SACrD;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACvD;QACD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/C;IACL,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,SAAS,CAAC;QACd,IAAI,MAAM,CAAC;QACX,IAAI,QAAQ,CAAC;QACb,IAAI,CAAC,CAAC;QACN,IAAI,WAAW,CAAC;QAChB,IAAI,qBAAqB,GAAG,KAAK,CAAC;QAElC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACpD,SAAS,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,iBAAW,CAAC,KAAK,CAAC;gBACd,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,0DAA0D;aACtE,CAAC,CAAC;YAEH,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzB,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC/C,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;wBACjC,WAAW,GAAG,IAAI,CAAC;wBACnB,MAAM;qBACT;iBACJ;gBACD,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;gBACxB,IAAI,QAAQ,CAAC,cAAc,EAAE;oBACzB,qBAAqB,GAAG,IAAI,CAAC;iBAChC;aACJ;YAED,IAAI,WAAW,EAAE;gBACb,IAAM,gBAAgB,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC3C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;oBACzB,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACxB,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;iBACjD;gBACD,IAAI,CAAC,KAAK,CAAC,SAAS,CAChB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAC1B,CAAC,WAAW,CAAC,EACb,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EACvB,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EACvB,UAAS,GAAG,EAAE,MAAM;oBAChB,IAAI,MAAM,EAAE;wBACR,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;qBAC1C;gBACL,CAAC,CAAC,CAAC;aACV;YAED,iBAAW,CAAC,KAAK,EAAE,CAAC;SACvB;aAAM;YACH,qBAAqB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5D,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACzF,IAAI,IAAI,CAAC;QACT,IAAI,OAAO,CAAC;QAEZ,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;QAC/B,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACzB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACnC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAEzC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;SACtC;QAED,IAAI,CAAC,qBAAqB,EAAE;YACxB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;SACpB;QAED,mEAAmE;QACnE,qCAAqC;QACrC,OAAO,CAAC,gBAAgB,GAAG,CAAC,UAAU,MAAM;YACxC,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;YACxB,IAAI,KAAK,CAAC;YACV,OAAQ,CAAC,KAAK,CAAC,EAAG,EAAE,CAAC,EAAG;gBACpB,KAAK,GAAG,MAAM,CAAE,CAAC,CAAE,CAAC,gBAAgB,CAAC;gBACrC,IAAK,KAAK,EAAG;oBAAE,OAAO,KAAK,CAAC;iBAAE;aACjC;YACD,OAAO,2BAAsB,CAAC;QAClC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAE7B,+CAA+C;QAC/C,IAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE3B,qBAAqB;QACrB,IAAI,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,CAAC,SAAS,GAAG,YAAY,GAAG,EAAE,CAAC;SACzC;QACD,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAErC,mBAAmB;QACnB,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAChE,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAChC;QAED,6CAA6C;QAC7C,8DAA8D;QAC9D,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;QAC9B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACnC;SACJ;QAED,IAAM,eAAe,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEjF,wBAAwB;QACxB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;gBAC3B,0BAA0B;gBAC1B,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAS,CAAC;oBACxC,IAAI,CAAC,CAAC,YAAY,qBAAW,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;wBAC1C,8CAA8C;wBAC9C,+CAA+C;wBAC/C,qDAAqD;wBACrD,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;qBACtC;oBACD,OAAO,IAAI,CAAC;gBAChB,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpD,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACtB,OAAO,CAAC,UAAU,EAAE,CAAC;aACxB;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAM,cAAc,EAAE;gBACtC,0BAA0B;gBAC1B,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAS,CAAC;oBAC9C,IAAI,CAAC,CAAC,YAAY,qBAAW,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;wBAC1C,kCAAkC;wBAClC,OAAO,KAAK,CAAC;qBAChB;oBACD,OAAO,IAAI,CAAC;gBAChB,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpD,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACtB,OAAO,CAAC,UAAU,EAAE,CAAC;aACxB;SACJ;QAED,2BAA2B;QAC3B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;aAC7D;SACJ;QAED,2BAA2B;QAC3B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,8DAA8D;YAC9D,IAAI,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1E,8CAA8C;gBAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,oBAAoB,EAAE,EAAE;oBAC/D,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;oBAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC5C,IAAI,OAAO,YAAY,cAAI,EAAE;4BACzB,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;4BAClD,IAAI,CAAC,CAAC,OAAO,YAAY,qBAAW,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gCACxD,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;6BACnC;yBACJ;qBACJ;iBACJ;aACJ;SACJ;QAED,gBAAgB;QAChB,SAAS,CAAC,KAAK,EAAE,CAAC;QAClB,YAAY,CAAC,KAAK,EAAE,CAAC;QAErB,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,KAAK,CAAC,GAAG,eAAe,EAAE,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;aACrD;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,YAAC,OAAO;QACf,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,CAAC;QACN,IAAI,WAAW,CAAC;QAChB,IAAI,CAAC,KAAK,EAAE;YAAE,OAAO;SAAE;QAEvB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC5B,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACrC,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;oBACjE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;oBACtD,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC/B;qBAAM;oBACH,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;iBACnC;gBACD,IAAI,CAAC,UAAU,EAAE,CAAC;aACrB;SACJ;IACL,CAAC;IAED,aAAa;QACT,IAAM,MAAM,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;YACjE,IAAI,CAAC,CAAC,aAAa,EAAE;gBACjB,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;aAC5B;iBAAM;gBACH,OAAO,CAAC,CAAC;aACZ;QACL,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAE/C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,SAAS,YAAC,IAAI;QACV,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,4CAA4C;IAC5C,cAAc,YAAC,IAAI,EAAE,OAAO;QACxB,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;YAC9B,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,YAAY,CAAC,SAAS;YACtB,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CACxB,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EACrB,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;QACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACvB,CAAC;IAED,SAAS;QACL,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC;gBACpE,IAAI,CAAC,YAAY,qBAAW,IAAI,CAAC,CAAC,QAAQ,KAAK,IAAI,EAAE;oBACjD,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACpB;gBACD,iFAAiF;gBACjF,6CAA6C;gBAC7C,kEAAkE;gBAClE,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnD,IAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;oBAChC,KAAK,IAAM,MAAI,IAAI,IAAI,EAAE;wBACrB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAI,CAAC,EAAE;4BAC3B,IAAI,CAAC,MAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAI,CAAC,CAAC;yBACtC;qBACJ;iBACJ;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,EAAE,EAAE,CAAC,CAAC;SACV;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC;gBACrE,IAAI,CAAC,YAAY,qBAAW,IAAI,CAAC,CAAC,QAAQ,KAAK,IAAI,EAAE;oBACjD,IAAM,MAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,iBAAO,CAAC,CAAC,CAAC;wBAClE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC7B,+CAA+C;oBAC/C,IAAI,CAAC,IAAI,CAAC,MAAI,MAAM,CAAC,EAAE;wBACnB,IAAI,CAAC,MAAI,MAAM,CAAC,GAAG,CAAE,CAAC,CAAE,CAAC;qBAC5B;yBACI;wBACD,IAAI,CAAC,MAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBAC5B;iBACJ;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,EAAE,EAAE,CAAC,CAAC;SACV;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,QAAQ,YAAC,IAAI;QACT,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAChC;IACL,CAAC;IAED,QAAQ,YAAC,IAAI;QACT,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAChC;IACL,CAAC;IAED,eAAe;QACX,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,IAAI,IAAI,YAAY,qBAAW,EAAE;gBAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aAChC;SACJ;IACL,CAAC;IAED,UAAU,YAAC,OAAO;QACd,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,SAAS,oBAAoB,CAAC,IAAI;YAC9B,IAAI,IAAI,CAAC,KAAK,YAAY,mBAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACjD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE;oBACtC,IAAI,CAAC,KAAK,CAAC,SAAS,CAChB,IAAI,CAAC,KAAK,CAAC,KAAK,EAChB,CAAC,OAAO,EAAE,WAAW,CAAC,EACtB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EACrB,IAAI,CAAC,QAAQ,EAAE,EACf,UAAS,GAAG,EAAE,MAAM;wBAChB,IAAI,GAAG,EAAE;4BACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;yBACtB;wBACD,IAAI,MAAM,EAAE;4BACR,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;4BACvB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;4BACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;yBACtB;oBACL,CAAC,CAAC,CAAC;iBACV;qBAAM;oBACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;iBACtB;gBAED,OAAO,IAAI,CAAC;aACf;iBACI;gBACD,OAAO,IAAI,CAAC;aACf;QACL,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACnD;aACI;YACD,IAAM,OAAK,GAAG,EAAE,CAAC;YACjB,OAAO,CAAC,OAAO,CAAC,UAAS,CAAC;gBACtB,OAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,OAAO,OAAK,CAAC;SAChB;IACL,CAAC;IAED,QAAQ;QACJ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YAAE,OAAO,EAAE,CAAC;SAAE;QAE/B,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,CAAC;QACN,IAAI,IAAI,CAAC;QAET,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxB;SACJ;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,WAAW,YAAC,IAAI;QACZ,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACvB;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,CAAE,IAAI,CAAE,CAAC;SACzB;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,YAAC,QAAQ,EAAE,IAAI,EAAE,MAAM;QACvB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC;QACpB,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,KAAK,CAAC;QACV,IAAI,WAAW,CAAC;QAChB,IAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QAE7B,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SAAE;QAExD,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,UAAU,IAAI;YAClC,IAAI,IAAI,KAAK,IAAI,EAAE;gBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,IAAI,KAAK,EAAE;wBACP,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE;4BAClC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;gCACzB,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,kBAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;gCACpF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oCACzC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iCAClC;gCACD,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;6BAClD;yBACJ;6BAAM;4BACH,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC,CAAC;yBACjC;wBACD,MAAM;qBACT;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC3B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,IAAI,wBAAwB;QACxB,SAAS,CAAC;QAEd,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC;QAET,OAAO,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAO,CAAC,QAAQ,EAAE,CAAC;SACtB;QAED,IAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClF,IAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7E,IAAI,GAAG,CAAC;QAER,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,IAAI,YAAY,iBAAO,EAAE;gBACzB,IAAI,eAAe,KAAK,CAAC,EAAE;oBACvB,eAAe,EAAE,CAAC;iBACrB;gBACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxB;iBAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;gBAC3C,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC5C,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,CAAC;aACrB;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC/B,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC3C,eAAe,EAAE,CAAC;aACrB;iBAAM;gBACH,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxB;SACJ;QACD,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAE/C,4CAA4C;QAC5C,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,SAAS,GAAG,oBAAY,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAEnD,IAAI,SAAS,EAAE;gBACX,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACtB,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aACzB;YAED,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,IAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;YAC7B,IAAI,UAAU,SAAA,CAAC;YAEf,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAM,SAAW,CAAC,CAAC;YAEnD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChB,IAAI,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE;oBAAE,SAAS;iBAAE;gBAC9C,IAAI,CAAC,GAAG,CAAC,EAAE;oBAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBAAE;gBAE/B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAEhC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC9B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;oBAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;iBACnC;aACJ;YAED,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC;SAC9D;QAED,6BAA6B;QAC7B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAEpC,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE;gBAC5B,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;aAC3B;YAED,IAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;YACzC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;gBAC1B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;aAC5B;YAED,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAChC;iBAAM,IAAI,IAAI,CAAC,KAAK,EAAE;gBACnB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;aACrC;YAED,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC;YAEnC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;gBACvC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAK,UAAY,CAAC,CAAC,CAAC;aAC3D;iBAAM;gBACH,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;aAC5B;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAK,SAAS,MAAG,CAAC,CAAC,CAAC;YACzD,OAAO,CAAC,QAAQ,EAAE,CAAC;SACtB;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YAC1D,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACpB;IACL,CAAC;IAED,aAAa,YAAC,KAAK,EAAE,OAAO,EAAE,SAAS;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACnD;IACL,CAAC;IAED,YAAY,YAAC,KAAK,EAAE,OAAO,EAAE,QAAQ;QAEjC,SAAS,iBAAiB,CAAC,aAAa,EAAE,eAAe;YACrD,IAAI,gBAAgB,EAAE,CAAC,CAAC;YACxB,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC5B,gBAAgB,GAAG,IAAI,eAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;aAClD;iBAAM;gBACH,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACrD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACvC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,iBAAO,CACzB,IAAI,EACJ,aAAa,CAAC,CAAC,CAAC,EAChB,eAAe,CAAC,UAAU,EAC1B,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,SAAS,CAC5B,CAAC;iBACL;gBACD,gBAAgB,GAAG,IAAI,eAAK,CAAC,IAAI,kBAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;aAC5D;YACD,OAAO,gBAAgB,CAAC;QAC5B,CAAC;QAED,SAAS,cAAc,CAAC,gBAAgB,EAAE,eAAe;YACrD,IAAI,OAAO,EAAE,QAAQ,CAAC;YACtB,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,gBAAgB,EAAE,eAAe,CAAC,UAAU,EAAE,eAAe,CAAC,MAAM,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;YAC7H,QAAQ,GAAG,IAAI,kBAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACnC,OAAO,QAAQ,CAAC;QACpB,CAAC;QAED,2EAA2E;QAC3E,yEAAyE;QACzE,4BAA4B;QAC5B,SAAS,sBAAsB,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB;YACrF,IAAI,eAAe,EAAE,YAAY,EAAE,iBAAiB,CAAC;YACrD,wBAAwB;YACxB,eAAe,GAAG,EAAE,CAAC;YAErB,8EAA8E;YAC9E,4EAA4E;YAC5E,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;gBACjD,YAAY,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC;gBACrC,iBAAiB,GAAG,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC9F;iBACI;gBACD,iBAAiB,GAAG,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;aAC1D;YAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,6DAA6D;gBAC7D,gDAAgD;gBAChD,gEAAgE;gBAChE,2DAA2D;gBAC3D,yFAAyF;gBACzF,IAAI,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;gBAE5C,IAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,UAAU,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,EAAE;oBACxE,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;iBACpC;gBACD,6DAA6D;gBAC7D,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,iBAAO,CACvC,UAAU,EACV,QAAQ,CAAC,KAAK,EACd,eAAe,CAAC,UAAU,EAC1B,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,SAAS,CAC5B,CAAC,CAAC;gBACH,iBAAiB,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAChG;YAED,4DAA4D;YAC5D,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC3C;YAED,iFAAiF;YACjF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAClC,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,QAAQ;oBAC1C,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC;gBACH,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;aACxD;YACD,OAAO,eAAe,CAAC;QAC3B,CAAC;QAED,wFAAwF;QACxF,yEAAyE;QACzE,4CAA4C;QAC5C,SAAS,0BAA0B,CAAE,aAAa,EAAE,QAAQ,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM;YACnG,IAAI,CAAC,CAAC;YACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvC,IAAM,eAAe,GAAG,sBAAsB,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC;gBAC9G,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aAChC;YACD,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,SAAS,0BAA0B,CAAC,QAAQ,EAAE,SAAS;YACnD,IAAI,CAAC,EAAE,GAAG,CAAC;YAEX,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvB,OAAQ;aACX;YACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,SAAS,CAAC,IAAI,CAAC,CAAE,IAAI,kBAAQ,CAAC,QAAQ,CAAC,CAAE,CAAC,CAAC;gBAC3C,OAAO;aACV;YAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACnC,uEAAuE;gBACvE,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;oBAChB,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC1G;qBACI;oBACD,GAAG,CAAC,IAAI,CAAC,IAAI,kBAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;iBACpC;aACJ;QACL,CAAC;QAED,iFAAiF;QACjF,wDAAwD;QACxD,sEAAsE;QACtE,SAAS,qBAAqB,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU;YACrD,6BAA6B;YAC7B,wDAAwD;YACxD,8DAA8D;YAC9D,OAAO;YACP,WAAW;YACX,SAAS;YACT,MAAM;YACN,IAAI;YACJ,6BAA6B;YAC7B,EAAE;YACF,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,eAAe,EAAE,YAAY,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,EAAE,iBAAiB,GAAG,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC;YAC1H,SAAS,kBAAkB,CAAC,OAAO;gBAC/B,IAAI,aAAa,CAAC;gBAClB,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,eAAK,CAAC,EAAE;oBACnC,OAAO,IAAI,CAAC;iBACf;gBAED,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;gBACpC,IAAI,CAAC,CAAC,aAAa,YAAY,kBAAQ,CAAC,EAAE;oBACtC,OAAO,IAAI,CAAC;iBACf;gBAED,OAAO,aAAa,CAAC;YACzB,CAAC;YAED,gDAAgD;YAChD,eAAe,GAAG,EAAE,CAAC;YACrB,wDAAwD;YACxD,iGAAiG;YACjG,iBAAiB;YACjB,YAAY,GAAG;gBACX,EAAE;aACL,CAAC;YAEF,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5C,+CAA+C;gBAC/C,IAAI,EAAE,CAAC,KAAK,KAAK,GAAG,EAAE;oBAClB,IAAM,cAAc,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAC;oBAC9C,IAAI,cAAc,IAAI,IAAI,EAAE;wBACxB,yDAAyD;wBACzD,6CAA6C;wBAC7C,0BAA0B,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;wBAE1D,IAAM,WAAW,GAAG,EAAE,CAAC;wBACvB,IAAI,QAAQ,SAAA,CAAC;wBACb,IAAM,oBAAoB,GAAG,EAAE,CAAC;wBAChC,QAAQ,GAAG,qBAAqB,CAAC,WAAW,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;wBACvE,iBAAiB,GAAG,iBAAiB,IAAI,QAAQ,CAAC;wBAClD,wGAAwG;wBACxG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BACrC,IAAM,mBAAmB,GAAG,cAAc,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;4BACtF,0BAA0B,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,oBAAoB,CAAC,CAAC;yBACzG;wBACD,YAAY,GAAG,oBAAoB,CAAC;wBACpC,eAAe,GAAG,EAAE,CAAC;qBACxB;yBAAM;wBACH,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;qBAC5B;iBAEJ;qBAAM;oBACH,iBAAiB,GAAG,IAAI,CAAC;oBACzB,mCAAmC;oBACnC,mBAAmB,GAAG,EAAE,CAAC;oBAEzB,yDAAyD;oBACzD,6CAA6C;oBAC7C,0BAA0B,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;oBAE1D,qCAAqC;oBACrC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACtC,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;wBACtB,sFAAsF;wBACtF,mCAAmC;wBACnC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;4BACtB,sFAAsF;4BACtF,iBAAiB;4BACjB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gCAChB,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,iBAAO,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;6BAChG;4BACD,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACjC;6BACI;4BACD,2BAA2B;4BAC3B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gCACjC,uCAAuC;gCACvC,qEAAqE;gCACrE,IAAM,eAAe,GAAG,sBAAsB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC;gCAChF,uCAAuC;gCACvC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;6BAC7C;yBACJ;qBACJ;oBAED,4DAA4D;oBAC5D,YAAY,GAAG,mBAAmB,CAAC;oBACnC,eAAe,GAAG,EAAE,CAAC;iBACxB;aACJ;YAED,wDAAwD;YACxD,2CAA2C;YAC3C,0BAA0B,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE1D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAChC,IAAI,MAAM,GAAG,CAAC,EAAE;oBACZ,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC3C,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;iBAC1G;aACJ;YAED,OAAO,iBAAiB,CAAC;QAC7B,CAAC;QAED,SAAS,cAAc,CAAC,cAAc,EAAE,UAAU;YAC9C,IAAM,WAAW,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;YACpH,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAC/C,OAAO,WAAW,CAAC;QACvB,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAiB,CAAC;QAEnC,QAAQ,GAAG,EAAE,CAAC;QACd,iBAAiB,GAAG,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEvE,IAAI,CAAC,iBAAiB,EAAE;YACpB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,QAAQ,GAAG,EAAE,CAAC;gBACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAEjC,IAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;oBAE1F,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5B,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC/B;aACJ;iBACI;gBACD,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC3B;SACJ;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3B;IAEL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,OAAO,CAAC", "sourcesContent": ["import Node from './node';\nimport Declaration from './declaration';\nimport Keyword from './keyword';\nimport Comment from './comment';\nimport Paren from './paren';\nimport Selector from './selector';\nimport Element from './element';\nimport Anonymous from './anonymous';\nimport contexts from '../contexts';\nimport globalFunctionRegistry from '../functions/function-registry';\nimport defaultFunc from '../functions/default';\nimport getDebugInfo from './debug-info';\nimport * as utils from '../utils';\n\nconst Ruleset = function(selectors, rules, strictImports, visibilityInfo) {\n    this.selectors = selectors;\n    this.rules = rules;\n    this._lookups = {};\n    this._variables = null;\n    this._properties = null;\n    this.strictImports = strictImports;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n\n    this.setParent(this.selectors, this);\n    this.setParent(this.rules, this);\n}\n\nRuleset.prototype = Object.assign(new Node(), {\n    type: 'Ruleset',\n    isRuleset: true,\n\n    isRulesetLike() { return true; },\n\n    accept(visitor) {\n        if (this.paths) {\n            this.paths = visitor.visitArray(this.paths, true);\n        } else if (this.selectors) {\n            this.selectors = visitor.visitArray(this.selectors);\n        }\n        if (this.rules && this.rules.length) {\n            this.rules = visitor.visitArray(this.rules);\n        }\n    },\n\n    eval(context) {\n        const that = this;\n        let selectors;\n        let selCnt;\n        let selector;\n        let i;\n        let hasVariable;\n        let hasOnePassingSelector = false;\n\n        if (this.selectors && (selCnt = this.selectors.length)) {\n            selectors = new Array(selCnt);\n            defaultFunc.error({\n                type: 'Syntax',\n                message: 'it is currently only allowed in parametric mixin guards,'\n            });\n\n            for (i = 0; i < selCnt; i++) {\n                selector = this.selectors[i].eval(context);\n                for (var j = 0; j < selector.elements.length; j++) {\n                    if (selector.elements[j].isVariable) {\n                        hasVariable = true;\n                        break;\n                    }\n                }\n                selectors[i] = selector;\n                if (selector.evaldCondition) {\n                    hasOnePassingSelector = true;\n                }\n            }\n\n            if (hasVariable) {\n                const toParseSelectors = new Array(selCnt);\n                for (i = 0; i < selCnt; i++) {\n                    selector = selectors[i];\n                    toParseSelectors[i] = selector.toCSS(context);\n                }\n                this.parse.parseNode(\n                    toParseSelectors.join(','),\n                    [\"selectors\"], \n                    selectors[0].getIndex(), \n                    selectors[0].fileInfo(), \n                    function(err, result) {\n                        if (result) {\n                            selectors = utils.flattenArray(result);\n                        }\n                    });\n            }\n\n            defaultFunc.reset();\n        } else {\n            hasOnePassingSelector = true;\n        }\n\n        let rules = this.rules ? utils.copyArray(this.rules) : null;\n        const ruleset = new Ruleset(selectors, rules, this.strictImports, this.visibilityInfo());\n        let rule;\n        let subRule;\n\n        ruleset.originalRuleset = this;\n        ruleset.root = this.root;\n        ruleset.firstRoot = this.firstRoot;\n        ruleset.allowImports = this.allowImports;\n\n        if (this.debugInfo) {\n            ruleset.debugInfo = this.debugInfo;\n        }\n\n        if (!hasOnePassingSelector) {\n            rules.length = 0;\n        }\n\n        // inherit a function registry from the frames stack when possible;\n        // otherwise from the global registry\n        ruleset.functionRegistry = (function (frames) {\n            let i = 0;\n            const n = frames.length;\n            let found;\n            for ( ; i !== n ; ++i ) {\n                found = frames[ i ].functionRegistry;\n                if ( found ) { return found; }\n            }\n            return globalFunctionRegistry;\n        }(context.frames)).inherit();\n\n        // push the current ruleset to the frames stack\n        const ctxFrames = context.frames;\n        ctxFrames.unshift(ruleset);\n\n        // currrent selectors\n        let ctxSelectors = context.selectors;\n        if (!ctxSelectors) {\n            context.selectors = ctxSelectors = [];\n        }\n        ctxSelectors.unshift(this.selectors);\n\n        // Evaluate imports\n        if (ruleset.root || ruleset.allowImports || !ruleset.strictImports) {\n            ruleset.evalImports(context);\n        }\n\n        // Store the frames around mixin definitions,\n        // so they can be evaluated like closures when the time comes.\n        const rsRules = ruleset.rules;\n        for (i = 0; (rule = rsRules[i]); i++) {\n            if (rule.evalFirst) {\n                rsRules[i] = rule.eval(context);\n            }\n        }\n\n        const mediaBlockCount = (context.mediaBlocks && context.mediaBlocks.length) || 0;\n\n        // Evaluate mixin calls.\n        for (i = 0; (rule = rsRules[i]); i++) {\n            if (rule.type === 'MixinCall') {\n                /* jshint loopfunc:true */\n                rules = rule.eval(context).filter(function(r) {\n                    if ((r instanceof Declaration) && r.variable) {\n                        // do not pollute the scope if the variable is\n                        // already there. consider returning false here\n                        // but we need a way to \"return\" variable from mixins\n                        return !(ruleset.variable(r.name));\n                    }\n                    return true;\n                });\n                rsRules.splice.apply(rsRules, [i, 1].concat(rules));\n                i += rules.length - 1;\n                ruleset.resetCache();\n            } else if (rule.type ===  'VariableCall') {\n                /* jshint loopfunc:true */\n                rules = rule.eval(context).rules.filter(function(r) {\n                    if ((r instanceof Declaration) && r.variable) {\n                        // do not pollute the scope at all\n                        return false;\n                    }\n                    return true;\n                });\n                rsRules.splice.apply(rsRules, [i, 1].concat(rules));\n                i += rules.length - 1;\n                ruleset.resetCache();\n            }\n        }\n\n        // Evaluate everything else\n        for (i = 0; (rule = rsRules[i]); i++) {\n            if (!rule.evalFirst) {\n                rsRules[i] = rule = rule.eval ? rule.eval(context) : rule;\n            }\n        }\n\n        // Evaluate everything else\n        for (i = 0; (rule = rsRules[i]); i++) {\n            // for rulesets, check if it is a css guard and can be removed\n            if (rule instanceof Ruleset && rule.selectors && rule.selectors.length === 1) {\n                // check if it can be folded in (e.g. & where)\n                if (rule.selectors[0] && rule.selectors[0].isJustParentSelector()) {\n                    rsRules.splice(i--, 1);\n\n                    for (var j = 0; (subRule = rule.rules[j]); j++) {\n                        if (subRule instanceof Node) {\n                            subRule.copyVisibilityInfo(rule.visibilityInfo());\n                            if (!(subRule instanceof Declaration) || !subRule.variable) {\n                                rsRules.splice(++i, 0, subRule);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n\n        // Pop the stack\n        ctxFrames.shift();\n        ctxSelectors.shift();\n\n        if (context.mediaBlocks) {\n            for (i = mediaBlockCount; i < context.mediaBlocks.length; i++) {\n                context.mediaBlocks[i].bubbleSelectors(selectors);\n            }\n        }\n\n        return ruleset;\n    },\n\n    evalImports(context) {\n        const rules = this.rules;\n        let i;\n        let importRules;\n        if (!rules) { return; }\n\n        for (i = 0; i < rules.length; i++) {\n            if (rules[i].type === 'Import') {\n                importRules = rules[i].eval(context);\n                if (importRules && (importRules.length || importRules.length === 0)) {\n                    rules.splice.apply(rules, [i, 1].concat(importRules));\n                    i += importRules.length - 1;\n                } else {\n                    rules.splice(i, 1, importRules);\n                }\n                this.resetCache();\n            }\n        }\n    },\n\n    makeImportant() {\n        const result = new Ruleset(this.selectors, this.rules.map(function (r) {\n            if (r.makeImportant) {\n                return r.makeImportant();\n            } else {\n                return r;\n            }\n        }), this.strictImports, this.visibilityInfo());\n\n        return result;\n    },\n\n    matchArgs(args) {\n        return !args || args.length === 0;\n    },\n\n    // lets you call a css selector with a guard\n    matchCondition(args, context) {\n        const lastSelector = this.selectors[this.selectors.length - 1];\n        if (!lastSelector.evaldCondition) {\n            return false;\n        }\n        if (lastSelector.condition &&\n            !lastSelector.condition.eval(\n                new contexts.Eval(context,\n                    context.frames))) {\n            return false;\n        }\n        return true;\n    },\n\n    resetCache() {\n        this._rulesets = null;\n        this._variables = null;\n        this._properties = null;\n        this._lookups = {};\n    },\n\n    variables() {\n        if (!this._variables) {\n            this._variables = !this.rules ? {} : this.rules.reduce(function (hash, r) {\n                if (r instanceof Declaration && r.variable === true) {\n                    hash[r.name] = r;\n                }\n                // when evaluating variables in an import statement, imports have not been eval'd\n                // so we need to go inside import statements.\n                // guard against root being a string (in the case of inlined less)\n                if (r.type === 'Import' && r.root && r.root.variables) {\n                    const vars = r.root.variables();\n                    for (const name in vars) {\n                        if (vars.hasOwnProperty(name)) {\n                            hash[name] = r.root.variable(name);\n                        }\n                    }\n                }\n                return hash;\n            }, {});\n        }\n        return this._variables;\n    },\n\n    properties() {\n        if (!this._properties) {\n            this._properties = !this.rules ? {} : this.rules.reduce(function (hash, r) {\n                if (r instanceof Declaration && r.variable !== true) {\n                    const name = (r.name.length === 1) && (r.name[0] instanceof Keyword) ?\n                        r.name[0].value : r.name;\n                    // Properties don't overwrite as they can merge\n                    if (!hash[`$${name}`]) {\n                        hash[`$${name}`] = [ r ];\n                    }\n                    else {\n                        hash[`$${name}`].push(r);\n                    }\n                }\n                return hash;\n            }, {});\n        }\n        return this._properties;\n    },\n\n    variable(name) {\n        const decl = this.variables()[name];\n        if (decl) {\n            return this.parseValue(decl);\n        }\n    },\n\n    property(name) {\n        const decl = this.properties()[name];\n        if (decl) {\n            return this.parseValue(decl);\n        }\n    },\n\n    lastDeclaration() {\n        for (let i = this.rules.length; i > 0; i--) {\n            const decl = this.rules[i - 1];\n            if (decl instanceof Declaration) {\n                return this.parseValue(decl);\n            }\n        }\n    },\n\n    parseValue(toParse) {\n        const self = this;\n        function transformDeclaration(decl) {\n            if (decl.value instanceof Anonymous && !decl.parsed) {\n                if (typeof decl.value.value === 'string') {\n                    this.parse.parseNode(\n                        decl.value.value,\n                        ['value', 'important'], \n                        decl.value.getIndex(), \n                        decl.fileInfo(), \n                        function(err, result) {\n                            if (err) {\n                                decl.parsed = true;\n                            }\n                            if (result) {\n                                decl.value = result[0];\n                                decl.important = result[1] || '';\n                                decl.parsed = true;\n                            }\n                        });\n                } else {\n                    decl.parsed = true;\n                }\n\n                return decl;\n            }\n            else {\n                return decl;\n            }\n        }\n        if (!Array.isArray(toParse)) {\n            return transformDeclaration.call(self, toParse);\n        }\n        else {\n            const nodes = [];\n            toParse.forEach(function(n) {\n                nodes.push(transformDeclaration.call(self, n));\n            });\n            return nodes;\n        }\n    },\n\n    rulesets() {\n        if (!this.rules) { return []; }\n\n        const filtRules = [];\n        const rules = this.rules;\n        let i;\n        let rule;\n\n        for (i = 0; (rule = rules[i]); i++) {\n            if (rule.isRuleset) {\n                filtRules.push(rule);\n            }\n        }\n\n        return filtRules;\n    },\n\n    prependRule(rule) {\n        const rules = this.rules;\n        if (rules) {\n            rules.unshift(rule);\n        } else {\n            this.rules = [ rule ];\n        }\n        this.setParent(rule, this);\n    },\n\n    find(selector, self, filter) {\n        self = self || this;\n        const rules = [];\n        let match;\n        let foundMixins;\n        const key = selector.toCSS();\n\n        if (key in this._lookups) { return this._lookups[key]; }\n\n        this.rulesets().forEach(function (rule) {\n            if (rule !== self) {\n                for (let j = 0; j < rule.selectors.length; j++) {\n                    match = selector.match(rule.selectors[j]);\n                    if (match) {\n                        if (selector.elements.length > match) {\n                            if (!filter || filter(rule)) {\n                                foundMixins = rule.find(new Selector(selector.elements.slice(match)), self, filter);\n                                for (let i = 0; i < foundMixins.length; ++i) {\n                                    foundMixins[i].path.push(rule);\n                                }\n                                Array.prototype.push.apply(rules, foundMixins);\n                            }\n                        } else {\n                            rules.push({ rule, path: []});\n                        }\n                        break;\n                    }\n                }\n            }\n        });\n        this._lookups[key] = rules;\n        return rules;\n    },\n\n    genCSS(context, output) {\n        let i;\n        let j;\n        const charsetRuleNodes = [];\n        let ruleNodes = [];\n\n        let // Line number debugging\n            debugInfo;\n\n        let rule;\n        let path;\n\n        context.tabLevel = (context.tabLevel || 0);\n\n        if (!this.root) {\n            context.tabLevel++;\n        }\n\n        const tabRuleStr = context.compress ? '' : Array(context.tabLevel + 1).join('  ');\n        const tabSetStr = context.compress ? '' : Array(context.tabLevel).join('  ');\n        let sep;\n\n        let charsetNodeIndex = 0;\n        let importNodeIndex = 0;\n        for (i = 0; (rule = this.rules[i]); i++) {\n            if (rule instanceof Comment) {\n                if (importNodeIndex === i) {\n                    importNodeIndex++;\n                }\n                ruleNodes.push(rule);\n            } else if (rule.isCharset && rule.isCharset()) {\n                ruleNodes.splice(charsetNodeIndex, 0, rule);\n                charsetNodeIndex++;\n                importNodeIndex++;\n            } else if (rule.type === 'Import') {\n                ruleNodes.splice(importNodeIndex, 0, rule);\n                importNodeIndex++;\n            } else {\n                ruleNodes.push(rule);\n            }\n        }\n        ruleNodes = charsetRuleNodes.concat(ruleNodes);\n\n        // If this is the root node, we don't render\n        // a selector, or {}.\n        if (!this.root) {\n            debugInfo = getDebugInfo(context, this, tabSetStr);\n\n            if (debugInfo) {\n                output.add(debugInfo);\n                output.add(tabSetStr);\n            }\n\n            const paths = this.paths;\n            const pathCnt = paths.length;\n            let pathSubCnt;\n\n            sep = context.compress ? ',' : (`,\\n${tabSetStr}`);\n\n            for (i = 0; i < pathCnt; i++) {\n                path = paths[i];\n                if (!(pathSubCnt = path.length)) { continue; }\n                if (i > 0) { output.add(sep); }\n\n                context.firstSelector = true;\n                path[0].genCSS(context, output);\n\n                context.firstSelector = false;\n                for (j = 1; j < pathSubCnt; j++) {\n                    path[j].genCSS(context, output);\n                }\n            }\n\n            output.add((context.compress ? '{' : ' {\\n') + tabRuleStr);\n        }\n\n        // Compile rules and rulesets\n        for (i = 0; (rule = ruleNodes[i]); i++) {\n\n            if (i + 1 === ruleNodes.length) {\n                context.lastRule = true;\n            }\n\n            const currentLastRule = context.lastRule;\n            if (rule.isRulesetLike(rule)) {\n                context.lastRule = false;\n            }\n\n            if (rule.genCSS) {\n                rule.genCSS(context, output);\n            } else if (rule.value) {\n                output.add(rule.value.toString());\n            }\n\n            context.lastRule = currentLastRule;\n\n            if (!context.lastRule && rule.isVisible()) {\n                output.add(context.compress ? '' : (`\\n${tabRuleStr}`));\n            } else {\n                context.lastRule = false;\n            }\n        }\n\n        if (!this.root) {\n            output.add((context.compress ? '}' : `\\n${tabSetStr}}`));\n            context.tabLevel--;\n        }\n\n        if (!output.isEmpty() && !context.compress && this.firstRoot) {\n            output.add('\\n');\n        }\n    },\n\n    joinSelectors(paths, context, selectors) {\n        for (let s = 0; s < selectors.length; s++) {\n            this.joinSelector(paths, context, selectors[s]);\n        }\n    },\n\n    joinSelector(paths, context, selector) {\n\n        function createParenthesis(elementsToPak, originalElement) {\n            let replacementParen, j;\n            if (elementsToPak.length === 0) {\n                replacementParen = new Paren(elementsToPak[0]);\n            } else {\n                const insideParent = new Array(elementsToPak.length);\n                for (j = 0; j < elementsToPak.length; j++) {\n                    insideParent[j] = new Element(\n                        null,\n                        elementsToPak[j],\n                        originalElement.isVariable,\n                        originalElement._index,\n                        originalElement._fileInfo\n                    );\n                }\n                replacementParen = new Paren(new Selector(insideParent));\n            }\n            return replacementParen;\n        }\n\n        function createSelector(containedElement, originalElement) {\n            let element, selector;\n            element = new Element(null, containedElement, originalElement.isVariable, originalElement._index, originalElement._fileInfo);\n            selector = new Selector([element]);\n            return selector;\n        }\n\n        // joins selector path from `beginningPath` with selector path in `addPath`\n        // `replacedElement` contains element that is being replaced by `addPath`\n        // returns concatenated path\n        function addReplacementIntoPath(beginningPath, addPath, replacedElement, originalSelector) {\n            let newSelectorPath, lastSelector, newJoinedSelector;\n            // our new selector path\n            newSelectorPath = [];\n\n            // construct the joined selector - if & is the first thing this will be empty,\n            // if not newJoinedSelector will be the last set of elements in the selector\n            if (beginningPath.length > 0) {\n                newSelectorPath = utils.copyArray(beginningPath);\n                lastSelector = newSelectorPath.pop();\n                newJoinedSelector = originalSelector.createDerived(utils.copyArray(lastSelector.elements));\n            }\n            else {\n                newJoinedSelector = originalSelector.createDerived([]);\n            }\n\n            if (addPath.length > 0) {\n                // /deep/ is a CSS4 selector - (removed, so should deprecate)\n                // that is valid without anything in front of it\n                // so if the & does not have a combinator that is \"\" or \" \" then\n                // and there is a combinator on the parent, then grab that.\n                // this also allows + a { & .b { .a & { ... though not sure why you would want to do that\n                let combinator = replacedElement.combinator;\n\n                const parentEl = addPath[0].elements[0];\n                if (combinator.emptyOrWhitespace && !parentEl.combinator.emptyOrWhitespace) {\n                    combinator = parentEl.combinator;\n                }\n                // join the elements so far with the first part of the parent\n                newJoinedSelector.elements.push(new Element(\n                    combinator,\n                    parentEl.value,\n                    replacedElement.isVariable,\n                    replacedElement._index,\n                    replacedElement._fileInfo\n                ));\n                newJoinedSelector.elements = newJoinedSelector.elements.concat(addPath[0].elements.slice(1));\n            }\n\n            // now add the joined selector - but only if it is not empty\n            if (newJoinedSelector.elements.length !== 0) {\n                newSelectorPath.push(newJoinedSelector);\n            }\n\n            // put together the parent selectors after the join (e.g. the rest of the parent)\n            if (addPath.length > 1) {\n                let restOfPath = addPath.slice(1);\n                restOfPath = restOfPath.map(function (selector) {\n                    return selector.createDerived(selector.elements, []);\n                });\n                newSelectorPath = newSelectorPath.concat(restOfPath);\n            }\n            return newSelectorPath;\n        }\n\n        // joins selector path from `beginningPath` with every selector path in `addPaths` array\n        // `replacedElement` contains element that is being replaced by `addPath`\n        // returns array with all concatenated paths\n        function addAllReplacementsIntoPath( beginningPath, addPaths, replacedElement, originalSelector, result) {\n            let j;\n            for (j = 0; j < beginningPath.length; j++) {\n                const newSelectorPath = addReplacementIntoPath(beginningPath[j], addPaths, replacedElement, originalSelector);\n                result.push(newSelectorPath);\n            }\n            return result;\n        }\n\n        function mergeElementsOnToSelectors(elements, selectors) {\n            let i, sel;\n\n            if (elements.length === 0) {\n                return ;\n            }\n            if (selectors.length === 0) {\n                selectors.push([ new Selector(elements) ]);\n                return;\n            }\n\n            for (i = 0; (sel = selectors[i]); i++) {\n                // if the previous thing in sel is a parent this needs to join on to it\n                if (sel.length > 0) {\n                    sel[sel.length - 1] = sel[sel.length - 1].createDerived(sel[sel.length - 1].elements.concat(elements));\n                }\n                else {\n                    sel.push(new Selector(elements));\n                }\n            }\n        }\n\n        // replace all parent selectors inside `inSelector` by content of `context` array\n        // resulting selectors are returned inside `paths` array\n        // returns true if `inSelector` contained at least one parent selector\n        function replaceParentSelector(paths, context, inSelector) {\n            // The paths are [[Selector]]\n            // The first list is a list of comma separated selectors\n            // The inner list is a list of inheritance separated selectors\n            // e.g.\n            // .a, .b {\n            //   .c {\n            //   }\n            // }\n            // == [[.a] [.c]] [[.b] [.c]]\n            //\n            let i, j, k, currentElements, newSelectors, selectorsMultiplied, sel, el, hadParentSelector = false, length, lastSelector;\n            function findNestedSelector(element) {\n                let maybeSelector;\n                if (!(element.value instanceof Paren)) {\n                    return null;\n                }\n\n                maybeSelector = element.value.value;\n                if (!(maybeSelector instanceof Selector)) {\n                    return null;\n                }\n\n                return maybeSelector;\n            }\n\n            // the elements from the current selector so far\n            currentElements = [];\n            // the current list of new selectors to add to the path.\n            // We will build it up. We initiate it with one empty selector as we \"multiply\" the new selectors\n            // by the parents\n            newSelectors = [\n                []\n            ];\n\n            for (i = 0; (el = inSelector.elements[i]); i++) {\n                // non parent reference elements just get added\n                if (el.value !== '&') {\n                    const nestedSelector = findNestedSelector(el);\n                    if (nestedSelector != null) {\n                        // merge the current list of non parent selector elements\n                        // on to the current list of selectors to add\n                        mergeElementsOnToSelectors(currentElements, newSelectors);\n\n                        const nestedPaths = [];\n                        let replaced;\n                        const replacedNewSelectors = [];\n                        replaced = replaceParentSelector(nestedPaths, context, nestedSelector);\n                        hadParentSelector = hadParentSelector || replaced;\n                        // the nestedPaths array should have only one member - replaceParentSelector does not multiply selectors\n                        for (k = 0; k < nestedPaths.length; k++) {\n                            const replacementSelector = createSelector(createParenthesis(nestedPaths[k], el), el);\n                            addAllReplacementsIntoPath(newSelectors, [replacementSelector], el, inSelector, replacedNewSelectors);\n                        }\n                        newSelectors = replacedNewSelectors;\n                        currentElements = [];\n                    } else {\n                        currentElements.push(el);\n                    }\n\n                } else {\n                    hadParentSelector = true;\n                    // the new list of selectors to add\n                    selectorsMultiplied = [];\n\n                    // merge the current list of non parent selector elements\n                    // on to the current list of selectors to add\n                    mergeElementsOnToSelectors(currentElements, newSelectors);\n\n                    // loop through our current selectors\n                    for (j = 0; j < newSelectors.length; j++) {\n                        sel = newSelectors[j];\n                        // if we don't have any parent paths, the & might be in a mixin so that it can be used\n                        // whether there are parents or not\n                        if (context.length === 0) {\n                            // the combinator used on el should now be applied to the next element instead so that\n                            // it is not lost\n                            if (sel.length > 0) {\n                                sel[0].elements.push(new Element(el.combinator, '', el.isVariable, el._index, el._fileInfo));\n                            }\n                            selectorsMultiplied.push(sel);\n                        }\n                        else {\n                            // and the parent selectors\n                            for (k = 0; k < context.length; k++) {\n                                // We need to put the current selectors\n                                // then join the last selector's elements on to the parents selectors\n                                const newSelectorPath = addReplacementIntoPath(sel, context[k], el, inSelector);\n                                // add that to our new set of selectors\n                                selectorsMultiplied.push(newSelectorPath);\n                            }\n                        }\n                    }\n\n                    // our new selectors has been multiplied, so reset the state\n                    newSelectors = selectorsMultiplied;\n                    currentElements = [];\n                }\n            }\n\n            // if we have any elements left over (e.g. .a& .b == .b)\n            // add them on to all the current selectors\n            mergeElementsOnToSelectors(currentElements, newSelectors);\n\n            for (i = 0; i < newSelectors.length; i++) {\n                length = newSelectors[i].length;\n                if (length > 0) {\n                    paths.push(newSelectors[i]);\n                    lastSelector = newSelectors[i][length - 1];\n                    newSelectors[i][length - 1] = lastSelector.createDerived(lastSelector.elements, inSelector.extendList);\n                }\n            }\n\n            return hadParentSelector;\n        }\n\n        function deriveSelector(visibilityInfo, deriveFrom) {\n            const newSelector = deriveFrom.createDerived(deriveFrom.elements, deriveFrom.extendList, deriveFrom.evaldCondition);\n            newSelector.copyVisibilityInfo(visibilityInfo);\n            return newSelector;\n        }\n\n        // joinSelector code follows\n        let i, newPaths, hadParentSelector;\n\n        newPaths = [];\n        hadParentSelector = replaceParentSelector(newPaths, context, selector);\n\n        if (!hadParentSelector) {\n            if (context.length > 0) {\n                newPaths = [];\n                for (i = 0; i < context.length; i++) {\n\n                    const concatenated = context[i].map(deriveSelector.bind(this, selector.visibilityInfo()));\n\n                    concatenated.push(selector);\n                    newPaths.push(concatenated);\n                }\n            }\n            else {\n                newPaths = [[selector]];\n            }\n        }\n\n        for (i = 0; i < newPaths.length; i++) {\n            paths.push(newPaths[i]);\n        }\n\n    }\n});\n\nexport default Ruleset;\n"]}