import{u as b,d as oe,a as O,b as P,w as re,c as ne,r as _,s as K,e as se,o as ie,f as le,g as ce}from"./index-BU6JD9Pc.js";import{B as ee}from"./BackendInfo.vue-CAumvPj0.js";import{E as pe}from"./ErrorBox.vue-BG7XLG1f.js";const U={props:{id:String,type:{type:String,validator(e){return["success","info","warning","error"].includes(e)},default:"info"},message:{type:String,required:!0},details:{type:Array,default:[]}},template:`
    <a-alert
      :type='$props.type'
      show-icon
      closable
    >
      <template #message>
        <strong>{{$props.message}}</strong>
      </template>
      <template #description>
        <div v-for="detail in $props.details">{{detail}}</div>
      </template>
    </a-alert>
  `},{onAuthClick:ue,redirectAfterSignin:me,SessionType:A}=pragma.auth,{store:I}=pragma,{Icon:de}=pragma.ui.components,{appBlueprint:ge}=pragma.app.blueprint,D=pragma.utils,fe=pragma.ui.helpers.flashMessage,te={name:"SignInPage",components:{Icon:de,BackendInfo:ee,FlashMessage:U},setup(){const e=b(),t=I.get("app.sessionType"),a=t==A.OPERATOR,r=t==A.PLAYER;D.capitalizeFirstLetter(I.get("appInfo.backendMode").toLowerCase());const n=fe.retrieveAndRemoveAll(),s=(I.get("config.authenticationIdProviders")||[]).filter(u=>{var f,g,k;return((f=u.config)==null?void 0:f.showPortalLoginButton)==="true"&&(t==A.PLAYER&&((g=u.config)==null?void 0:g.playerLoginEnabled)=="true"||t==A.OPERATOR&&((k=u.config)==null?void 0:k.operatorLoginEnabled)=="true")}),m=D.sortItemsByPosition(ge.authProviders);return{showProvider:u=>I.get("config.isDevelopment")||s.find(f=>f.providerId===u),redirect:()=>me(e),authProviders:m,utils:D,router:e,isOperatorPortal:a,isPlayerPortal:r,flashMessages:n,onAuthClick:ue}},template:`
    <div class='SignInPage auth-container'>
      <Icon name="logoPortal" size='30' class='logo' />
      <a-card :bordered="false" class='card card-auth'>
        <div>
          <a-space v-if="isOperatorPortal" direction='vertical' size='middle' align='center' class='alert'>
            <BackendInfo />
          </a-space>

          <component
            v-if="$route.meta.component"
            :is='$route.meta.component'
            @success="redirect"
          />

          <div v-else class='options'>
            <h4 class='header'>Sign In</h4>
            <a-space direction='vertical' size='middle' align='center' class='alert'>
              <template v-for='(message, id) in flashMessages' >
                <FlashMessage
                  :id='id'
                  :type='message.type'
                  :message='message.message'
                  :details='message.details'
                />
              </template>
            </a-space>
            <p class='text'>Choose how to sign in to your account</p>
            <a-form layout='vertical' class="auth-form-2col">
              <template v-for="authProvider in authProviders">
                <a-form-item v-if="showProvider(authProvider.id)">
                  <a-button class='ant-btn-secondary' @click="onAuthClick(authProvider, router)"
                    size='large' :block="true" style="text-align: left; justify-content: revert;">
                    <Icon :name='authProvider.iconName' />{{utils.convertCamelToSpaces(authProvider.name)}}
                  </a-button>
                </a-form-item>
              </template>
            </a-form>
            
            <span v-if='isPlayerPortal'>Don't have an account? <router-link :to="{name: 'CreateAccount'}">Create account</router-link></span>
          </div>
          

        </div>
      </a-card>
    </div>
  `},{onCreateAccountClick:ve,redirectAfterSignin:he,SessionType:H}=pragma.auth,{store:N}=pragma,{Icon:ye}=pragma.ui.components,{appBlueprint:Pe}=pragma.app.blueprint,Q=pragma.utils,be=pragma.ui.helpers.flashMessage,ke={name:"CreateAccountPage",components:{Icon:ye,BackendInfo:ee,FlashMessage:U},setup(){const e=b(),t=N.get("app.sessionType"),a=t==H.OPERATOR,r=be.retrieveAndRemoveAll(),n=(N.get("config.authenticationIdProviders")||[]).filter(p=>{var u,f,g;return((u=p.config)==null?void 0:u.showPortalLoginButton)==="true"&&(t==H.PLAYER&&((f=p.config)==null?void 0:f.playerLoginEnabled)=="true"||t==H.OPERATOR&&((g=p.config)==null?void 0:g.operatorLoginEnabled)=="true")}),s=Q.sortItemsByPosition(Pe.authProviders);return{showProvider:p=>N.get("config.isDevelopment")||n.find(u=>u.providerId===p),redirect:()=>he(e),authProviders:s,utils:Q,router:e,isOperatorPortal:a,flashMessages:r,onCreateAccountClick:ve}},template:`
    <div class='CreateAccountPage auth-container'>
      <Icon name='logoPortal' size='30' class='logo' />
      <a-card :bordered='false' class='card card-auth'>
        <div>
          <a-space v-if='isOperatorPortal' direction='vertical' size='middle' align='center' class='alert'>
            <BackendInfo />
          </a-space>

          <component
            v-if='$route.meta.component'
            :is='$route.meta.component'
            @success='redirect'
          />

          <div v-else class='options'>
            <h4 class='header'>Create Account</h4>
            <a-space direction='vertical' size='middle' align='center' class='alert'>
              <template v-for='(message, id) in flashMessages'>
                <FlashMessage
                  :id='id'
                  :type='message.type'
                  :message='message.message'
                  :details='message.details'
                />
              </template>
            </a-space>
            <p class='text'>Choose how to create your account</p>
            <a-form layout='vertical' class="auth-form-2col">
              <template v-for='authProvider in authProviders'>
                <a-form-item v-if='showProvider(authProvider.id)'>
                  <a-button class='ant-btn-secondary' @click='onCreateAccountClick(authProvider, router)'
                            size='large' :block='true'>
                    <Icon :name='authProvider.iconName' />
                    {{ utils.convertCamelToSpaces(authProvider.name) }}
                  </a-button>
                </a-form-item>
              </template>
            </a-form>
          </div>

          <span>Have an account? <router-link :to="{name: 'SignIn'}">Sign in</router-link></span>

        </div>
      </a-card>
    </div>
  `},v=pragma.ui.helpers.toast,Re=pragma.auth.LINK_ERROR,h=20,Ce={name:"LinkPage",components:{},setup(){return{handleLinkResult:Te}},template:`
    <div class='LinkPage auth-container'>
      <component
        v-if='$route.meta.component'
        :is='$route.meta.component'
        @linkResult='handleLinkResult'
      />
      <div class="loading"></div>
    </div>
  `};function we(e){return e!=="AccountRpc.LinkIdentityProviderAccountV2Response"}function Te(e,t){if(e.ok){const a=e.type;if(we(a))switch(a){case"AccountRpc.AccountNotVerifiedApplicationError":v.error(`Could not link account. This ${t} account is not verified.`,h);break;case"AccountRpc.UnauthorizedApplicationError":v.error(`Could not link account. This ${t} account is not authorized.`,h);break;case"AccountRpc.IdProviderAlreadyLinkedToAccountApplicationError":v.error(`Could not link account. A ${t} account is already associated with this account.`,h);break;case"AccountRpc.IdProviderLinkedToOtherAccountApplicationError":v.error(`Could not link account. This ${t} account is already associated with another account.`,h);break;case"AccountRpc.IdProviderUnexpectedResponseApplicationError":v.error(`Could not link account. Got an unexpected response from ${t}.`,h);break;case"AccountRpc.LinkCooldownApplicationError":const r=e.payload.timeRemainingMillis??31556926e4,n=oe.duration({milliseconds:r}).humanize();v.error(`A ${t} account was recently linked. You'll need to wait ${n} before you can link a new ${t} account.`,h);break;case"AccountRpc.LinkOneAssociationOnlyApplicationError":v.error(`This ${t} account can't be linked because a different ${t} account was already linked. Your account can only be associated with one ${t} account in its lifetime.`,h);break;default:e.payload.message?v.error(`Could not link account due to ${e.type}. ${e.payload.message}`,h):(v.error(`Could not link account due to ${e.type}.`,h),console.error(e.payload))}else v.success(`Successfully linked ${t} account.`,h)}else switch(e.error){case Re:v.error("Could not link account. Please try again.",h);break;default:v.error(`Could not link account due to: ${e.error}`,h)}}const Ae={name:"NotFound",template:`
    <a-result status="404" title="404" sub-title="Sorry, the page you visited does not exist.">
    </a-result>
  `},Ie={props:{portlet:Object,page:Object},setup(e){const t=O(),a=e.portlet.pages.find(s=>t.path.startsWith(s.urlPath)),r=a&&a.id,n=e.portlet.navPagesTree.find(s=>s.id===e.page.parentGroupId);return{selectedSidebarPage:r,openPageGroupId:n==null?void 0:n.id}},template:`
    <a-layout-sider class='portletSidebar'>
      <a-menu
        mode="inline"
        :selectedKeys="[selectedSidebarPage]"
        :openKeys="[openPageGroupId]"
      >
        <template v-for="pageRaw in portlet.navPagesTree">
          <template v-if="pageRaw.type === 'section'">
            <a-menu-divider class='menuSectionDivider' />
            <a-menu-item
              v-for="sectionPage in pageRaw.pages"
              :key="sectionPage.id"
              @click="$router.push({name: sectionPage.id})"
            >
              <span>{{sectionPage.name}}</span>
            </a-menu-item>
            <a-menu-divider class='menuSectionDivider' />
          </template>

          <a-menu-item
            v-if="pageRaw.type === 'page'"
            :key="pageRaw.id"
            @click="$router.push({name: pageRaw.id})"
          >
            <span>{{pageRaw.name}}</span>
          </a-menu-item>

          <a-sub-menu
            v-if="pageRaw.type === 'group'"
            :key="pageRaw.id"
          >
            <template #title><span class='subMenuTitle'>{{pageRaw.name}}</span></template>
            <a-menu-item
              v-for="groupPage in pageRaw.pages"
              :key="groupPage.id"
              @click="$router.push({name: groupPage.id})"
              class="subMenuItem"
            >
              <span>{{groupPage.name}}</span>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
    </a-layout-sider>
  `},{utils:Se}=pragma,Ee=pragma.ui.helpers,q={props:{components:Array,pageState:Object,wrapper:[String,Object],wrapperProps:Object},setup({components:e}){return{stageComponents:Se.sortItemsByPosition(e,a=>a.options.position).map(a=>(Ee.addPropToComponent(a.component,"pageState",Object),a))}},template:`
    <template v-for="{component, options, params} in stageComponents">
      <component :is='wrapper' v-bind='wrapperProps' v-if='wrapper'>
        <component
          v-if="!options.isVisible || options.isVisible()"
          v-bind="params"
          :is="component"
          :pageState="pageState"
        />
      </component>

      <component
        v-if="!wrapper && (!options.isVisible || options.isVisible())"
        v-bind="params"
        :is="component"
        :pageState="pageState"
      />
    </template>
  `};let Y="Loading",S="",E=!1;const{eventEmitter:$e}=pragma.ui.helpers,{getQueryForRoute:xe}=pragma.ui.helpers.queryParamsHistory,Le={components:{DynamicPageComponents:q},props:{portlet:Object,page:Object,pageState:Object},setup(e){var n,s,m;const t=O(),a=b(),r=i=>{i.page&&a.push({name:i.page,params:i.params}),i.emit&&$e.emit(i.emit)};if(e.page.resolvedTabs&&e.page.resolvedTabs.forEach(i=>{i.active=i.page===e.page.id,i.params=t.params}),e.page.root){Y=(n=e.page.root)==null?void 0:n.name;const i=(m=(s=e.page.root)==null?void 0:s.pages[0])==null?void 0:m.subTitle;i?typeof i=="string"?(S=i,E=!1):(S=i.name,E=i.showAsLabel):(S="",E=!1)}return{route:t,getQueryForRoute:xe,actionButtonClick:r,title:Y,subTitle:S,showAsLabel:E}},template:`
    <a-page-header>
      <template #title>
         {{ title }}
      </template>

      <template #subTitle>
        <a-tag 
          v-if="subTitle && showAsLabel"
          :color="showAsLabel === true ? 'default' : showAsLabel"
        >
          {{ subTitle }}
        </a-tag>
        <template v-else>{{subTitle}}</template>
      </template>

      <DynamicPageComponents
        v-if='!!page.root'
        :components='page.headerComponents'
        :pageState='pageState'
      />
    </a-page-header>
  `},{store:W,auth:Oe}=pragma,{signOut:Me,SessionType:Be}=pragma.auth,{Icon:Fe,CopyableCell:De}=pragma.ui.components,He=["ContentCatalogsEditor"],Ne={name:"Navbar",components:{Icon:Fe,CopyableCell:De,DynamicPageComponents:q},props:{portlets:{type:Object,default:{}},portlet:Object,navbarComponents:{type:Array,default:[]},shouldRenderComponents:{type:Boolean,default:!0},pageState:Object},setup(e){const t=b(),r=W.get("app.sessionType")===Be.PLAYER,n=()=>Me(t),s=P(!1),m=P(),i=Object.values(e.portlets),p=i.filter(o=>!o.hidden&&o.type==="service").sort((o,c)=>o.order===c.order?o.name.localeCompare(c.name):o.order-c.order),u=p.filter(o=>o.topLevel),f=p.filter(o=>!o.topLevel);let g=[];for(const o of f){const c=g.find(y=>y.name===o.dropdown);c?c.portlets.push(o):g.push({name:o.dropdown,portlets:[o]})}g=g.sort((o,c)=>o.name==="More"?1:o.name.localeCompare(c.name));const k=p.length>1,M=i.filter(o=>!o.hidden&&o.type==="tool").sort((o,c)=>o.name>c.name?1:-1),B=i.filter(o=>!o.hidden&&o.type==="standalone").sort((o,c)=>o.name>c.name?1:-1),T=p.filter(o=>o.metadata.isPragma),R=p.filter(o=>!o.metadata.isPragma),F=o=>{let c=!1;return R.forEach(y=>{y.id==o&&(c=!0)}),c};return re(()=>{var o;m.value=(o=e.portlet)==null?void 0:o.id}),{portlets:p,pragmaPortlets:T,customPortlets:R,portletsTopLevel:u,dropdowns:g,showServicesLink:k,currentPortletId:m,store:W,auth:Oe,signOutNow:n,servicesOpen:s,TEST_ENVIRONMENT:He,toolPortlets:M,standalonePortlets:B,antd:ne,isPlayerSessionType:r,isCurrentPageCustomPortlet:F}},template:`
    <a-layout-header
      class="Navbar"
    >
      <a-flex class='mainNav' align="center" gap="middle">
        <router-link class='mainLogo' :to="{name: 'HomePage'}">
          <Icon size='22' :name="'logo' + store.get('app.backendType')" />
        </router-link>
        <router-link
          v-for="(portlet, pIndex) in standalonePortlets"
          :key="'t' + pIndex"
          :to="{name: portlet.pages[0]?.id}"
          :class="{'general-nav': true,  active: currentPortletId === portlet.id}"
        >{{portlet.name}}</router-link>
         
        <DynamicPageComponents
          v-if="shouldRenderComponents"
          :components='navbarComponents'
          :pageState='pageState'
          :wrapper='antd.Space'
        />
      </a-flex>

      <a-flex gap="middle">
        <a-dropdown :trigger="['click']" class="dropdown-user-menu-trigger">
          <a class="ant-dropdown-link" @click.prevent>
            <a-flex align="center">
              {{auth.getAuthData('displayName')}}
              <a-typography-text type="secondary">#{{auth.getAuthData('discriminator')}}</a-typography-text>
              <Icon name='chevron' />
            </a-flex>
          </a>
          <template #overlay>
            <a-menu class="dropdown-user-menu">
              <a-menu-item v-if="!isPlayerSessionType" key="0">
                <a href="https://docs.pragma.gg" target="_blank">Docs</a>
              </a-menu-item>
              
              <a-menu-item v-if="!isPlayerSessionType" v-for="(portlet, portletIndex) in toolPortlets" class='nowrap' :key="'p' + portletIndex">
                <router-link
                  :to="{name: portlet.pages[0]?.id}"
                >
                  {{portlet.name}}
                </router-link>
              </a-menu-item>

              <a-menu-item key="1">
                <a href="" @click.prevent.stop="signOutNow">Sign Out</a>
              </a-menu-item>

              <a-divider v-if="!isPlayerSessionType" />
              
              <a-flex gap="small" vertical v-if="!isPlayerSessionType" key="2" class="platform-info-container">
                <a-flex vertical>
                  <a-typography-text class="platform-info-label">Platform Version</a-typography-text>
                  <CopyableCell :text="store.get('appHealthCheck.version')" class="ant-typography-code typography-small" />
                </a-flex>

                <a-flex vertical>
                  <a-typography-text class="platform-info-label">Portal Version</a-typography-text>
                  <CopyableCell :text="store.get('appHealthCheck.portalVersion')" class="ant-typography-code typography-small" />
                </a-flex>
              </a-flex>
            </a-menu>
          </template>
        </a-dropdown>
      </a-flex>
    </a-layout-header>
    
    <a-flex gap="middle" class="primary-navigation">
      <router-link v-for="portlet in portletsTopLevel"
        class='primary-navigation-item'
        :to="{name: portlet.pages[0]?.id}"
      >
        {{portlet.name}}
      </router-link>

      <template v-if="dropdowns.length > 0 && !isPlayerSessionType">
        <a-dropdown v-for="dropdown in dropdowns" :trigger="['click']">
          <a 
            :class="{'primary-navigation-item': true, 'router-link-active': isCurrentPageCustomPortlet(currentPortletId)}"
            @click.prevent>
            {{ dropdown.name }}
            <Icon name='chevron' style="transform: rotate(90deg);" />
          </a>
          <template #overlay>
            <a-menu style="margin-top: -8px;">
              <a-menu-item
                v-for="(portlet, portletIndex) in dropdown.portlets" class='nowrap' :key="'cp' + portletIndex"
              >
                <router-link :to="{name: portlet.pages[0]?.id}">
                  {{portlet.name}}
                </router-link>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </a-flex>
  `},{utils:$,store:Ve}=pragma,{useSubscribe:G}=pragma.ui.helpers.eventEmitter,{builderHelpers:V}=pragma.app,{appBlueprint:j}=pragma.app.blueprint,{Icon:je}=pragma.ui.components,ze=pragma.ui.helpers.flashMessage,{eventEmitter:Ue}=pragma.ui.helpers,{getQueryForRoute:qe}=pragma.ui.helpers.queryParamsHistory,_e={name:"PortletRenderer",components:{ErrorBox:pe,FlashMessage:U,PortletSidebar:Ie,PortletPageHeader:Le,DynamicPageComponents:q,Navbar:Ne,Icon:je},setup(){const e=O(),t=b(),{portlet:a,page:r}=j.routes[e.name],n=j.portlets,s=[...j.navbarComponents,...a.navbarComponents,...r.navbarComponents],m=P("initial"),i=P(""),p=P(!1),u=_({message:"",stack:""}),f=K({}),g=K([]),k=_({}),M=ze.retrieveAndRemoveAll(),B=e.name,T=()=>{p.value=!0,m.value=$.getRandomString()},R=l=>({rpc:l,urlParams:e.params,queryParams:e.query}),F=l=>V.resolveDynamicPageProps({page:r,context:R(l)}),o=l=>V.resolveDynamicComponents({components:s,context:R(l)});let c=!1;const y=({refreshCache:l}={refreshCache:c})=>{V.retrievePageRpc({page:r,context:R(),refreshCache:l}).then(C=>{f.value=F(C),g.value=o(C),$.setDocumentTitle(f.value.title,Ve.get("app.options.pageTitle")),T()}).catch(C=>{C=="Request failed with status code 401"?p.value=!1:i.value=C,T()}),c=!1},ae=l=>{l.page&&t.push({name:l.page,params:l.params}),l.emit&&Ue.emit(l.emit)};return G("refreshRpcCacheNextCall",()=>{c=!0}),G("reloadData",l=>{y({refreshCache:l||!0})}),se(()=>e.query,()=>e.name===B&&y()),ie(l=>(u.message=l.message,u.stack=l.stack,$.logError(l.stack),!1)),y(),typeof r.title=="string"&&$.setDocumentTitle(r.title),{resolvedPage:f,portlet:a,page:r,portlets:n,resolvedNavbarComponents:g,shouldRender:p,pageState:k,rpcFailedMessage:i,errorCaughtInComponents:u,flashMessages:M,renderKey:m,actionButtonClick:ae,getQueryForRoute:qe}},template:`
    <a-layout>
      <Navbar
        :key="'navbar' + $route.fullPath + renderKey"
        :portlet='portlet'
        :portlets='portlets'
        :pageState='pageState'
        :navbarComponents='resolvedNavbarComponents'
        :shouldRenderComponents='shouldRender'
      />

      <PortletPageHeader
        :key="'pageHeader' + $route.fullPath + renderKey"
        :portlet='portlet'
        :page='resolvedPage'
        :pageState='pageState'
      />

      <a-layout class="portletRenderer">
        <PortletSidebar v-if='portlet.navPagesTree.length > 1' :portlet='portlet' :page='page' />

        <a-layout v-if="!rpcFailedMessage" :class="'page-' + resolvedPage.id">
          <!-- If there are tabs, and a Sidebar hasn't already been rendered, make a sidebar -->  
          <a-layout-sider 
            v-if="page.resolvedTabs.length > 0 && portlet.navPagesTree.length <= 1" 
            class='portletSidebar'
          >
            <a-menu 
              mode="inline"
              :selectedKeys="[resolvedPage.id]"
            >
              <a-menu-item 
                v-if="page.breadcrumbParentRoutes.length"
                class="menu-item-back-link"
              >
                <router-link
                  :to="{
                    name: page.breadcrumbParentRoutes.slice(-1)[0].name,
                    params: $route.params,
                    query: getQueryForRoute(page.breadcrumbParentRoutes.slice(-1)[0].name)
                  }"
                >
                  <Icon name="chevron" style="transform: rotate(180deg)" />
                  {{page.breadcrumbParentRoutes.slice(-1)[0].name}}
                </router-link>
              </a-menu-item>

              <a-menu-item 
                v-for="tab in page.resolvedTabs"
                :key="tab.page"
                @click="$router.push({name: tab.page })"
              >
                  {{ tab.title }}
              </a-menu-item>
            </a-menu>
          </a-layout-sider>

          <a-flex vertical gap="medium" flex="1 1 auto">
            <a-flex justify="space-between" align="center" class="section-header">
              <a-flex align="baseline" gap="middle">
                <a-typography-title :level="4">{{resolvedPage.title}}</a-typography-title>
                <a-tag 
                  v-if="resolvedPage.subTitle && resolvedPage.subTitle.showAsLabel"
                  :color="resolvedPage.subTitle.showAsLabel === true ? 'default' : resolvedPage.subTitle.showAsLabel"
                >
                  {{ resolvedPage.subTitle.name }}
                </a-tag>
                <a-typography-text type="secondary" class="typography-small" v-else>{{ typeof resolvedPage?.subTitle?.name !== "undefined" ? resolvedPage.subTitle.name : resolvedPage?.subTitle}}</a-typography-text>
              </a-flex>
              <a-flex gap="8">
                <template v-for="action in resolvedPage.actions">
                  <a-button
                    v-if="!action.isVisible || action.isVisible()"
                    :type='action.type !== "secondary" && action.type !== "danger" && action.type || "primary"'
                    :secondary="action.type === 'secondary' ? true : null"
                    :danger="action.type === 'danger' ? true : null"
                    @click="actionButtonClick(action)"
                  >
                    <Icon v-if='action.icon' :name='action.icon'/><span>{{action.name}}</span>
                  </a-button>
                </template>
              </a-flex>
            </a-flex>

            <!-- If there are tabs and a sidebar has been rendered, render top tabs -->
            <a-menu
              v-if="page.resolvedTabs.length > 0 && portlet.navPagesTree.length > 1" 
              :selectedKeys="[resolvedPage.id]"
              mode="horizontal"
            >
              <a-menu-item
                v-for="tab in page.resolvedTabs"
                :key="tab.page"
                @click="$router.push({name: tab.page })"
              >
                {{ tab.title }}
              </a-menu-item>
            </a-menu>

            <a-layout-content class="stage">
              <template v-for='(message, id) in flashMessages' >
                <FlashMessage
                  :id='id'
                  :type='message.type'
                  :message='message.message'
                  :details='message.details'
                />
              </template>

              <DynamicPageComponents
                :key="'pageComponents' + $route.fullPath + renderKey"
                v-if="shouldRender && !errorCaughtInComponents.message"
                :components='resolvedPage.components'
                :pageState='pageState'
              />
              <ErrorBox v-if="errorCaughtInComponents.message" :error="errorCaughtInComponents" />
            </a-layout-content>
          </a-flex>
        </a-layout>

        <ErrorBox
          v-if="rpcFailedMessage"
          title="There are some problems with the rpc calls."
          :error="rpcFailedMessage.toString().slice(0, 150)"
        />

      </a-layout>
    </a-layout>
  `},{appBlueprint:Ke}=pragma.app.blueprint,Qe=e=>Object.values(e).filter(r=>!r.hidden).sort((r,n)=>n.priority-r.priority)[0],Ye={setup(){var a;const e=P(""),t=b();try{const r=Qe(Ke.portlets);t.push({name:(a=r.pages[0])==null?void 0:a.id})}catch(r){console.error(r),e.value="Cannot load a default portlet."}return{message:e}},template:"<p>{{ message }}</p>"},{Icon:We}=pragma.ui.components,{utils:Ge}=pragma,{auth:Je}=pragma,{verifyEmail:Xe,redirectAfterSignin:Ze}=pragma.auth,et=pragma.ui.helpers.flashMessage,tt={name:"VerifyEmail",path:"verify-email",title:"ASDF",components:{Icon:We},setup(e){const t=P(""),a=O(),r=b(),n=a.query.code||Ge.getRawUrlParam("code");return n?Xe(n).then(s=>{if(!s.ok){switch(t.value="Something went wrong. Please try again.",s.error){case"AccountRpc.TokenExpiredApplicationError":t.value="Could not verify email. Email verification code is expired";break;case"AccountRpc.NotFoundApplicationError":t.value="Could not verify email. Email not found";break;case"AccountRpc.TokenSignatureVerificationFailedApplicationError":t.value="Could not verify email. Invalid verification code";break;default:t.value="Could not verify email. Something went wrong. Please try again."}return}et.add({type:"success",message:"Email Verified"}),Je.getAuthData("authSocialToken")?Ze(r):r.push({name:"SignIn"})}):t.value="No verification code provided.",{errorMessage:t}},template:`
    <div class='VerifyEmail auth-container'>
      <Icon name="logoPortal" size='30' class='logo' />
      <a-card :bordered="false" class='card card-auth' >
        <div>
          <component
            v-if="$route.meta.component"
            :is='$route.meta.component'
            @success="redirect"
          />

          <div v-else class='options'>
            <h5 class='header'>
              Verifying Email
            </h5>

            <div v-if="!errorMessage" class='loading'></div>
            <div v-else>
              <a-card style="margin-bottom: 10px">
                <div class='errorText'>{{errorMessage}}</div>
              </a-card>

              <router-link :to="{name: 'SignIn'}"><Icon name='arrowLeft' align='-2' /> &nbsp; Back to all sign in options</router-link>
            </div>
          </div>

        </div>
      </a-card>
    </div>
  `},{isAuthenticated:z,setRedirectAfterSigninPath:at}=pragma.auth,{store:x}=pragma,{appBlueprint:L}=pragma.app.blueprint,{utils:d}=pragma,{saveQueryForRoute:ot}=pragma.ui.helpers.queryParamsHistory,J="/signin",X="/createaccount",Z="_pRoute",rt=[{path:J,name:"SignIn",component:te,beforeEnter:(e,t)=>{if(z())return{name:"HomePage"};const a=x.get("appInfo.socialBackend");if(x.get("config.redirectSignInToSocial")&&d.getWindowLocation().origin!==d.buildEndpoint(a))return d.goToUrl(`${d.buildEndpoint(a)}/#${J}`),!1}},{path:X,name:"CreateAccount",component:ke,beforeEnter:(e,t)=>{if(z())return{name:"HomePage"};const a=x.get("appInfo.socialBackend");if(x.get("config.redirectSignInToSocial")&&d.getWindowLocation().origin!==d.buildEndpoint(a))return d.goToUrl(`${d.buildEndpoint(a)}/#${X}`),!1}},{path:"/",name:"HomePage",meta:{private:!0},component:Ye},{path:"/verify-email",name:"VerifyEmail",component:tt},{path:"/:pathMatch(.*)*",name:"NotFound",component:Ae}],ct=()=>{const e=le({history:ce(),routes:nt()});return e.beforeEach((t,a)=>{if(w.handleRedirects({router:e})!=="continue")return!1;if(t.meta.private&&!z())return at({path:t.fullPath,origin:d.getWindowLocation().origin}),{name:"SignIn"}}),e.afterEach((t,a)=>{ot({routeName:a.name,query:a.query})}),e},nt=()=>{const{routes:e}=L,t=Object.keys(e).map(n=>({name:n,path:e[n].path,redirect:e[n].redirect,component:{..._e},meta:{private:!0}})),a=L.authProviders.map(n=>n.registeredRoutes.map(s=>({path:s.path,name:s.name,meta:{component:s.component},component:te}))).flat(),r=L.authProviders.map(n=>(n.registeredLinkRoutes??[]).map(m=>({path:m.path,name:m.name,meta:{component:m.component},component:Ce}))).flat();return[...a,...r,...rt,...t]},w={handleRedirects({router:e}){return w.redirectAuthProviders({router:e})||w.redirectToAppFromQuery({router:e})?"redirect":"continue"},redirectAuthProviders({router:e}={}){return L.authProviders.map(a=>a.routerBeforeHook||(()=>!1)).some(a=>a({router:e}))},redirectToAppFromQuery({router:e}){var s;const t=d.getRawUrlParam(Z);if(!t)return;const a=((s=w.getRouteByName({router:e,name:t}))==null?void 0:s.path)||"/",r=d.getRawUrlQueryAsJson({excludeKeys:[Z]}),n=d.convertJsonToUrlQuery(r);return w.forceRedirectToAppPath({path:a,query:n}),!0},getRouteByName({router:e,name:t}){return e.getRoutes().find(a=>a.name===t)},forceRedirectToAppPath({path:e,query:t}){d.goToUrl(d.getWindowLocation().origin+d.getWindowLocation().pathname+"#"+e+"?"+t)}};export{X as CREATEACCOUNT_PATH_PREFIX,J as SIGNIN_PATH_PREFIX,ct as generateRouter,nt as generateRoutes,w as routerUtils};
