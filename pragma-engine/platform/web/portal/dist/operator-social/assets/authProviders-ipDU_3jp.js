import{v as l0,b as M,x as Er,y as U,a as Ut,u as $t,r as pe}from"./index-BU6JD9Pc.js";import{B as Cr}from"./BackendInfo.vue-CAumvPj0.js";const{utils:Br}=pragma,{signIn:Dr,link:gr,getIdProviderConfig:_r,getAuthenticateBaseUri:Rr,getRedirectUri:Nt}=pragma.auth,u0={SIGNIN_REDIRECT_PATH:"/redirect/SignInAuth0",LINK_REDIRECT_PATH:"/redirect/LinkAuth0",PROVIDER_ID:"AUTH0",BACKEND_REDIRECT:"/v1/account/oauth-redirect/auth0"},mr=async n=>await Dr({providerId:u0.PROVIDER_ID,providerToken:n}),Fr=async n=>await gr({providerId:u0.PROVIDER_ID,providerToken:n}),Pe=n=>{const d=_r(u0.PROVIDER_ID),r=new URLSearchParams({client_id:d.clientId,redirect_uri:n,scope:["openid","profile","email"].join(" "),response_type:"code",access_type:"offline",prompt:"consent"}),e=`${d.authorizationUri}?${r}`;Br.goToUrl(e)},Ir=async n=>{const d=await Ot(n,Nt(u0.SIGNIN_REDIRECT_PATH));return d?await mr(d):{ok:!1,error:"Invalid code."}},kr=async n=>{const d=await Ot(n,Nt(u0.SIGNIN_REDIRECT_PATH));return d?await Fr(d):{ok:!1,error:"Invalid code."}},Ot=async(n,d)=>{const r=new URLSearchParams({code:n,redirect_uri:d}),e={method:"get"},A=`${Rr(u0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await l0(A,e)).data.access_token}catch(v){console.warn(v);return}},{Icon:yr,IdProviderAuthResultHandler:br}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Sr}=pragma.auth,Pr={name:"SignInAuth0",components:{Icon:yr,IdProviderAuthResultHandler:br},emits:["success"],setup(n,{emit:d}){const r=M();return Sr(Ir,r),{Auth0:u0,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Auth0.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},wr=pragma.ui.helpers.eventEmitter,Tr=pragma.auth.initiateLinkAndRedirectWithCode,Hr={name:"LinkAuth0",components:{},emits:["linkResult"],setup(n,{emit:d}){return Tr(kr,"Auth0",d,wr),{}},template:"<div/>"},we=pragma.utils,{getRedirectUri:Te}=pragma.auth,Lr={id:"AUTH0",name:"Auth0",iconName:"auth0",enumValue:11,onSignInButtonClick:({router:n})=>{Pe(Te(u0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Pe(Te(u0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInAuth0",path:"/signin-auth0",component:Pr}],registeredLinkRoutes:[{name:"LinkAuth0",path:"/link-auth0",component:Hr}],routerBeforeHook:({router:n})=>{let d=we.getWindowLocation().href;if(d.search("#/access_token=")>=0)return d=d.replace("#/access_token=","&access_token="),we.goToUrl(d),!0}};var zt={exports:{}};function Ur(n){throw new Error('Could not dynamically require "'+n+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var H0={exports:{}};const $r={},Nr=Object.freeze(Object.defineProperty({__proto__:null,default:$r},Symbol.toStringTag,{value:"Module"})),Or=Er(Nr);var He;function N(){return He||(He=1,function(n,d){(function(r,e){n.exports=e()})(U,function(){var r=r||function(e,A){var v;if(typeof window<"u"&&window.crypto&&(v=window.crypto),typeof self<"u"&&self.crypto&&(v=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(v=globalThis.crypto),!v&&typeof window<"u"&&window.msCrypto&&(v=window.msCrypto),!v&&typeof U<"u"&&U.crypto&&(v=U.crypto),!v&&typeof Ur=="function")try{v=Or}catch{}var F=function(){if(v){if(typeof v.getRandomValues=="function")try{return v.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof v.randomBytes=="function")try{return v.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},h=Object.create||function(){function i(){}return function(x){var u;return i.prototype=x,u=new i,i.prototype=null,u}}(),p={},t=p.lib={},o=t.Base=function(){return{extend:function(i){var x=h(this);return i&&x.mixIn(i),(!x.hasOwnProperty("init")||this.init===x.init)&&(x.init=function(){x.$super.init.apply(this,arguments)}),x.init.prototype=x,x.$super=this,x},create:function(){var i=this.extend();return i.init.apply(i,arguments),i},init:function(){},mixIn:function(i){for(var x in i)i.hasOwnProperty(x)&&(this[x]=i[x]);i.hasOwnProperty("toString")&&(this.toString=i.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),C=t.WordArray=o.extend({init:function(i,x){i=this.words=i||[],x!=A?this.sigBytes=x:this.sigBytes=i.length*4},toString:function(i){return(i||c).stringify(this)},concat:function(i){var x=this.words,u=i.words,g=this.sigBytes,D=i.sigBytes;if(this.clamp(),g%4)for(var _=0;_<D;_++){var m=u[_>>>2]>>>24-_%4*8&255;x[g+_>>>2]|=m<<24-(g+_)%4*8}else for(var w=0;w<D;w+=4)x[g+w>>>2]=u[w>>>2];return this.sigBytes+=D,this},clamp:function(){var i=this.words,x=this.sigBytes;i[x>>>2]&=4294967295<<32-x%4*8,i.length=e.ceil(x/4)},clone:function(){var i=o.clone.call(this);return i.words=this.words.slice(0),i},random:function(i){for(var x=[],u=0;u<i;u+=4)x.push(F());return new C.init(x,i)}}),a=p.enc={},c=a.Hex={stringify:function(i){for(var x=i.words,u=i.sigBytes,g=[],D=0;D<u;D++){var _=x[D>>>2]>>>24-D%4*8&255;g.push((_>>>4).toString(16)),g.push((_&15).toString(16))}return g.join("")},parse:function(i){for(var x=i.length,u=[],g=0;g<x;g+=2)u[g>>>3]|=parseInt(i.substr(g,2),16)<<24-g%8*4;return new C.init(u,x/2)}},s=a.Latin1={stringify:function(i){for(var x=i.words,u=i.sigBytes,g=[],D=0;D<u;D++){var _=x[D>>>2]>>>24-D%4*8&255;g.push(String.fromCharCode(_))}return g.join("")},parse:function(i){for(var x=i.length,u=[],g=0;g<x;g++)u[g>>>2]|=(i.charCodeAt(g)&255)<<24-g%4*8;return new C.init(u,x)}},l=a.Utf8={stringify:function(i){try{return decodeURIComponent(escape(s.stringify(i)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(i){return s.parse(unescape(encodeURIComponent(i)))}},f=t.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new C.init,this._nDataBytes=0},_append:function(i){typeof i=="string"&&(i=l.parse(i)),this._data.concat(i),this._nDataBytes+=i.sigBytes},_process:function(i){var x,u=this._data,g=u.words,D=u.sigBytes,_=this.blockSize,m=_*4,w=D/m;i?w=e.ceil(w):w=e.max((w|0)-this._minBufferSize,0);var E=w*_,R=e.min(E*4,D);if(E){for(var k=0;k<E;k+=_)this._doProcessBlock(g,k);x=g.splice(0,E),u.sigBytes-=R}return new C.init(x,R)},clone:function(){var i=o.clone.call(this);return i._data=this._data.clone(),i},_minBufferSize:0});t.Hasher=f.extend({cfg:o.extend(),init:function(i){this.cfg=this.cfg.extend(i),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(i){return this._append(i),this._process(),this},finalize:function(i){i&&this._append(i);var x=this._doFinalize();return x},blockSize:16,_createHelper:function(i){return function(x,u){return new i.init(u).finalize(x)}},_createHmacHelper:function(i){return function(x,u){return new B.HMAC.init(i,u).finalize(x)}}});var B=p.algo={};return p}(Math);return r})}(H0)),H0.exports}var L0={exports:{}},Le;function P0(){return Le||(Le=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(e){var A=r,v=A.lib,F=v.Base,h=v.WordArray,p=A.x64={};p.Word=F.extend({init:function(t,o){this.high=t,this.low=o}}),p.WordArray=F.extend({init:function(t,o){t=this.words=t||[],o!=e?this.sigBytes=o:this.sigBytes=t.length*8},toX32:function(){for(var t=this.words,o=t.length,C=[],a=0;a<o;a++){var c=t[a];C.push(c.high),C.push(c.low)}return h.create(C,this.sigBytes)},clone:function(){for(var t=F.clone.call(this),o=t.words=this.words.slice(0),C=o.length,a=0;a<C;a++)o[a]=o[a].clone();return t}})}(),r})}(L0)),L0.exports}var U0={exports:{}},Ue;function zr(){return Ue||(Ue=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(){if(typeof ArrayBuffer=="function"){var e=r,A=e.lib,v=A.WordArray,F=v.init,h=v.init=function(p){if(p instanceof ArrayBuffer&&(p=new Uint8Array(p)),(p instanceof Int8Array||typeof Uint8ClampedArray<"u"&&p instanceof Uint8ClampedArray||p instanceof Int16Array||p instanceof Uint16Array||p instanceof Int32Array||p instanceof Uint32Array||p instanceof Float32Array||p instanceof Float64Array)&&(p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength)),p instanceof Uint8Array){for(var t=p.byteLength,o=[],C=0;C<t;C++)o[C>>>2]|=p[C]<<24-C%4*8;F.call(this,o,t)}else F.apply(this,arguments)};h.prototype=v}}(),r.lib.WordArray})}(U0)),U0.exports}var $0={exports:{}},$e;function Wr(){return $e||($e=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,A=e.lib,v=A.WordArray,F=e.enc;F.Utf16=F.Utf16BE={stringify:function(p){for(var t=p.words,o=p.sigBytes,C=[],a=0;a<o;a+=2){var c=t[a>>>2]>>>16-a%4*8&65535;C.push(String.fromCharCode(c))}return C.join("")},parse:function(p){for(var t=p.length,o=[],C=0;C<t;C++)o[C>>>1]|=p.charCodeAt(C)<<16-C%2*16;return v.create(o,t*2)}},F.Utf16LE={stringify:function(p){for(var t=p.words,o=p.sigBytes,C=[],a=0;a<o;a+=2){var c=h(t[a>>>2]>>>16-a%4*8&65535);C.push(String.fromCharCode(c))}return C.join("")},parse:function(p){for(var t=p.length,o=[],C=0;C<t;C++)o[C>>>1]|=h(p.charCodeAt(C)<<16-C%2*16);return v.create(o,t*2)}};function h(p){return p<<8&4278255360|p>>>8&16711935}}(),r.enc.Utf16})}($0)),$0.exports}var N0={exports:{}},Ne;function h0(){return Ne||(Ne=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,A=e.lib,v=A.WordArray,F=e.enc;F.Base64={stringify:function(p){var t=p.words,o=p.sigBytes,C=this._map;p.clamp();for(var a=[],c=0;c<o;c+=3)for(var s=t[c>>>2]>>>24-c%4*8&255,l=t[c+1>>>2]>>>24-(c+1)%4*8&255,f=t[c+2>>>2]>>>24-(c+2)%4*8&255,B=s<<16|l<<8|f,i=0;i<4&&c+i*.75<o;i++)a.push(C.charAt(B>>>6*(3-i)&63));var x=C.charAt(64);if(x)for(;a.length%4;)a.push(x);return a.join("")},parse:function(p){var t=p.length,o=this._map,C=this._reverseMap;if(!C){C=this._reverseMap=[];for(var a=0;a<o.length;a++)C[o.charCodeAt(a)]=a}var c=o.charAt(64);if(c){var s=p.indexOf(c);s!==-1&&(t=s)}return h(p,t,C)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function h(p,t,o){for(var C=[],a=0,c=0;c<t;c++)if(c%4){var s=o[p.charCodeAt(c-1)]<<c%4*2,l=o[p.charCodeAt(c)]>>>6-c%4*2,f=s|l;C[a>>>2]|=f<<24-a%4*8,a++}return v.create(C,a)}}(),r.enc.Base64})}(N0)),N0.exports}var O0={exports:{}},Oe;function Gr(){return Oe||(Oe=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,A=e.lib,v=A.WordArray,F=e.enc;F.Base64url={stringify:function(p,t){t===void 0&&(t=!0);var o=p.words,C=p.sigBytes,a=t?this._safe_map:this._map;p.clamp();for(var c=[],s=0;s<C;s+=3)for(var l=o[s>>>2]>>>24-s%4*8&255,f=o[s+1>>>2]>>>24-(s+1)%4*8&255,B=o[s+2>>>2]>>>24-(s+2)%4*8&255,i=l<<16|f<<8|B,x=0;x<4&&s+x*.75<C;x++)c.push(a.charAt(i>>>6*(3-x)&63));var u=a.charAt(64);if(u)for(;c.length%4;)c.push(u);return c.join("")},parse:function(p,t){t===void 0&&(t=!0);var o=p.length,C=t?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var c=0;c<C.length;c++)a[C.charCodeAt(c)]=c}var s=C.charAt(64);if(s){var l=p.indexOf(s);l!==-1&&(o=l)}return h(p,o,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function h(p,t,o){for(var C=[],a=0,c=0;c<t;c++)if(c%4){var s=o[p.charCodeAt(c-1)]<<c%4*2,l=o[p.charCodeAt(c)]>>>6-c%4*2,f=s|l;C[a>>>2]|=f<<24-a%4*8,a++}return v.create(C,a)}}(),r.enc.Base64url})}(O0)),O0.exports}var z0={exports:{}},ze;function p0(){return ze||(ze=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(e){var A=r,v=A.lib,F=v.WordArray,h=v.Hasher,p=A.algo,t=[];(function(){for(var l=0;l<64;l++)t[l]=e.abs(e.sin(l+1))*4294967296|0})();var o=p.MD5=h.extend({_doReset:function(){this._hash=new F.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(l,f){for(var B=0;B<16;B++){var i=f+B,x=l[i];l[i]=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360}var u=this._hash.words,g=l[f+0],D=l[f+1],_=l[f+2],m=l[f+3],w=l[f+4],E=l[f+5],R=l[f+6],k=l[f+7],y=l[f+8],T=l[f+9],H=l[f+10],L=l[f+11],q=l[f+12],O=l[f+13],W=l[f+14],z=l[f+15],I=u[0],S=u[1],P=u[2],b=u[3];I=C(I,S,P,b,g,7,t[0]),b=C(b,I,S,P,D,12,t[1]),P=C(P,b,I,S,_,17,t[2]),S=C(S,P,b,I,m,22,t[3]),I=C(I,S,P,b,w,7,t[4]),b=C(b,I,S,P,E,12,t[5]),P=C(P,b,I,S,R,17,t[6]),S=C(S,P,b,I,k,22,t[7]),I=C(I,S,P,b,y,7,t[8]),b=C(b,I,S,P,T,12,t[9]),P=C(P,b,I,S,H,17,t[10]),S=C(S,P,b,I,L,22,t[11]),I=C(I,S,P,b,q,7,t[12]),b=C(b,I,S,P,O,12,t[13]),P=C(P,b,I,S,W,17,t[14]),S=C(S,P,b,I,z,22,t[15]),I=a(I,S,P,b,D,5,t[16]),b=a(b,I,S,P,R,9,t[17]),P=a(P,b,I,S,L,14,t[18]),S=a(S,P,b,I,g,20,t[19]),I=a(I,S,P,b,E,5,t[20]),b=a(b,I,S,P,H,9,t[21]),P=a(P,b,I,S,z,14,t[22]),S=a(S,P,b,I,w,20,t[23]),I=a(I,S,P,b,T,5,t[24]),b=a(b,I,S,P,W,9,t[25]),P=a(P,b,I,S,m,14,t[26]),S=a(S,P,b,I,y,20,t[27]),I=a(I,S,P,b,O,5,t[28]),b=a(b,I,S,P,_,9,t[29]),P=a(P,b,I,S,k,14,t[30]),S=a(S,P,b,I,q,20,t[31]),I=c(I,S,P,b,E,4,t[32]),b=c(b,I,S,P,y,11,t[33]),P=c(P,b,I,S,L,16,t[34]),S=c(S,P,b,I,W,23,t[35]),I=c(I,S,P,b,D,4,t[36]),b=c(b,I,S,P,w,11,t[37]),P=c(P,b,I,S,k,16,t[38]),S=c(S,P,b,I,H,23,t[39]),I=c(I,S,P,b,O,4,t[40]),b=c(b,I,S,P,g,11,t[41]),P=c(P,b,I,S,m,16,t[42]),S=c(S,P,b,I,R,23,t[43]),I=c(I,S,P,b,T,4,t[44]),b=c(b,I,S,P,q,11,t[45]),P=c(P,b,I,S,z,16,t[46]),S=c(S,P,b,I,_,23,t[47]),I=s(I,S,P,b,g,6,t[48]),b=s(b,I,S,P,k,10,t[49]),P=s(P,b,I,S,W,15,t[50]),S=s(S,P,b,I,E,21,t[51]),I=s(I,S,P,b,q,6,t[52]),b=s(b,I,S,P,m,10,t[53]),P=s(P,b,I,S,H,15,t[54]),S=s(S,P,b,I,D,21,t[55]),I=s(I,S,P,b,y,6,t[56]),b=s(b,I,S,P,z,10,t[57]),P=s(P,b,I,S,R,15,t[58]),S=s(S,P,b,I,O,21,t[59]),I=s(I,S,P,b,w,6,t[60]),b=s(b,I,S,P,L,10,t[61]),P=s(P,b,I,S,_,15,t[62]),S=s(S,P,b,I,T,21,t[63]),u[0]=u[0]+I|0,u[1]=u[1]+S|0,u[2]=u[2]+P|0,u[3]=u[3]+b|0},_doFinalize:function(){var l=this._data,f=l.words,B=this._nDataBytes*8,i=l.sigBytes*8;f[i>>>5]|=128<<24-i%32;var x=e.floor(B/4294967296),u=B;f[(i+64>>>9<<4)+15]=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,f[(i+64>>>9<<4)+14]=(u<<8|u>>>24)&16711935|(u<<24|u>>>8)&4278255360,l.sigBytes=(f.length+1)*4,this._process();for(var g=this._hash,D=g.words,_=0;_<4;_++){var m=D[_];D[_]=(m<<8|m>>>24)&16711935|(m<<24|m>>>8)&4278255360}return g},clone:function(){var l=h.clone.call(this);return l._hash=this._hash.clone(),l}});function C(l,f,B,i,x,u,g){var D=l+(f&B|~f&i)+x+g;return(D<<u|D>>>32-u)+f}function a(l,f,B,i,x,u,g){var D=l+(f&i|B&~i)+x+g;return(D<<u|D>>>32-u)+f}function c(l,f,B,i,x,u,g){var D=l+(f^B^i)+x+g;return(D<<u|D>>>32-u)+f}function s(l,f,B,i,x,u,g){var D=l+(B^(f|~i))+x+g;return(D<<u|D>>>32-u)+f}A.MD5=h._createHelper(o),A.HmacMD5=h._createHmacHelper(o)}(Math),r.MD5})}(z0)),z0.exports}var W0={exports:{}},We;function Wt(){return We||(We=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,A=e.lib,v=A.WordArray,F=A.Hasher,h=e.algo,p=[],t=h.SHA1=F.extend({_doReset:function(){this._hash=new v.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(o,C){for(var a=this._hash.words,c=a[0],s=a[1],l=a[2],f=a[3],B=a[4],i=0;i<80;i++){if(i<16)p[i]=o[C+i]|0;else{var x=p[i-3]^p[i-8]^p[i-14]^p[i-16];p[i]=x<<1|x>>>31}var u=(c<<5|c>>>27)+B+p[i];i<20?u+=(s&l|~s&f)+1518500249:i<40?u+=(s^l^f)+1859775393:i<60?u+=(s&l|s&f|l&f)-1894007588:u+=(s^l^f)-899497514,B=f,f=l,l=s<<30|s>>>2,s=c,c=u}a[0]=a[0]+c|0,a[1]=a[1]+s|0,a[2]=a[2]+l|0,a[3]=a[3]+f|0,a[4]=a[4]+B|0},_doFinalize:function(){var o=this._data,C=o.words,a=this._nDataBytes*8,c=o.sigBytes*8;return C[c>>>5]|=128<<24-c%32,C[(c+64>>>9<<4)+14]=Math.floor(a/4294967296),C[(c+64>>>9<<4)+15]=a,o.sigBytes=C.length*4,this._process(),this._hash},clone:function(){var o=F.clone.call(this);return o._hash=this._hash.clone(),o}});e.SHA1=F._createHelper(t),e.HmacSHA1=F._createHmacHelper(t)}(),r.SHA1})}(W0)),W0.exports}var G0={exports:{}},Ge;function Ae(){return Ge||(Ge=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){return function(e){var A=r,v=A.lib,F=v.WordArray,h=v.Hasher,p=A.algo,t=[],o=[];(function(){function c(B){for(var i=e.sqrt(B),x=2;x<=i;x++)if(!(B%x))return!1;return!0}function s(B){return(B-(B|0))*4294967296|0}for(var l=2,f=0;f<64;)c(l)&&(f<8&&(t[f]=s(e.pow(l,1/2))),o[f]=s(e.pow(l,1/3)),f++),l++})();var C=[],a=p.SHA256=h.extend({_doReset:function(){this._hash=new F.init(t.slice(0))},_doProcessBlock:function(c,s){for(var l=this._hash.words,f=l[0],B=l[1],i=l[2],x=l[3],u=l[4],g=l[5],D=l[6],_=l[7],m=0;m<64;m++){if(m<16)C[m]=c[s+m]|0;else{var w=C[m-15],E=(w<<25|w>>>7)^(w<<14|w>>>18)^w>>>3,R=C[m-2],k=(R<<15|R>>>17)^(R<<13|R>>>19)^R>>>10;C[m]=E+C[m-7]+k+C[m-16]}var y=u&g^~u&D,T=f&B^f&i^B&i,H=(f<<30|f>>>2)^(f<<19|f>>>13)^(f<<10|f>>>22),L=(u<<26|u>>>6)^(u<<21|u>>>11)^(u<<7|u>>>25),q=_+L+y+o[m]+C[m],O=H+T;_=D,D=g,g=u,u=x+q|0,x=i,i=B,B=f,f=q+O|0}l[0]=l[0]+f|0,l[1]=l[1]+B|0,l[2]=l[2]+i|0,l[3]=l[3]+x|0,l[4]=l[4]+u|0,l[5]=l[5]+g|0,l[6]=l[6]+D|0,l[7]=l[7]+_|0},_doFinalize:function(){var c=this._data,s=c.words,l=this._nDataBytes*8,f=c.sigBytes*8;return s[f>>>5]|=128<<24-f%32,s[(f+64>>>9<<4)+14]=e.floor(l/4294967296),s[(f+64>>>9<<4)+15]=l,c.sigBytes=s.length*4,this._process(),this._hash},clone:function(){var c=h.clone.call(this);return c._hash=this._hash.clone(),c}});A.SHA256=h._createHelper(a),A.HmacSHA256=h._createHmacHelper(a)}(Math),r.SHA256})}(G0)),G0.exports}var q0={exports:{}},qe;function qr(){return qe||(qe=1,function(n,d){(function(r,e,A){n.exports=e(N(),Ae())})(U,function(r){return function(){var e=r,A=e.lib,v=A.WordArray,F=e.algo,h=F.SHA256,p=F.SHA224=h.extend({_doReset:function(){this._hash=new v.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=h._doFinalize.call(this);return t.sigBytes-=4,t}});e.SHA224=h._createHelper(p),e.HmacSHA224=h._createHmacHelper(p)}(),r.SHA224})}(q0)),q0.exports}var V0={exports:{}},Ve;function Gt(){return Ve||(Ve=1,function(n,d){(function(r,e,A){n.exports=e(N(),P0())})(U,function(r){return function(){var e=r,A=e.lib,v=A.Hasher,F=e.x64,h=F.Word,p=F.WordArray,t=e.algo;function o(){return h.create.apply(h,arguments)}var C=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],a=[];(function(){for(var s=0;s<80;s++)a[s]=o()})();var c=t.SHA512=v.extend({_doReset:function(){this._hash=new p.init([new h.init(1779033703,4089235720),new h.init(3144134277,2227873595),new h.init(1013904242,4271175723),new h.init(2773480762,1595750129),new h.init(1359893119,2917565137),new h.init(2600822924,725511199),new h.init(528734635,4215389547),new h.init(1541459225,327033209)])},_doProcessBlock:function(s,l){for(var f=this._hash.words,B=f[0],i=f[1],x=f[2],u=f[3],g=f[4],D=f[5],_=f[6],m=f[7],w=B.high,E=B.low,R=i.high,k=i.low,y=x.high,T=x.low,H=u.high,L=u.low,q=g.high,O=g.low,W=D.high,z=D.low,I=_.high,S=_.low,P=m.high,b=m.low,V=w,G=E,Q=R,$=k,B0=y,A0=T,w0=H,D0=L,t0=q,X=O,y0=W,g0=z,b0=I,_0=S,T0=P,R0=b,r0=0;r0<80;r0++){var e0,d0,S0=a[r0];if(r0<16)d0=S0.high=s[l+r0*2]|0,e0=S0.low=s[l+r0*2+1]|0;else{var Be=a[r0-15],E0=Be.high,m0=Be.low,or=(E0>>>1|m0<<31)^(E0>>>8|m0<<24)^E0>>>7,De=(m0>>>1|E0<<31)^(m0>>>8|E0<<24)^(m0>>>7|E0<<25),ge=a[r0-2],C0=ge.high,F0=ge.low,ir=(C0>>>19|F0<<13)^(C0<<3|F0>>>29)^C0>>>6,_e=(F0>>>19|C0<<13)^(F0<<3|C0>>>29)^(F0>>>6|C0<<26),Re=a[r0-7],xr=Re.high,sr=Re.low,me=a[r0-16],cr=me.high,Fe=me.low;e0=De+sr,d0=or+xr+(e0>>>0<De>>>0?1:0),e0=e0+_e,d0=d0+ir+(e0>>>0<_e>>>0?1:0),e0=e0+Fe,d0=d0+cr+(e0>>>0<Fe>>>0?1:0),S0.high=d0,S0.low=e0}var ur=t0&y0^~t0&b0,Ie=X&g0^~X&_0,dr=V&Q^V&B0^Q&B0,fr=G&$^G&A0^$&A0,lr=(V>>>28|G<<4)^(V<<30|G>>>2)^(V<<25|G>>>7),ke=(G>>>28|V<<4)^(G<<30|V>>>2)^(G<<25|V>>>7),vr=(t0>>>14|X<<18)^(t0>>>18|X<<14)^(t0<<23|X>>>9),hr=(X>>>14|t0<<18)^(X>>>18|t0<<14)^(X<<23|t0>>>9),ye=C[r0],pr=ye.high,be=ye.low,Z=R0+hr,f0=T0+vr+(Z>>>0<R0>>>0?1:0),Z=Z+Ie,f0=f0+ur+(Z>>>0<Ie>>>0?1:0),Z=Z+be,f0=f0+pr+(Z>>>0<be>>>0?1:0),Z=Z+e0,f0=f0+d0+(Z>>>0<e0>>>0?1:0),Se=ke+fr,Ar=lr+dr+(Se>>>0<ke>>>0?1:0);T0=b0,R0=_0,b0=y0,_0=g0,y0=t0,g0=X,X=D0+Z|0,t0=w0+f0+(X>>>0<D0>>>0?1:0)|0,w0=B0,D0=A0,B0=Q,A0=$,Q=V,$=G,G=Z+Se|0,V=f0+Ar+(G>>>0<Z>>>0?1:0)|0}E=B.low=E+G,B.high=w+V+(E>>>0<G>>>0?1:0),k=i.low=k+$,i.high=R+Q+(k>>>0<$>>>0?1:0),T=x.low=T+A0,x.high=y+B0+(T>>>0<A0>>>0?1:0),L=u.low=L+D0,u.high=H+w0+(L>>>0<D0>>>0?1:0),O=g.low=O+X,g.high=q+t0+(O>>>0<X>>>0?1:0),z=D.low=z+g0,D.high=W+y0+(z>>>0<g0>>>0?1:0),S=_.low=S+_0,_.high=I+b0+(S>>>0<_0>>>0?1:0),b=m.low=b+R0,m.high=P+T0+(b>>>0<R0>>>0?1:0)},_doFinalize:function(){var s=this._data,l=s.words,f=this._nDataBytes*8,B=s.sigBytes*8;l[B>>>5]|=128<<24-B%32,l[(B+128>>>10<<5)+30]=Math.floor(f/4294967296),l[(B+128>>>10<<5)+31]=f,s.sigBytes=l.length*4,this._process();var i=this._hash.toX32();return i},clone:function(){var s=v.clone.call(this);return s._hash=this._hash.clone(),s},blockSize:1024/32});e.SHA512=v._createHelper(c),e.HmacSHA512=v._createHmacHelper(c)}(),r.SHA512})}(V0)),V0.exports}var K0={exports:{}},Ke;function Vr(){return Ke||(Ke=1,function(n,d){(function(r,e,A){n.exports=e(N(),P0(),Gt())})(U,function(r){return function(){var e=r,A=e.x64,v=A.Word,F=A.WordArray,h=e.algo,p=h.SHA512,t=h.SHA384=p.extend({_doReset:function(){this._hash=new F.init([new v.init(3418070365,3238371032),new v.init(1654270250,914150663),new v.init(2438529370,812702999),new v.init(355462360,4144912697),new v.init(1731405415,4290775857),new v.init(2394180231,1750603025),new v.init(3675008525,1694076839),new v.init(1203062813,3204075428)])},_doFinalize:function(){var o=p._doFinalize.call(this);return o.sigBytes-=16,o}});e.SHA384=p._createHelper(t),e.HmacSHA384=p._createHmacHelper(t)}(),r.SHA384})}(K0)),K0.exports}var M0={exports:{}},Me;function Kr(){return Me||(Me=1,function(n,d){(function(r,e,A){n.exports=e(N(),P0())})(U,function(r){return function(e){var A=r,v=A.lib,F=v.WordArray,h=v.Hasher,p=A.x64,t=p.Word,o=A.algo,C=[],a=[],c=[];(function(){for(var f=1,B=0,i=0;i<24;i++){C[f+5*B]=(i+1)*(i+2)/2%64;var x=B%5,u=(2*f+3*B)%5;f=x,B=u}for(var f=0;f<5;f++)for(var B=0;B<5;B++)a[f+5*B]=B+(2*f+3*B)%5*5;for(var g=1,D=0;D<24;D++){for(var _=0,m=0,w=0;w<7;w++){if(g&1){var E=(1<<w)-1;E<32?m^=1<<E:_^=1<<E-32}g&128?g=g<<1^113:g<<=1}c[D]=t.create(_,m)}})();var s=[];(function(){for(var f=0;f<25;f++)s[f]=t.create()})();var l=o.SHA3=h.extend({cfg:h.cfg.extend({outputLength:512}),_doReset:function(){for(var f=this._state=[],B=0;B<25;B++)f[B]=new t.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(f,B){for(var i=this._state,x=this.blockSize/2,u=0;u<x;u++){var g=f[B+2*u],D=f[B+2*u+1];g=(g<<8|g>>>24)&16711935|(g<<24|g>>>8)&4278255360,D=(D<<8|D>>>24)&16711935|(D<<24|D>>>8)&4278255360;var _=i[u];_.high^=D,_.low^=g}for(var m=0;m<24;m++){for(var w=0;w<5;w++){for(var E=0,R=0,k=0;k<5;k++){var _=i[w+5*k];E^=_.high,R^=_.low}var y=s[w];y.high=E,y.low=R}for(var w=0;w<5;w++)for(var T=s[(w+4)%5],H=s[(w+1)%5],L=H.high,q=H.low,E=T.high^(L<<1|q>>>31),R=T.low^(q<<1|L>>>31),k=0;k<5;k++){var _=i[w+5*k];_.high^=E,_.low^=R}for(var O=1;O<25;O++){var E,R,_=i[O],W=_.high,z=_.low,I=C[O];I<32?(E=W<<I|z>>>32-I,R=z<<I|W>>>32-I):(E=z<<I-32|W>>>64-I,R=W<<I-32|z>>>64-I);var S=s[a[O]];S.high=E,S.low=R}var P=s[0],b=i[0];P.high=b.high,P.low=b.low;for(var w=0;w<5;w++)for(var k=0;k<5;k++){var O=w+5*k,_=i[O],V=s[O],G=s[(w+1)%5+5*k],Q=s[(w+2)%5+5*k];_.high=V.high^~G.high&Q.high,_.low=V.low^~G.low&Q.low}var _=i[0],$=c[m];_.high^=$.high,_.low^=$.low}},_doFinalize:function(){var f=this._data,B=f.words;this._nDataBytes*8;var i=f.sigBytes*8,x=this.blockSize*32;B[i>>>5]|=1<<24-i%32,B[(e.ceil((i+1)/x)*x>>>5)-1]|=128,f.sigBytes=B.length*4,this._process();for(var u=this._state,g=this.cfg.outputLength/8,D=g/8,_=[],m=0;m<D;m++){var w=u[m],E=w.high,R=w.low;E=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360,R=(R<<8|R>>>24)&16711935|(R<<24|R>>>8)&4278255360,_.push(R),_.push(E)}return new F.init(_,g)},clone:function(){for(var f=h.clone.call(this),B=f._state=this._state.slice(0),i=0;i<25;i++)B[i]=B[i].clone();return f}});A.SHA3=h._createHelper(l),A.HmacSHA3=h._createHmacHelper(l)}(Math),r.SHA3})}(M0)),M0.exports}var Q0={exports:{}},Qe;function Mr(){return Qe||(Qe=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){/** @preserve
			(c) 2012 by Cédric Mesnil. All rights reserved.

			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
			*/return function(e){var A=r,v=A.lib,F=v.WordArray,h=v.Hasher,p=A.algo,t=F.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),o=F.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),C=F.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),a=F.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),c=F.create([0,1518500249,1859775393,2400959708,2840853838]),s=F.create([1352829926,1548603684,1836072691,2053994217,0]),l=p.RIPEMD160=h.extend({_doReset:function(){this._hash=F.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(D,_){for(var m=0;m<16;m++){var w=_+m,E=D[w];D[w]=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360}var R=this._hash.words,k=c.words,y=s.words,T=t.words,H=o.words,L=C.words,q=a.words,O,W,z,I,S,P,b,V,G,Q;P=O=R[0],b=W=R[1],V=z=R[2],G=I=R[3],Q=S=R[4];for(var $,m=0;m<80;m+=1)$=O+D[_+T[m]]|0,m<16?$+=f(W,z,I)+k[0]:m<32?$+=B(W,z,I)+k[1]:m<48?$+=i(W,z,I)+k[2]:m<64?$+=x(W,z,I)+k[3]:$+=u(W,z,I)+k[4],$=$|0,$=g($,L[m]),$=$+S|0,O=S,S=I,I=g(z,10),z=W,W=$,$=P+D[_+H[m]]|0,m<16?$+=u(b,V,G)+y[0]:m<32?$+=x(b,V,G)+y[1]:m<48?$+=i(b,V,G)+y[2]:m<64?$+=B(b,V,G)+y[3]:$+=f(b,V,G)+y[4],$=$|0,$=g($,q[m]),$=$+Q|0,P=Q,Q=G,G=g(V,10),V=b,b=$;$=R[1]+z+G|0,R[1]=R[2]+I+Q|0,R[2]=R[3]+S+P|0,R[3]=R[4]+O+b|0,R[4]=R[0]+W+V|0,R[0]=$},_doFinalize:function(){var D=this._data,_=D.words,m=this._nDataBytes*8,w=D.sigBytes*8;_[w>>>5]|=128<<24-w%32,_[(w+64>>>9<<4)+14]=(m<<8|m>>>24)&16711935|(m<<24|m>>>8)&4278255360,D.sigBytes=(_.length+1)*4,this._process();for(var E=this._hash,R=E.words,k=0;k<5;k++){var y=R[k];R[k]=(y<<8|y>>>24)&16711935|(y<<24|y>>>8)&4278255360}return E},clone:function(){var D=h.clone.call(this);return D._hash=this._hash.clone(),D}});function f(D,_,m){return D^_^m}function B(D,_,m){return D&_|~D&m}function i(D,_,m){return(D|~_)^m}function x(D,_,m){return D&m|_&~m}function u(D,_,m){return D^(_|~m)}function g(D,_){return D<<_|D>>>32-_}A.RIPEMD160=h._createHelper(l),A.HmacRIPEMD160=h._createHmacHelper(l)}(),r.RIPEMD160})}(Q0)),Q0.exports}var X0={exports:{}},Xe;function Ee(){return Xe||(Xe=1,function(n,d){(function(r,e){n.exports=e(N())})(U,function(r){(function(){var e=r,A=e.lib,v=A.Base,F=e.enc,h=F.Utf8,p=e.algo;p.HMAC=v.extend({init:function(t,o){t=this._hasher=new t.init,typeof o=="string"&&(o=h.parse(o));var C=t.blockSize,a=C*4;o.sigBytes>a&&(o=t.finalize(o)),o.clamp();for(var c=this._oKey=o.clone(),s=this._iKey=o.clone(),l=c.words,f=s.words,B=0;B<C;B++)l[B]^=1549556828,f[B]^=909522486;c.sigBytes=s.sigBytes=a,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var o=this._hasher,C=o.finalize(t);o.reset();var a=o.finalize(this._oKey.clone().concat(C));return a}})})()})}(X0)),X0.exports}var Z0={exports:{}},Ze;function Qr(){return Ze||(Ze=1,function(n,d){(function(r,e,A){n.exports=e(N(),Ae(),Ee())})(U,function(r){return function(){var e=r,A=e.lib,v=A.Base,F=A.WordArray,h=e.algo,p=h.SHA256,t=h.HMAC,o=h.PBKDF2=v.extend({cfg:v.extend({keySize:128/32,hasher:p,iterations:25e4}),init:function(C){this.cfg=this.cfg.extend(C)},compute:function(C,a){for(var c=this.cfg,s=t.create(c.hasher,C),l=F.create(),f=F.create([1]),B=l.words,i=f.words,x=c.keySize,u=c.iterations;B.length<x;){var g=s.update(a).finalize(f);s.reset();for(var D=g.words,_=D.length,m=g,w=1;w<u;w++){m=s.finalize(m),s.reset();for(var E=m.words,R=0;R<_;R++)D[R]^=E[R]}l.concat(g),i[0]++}return l.sigBytes=x*4,l}});e.PBKDF2=function(C,a,c){return o.create(c).compute(C,a)}}(),r.PBKDF2})}(Z0)),Z0.exports}var Y0={exports:{}},Ye;function v0(){return Ye||(Ye=1,function(n,d){(function(r,e,A){n.exports=e(N(),Wt(),Ee())})(U,function(r){return function(){var e=r,A=e.lib,v=A.Base,F=A.WordArray,h=e.algo,p=h.MD5,t=h.EvpKDF=v.extend({cfg:v.extend({keySize:128/32,hasher:p,iterations:1}),init:function(o){this.cfg=this.cfg.extend(o)},compute:function(o,C){for(var a,c=this.cfg,s=c.hasher.create(),l=F.create(),f=l.words,B=c.keySize,i=c.iterations;f.length<B;){a&&s.update(a),a=s.update(o).finalize(C),s.reset();for(var x=1;x<i;x++)a=s.finalize(a),s.reset();l.concat(a)}return l.sigBytes=B*4,l}});e.EvpKDF=function(o,C,a){return t.create(a).compute(o,C)}}(),r.EvpKDF})}(Y0)),Y0.exports}var j0={exports:{}},je;function K(){return je||(je=1,function(n,d){(function(r,e,A){n.exports=e(N(),v0())})(U,function(r){r.lib.Cipher||function(e){var A=r,v=A.lib,F=v.Base,h=v.WordArray,p=v.BufferedBlockAlgorithm,t=A.enc;t.Utf8;var o=t.Base64,C=A.algo,a=C.EvpKDF,c=v.Cipher=p.extend({cfg:F.extend(),createEncryptor:function(E,R){return this.create(this._ENC_XFORM_MODE,E,R)},createDecryptor:function(E,R){return this.create(this._DEC_XFORM_MODE,E,R)},init:function(E,R,k){this.cfg=this.cfg.extend(k),this._xformMode=E,this._key=R,this.reset()},reset:function(){p.reset.call(this),this._doReset()},process:function(E){return this._append(E),this._process()},finalize:function(E){E&&this._append(E);var R=this._doFinalize();return R},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function E(R){return typeof R=="string"?w:D}return function(R){return{encrypt:function(k,y,T){return E(y).encrypt(R,k,y,T)},decrypt:function(k,y,T){return E(y).decrypt(R,k,y,T)}}}}()});v.StreamCipher=c.extend({_doFinalize:function(){var E=this._process(!0);return E},blockSize:1});var s=A.mode={},l=v.BlockCipherMode=F.extend({createEncryptor:function(E,R){return this.Encryptor.create(E,R)},createDecryptor:function(E,R){return this.Decryptor.create(E,R)},init:function(E,R){this._cipher=E,this._iv=R}}),f=s.CBC=function(){var E=l.extend();E.Encryptor=E.extend({processBlock:function(k,y){var T=this._cipher,H=T.blockSize;R.call(this,k,y,H),T.encryptBlock(k,y),this._prevBlock=k.slice(y,y+H)}}),E.Decryptor=E.extend({processBlock:function(k,y){var T=this._cipher,H=T.blockSize,L=k.slice(y,y+H);T.decryptBlock(k,y),R.call(this,k,y,H),this._prevBlock=L}});function R(k,y,T){var H,L=this._iv;L?(H=L,this._iv=e):H=this._prevBlock;for(var q=0;q<T;q++)k[y+q]^=H[q]}return E}(),B=A.pad={},i=B.Pkcs7={pad:function(E,R){for(var k=R*4,y=k-E.sigBytes%k,T=y<<24|y<<16|y<<8|y,H=[],L=0;L<y;L+=4)H.push(T);var q=h.create(H,y);E.concat(q)},unpad:function(E){var R=E.words[E.sigBytes-1>>>2]&255;E.sigBytes-=R}};v.BlockCipher=c.extend({cfg:c.cfg.extend({mode:f,padding:i}),reset:function(){var E;c.reset.call(this);var R=this.cfg,k=R.iv,y=R.mode;this._xformMode==this._ENC_XFORM_MODE?E=y.createEncryptor:(E=y.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==E?this._mode.init(this,k&&k.words):(this._mode=E.call(y,this,k&&k.words),this._mode.__creator=E)},_doProcessBlock:function(E,R){this._mode.processBlock(E,R)},_doFinalize:function(){var E,R=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(R.pad(this._data,this.blockSize),E=this._process(!0)):(E=this._process(!0),R.unpad(E)),E},blockSize:128/32});var x=v.CipherParams=F.extend({init:function(E){this.mixIn(E)},toString:function(E){return(E||this.formatter).stringify(this)}}),u=A.format={},g=u.OpenSSL={stringify:function(E){var R,k=E.ciphertext,y=E.salt;return y?R=h.create([1398893684,1701076831]).concat(y).concat(k):R=k,R.toString(o)},parse:function(E){var R,k=o.parse(E),y=k.words;return y[0]==1398893684&&y[1]==1701076831&&(R=h.create(y.slice(2,4)),y.splice(0,4),k.sigBytes-=16),x.create({ciphertext:k,salt:R})}},D=v.SerializableCipher=F.extend({cfg:F.extend({format:g}),encrypt:function(E,R,k,y){y=this.cfg.extend(y);var T=E.createEncryptor(k,y),H=T.finalize(R),L=T.cfg;return x.create({ciphertext:H,key:k,iv:L.iv,algorithm:E,mode:L.mode,padding:L.padding,blockSize:E.blockSize,formatter:y.format})},decrypt:function(E,R,k,y){y=this.cfg.extend(y),R=this._parse(R,y.format);var T=E.createDecryptor(k,y).finalize(R.ciphertext);return T},_parse:function(E,R){return typeof E=="string"?R.parse(E,this):E}}),_=A.kdf={},m=_.OpenSSL={execute:function(E,R,k,y,T){if(y||(y=h.random(64/8)),T)var H=a.create({keySize:R+k,hasher:T}).compute(E,y);else var H=a.create({keySize:R+k}).compute(E,y);var L=h.create(H.words.slice(R),k*4);return H.sigBytes=R*4,x.create({key:H,iv:L,salt:y})}},w=v.PasswordBasedCipher=D.extend({cfg:D.cfg.extend({kdf:m}),encrypt:function(E,R,k,y){y=this.cfg.extend(y);var T=y.kdf.execute(k,E.keySize,E.ivSize,y.salt,y.hasher);y.iv=T.iv;var H=D.encrypt.call(this,E,R,T.key,y);return H.mixIn(T),H},decrypt:function(E,R,k,y){y=this.cfg.extend(y),R=this._parse(R,y.format);var T=y.kdf.execute(k,E.keySize,E.ivSize,R.salt,y.hasher);y.iv=T.iv;var H=D.decrypt.call(this,E,R,T.key,y);return H}})}()})}(j0)),j0.exports}var J0={exports:{}},Je;function Xr(){return Je||(Je=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();e.Encryptor=e.extend({processBlock:function(v,F){var h=this._cipher,p=h.blockSize;A.call(this,v,F,p,h),this._prevBlock=v.slice(F,F+p)}}),e.Decryptor=e.extend({processBlock:function(v,F){var h=this._cipher,p=h.blockSize,t=v.slice(F,F+p);A.call(this,v,F,p,h),this._prevBlock=t}});function A(v,F,h,p){var t,o=this._iv;o?(t=o.slice(0),this._iv=void 0):t=this._prevBlock,p.encryptBlock(t,0);for(var C=0;C<h;C++)v[F+C]^=t[C]}return e}(),r.mode.CFB})}(J0)),J0.exports}var ee={exports:{}},et;function Zr(){return et||(et=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.mode.CTR=function(){var e=r.lib.BlockCipherMode.extend(),A=e.Encryptor=e.extend({processBlock:function(v,F){var h=this._cipher,p=h.blockSize,t=this._iv,o=this._counter;t&&(o=this._counter=t.slice(0),this._iv=void 0);var C=o.slice(0);h.encryptBlock(C,0),o[p-1]=o[p-1]+1|0;for(var a=0;a<p;a++)v[F+a]^=C[a]}});return e.Decryptor=A,e}(),r.mode.CTR})}(ee)),ee.exports}var te={exports:{}},tt;function Yr(){return tt||(tt=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){/** @preserve
 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
 * derived from CryptoJS.mode.CTR
 * <NAME_EMAIL>
 */return r.mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function A(h){if((h>>24&255)===255){var p=h>>16&255,t=h>>8&255,o=h&255;p===255?(p=0,t===255?(t=0,o===255?o=0:++o):++t):++p,h=0,h+=p<<16,h+=t<<8,h+=o}else h+=1<<24;return h}function v(h){return(h[0]=A(h[0]))===0&&(h[1]=A(h[1])),h}var F=e.Encryptor=e.extend({processBlock:function(h,p){var t=this._cipher,o=t.blockSize,C=this._iv,a=this._counter;C&&(a=this._counter=C.slice(0),this._iv=void 0),v(a);var c=a.slice(0);t.encryptBlock(c,0);for(var s=0;s<o;s++)h[p+s]^=c[s]}});return e.Decryptor=F,e}(),r.mode.CTRGladman})}(te)),te.exports}var re={exports:{}},rt;function jr(){return rt||(rt=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.mode.OFB=function(){var e=r.lib.BlockCipherMode.extend(),A=e.Encryptor=e.extend({processBlock:function(v,F){var h=this._cipher,p=h.blockSize,t=this._iv,o=this._keystream;t&&(o=this._keystream=t.slice(0),this._iv=void 0),h.encryptBlock(o,0);for(var C=0;C<p;C++)v[F+C]^=o[C]}});return e.Decryptor=A,e}(),r.mode.OFB})}(re)),re.exports}var ne={exports:{}},nt;function Jr(){return nt||(nt=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.mode.ECB=function(){var e=r.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(A,v){this._cipher.encryptBlock(A,v)}}),e.Decryptor=e.extend({processBlock:function(A,v){this._cipher.decryptBlock(A,v)}}),e}(),r.mode.ECB})}(ne)),ne.exports}var ae={exports:{}},at;function en(){return at||(at=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.pad.AnsiX923={pad:function(e,A){var v=e.sigBytes,F=A*4,h=F-v%F,p=v+h-1;e.clamp(),e.words[p>>>2]|=h<<24-p%4*8,e.sigBytes+=h},unpad:function(e){var A=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=A}},r.pad.Ansix923})}(ae)),ae.exports}var oe={exports:{}},ot;function tn(){return ot||(ot=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.pad.Iso10126={pad:function(e,A){var v=A*4,F=v-e.sigBytes%v;e.concat(r.lib.WordArray.random(F-1)).concat(r.lib.WordArray.create([F<<24],1))},unpad:function(e){var A=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=A}},r.pad.Iso10126})}(oe)),oe.exports}var ie={exports:{}},it;function rn(){return it||(it=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.pad.Iso97971={pad:function(e,A){e.concat(r.lib.WordArray.create([2147483648],1)),r.pad.ZeroPadding.pad(e,A)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},r.pad.Iso97971})}(ie)),ie.exports}var xe={exports:{}},xt;function nn(){return xt||(xt=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.pad.ZeroPadding={pad:function(e,A){var v=A*4;e.clamp(),e.sigBytes+=v-(e.sigBytes%v||v)},unpad:function(e){for(var A=e.words,v=e.sigBytes-1,v=e.sigBytes-1;v>=0;v--)if(A[v>>>2]>>>24-v%4*8&255){e.sigBytes=v+1;break}}},r.pad.ZeroPadding})}(xe)),xe.exports}var se={exports:{}},st;function an(){return st||(st=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return r.pad.NoPadding={pad:function(){},unpad:function(){}},r.pad.NoPadding})}(se)),se.exports}var ce={exports:{}},ct;function on(){return ct||(ct=1,function(n,d){(function(r,e,A){n.exports=e(N(),K())})(U,function(r){return function(e){var A=r,v=A.lib,F=v.CipherParams,h=A.enc,p=h.Hex,t=A.format;t.Hex={stringify:function(o){return o.ciphertext.toString(p)},parse:function(o){var C=p.parse(o);return F.create({ciphertext:C})}}}(),r.format.Hex})}(ce)),ce.exports}var ue={exports:{}},ut;function xn(){return ut||(ut=1,function(n,d){(function(r,e,A){n.exports=e(N(),h0(),p0(),v0(),K())})(U,function(r){return function(){var e=r,A=e.lib,v=A.BlockCipher,F=e.algo,h=[],p=[],t=[],o=[],C=[],a=[],c=[],s=[],l=[],f=[];(function(){for(var x=[],u=0;u<256;u++)u<128?x[u]=u<<1:x[u]=u<<1^283;for(var g=0,D=0,u=0;u<256;u++){var _=D^D<<1^D<<2^D<<3^D<<4;_=_>>>8^_&255^99,h[g]=_,p[_]=g;var m=x[g],w=x[m],E=x[w],R=x[_]*257^_*16843008;t[g]=R<<24|R>>>8,o[g]=R<<16|R>>>16,C[g]=R<<8|R>>>24,a[g]=R;var R=E*16843009^w*65537^m*257^g*16843008;c[_]=R<<24|R>>>8,s[_]=R<<16|R>>>16,l[_]=R<<8|R>>>24,f[_]=R,g?(g=m^x[x[x[E^m]]],D^=x[x[D]]):g=D=1}})();var B=[0,1,2,4,8,16,32,64,128,27,54],i=F.AES=v.extend({_doReset:function(){var x;if(!(this._nRounds&&this._keyPriorReset===this._key)){for(var u=this._keyPriorReset=this._key,g=u.words,D=u.sigBytes/4,_=this._nRounds=D+6,m=(_+1)*4,w=this._keySchedule=[],E=0;E<m;E++)E<D?w[E]=g[E]:(x=w[E-1],E%D?D>6&&E%D==4&&(x=h[x>>>24]<<24|h[x>>>16&255]<<16|h[x>>>8&255]<<8|h[x&255]):(x=x<<8|x>>>24,x=h[x>>>24]<<24|h[x>>>16&255]<<16|h[x>>>8&255]<<8|h[x&255],x^=B[E/D|0]<<24),w[E]=w[E-D]^x);for(var R=this._invKeySchedule=[],k=0;k<m;k++){var E=m-k;if(k%4)var x=w[E];else var x=w[E-4];k<4||E<=4?R[k]=x:R[k]=c[h[x>>>24]]^s[h[x>>>16&255]]^l[h[x>>>8&255]]^f[h[x&255]]}}},encryptBlock:function(x,u){this._doCryptBlock(x,u,this._keySchedule,t,o,C,a,h)},decryptBlock:function(x,u){var g=x[u+1];x[u+1]=x[u+3],x[u+3]=g,this._doCryptBlock(x,u,this._invKeySchedule,c,s,l,f,p);var g=x[u+1];x[u+1]=x[u+3],x[u+3]=g},_doCryptBlock:function(x,u,g,D,_,m,w,E){for(var R=this._nRounds,k=x[u]^g[0],y=x[u+1]^g[1],T=x[u+2]^g[2],H=x[u+3]^g[3],L=4,q=1;q<R;q++){var O=D[k>>>24]^_[y>>>16&255]^m[T>>>8&255]^w[H&255]^g[L++],W=D[y>>>24]^_[T>>>16&255]^m[H>>>8&255]^w[k&255]^g[L++],z=D[T>>>24]^_[H>>>16&255]^m[k>>>8&255]^w[y&255]^g[L++],I=D[H>>>24]^_[k>>>16&255]^m[y>>>8&255]^w[T&255]^g[L++];k=O,y=W,T=z,H=I}var O=(E[k>>>24]<<24|E[y>>>16&255]<<16|E[T>>>8&255]<<8|E[H&255])^g[L++],W=(E[y>>>24]<<24|E[T>>>16&255]<<16|E[H>>>8&255]<<8|E[k&255])^g[L++],z=(E[T>>>24]<<24|E[H>>>16&255]<<16|E[k>>>8&255]<<8|E[y&255])^g[L++],I=(E[H>>>24]<<24|E[k>>>16&255]<<16|E[y>>>8&255]<<8|E[T&255])^g[L++];x[u]=O,x[u+1]=W,x[u+2]=z,x[u+3]=I},keySize:256/32});e.AES=v._createHelper(i)}(),r.AES})}(ue)),ue.exports}var de={exports:{}},dt;function sn(){return dt||(dt=1,function(n,d){(function(r,e,A){n.exports=e(N(),h0(),p0(),v0(),K())})(U,function(r){return function(){var e=r,A=e.lib,v=A.WordArray,F=A.BlockCipher,h=e.algo,p=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],t=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],o=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],C=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],a=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],c=h.DES=F.extend({_doReset:function(){for(var B=this._key,i=B.words,x=[],u=0;u<56;u++){var g=p[u]-1;x[u]=i[g>>>5]>>>31-g%32&1}for(var D=this._subKeys=[],_=0;_<16;_++){for(var m=D[_]=[],w=o[_],u=0;u<24;u++)m[u/6|0]|=x[(t[u]-1+w)%28]<<31-u%6,m[4+(u/6|0)]|=x[28+(t[u+24]-1+w)%28]<<31-u%6;m[0]=m[0]<<1|m[0]>>>31;for(var u=1;u<7;u++)m[u]=m[u]>>>(u-1)*4+3;m[7]=m[7]<<5|m[7]>>>27}for(var E=this._invSubKeys=[],u=0;u<16;u++)E[u]=D[15-u]},encryptBlock:function(B,i){this._doCryptBlock(B,i,this._subKeys)},decryptBlock:function(B,i){this._doCryptBlock(B,i,this._invSubKeys)},_doCryptBlock:function(B,i,x){this._lBlock=B[i],this._rBlock=B[i+1],s.call(this,4,252645135),s.call(this,16,65535),l.call(this,2,858993459),l.call(this,8,16711935),s.call(this,1,1431655765);for(var u=0;u<16;u++){for(var g=x[u],D=this._lBlock,_=this._rBlock,m=0,w=0;w<8;w++)m|=C[w][((_^g[w])&a[w])>>>0];this._lBlock=_,this._rBlock=D^m}var E=this._lBlock;this._lBlock=this._rBlock,this._rBlock=E,s.call(this,1,1431655765),l.call(this,8,16711935),l.call(this,2,858993459),s.call(this,16,65535),s.call(this,4,252645135),B[i]=this._lBlock,B[i+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function s(B,i){var x=(this._lBlock>>>B^this._rBlock)&i;this._rBlock^=x,this._lBlock^=x<<B}function l(B,i){var x=(this._rBlock>>>B^this._lBlock)&i;this._lBlock^=x,this._rBlock^=x<<B}e.DES=F._createHelper(c);var f=h.TripleDES=F.extend({_doReset:function(){var B=this._key,i=B.words;if(i.length!==2&&i.length!==4&&i.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var x=i.slice(0,2),u=i.length<4?i.slice(0,2):i.slice(2,4),g=i.length<6?i.slice(0,2):i.slice(4,6);this._des1=c.createEncryptor(v.create(x)),this._des2=c.createEncryptor(v.create(u)),this._des3=c.createEncryptor(v.create(g))},encryptBlock:function(B,i){this._des1.encryptBlock(B,i),this._des2.decryptBlock(B,i),this._des3.encryptBlock(B,i)},decryptBlock:function(B,i){this._des3.decryptBlock(B,i),this._des2.encryptBlock(B,i),this._des1.decryptBlock(B,i)},keySize:192/32,ivSize:64/32,blockSize:64/32});e.TripleDES=F._createHelper(f)}(),r.TripleDES})}(de)),de.exports}var fe={exports:{}},ft;function cn(){return ft||(ft=1,function(n,d){(function(r,e,A){n.exports=e(N(),h0(),p0(),v0(),K())})(U,function(r){return function(){var e=r,A=e.lib,v=A.StreamCipher,F=e.algo,h=F.RC4=v.extend({_doReset:function(){for(var o=this._key,C=o.words,a=o.sigBytes,c=this._S=[],s=0;s<256;s++)c[s]=s;for(var s=0,l=0;s<256;s++){var f=s%a,B=C[f>>>2]>>>24-f%4*8&255;l=(l+c[s]+B)%256;var i=c[s];c[s]=c[l],c[l]=i}this._i=this._j=0},_doProcessBlock:function(o,C){o[C]^=p.call(this)},keySize:256/32,ivSize:0});function p(){for(var o=this._S,C=this._i,a=this._j,c=0,s=0;s<4;s++){C=(C+1)%256,a=(a+o[C])%256;var l=o[C];o[C]=o[a],o[a]=l,c|=o[(o[C]+o[a])%256]<<24-s*8}return this._i=C,this._j=a,c}e.RC4=v._createHelper(h);var t=F.RC4Drop=h.extend({cfg:h.cfg.extend({drop:192}),_doReset:function(){h._doReset.call(this);for(var o=this.cfg.drop;o>0;o--)p.call(this)}});e.RC4Drop=v._createHelper(t)}(),r.RC4})}(fe)),fe.exports}var le={exports:{}},lt;function un(){return lt||(lt=1,function(n,d){(function(r,e,A){n.exports=e(N(),h0(),p0(),v0(),K())})(U,function(r){return function(){var e=r,A=e.lib,v=A.StreamCipher,F=e.algo,h=[],p=[],t=[],o=F.Rabbit=v.extend({_doReset:function(){for(var a=this._key.words,c=this.cfg.iv,s=0;s<4;s++)a[s]=(a[s]<<8|a[s]>>>24)&16711935|(a[s]<<24|a[s]>>>8)&4278255360;var l=this._X=[a[0],a[3]<<16|a[2]>>>16,a[1],a[0]<<16|a[3]>>>16,a[2],a[1]<<16|a[0]>>>16,a[3],a[2]<<16|a[1]>>>16],f=this._C=[a[2]<<16|a[2]>>>16,a[0]&4294901760|a[1]&65535,a[3]<<16|a[3]>>>16,a[1]&4294901760|a[2]&65535,a[0]<<16|a[0]>>>16,a[2]&4294901760|a[3]&65535,a[1]<<16|a[1]>>>16,a[3]&4294901760|a[0]&65535];this._b=0;for(var s=0;s<4;s++)C.call(this);for(var s=0;s<8;s++)f[s]^=l[s+4&7];if(c){var B=c.words,i=B[0],x=B[1],u=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360,g=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,D=u>>>16|g&4294901760,_=g<<16|u&65535;f[0]^=u,f[1]^=D,f[2]^=g,f[3]^=_,f[4]^=u,f[5]^=D,f[6]^=g,f[7]^=_;for(var s=0;s<4;s++)C.call(this)}},_doProcessBlock:function(a,c){var s=this._X;C.call(this),h[0]=s[0]^s[5]>>>16^s[3]<<16,h[1]=s[2]^s[7]>>>16^s[5]<<16,h[2]=s[4]^s[1]>>>16^s[7]<<16,h[3]=s[6]^s[3]>>>16^s[1]<<16;for(var l=0;l<4;l++)h[l]=(h[l]<<8|h[l]>>>24)&16711935|(h[l]<<24|h[l]>>>8)&4278255360,a[c+l]^=h[l]},blockSize:128/32,ivSize:64/32});function C(){for(var a=this._X,c=this._C,s=0;s<8;s++)p[s]=c[s];c[0]=c[0]+1295307597+this._b|0,c[1]=c[1]+3545052371+(c[0]>>>0<p[0]>>>0?1:0)|0,c[2]=c[2]+886263092+(c[1]>>>0<p[1]>>>0?1:0)|0,c[3]=c[3]+1295307597+(c[2]>>>0<p[2]>>>0?1:0)|0,c[4]=c[4]+3545052371+(c[3]>>>0<p[3]>>>0?1:0)|0,c[5]=c[5]+886263092+(c[4]>>>0<p[4]>>>0?1:0)|0,c[6]=c[6]+1295307597+(c[5]>>>0<p[5]>>>0?1:0)|0,c[7]=c[7]+3545052371+(c[6]>>>0<p[6]>>>0?1:0)|0,this._b=c[7]>>>0<p[7]>>>0?1:0;for(var s=0;s<8;s++){var l=a[s]+c[s],f=l&65535,B=l>>>16,i=((f*f>>>17)+f*B>>>15)+B*B,x=((l&4294901760)*l|0)+((l&65535)*l|0);t[s]=i^x}a[0]=t[0]+(t[7]<<16|t[7]>>>16)+(t[6]<<16|t[6]>>>16)|0,a[1]=t[1]+(t[0]<<8|t[0]>>>24)+t[7]|0,a[2]=t[2]+(t[1]<<16|t[1]>>>16)+(t[0]<<16|t[0]>>>16)|0,a[3]=t[3]+(t[2]<<8|t[2]>>>24)+t[1]|0,a[4]=t[4]+(t[3]<<16|t[3]>>>16)+(t[2]<<16|t[2]>>>16)|0,a[5]=t[5]+(t[4]<<8|t[4]>>>24)+t[3]|0,a[6]=t[6]+(t[5]<<16|t[5]>>>16)+(t[4]<<16|t[4]>>>16)|0,a[7]=t[7]+(t[6]<<8|t[6]>>>24)+t[5]|0}e.Rabbit=v._createHelper(o)}(),r.Rabbit})}(le)),le.exports}var ve={exports:{}},vt;function dn(){return vt||(vt=1,function(n,d){(function(r,e,A){n.exports=e(N(),h0(),p0(),v0(),K())})(U,function(r){return function(){var e=r,A=e.lib,v=A.StreamCipher,F=e.algo,h=[],p=[],t=[],o=F.RabbitLegacy=v.extend({_doReset:function(){var a=this._key.words,c=this.cfg.iv,s=this._X=[a[0],a[3]<<16|a[2]>>>16,a[1],a[0]<<16|a[3]>>>16,a[2],a[1]<<16|a[0]>>>16,a[3],a[2]<<16|a[1]>>>16],l=this._C=[a[2]<<16|a[2]>>>16,a[0]&4294901760|a[1]&65535,a[3]<<16|a[3]>>>16,a[1]&4294901760|a[2]&65535,a[0]<<16|a[0]>>>16,a[2]&4294901760|a[3]&65535,a[1]<<16|a[1]>>>16,a[3]&4294901760|a[0]&65535];this._b=0;for(var f=0;f<4;f++)C.call(this);for(var f=0;f<8;f++)l[f]^=s[f+4&7];if(c){var B=c.words,i=B[0],x=B[1],u=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360,g=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,D=u>>>16|g&4294901760,_=g<<16|u&65535;l[0]^=u,l[1]^=D,l[2]^=g,l[3]^=_,l[4]^=u,l[5]^=D,l[6]^=g,l[7]^=_;for(var f=0;f<4;f++)C.call(this)}},_doProcessBlock:function(a,c){var s=this._X;C.call(this),h[0]=s[0]^s[5]>>>16^s[3]<<16,h[1]=s[2]^s[7]>>>16^s[5]<<16,h[2]=s[4]^s[1]>>>16^s[7]<<16,h[3]=s[6]^s[3]>>>16^s[1]<<16;for(var l=0;l<4;l++)h[l]=(h[l]<<8|h[l]>>>24)&16711935|(h[l]<<24|h[l]>>>8)&4278255360,a[c+l]^=h[l]},blockSize:128/32,ivSize:64/32});function C(){for(var a=this._X,c=this._C,s=0;s<8;s++)p[s]=c[s];c[0]=c[0]+1295307597+this._b|0,c[1]=c[1]+3545052371+(c[0]>>>0<p[0]>>>0?1:0)|0,c[2]=c[2]+886263092+(c[1]>>>0<p[1]>>>0?1:0)|0,c[3]=c[3]+1295307597+(c[2]>>>0<p[2]>>>0?1:0)|0,c[4]=c[4]+3545052371+(c[3]>>>0<p[3]>>>0?1:0)|0,c[5]=c[5]+886263092+(c[4]>>>0<p[4]>>>0?1:0)|0,c[6]=c[6]+1295307597+(c[5]>>>0<p[5]>>>0?1:0)|0,c[7]=c[7]+3545052371+(c[6]>>>0<p[6]>>>0?1:0)|0,this._b=c[7]>>>0<p[7]>>>0?1:0;for(var s=0;s<8;s++){var l=a[s]+c[s],f=l&65535,B=l>>>16,i=((f*f>>>17)+f*B>>>15)+B*B,x=((l&4294901760)*l|0)+((l&65535)*l|0);t[s]=i^x}a[0]=t[0]+(t[7]<<16|t[7]>>>16)+(t[6]<<16|t[6]>>>16)|0,a[1]=t[1]+(t[0]<<8|t[0]>>>24)+t[7]|0,a[2]=t[2]+(t[1]<<16|t[1]>>>16)+(t[0]<<16|t[0]>>>16)|0,a[3]=t[3]+(t[2]<<8|t[2]>>>24)+t[1]|0,a[4]=t[4]+(t[3]<<16|t[3]>>>16)+(t[2]<<16|t[2]>>>16)|0,a[5]=t[5]+(t[4]<<8|t[4]>>>24)+t[3]|0,a[6]=t[6]+(t[5]<<16|t[5]>>>16)+(t[4]<<16|t[4]>>>16)|0,a[7]=t[7]+(t[6]<<8|t[6]>>>24)+t[5]|0}e.RabbitLegacy=v._createHelper(o)}(),r.RabbitLegacy})}(ve)),ve.exports}var he={exports:{}},ht;function fn(){return ht||(ht=1,function(n,d){(function(r,e,A){n.exports=e(N(),h0(),p0(),v0(),K())})(U,function(r){return function(){var e=r,A=e.lib,v=A.BlockCipher,F=e.algo;const h=16,p=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],t=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var o={pbox:[],sbox:[]};function C(f,B){let i=B>>24&255,x=B>>16&255,u=B>>8&255,g=B&255,D=f.sbox[0][i]+f.sbox[1][x];return D=D^f.sbox[2][u],D=D+f.sbox[3][g],D}function a(f,B,i){let x=B,u=i,g;for(let D=0;D<h;++D)x=x^f.pbox[D],u=C(f,x)^u,g=x,x=u,u=g;return g=x,x=u,u=g,u=u^f.pbox[h],x=x^f.pbox[h+1],{left:x,right:u}}function c(f,B,i){let x=B,u=i,g;for(let D=h+1;D>1;--D)x=x^f.pbox[D],u=C(f,x)^u,g=x,x=u,u=g;return g=x,x=u,u=g,u=u^f.pbox[1],x=x^f.pbox[0],{left:x,right:u}}function s(f,B,i){for(let _=0;_<4;_++){f.sbox[_]=[];for(let m=0;m<256;m++)f.sbox[_][m]=t[_][m]}let x=0;for(let _=0;_<h+2;_++)f.pbox[_]=p[_]^B[x],x++,x>=i&&(x=0);let u=0,g=0,D=0;for(let _=0;_<h+2;_+=2)D=a(f,u,g),u=D.left,g=D.right,f.pbox[_]=u,f.pbox[_+1]=g;for(let _=0;_<4;_++)for(let m=0;m<256;m+=2)D=a(f,u,g),u=D.left,g=D.right,f.sbox[_][m]=u,f.sbox[_][m+1]=g;return!0}var l=F.Blowfish=v.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var f=this._keyPriorReset=this._key,B=f.words,i=f.sigBytes/4;s(o,B,i)}},encryptBlock:function(f,B){var i=a(o,f[B],f[B+1]);f[B]=i.left,f[B+1]=i.right},decryptBlock:function(f,B){var i=c(o,f[B],f[B+1]);f[B]=i.left,f[B+1]=i.right},blockSize:64/32,keySize:128/32,ivSize:64/32});e.Blowfish=v._createHelper(l)}(),r.Blowfish})}(he)),he.exports}(function(n,d){(function(r,e,A){n.exports=e(N(),P0(),zr(),Wr(),h0(),Gr(),p0(),Wt(),Ae(),qr(),Gt(),Vr(),Kr(),Mr(),Ee(),Qr(),v0(),K(),Xr(),Zr(),Yr(),jr(),Jr(),en(),tn(),rn(),nn(),an(),on(),xn(),sn(),cn(),un(),dn(),fn())})(U,function(r){return r})})(zt);var pt=zt.exports;const{store:I0}=pragma,{utils:ln}=pragma,{signIn:vn,getIdProviderConfig:hn,link:pn,getRedirectUri:qt,getAuthenticateBaseUri:An}=pragma.auth,Y={PROVIDER_ID:"DISCORD",SIGNIN_REDIRECT_PATH:"/redirect/SignInDiscord",LINK_REDIRECT_PATH:"/redirect/LinkDiscord",AUTHORIZE_URL:"https://discord.com/oauth2/authorize",BACKEND_REDIRECT:"/v1/account/discord-redirect"},En=async n=>await vn({providerId:Y.PROVIDER_ID,providerToken:n}),Cn=async n=>await pn({providerId:Y.PROVIDER_ID,providerToken:n}),At=n=>{const d=gn(128),r=_n(d),e=hn(Y.PROVIDER_ID),A=new URLSearchParams({client_id:e.clientId,code_challenge:r,code_challenge_method:"S256",redirect_uri:n,scope:"identify email guilds",response_type:"code"}),v=`${Y.AUTHORIZE_URL}?${A}`;I0.set("$discordCodeVerifier",d),ln.goToUrl(v)},Bn=async n=>{const d=I0.get("$discordCodeVerifier");if(I0.set("$discordCodeVerifier",null),!d)return{ok:!1,error:"Invalid Discord verifier."};const r=await Vt(n,d,qt(Y.SIGNIN_REDIRECT_PATH));return r?await En(r):{ok:!1,error:"Invalid code."}},Dn=async n=>{const d=I0.get("$discordCodeVerifier");if(I0.set("$discordCodeVerifier",null),!d)return{ok:!1,error:"Invalid Discord verifier."};const r=await Vt(n,d,qt(Y.LINK_REDIRECT_PATH));return r?await Cn(r):{ok:!1,error:"Invalid code."}},Vt=async(n,d,r)=>{const e=new URLSearchParams({code:n,code_verifier:d,redirect_uri:r}),A={method:"get"},v=`${An(Y.BACKEND_REDIRECT)}?${e.toString()}`;try{return(await l0(v,A)).data.access_token}catch(F){return console.error(F),""}},gn=n=>{let d="";const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";for(let e=0;e<n;e++)d+=r.charAt(Math.floor(Math.random()*r.length));return d},_n=n=>pt.SHA256(n).toString(pt.enc.Base64).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),{Icon:Rn,IdProviderAuthResultHandler:mn}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Fn}=pragma.auth,In={name:"SignInDiscord",components:{Icon:Rn,IdProviderAuthResultHandler:mn},emits:["success"],setup(n,{emit:d}){const r=M();return Fn(Bn,r),{Discord:Y,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Discord.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},kn=pragma.ui.helpers.eventEmitter,yn=pragma.auth.initiateLinkAndRedirectWithCode,bn={name:"LinkDiscord",components:{},emits:["linkResult"],setup(n,{emit:d}){return yn(Dn,"Discord",d,kn),{}},template:"<div/>"},{getRedirectUri:Et}=pragma.auth,Sn={id:Y.PROVIDER_ID,name:"Discord",iconName:"discord",enumValue:3,onSignInButtonClick:({router:n})=>{At(Et(Y.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{At(Et(Y.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInDiscord",path:Y.SIGNIN_REDIRECT_PATH,component:In}],registeredLinkRoutes:[{name:"LinkDiscord",path:Y.LINK_REDIRECT_PATH,component:bn}],routerBeforeHook:({router:n})=>!1},{store:k0}=pragma,{utils:Pn}=pragma,{signIn:wn,link:Tn,getIdProviderConfig:Hn,getAuthenticateBaseUri:Ln,getRedirectUri:Kt}=pragma.auth,j={SIGNIN_REDIRECT_PATH:"/redirect/SignInEpic",LINK_REDIRECT_PATH:"/redirect/LinkEpic",EPIC_AUTHORIZE_URL:"https://www.epicgames.com/id/authorize",BACKEND_REDIRECT:"/v1/account/oauth-redirect/epic",PROVIDER_ID:"EPIC"},Un=async n=>await wn({providerId:j.PROVIDER_ID,providerToken:n}),$n=async n=>await Tn({providerId:j.PROVIDER_ID,providerToken:n}),Ct=n=>{const d=zn(128),r=Hn(j.PROVIDER_ID),e=["basic_profile","presence","friends_list"],A=new URLSearchParams({client_id:r.clientId,state:d,redirect_uri:n,scope:e.join(" "),response_type:"code"}),v=`${j.EPIC_AUTHORIZE_URL}?${A}`;k0.set("$epicStateVerifier",d),Pn.goToUrl(v)},Nn=async n=>{const d=k0.get("$epicStateVerifier");if(k0.set("$epicStateVerifier",null),!d)return{ok:!1,error:"Invalid Epic verifier."};const r=await Mt(n,d,Kt(j.SIGNIN_REDIRECT_PATH));return r?await Un(r):{ok:!1,error:"Invalid code."}},On=async n=>{const d=k0.get("$epicStateVerifier");if(k0.set("$epicStateVerifier",null),!d)return{ok:!1,error:"Invalid Epic verifier."};const r=await Mt(n,d,Kt(j.LINK_REDIRECT_PATH));return r?await $n(r):{ok:!1,error:"Invalid code."}},Mt=async(n,d,r)=>{const e=new URLSearchParams({code:n,code_verifier:d,redirect_uri:r}),A={method:"get"},v=`${Ln(j.BACKEND_REDIRECT)}?${e.toString()}`;try{return(await l0(v,A)).data.access_token}catch(F){return console.warn(F),""}},zn=n=>{let d="";const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";for(let e=0;e<n;e++)d+=r.charAt(Math.floor(Math.random()*r.length));return d},{Icon:Wn,IdProviderAuthResultHandler:Gn}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:qn}=pragma.auth,Vn={name:"SignInEpic",components:{Icon:Wn,IdProviderAuthResultHandler:Gn},emits:["success"],setup(n,{emit:d}){const r=M();return qn(Nn,r),{Epic:j,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Epic.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},Kn=pragma.ui.helpers.eventEmitter,Mn=pragma.auth.initiateLinkAndRedirectWithCode,Qn={name:"LinkEpic",components:{},emits:["linkResult"],setup(n,{emit:d}){return Mn(On,"Epic",d,Kn),{}},template:"<div/>"},{getRedirectUri:Bt}=pragma.auth,Xn={id:j.PROVIDER_ID,name:"Epic",iconName:"epic",enumValue:2,onSignInButtonClick:({router:n})=>{Ct(Bt(j.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Ct(Bt(j.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInEpic",path:j.SIGNIN_REDIRECT_PATH,component:Vn}],registeredLinkRoutes:[{name:"LinkEpic",path:j.LINK_REDIRECT_PATH,component:Qn}],routerBeforeHook:({router:n})=>!1},{utils:Zn}=pragma,{signIn:Yn,link:jn,getIdProviderConfig:Jn,getAuthenticateBaseUri:ea,getRedirectUri:Qt}=pragma.auth,n0={GOOGLE_OAUTH_URL:"https://accounts.google.com/o/oauth2/v2/auth",SIGNIN_REDIRECT_PATH:"/redirect/SignInGoogle",LINK_REDIRECT_PATH:"/redirect/LinkGoogle",PROVIDER_ID:"GOOGLE",BACKEND_REDIRECT:"/v1/account/oauth-redirect/google"},ta=async n=>await Yn({providerId:n0.PROVIDER_ID,providerToken:n}),ra=async n=>await jn({providerId:n0.PROVIDER_ID,providerToken:n}),Dt=n=>{const d=Jn(n0.PROVIDER_ID),r=new URLSearchParams({client_id:d.clientId,redirect_uri:n,scope:["https://www.googleapis.com/auth/userinfo.profile","https://www.googleapis.com/auth/userinfo.email"].join(" "),response_type:"code",access_type:"offline",prompt:"consent"}),e=`${n0.GOOGLE_OAUTH_URL}?${r}`;Zn.goToUrl(e)},na=async n=>{const d=await Xt(n,Qt(n0.SIGNIN_REDIRECT_PATH));return d?await ta(d):{ok:!1,error:"Invalid code."}},aa=async n=>{const d=await Xt(n,Qt(n0.LINK_REDIRECT_PATH));return d?await ra(d):{ok:!1,error:"Invalid code."}},Xt=async(n,d)=>{const r=new URLSearchParams({code:n,redirect_uri:d}),e={method:"get"},A=`${ea(n0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await l0(A,e)).data.access_token}catch(v){console.warn(v);return}},{Icon:oa,IdProviderAuthResultHandler:ia}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:xa}=pragma.auth,sa={name:"SignInGoogle",components:{Icon:oa,IdProviderAuthResultHandler:ia},emits:["success"],setup(n,{emit:d}){const r=M();return xa(na,r),{Google:n0,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Google.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},ca=pragma.ui.helpers.eventEmitter,ua=pragma.auth.initiateLinkAndRedirectWithCode,da={name:"LinkGoogle",components:{},emits:["linkResult"],setup(n,{emit:d}){return ua(aa,"Google",d,ca),{}},template:"<div/>"},{utils:gt}=pragma,{getRedirectUri:_t}=pragma.auth,fa={id:n0.PROVIDER_ID,name:"Google",iconName:"google",enumValue:6,onSignInButtonClick:({router:n})=>{Dt(_t(n0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Dt(_t(n0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInGoogle",path:"/signin-google",component:sa}],registeredLinkRoutes:[{name:"LinkGoogle",path:"/link-google",component:da}],routerBeforeHook:({router:n})=>{let d=gt.getWindowLocation().href;if(d.search("#/access_token=")>=0)return d=d.replace("#/access_token=","&access_token="),gt.goToUrl(d),!0}},{utils:la}=pragma,{signIn:va,link:ha,getIdProviderConfig:pa,getAuthenticateBaseUri:Aa,getRedirectUri:Zt}=pragma.auth,a0={GOOGLE_OAUTH_URL:"https://accounts.google.com/o/oauth2/v2/auth",SIGNIN_REDIRECT_PATH:"/redirect/SignInGoogleWorkspace",LINK_REDIRECT_PATH:"/redirect/LinkGoogleWorkspace",PROVIDER_ID:"GOOGLEWORKSPACE",BACKEND_REDIRECT:"/v1/account/oauth-redirect/googleworkspace"},Ea=async n=>await va({providerId:a0.PROVIDER_ID,providerToken:n}),Ca=async n=>await ha({providerId:a0.PROVIDER_ID,providerToken:n}),Rt=n=>{const d=pa(a0.PROVIDER_ID),r=new URLSearchParams({client_id:d.clientId,redirect_uri:n,scope:["https://www.googleapis.com/auth/userinfo.profile","https://www.googleapis.com/auth/userinfo.email"].join(" "),response_type:"code",access_type:"offline",prompt:"consent"}),e=`${a0.GOOGLE_OAUTH_URL}?${r}`;la.goToUrl(e)},Ba=async n=>{const d=await Yt(n,Zt(a0.SIGNIN_REDIRECT_PATH));return d?await Ea(d):{ok:!1,error:"Invalid code."}},Da=async n=>{const d=await Yt(n,Zt(a0.LINK_REDIRECT_PATH));return d?await Ca(d):{ok:!1,error:"Invalid code."}},Yt=async(n,d)=>{const r=new URLSearchParams({code:n,redirect_uri:d}),e={method:"get"},A=`${Aa(a0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await l0(A,e)).data.access_token}catch(v){console.warn(v);return}},{Icon:ga,IdProviderAuthResultHandler:_a}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Ra}=pragma.auth,ma={name:"SignInGoogleWorkspace",components:{Icon:ga,IdProviderAuthResultHandler:_a},emits:["success"],setup(n,{emit:d}){const r=M();return Ra(Ba,r),{GoogleWorkspace:a0,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='GoogleWorkspace.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},Fa=pragma.ui.helpers.eventEmitter,Ia=pragma.auth.initiateLinkAndRedirectWithCode,ka={name:"LinkGoogleWorkspace",components:{},emits:["linkResult"],setup(n,{emit:d}){return Ia(Da,"Google Workspace",d,Fa),{}},template:"<div/>"},mt=pragma.utils,{getRedirectUri:Ft}=pragma.auth,ya={id:a0.PROVIDER_ID,name:"Google Workspace",iconName:"google",enumValue:12,onSignInButtonClick:({router:n})=>{Rt(Ft(a0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Rt(Ft(a0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInGoogleWorkspace",path:"/signin-googleworkspace",component:ma}],registeredLinkRoutes:[{name:"LinkGoogleWorkspace",path:"/link-googleworkspace",component:ka}],routerBeforeHook:({router:n})=>{let d=mt.getWindowLocation().href;if(d.search("#/access_token=")>=0)return d=d.replace("#/access_token=","&access_token="),mt.goToUrl(d),!0}},{utils:ba}=pragma,{signIn:Sa,link:Pa,getIdProviderConfig:wa,getAuthenticateBaseUri:Ta,getRedirectUri:jt}=pragma.auth,o0={SIGNIN_REDIRECT_PATH:"/?_pRoute=SignInMetaQuest",LINK_REDIRECT_PATH:"/?_pRoute=LinkMetaQuest",META_QUEST_AUTHORIZE_URL:"https://auth.oculus.com/sso/",BACKEND_REDIRECT:"/v1/account/oauth-redirect/metaquest",PROVIDER_ID:"METAQUEST"},Ha=async n=>await Sa({providerId:o0.PROVIDER_ID,providerToken:n}),La=async n=>await Pa({providerId:o0.PROVIDER_ID,providerToken:n}),It=n=>{const d=wa(o0.PROVIDER_ID),r=new URLSearchParams({organization_id:d.orgId,redirect_uri:n}),e=`${o0.META_QUEST_AUTHORIZE_URL}?${r}`;ba.goToUrl(e)},Ua=async n=>{const d=await Jt(n,jt(o0.SIGNIN_REDIRECT_PATH));return d?await Ha(d):{ok:!1,error:"Invalid code."}},$a=async n=>{const d=await Jt(n,jt(o0.LINK_REDIRECT_PATH));return d?await La(d):{ok:!1,error:"Invalid code."}},Jt=async(n,d)=>{const r=new URLSearchParams({code:n,redirect_uri:d}),e={method:"get"},A=`${Ta(o0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await l0(A,e)).data.access_token}catch(v){return console.error(v),""}},{Icon:Na,IdProviderAuthResultHandler:Oa}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:za}=pragma.auth,Wa={name:"SignInMetaQuest",components:{Icon:Na,IdProviderAuthResultHandler:Oa},emits:["success"],setup(n,{emit:d}){const r=M();return za(Ua,r),{MetaQuest:o0,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='MetaQuest.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},Ga=pragma.ui.helpers.eventEmitter,qa=pragma.auth.initiateLinkAndRedirectWithCode,Va={name:"LinkMetaQuest",components:{},emits:["linkResult"],setup(n,{emit:d}){return qa($a,"MetaQuest",d,Ga),{}},template:"<div/>"},{getRedirectUri:kt}=pragma.auth,Ka={id:o0.PROVIDER_ID,name:"Meta Quest",iconName:"meta",enumValue:13,onSignInButtonClick:({router:n})=>{It(kt(o0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{It(kt(o0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInMetaQuest",path:"/signin-quest",component:Wa}],registeredLinkRoutes:[{name:"LinkMetaQuest",path:"/link-quest",component:Va}],routerBeforeHook:({router:n})=>{let d=pragma.utils.getWindowLocation().href;if(d.match(/_pRoute=(SignIn|Link)MetaQuest/)&&d.match(/#.{3,}/))return d=d.replace(/#\/?(.+)$/,"&code=$1"),pragma.utils.goToUrl(d),!0}},{utils:Ma}=pragma,{signIn:Qa,link:Xa,getIdProviderConfig:Za,getAuthenticateBaseUri:Ya,getRedirectUri:er}=pragma.auth,s0={SIGNIN_REDIRECT_PATH:"/redirect/SignInOkta",LINK_REDIRECT_PATH:"/redirect/LinkOkta",PROVIDER_ID:"OKTA",BACKEND_REDIRECT:"/v1/account/oauth-redirect/okta"},ja=async n=>await Qa({providerId:s0.PROVIDER_ID,providerToken:n}),Ja=async n=>await Xa({providerId:s0.PROVIDER_ID,providerToken:n}),yt=n=>{const d=Za(s0.PROVIDER_ID),r=new URLSearchParams({client_id:d.clientId,redirect_uri:n,scope:["openid","profile","email"].join(" "),response_type:"code",state:"login"}),e=`${d.authorizationUri}?${r}`;Ma.goToUrl(e)},eo=async n=>{const d=await tr(n,er(s0.SIGNIN_REDIRECT_PATH));return d?await ja(d):{ok:!1,error:"Invalid code."}},to=async n=>{const d=await tr(n,er(s0.LINK_REDIRECT_PATH));return d?await Ja(d):{ok:!1,error:"Invalid code."}},tr=async(n,d)=>{const r=new URLSearchParams({code:n,redirect_uri:d}),e={method:"get"},A=`${Ya(s0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await l0(A,e)).data.access_token}catch(v){console.warn(v);return}},{Icon:ro,IdProviderAuthResultHandler:no}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:ao}=pragma.auth,oo={name:"SignInOkta",components:{Icon:ro,IdProviderAuthResultHandler:no},emits:["success"],setup(n,{emit:d}){const r=M();return ao(eo,r),{Okta:s0,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Okta.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},io=pragma.ui.helpers.eventEmitter,xo=pragma.auth.initiateLinkAndRedirectWithCode,so={name:"LinkOkta",components:{},emits:["linkResult"],setup({emit:n}){return xo(to,"Okta",n,io),{}},template:"<div/>"},bt=pragma.utils,{getRedirectUri:St}=pragma.auth,co={id:s0.PROVIDER_ID,name:"Okta",iconName:"okta",enumValue:10,onSignInButtonClick:({router:n})=>{yt(St(s0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{yt(St(s0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInOkta",path:"/signin-okta",component:oo}],registeredLinkRoutes:[{name:"LinkOkta",path:"/link-okta",component:so}],routerBeforeHook:({router:n})=>{let d=bt.getWindowLocation().href;if(d.search("#/access_token=")>=0)return d=d.replace("#/access_token=","&access_token="),bt.goToUrl(d),!0}},{signIn:uo,link:fo}=pragma.auth,{utils:Pt}=pragma,c0={STEAM_OPENID_LOGIN_URL:"https://steamcommunity.com/openid/login",SIGNIN_REDIRECT_PATH:"/redirect/SignInSteam",LINK_REDIRECT_PATH:"/redirect/LinkSteam",PROVIDER_ID:"STEAM"};function wt(n){const d=new URLSearchParams({"openid.mode":"checkid_setup","openid.ns":"http://specs.openid.net/auth/2.0","openid.identity":"http://specs.openid.net/auth/2.0/identifier_select","openid.claimed_id":"http://specs.openid.net/auth/2.0/identifier_select","openid.return_to":n,"openid.realm":Pt.getWindowLocation().origin});Pt.goToUrl(`${c0.STEAM_OPENID_LOGIN_URL}?${d}`)}const lo=async n=>await uo({providerId:c0.PROVIDER_ID,providerToken:n}),vo=async n=>await fo({providerId:c0.PROVIDER_ID,providerToken:n});async function ho(n,d,r,e,A,v,F,h,p,t){const o=JSON.stringify({claimedId:e,ns:n,mode:d,opEndpoint:r,identity:A,returnTo:v,responseNonce:F,assocHandle:h,signed:p,sig:t});return await lo(o)}async function po(n,d,r,e,A,v,F,h,p,t){const o=JSON.stringify({claimedId:e,ns:n,mode:d,opEndpoint:r,identity:A,returnTo:v,responseNonce:F,assocHandle:h,signed:p,sig:t});return await vo(o)}const{Icon:Ao,IdProviderAuthResultHandler:Eo}=pragma.ui.components,{utils:Co}=pragma,{handleAuthResponse:Bo}=pragma.auth,i0=(n,d,r)=>n.query[r]||d.get(r),Do={name:"SignInSteam",components:{Icon:Ao,IdProviderAuthResultHandler:Eo},emits:["success"],setup(n,{emit:d}){const r=Ut(),e=Co.getRawUrlQuery();let A=i0(r,e,"openid.ns"),v=i0(r,e,"openid.mode"),F=i0(r,e,"openid.op_endpoint"),h=i0(r,e,"openid.claimed_id"),p=i0(r,e,"openid.identity"),t=i0(r,e,"openid.return_to"),o=i0(r,e,"openid.response_nonce"),C=i0(r,e,"openid.assoc_handle"),a=i0(r,e,"openid.signed"),c=i0(r,e,"openid.sig");const s=M();return ho(A,v,F,h,p,t,o,C,a,c).then(l=>{s.value=Bo(l)}),{Steam:c0,authResultRef:s,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Steam.PROVIDER_ID'
        :authResult='authResultRef'
        :canRetrySignIn='false'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},{utils:go}=pragma,_o=pragma.ui.helpers.eventEmitter,Ro=pragma.auth.handleLinkResponse,mo=pragma.auth.LINK_ERROR,x0=(n,d)=>n.query[d]||go.getRawUrlParam(d),Fo={name:"LinkSteam",components:{},emits:["linkResult"],setup(n,{emit:d}){const r=$t(),e=Ut();let A=x0(e,"openid.ns"),v=x0(e,"openid.mode"),F=x0(e,"openid.op_endpoint"),h=x0(e,"openid.claimed_id"),p=x0(e,"openid.identity"),t=x0(e,"openid.return_to"),o=x0(e,"openid.response_nonce"),C=x0(e,"openid.assoc_handle"),a=x0(e,"openid.signed"),c=x0(e,"openid.sig");return po(A,v,F,h,p,t,o,C,a,c).then(s=>{Ro(s,"Steam",d,_o),r.push({name:"LinkedAccounts"})}).catch(s=>{throw d("linkResult",{ok:!1,error:mo},idProviderName),r.push({name:"LinkedAccounts"}),s}),{}},template:"<div/>"},{getRedirectUri:Tt}=pragma.auth,Io={id:c0.PROVIDER_ID,name:"Steam",iconName:"steam",enumValue:4,onSignInButtonClick:({router:n})=>{wt(Tt(c0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{wt(Tt(c0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInSteam",path:c0.SIGNIN_REDIRECT_PATH,component:Do}],registeredLinkRoutes:[{name:"LinkSteam",path:c0.LINK_REDIRECT_PATH,component:Fo}],routerBeforeHook:({router:n})=>!1},{utils:ko}=pragma,{signIn:yo,link:bo,getIdProviderConfig:So,getRedirectUri:rr,getAuthenticateBaseUri:Po}=pragma.auth,J={PROVIDER_ID:"TWITCH",SIGNIN_REDIRECT_PATH:"/redirect/SignInTwitch",LINK_REDIRECT_PATH:"/redirect/LinkTwitch",TWITCH_AUTHORIZE_URL:"https://id.twitch.tv/oauth2/authorize",BACKEND_REDIRECT:"/v1/account/twitch-redirect"},wo=async n=>await yo({providerId:J.PROVIDER_ID,providerToken:n}),To=async n=>await bo({providerId:J.PROVIDER_ID,providerToken:n}),Ht=n=>{const d=So(J.PROVIDER_ID),r=new URLSearchParams({client_id:d.clientId,redirect_uri:n,scope:"",response_type:"code"}),e=`${J.TWITCH_AUTHORIZE_URL}?${r}`;ko.goToUrl(e)},Ho=async n=>{const d=await nr(n,rr(J.SIGNIN_REDIRECT_PATH));return d?await wo(d):{ok:!1,error:"Invalid code."}},Lo=async n=>{const d=await nr(n,rr(J.LINK_REDIRECT_PATH));return d?await To(d):{ok:!1,error:"Invalid code."}},nr=async(n,d)=>{const r=new URLSearchParams({code:n,redirect_uri:d}),e={method:"get"},A=`${Po(J.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await l0(A,e)).data.access_token}catch(v){console.warn(v);return}},{Icon:Uo,IdProviderAuthResultHandler:$o}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:No}=pragma.auth,Oo={name:"SignInTwitch",components:{Icon:Uo,IdProviderAuthResultHandler:$o},emits:["success"],setup(n,{emit:d}){const r=M();return No(Ho,r),{Twitch:J,authResultRef:r,emitSuccess:()=>d("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Twitch.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},zo=pragma.ui.helpers.eventEmitter,Wo=pragma.auth.initiateLinkAndRedirectWithCode,Go={name:"LinkTwitch",components:{},emits:["linkResult"],setup(n,{emit:d}){return Wo(Lo,"Twitch",d,zo),{}},template:"<div/>"},{getRedirectUri:Lt}=pragma.auth,qo={id:J.PROVIDER_ID,name:"Twitch",iconName:"twitch",enumValue:8,onSignInButtonClick:({router:n})=>{Ht(Lt(J.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Ht(Lt(J.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInTwitch",path:J.SIGNIN_REDIRECT_PATH,component:Oo}],registeredLinkRoutes:[{name:"LinkTwitch",path:J.LINK_REDIRECT_PATH,component:Go}],routerBeforeHook:({router:n})=>!1},{authenticatePragmaUnsafe:Vo,link:Ko}=pragma.auth,Ce={PROVIDER_ID:"UNSAFE"},ar=async n=>Vo(n),Mo=async n=>await Ko({providerId:Ce.PROVIDER_ID,providerToken:JSON.stringify({accountId:n,displayName:n})}),{handleAuthResponse:Qo}=pragma.auth,{Icon:Xo,IdProviderAuthResultHandler:Zo}=pragma.ui.components,Yo={name:"SignInUnsafe",components:{Icon:Xo,IdProviderAuthResultHandler:Zo},emits:["cancel","success"],setup(n,{emit:d}){const r=pe({username:""}),e=M(""),A=M(!1),v=M();return{onFinish:async h=>{const p=h.username.trim();if(e.value="",!p)return e.value="Username is required.";A.value=!0;const t=await ar(p);A.value=!1,v.value=Qo(t)},formState:r,errorMessage:e,disabled:A,Unsafe:Ce,authResultRef:v,emitSuccess:()=>d("success")}},template:`
    <IdProviderAuthResultHandler
      v-if='authResultRef'
      :idProviderType='Unsafe.PROVIDER_ID'
      :authResult='authResultRef'
      @success='emitSuccess'
    />
    <div v-else>
    <h5 class='header'>
      Sign in to your Pragma Portal account using Pragma Unsafe provider
    </h5>
    <a-form layout='vertical' @finish='onFinish' :model='formState' class="form-sign-in-unsafe">
      <a-form-item name='username' label='Username'
                   :help='errorMessage'
                   :validateStatus="errorMessage && 'error' || ''"
                   class='required'
      >
        <a-input autoFocus size='large' v-model:value='formState.username'></a-input>
      </a-form-item>
      <a-form-item>
        <a-button type='primary' html-type='submit' size='large' :block='true' :disabled='disabled'>
          Sign In
        </a-button>
      </a-form-item>
    </a-form>

    <router-link :to="{name: 'SignIn'}">
      <Icon name='arrowLeft' align='-2' /> &nbsp; Back to all sign in options
    </router-link>
    </div>
  `},{handleAuthResponse:jo}=pragma.auth,{Icon:Jo,IdProviderAuthResultHandler:ei}=pragma.ui.components,ti={name:"SignUpUnsafe",components:{Icon:Jo,IdProviderAuthResultHandler:ei},emits:["cancel","success"],setup(n,{emit:d}){const r=pe({username:""}),e=M(""),A=M(!1),v=M();return{onFinish:async h=>{const p=h.username.trim();if(e.value="",!p)return e.value="Username is required.";A.value=!0;const t=await ar(p);A.value=!1,v.value=jo(t)},formState:r,errorMessage:e,disabled:A,Unsafe:Ce,authResultRef:v,emitSuccess:()=>d("success")}},template:`
    <IdProviderAuthResultHandler
      v-if='authResultRef'
      :idProviderType='Unsafe.PROVIDER_ID'
      :authResult='authResultRef'
      @success='emitSuccess'
    />
    <div v-else>
    <h5 class='header'>
      Create your Pragma Portal account using Pragma Unsafe provider
    </h5>
    <a-form layout='vertical' @finish='onFinish' :model='formState'>
      <a-form-item name='username' label='Username'
                   :help='errorMessage'
                   :validateStatus="errorMessage && 'error' || ''"
                   class='required'
      >
        <a-input autoFocus size='large' v-model:value='formState.username'></a-input>
      </a-form-item>
      <a-form-item>
        <a-button type='primary' html-type='submit' size='large' :block='true' :disabled='disabled'>
          Sign Up
        </a-button>
      </a-form-item>
    </a-form>

    <router-link  :to="{name: 'CreateAccount'}">
      <Icon name='arrowLeft' align='-2' /> &nbsp; Back to all sign up options
    </router-link>
    </div>
  `},{store:ri}=pragma,{Icon:ni}=pragma.ui.components,ai=pragma.ui.helpers.eventEmitter,oi=pragma.utils,ii={name:"LinkUnsafe",components:{Icon:ni,BackendInfo:Cr},emits:["linkResult"],setup(n,{emit:d}){const r=pe({username:""}),e=M(""),A=M(!1),v=$t();return oi.capitalizeFirstLetter(ri.get("appInfo.backendMode").toLowerCase()),{onFinish:h=>{const p=h.username.trim();if(e.value="",!p)return e.value="Username is required.";A.value=!0,Mo(p).then(t=>{if(A.value=!1,!t.ok)return e.value="Something went wrong. Please try again.";v.push({name:"LinkedAccounts"}),d("linkResult",t,"Unsafe"),ai.emit("reloadData")})},formState:r,errorMessage:e,disabled:A}},template:`
    <div class='LinkUnsafe auth-container'>
      <Icon name='logoPortal' size='30' class='logo' />
      <a-card :bordered="false" class='card card-auth'>
        <div>
          <a-space direction='vertical' size='middle' align='center' class='alert'>
            <BackendInfo />
          </a-space>
          
          <div>
          <h5 class='header'>
            Link your Pragma Portal account using Pragma Unsafe provider
          </h5>
          <a-form layout='vertical' @finish='onFinish' :model='formState'>
            <a-form-item name='username' label='Username'
              :help='errorMessage'
              :validateStatus="errorMessage && 'error' || ''"
              class='required'
            >
              <a-input autoFocus size='large' v-model:value='formState.username'></a-input>
            </a-form-item>
            <a-form-item>
              <a-button type='primary' html-type='submit' size='large' :block='true' :disabled='disabled'>
                Link
              </a-button>
            </a-form-item>
          </a-form>
          </div>
          <router-link :to="{name: 'LinkedAccounts'}"><Icon name='arrowLeft' align='-2' /> &nbsp; Back to all link options</router-link>
        </div>
      </a-card>
    </div>
  `},{isCreateAccountUrl:xi}=pragma.auth,si={id:"UNSAFE",name:"Pragma Unsafe",iconName:"unlock",enumValue:1,onSignInButtonClick:({router:n})=>{xi()?n.push({name:"SignUpUnsafe"}):n.push({name:"SignInUnsafe"})},onLinkButtonClick:({router:n})=>{n.push({name:"LinkUnsafe"})},registeredRoutes:[{name:"SignInUnsafe",path:"/signin-unsafe",component:Yo},{name:"SignUpUnsafe",path:"/signup-unsafe",component:ti}],registeredLinkRoutes:[{name:"LinkUnsafe",path:"/link-unsafe",component:ii}],routerBeforeHook:({router:n})=>!1,position:0};Lr._source="pragma";Sn._source="pragma";Xn._source="pragma";fa._source="pragma";ya._source="pragma";Ka._source="pragma";co._source="pragma";Io._source="pragma";qo._source="pragma";si._source="pragma";export{Lr as auth0,Sn as discord,Xn as epic,fa as google,ya as googleworkspace,Ka as metaquest,co as okta,Io as steam,qo as twitch,si as unsafe};
