import{h as N,i as b,k as A,r as w}from"./index-KNWq7rbf.js";const h=pragma.ui.helpers.swappable,{DisplayNameViewOnly:L,EmailViewOnly:C}=pragma.ext.player.ui.components,T={name:"Settings",props:{playerData:Object},setup(){const o=h("Player.Account.Settings.DisplayName",{default:L,emits:null,data:!0,context:null}),n=h("Player.Account.Settings.Email",{default:C,emits:null,data:!0,context:null});return{displayNameRowRenderer:o,emailRowRenderer:n}},template:`
    <div class="account-settings">
      <h3>Account Information</h3>

      <div class="account-info">
        <component
          :is="displayNameRowRenderer"
          :data="$props.playerData"
        />
        <component
          :is="emailRowRenderer"
          :data="$props.playerData"
        />
      </div>

      <!-- <div class="download-account-info">
        <h3>Download Account Information</h3>
        <p>Create and download a copy of the information you’ve shared with us.</p>
        <p>This process can take up to 30 days and can be canceled at any time. Once your data is ready, you can download it here.</p>
        <a-button type="primary">Request Download</a-button>
      </div> -->

      <!-- <div class="delete-account-info">
        <h3>Delete Account</h3>
        <p>Permanently delete your account and remove your information from our servers.</p>
        <p>This process can take up to 28 days and can be canceled at any time. However, once complete, deletion is irreversible.</p>
        <a-button type="danger">Request Delete</a-button>
      </div> -->
    </div>
  `},S=o=>o.addSocialRpcData("playerData","AccountRpc.GetAccountV1Request").addComponent(T,{playerData:({rpc:n})=>n.playerData}).setTitle("Account Settings"),R=pragma.ui.components.Icon,{appBlueprint:k}=pragma.app.blueprint,{getIdProviderIconName:O,getIdProviderDisplayName:U}=pragma.ui.helpers,$=pragma.store,M={name:"AccountLinkModal",props:{idProviderType:String},components:{Icon:R},emits:["cancel","submit"],setup(o){const n=N(),a=k.authProviders.find(d=>d.id==o.idProviderType),r=async()=>{a.onLinkButtonClick({router:n})},s=U(o.idProviderType,k.authProviders),u=O(o.idProviderType,k.authProviders),c=$.get("config.authenticationIdProviders").filter(d=>d.providerId==o.idProviderType);let i;c[0]&&(i=c[0].config);const m=i.accountLinkingOneAssociationOnly=="true";return{onSubmit:r,idProviderDisplayName:s,idProviderIcon:u,idProviderOneAssociationOnly:m}},template:`
    <a-modal
      class="accountLinkModal"
      :maskClosable="false"
      :open="true"
      :destroyOnClose="true"
      ok-text="Continue"
      :okButtonProps="{class: 'linkAccountButton'}"
      @cancel="() => $emit('cancel')"
      @ok="onSubmit"
    >
      <template #title>
        Linking to your {{idProviderDisplayName}} Account
      </template>
      <div v-if="idProviderOneAssociationOnly">
        <p>Your account can only be associated with one {{idProviderDisplayName}} account in its lifetime. <b>Once you link this {{idProviderDisplayName}} account, you will not be able to link a different {{idProviderDisplayName}} account.</b></p>
        <p>Are you sure you want to link {{idProviderDisplayName}}? You will be redirected to {{idProviderDisplayName}}.</p>
      </div>
      <div v-else>
        <p>You will be redirected to {{idProviderDisplayName}} to finish the account linking process.</p>
      </div>
    </a-modal>
  `},{helpers:X,rpc:x}=pragma.api,q=pragma.ui.components.Icon,{appBlueprint:y}=pragma.app.blueprint,{getIdProviderIconName:B,getIdProviderDisplayName:D,toast:v}=pragma.ui.helpers,E=pragma.store,V={name:"AccountUnlinkModal",props:{idProviderType:String},components:{Icon:q},emits:["cancel","submit"],setup(o,{emit:n}){const a=N(),r=y.authProviders.find(e=>e.id==o.idProviderType),s=D(o.idProviderType,y.authProviders),u=B(o.idProviderType,y.authProviders),c=E.get("config.authenticationIdProviders").filter(e=>e.providerId==o.idProviderType);let i;c[0]&&(i=c[0].config);const m=b.duration({days:i.accountLinkingCooldownInDays}).humanize(),d=i.accountLinkingCooldownInDays>0,f=i.accountLinkingOneAssociationOnly=="true";return{onSubmit:async()=>{const e=await x.requestSocialRPC("AccountRpc.UnlinkIdentityProviderAccountV2Request",{idProvider:r.id});if(!e.ok)return n("cancel"),v.error("Something went wrong. Please try again!");const t=e.body.response.type,l=e.body.response.payload,p=D(l.idProvider,y.authProviders);if(t!=="AccountRpc.UnlinkIdentityProviderAccountV2Response"){switch(t){case"AccountRpc.CannotUnlinkOnlyLoginIdProviderApplicationError":v.error(`Cannot unlink ${p} since it is the last login provider.`);break;default:v.error(`Something went wrong. ${t}`);break}n("cancel")}else v.success(`${s} was unlinked.`),a.go()},idProviderDisplayName:s,idProviderIcon:u,idProviderCooldownDisplay:m,idProviderCooldownEnabled:d,idProviderOneAssociationOnly:f}},template:`
    <a-modal
      class="accountUnlinkModal"
      :maskClosable="false"
      :open="true"
      :destroyOnClose="true"
      ok-text="Unlink"
      :okButtonProps="{class: 'unlinkAccountButton'}"
      @cancel="() => $emit('cancel')"
      @ok="onSubmit"
    >
      <template #title>
        Unlinking your {{idProviderDisplayName}} Account
      </template>
      <div v-if="idProviderCooldownEnabled && !idProviderOneAssociationOnly">
        <p>Unlinking {{idProviderDisplayName}} will remove any entitlements or in-game purchases associated with this provider. You won't be able to link a new {{idProviderDisplayName}} account for {{idProviderCooldownDisplay}}.</p>
        <p>Are you sure you want to unlink {{idProviderDisplayName}} from this account?</p>
      </div>
      <div v-else>
        <p>Unlinking {{idProviderDisplayName}} will remove any entitlements or in-game purchases associated with this provider.</p>
        <p>Are you sure you want to unlink {{idProviderDisplayName}} from this account?</p>
      </div>
    </a-modal>
  `},Y=pragma.utils,j=pragma.ui.components.CopyableCell,z=pragma.ui.components.PageSection,G=pragma.ui.components.Icon,{appBlueprint:P}=pragma.app.blueprint,{getIdProviderIconName:W,getIdProviderDisplayName:_}=pragma.ui.helpers,I=pragma.store,F={name:"LinkedAccountsTable",components:{CopyableCell:j,PageSection:z,Icon:G,AccountLinkModal:M,AccountUnlinkModal:V},props:{playerData:Object},setup(o){var g;const n=A(!1),a=A(!1),r=w({value:{},key:0}),s=(e,t)=>{if(e){if(e.providerDisplayName.discriminator){const l=(t==null?void 0:t.separator)||"";return`${e.providerDisplayName.displayName}${l}${e.providerDisplayName.discriminator}`}return e.providerDisplayName.displayName}},c=I.get("config.authenticationIdProviders").filter(e=>e.config.accountLinkingEnabled=="true").map(e=>{const t=o.playerData.idProviderAccounts.find(p=>p.idProviderType==e.providerId);let l=!!t;return{idProviderType:e.providerId,idProviderDisplayName:_(e.providerId,P.authProviders),uniqueDisplayName:s(t,e),isAccountLinked:l,authProvider:P.authProviders.find(p=>p.id==e.providerId),icon:W(e.providerId,P.authProviders),provider:e}});return{linkedAccountsColumns:H,allAvailableAccounts:c,utils:Y,onLinkAccount:e=>{r.value=e,n.value=!0},onUnlinkAccount:e=>{r.value=e,a.value=!0},showLinkModal:n,showUnlinkModal:a,toggleLinkModal:e=>n.value=e,toggleUnlinkModal:e=>a.value=e,currentIdProvider:r,customerSupportLink:((g=I.get("config.content"))==null?void 0:g.customerSupportLink)??""}},template:`
    <PageSection title="Linked Accounts" :count="allAvailableAccounts.length" class="linkedAccountsTable">
      <a-table
        :locale="{emptyText: 'No Data'}"
        :columns="linkedAccountsColumns"
        :data-source="allAvailableAccounts"
        :pagination="false"
        size="middle"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'accountId'">
            <CopyableCell :text="text" />
          </template>
          <template v-if="column.dataIndex === 'idProviderType'">
            <Icon :name="record.icon" align="-2" />
            <span>&ensp;{{record.idProviderDisplayName}}</span>
          </template>

          <template v-if="column.dataIndex === 'uniqueDisplayName'">
            <span v-if="!record.isAccountLinked" class="accountNotLinked">Not linked</span>
            <span v-else>{{record.uniqueDisplayName}}</span>
          </template>

          <template v-if="column.dataIndex === 'action'">
            <a v-if="!record.isAccountLinked" @click="onLinkAccount(record.idProviderType)">Link</a>
            <span v-else>
              <a v-if="record.provider.config.accountUnlinkingEnabled == 'true'" @click="onUnlinkAccount(record.idProviderType)">Unlink</a>
              <span v-else-if="customerSupportLink != ''">Contact <a :href="customerSupportLink" target="_blank">player support</a> to unlink.</span>
              <span v-else>Contact player support to unlink.</span>
            </span>
          </template>
        </template>
      </a-table>
      <AccountLinkModal v-if="showLinkModal" :idProviderType="currentIdProvider.value" @cancel="toggleLinkModal" />
      <AccountUnlinkModal v-if="showUnlinkModal" :idProviderType="currentIdProvider.value" @cancel="toggleUnlinkModal" />
    </PageSection>
  `},H=[{title:"Provider",dataIndex:"idProviderType",key:"providerName"},{title:"Display Name",dataIndex:"uniqueDisplayName",key:"displayName"},{title:"",dataIndex:"action",key:"action"}],J=o=>o.addSocialRpcData("playerData","AccountRpc.GetAccountV1Request").addComponent(F,{playerData:({rpc:n})=>n.playerData}).setTitle("Linked Accounts");pragma;pragma.ui.helpers.featureToggles;const K=o=>o.addPage("Settings",S).addPage("LinkedAccounts",J).setName("My Account");K._source="pragma";export{K as Account};
