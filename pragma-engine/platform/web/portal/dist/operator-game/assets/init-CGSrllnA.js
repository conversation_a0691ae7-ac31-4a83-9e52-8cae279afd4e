const n="/config/config.json",i="Game",c="OPERATOR",s={mode:"dark",token:{colorBgBase:"#0a0a0a",colorTextBase:"#fafafa",colorTextLightSolid:"#e5e5e5",colorPrimaryBg:"#0a0a0a",colorPrimaryBgHover:"#262626",colorPrimaryBorder:"#404040",colorPrimaryBorderHover:"#525252",colorPrimaryTextHover:"#fafafa",colorPrimaryText:"#d4d4d4",colorPrimaryTextActive:"#fafafa",colorWhite:"#fafafa",colorText:"#d4d4d4",colorBorder:"#404040",colorBorderHover:"#737373",colorBorderSecondary:"#2d2d2d",colorBgContainer:"#171717",colorBgElevated:"#262626",colorBgLayout:"#171717",colorBgSpotlight:"#262626",colorLink:"#fafafa",colorLinkActive:"#d4d4d4",colorLinkHover:"#fafafa",fontFamily:"'Inter', sans-serif",fontSize:16,borderRadius:4,borderRadiusSM:2,borderRadiusLG:8,boxShadowSecondary:"0px 0px 1.1px rgba(0, 0, 0, 0.141),  0px 0.5px 3.8px rgba(0, 0, 0, 0.209),  0px 4px 17px rgba(0, 0, 0, 0.35)",backgroundCardEmphasis:"linear-gradient(var(--gradient-angle), #1EFFF1 0%, #1548FF 100%)",colorPrimary:"#0B30B7",colorSuccess:"#29C370",colorWarning:"#D89614",colorError:"#D32029",colorInfo:"#0B30B7"}},d={pageTitle:"Pragma Portal"},l={},{requestREST:t}=pragma.api.rest,{store:r}=pragma,{generateBlueprint:p}=pragma.app.blueprint,{turnFeatureOff:f,turnFeatureOn:g,isFeatureToggleSet:u}=pragma.ui.helpers.featureToggles,{dayjsInit:y}=pragma.utils,P=async()=>{m()&&h()&&k()&&await T()&&await B()&&await v()&&await x()&&(y(),await p())},m=()=>(r.set("app.backendType",i),r.set("app.sessionType",c),r.set("app.options",d),!0),h=()=>(r.set("style.theme",s),!0),k=()=>{{const o=l;for(const e in o)u(e)||(o[e]?g(e):f(e))}return!0},T=async()=>{const o=n;{const e=o,a=await t(e);return a.ok||(document.body.innerHTML="Failed to load "+e),r.set("config",a.data),a.ok}},B=async()=>{const o=await t(r.get("config.infoEndpoint"));return o.ok||(document.body.innerHTML="Failed to load "+r.get("config.infoEndpoint")),r.set("appInfo",o.data),o.ok},v=async()=>{const o=r.get("config.infoEndpoint").replace("info","healthcheck"),e=await t(o);return e.ok||(document.body.innerHTML="Failed to load "+o),r.set("appHealthCheck",e.data),e.ok},x=async()=>{const o=r.get("appInfo").authenticateBackend,e=`${pragma.utils.buildEndpoint(o)}/v1/idproviders`,a=await t(e);return a.ok||(document.body.innerHTML="Failed to load "+idProvidersCheckEndpoint),r.set("config.authenticationIdProviders",a.data.idProviders||[]),a.ok};export{P as initialize,T as initializeAppConfig,v as initializeAppHealthCheck,B as initializeAppInfo,x as initializeIdProviders};
