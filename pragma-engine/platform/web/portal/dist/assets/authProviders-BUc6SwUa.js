import{v as f0,b as K,x as mr,y as U,a as zt,u as Wt,r as Ee}from"./index-C7Yfo9M0.js";import{B as Fr}from"./BackendInfo.vue-DNcnOobk.js";pragma;const{utils:Ir}=pragma,{signIn:kr,link:yr,getIdProviderConfig:br,getAuthenticateBaseUri:Sr,getRedirectUri:Gt}=pragma.auth,d0={SIGNIN_REDIRECT_PATH:"/redirect/SignInAuth0",LINK_REDIRECT_PATH:"/redirect/LinkAuth0",PROVIDER_ID:"AUTH0",BACKEND_REDIRECT:"/v1/account/oauth-redirect/auth0"},Pr=async n=>await kr({providerId:d0.PROVIDER_ID,providerToken:n}),Tr=async n=>await yr({providerId:d0.PROVIDER_ID,providerToken:n}),He=n=>{const s=br(d0.PROVIDER_ID),r=new URLSearchParams({client_id:s.clientId,redirect_uri:n,scope:["openid","profile","email"].join(" "),response_type:"code",access_type:"offline",prompt:"consent"}),e=`${s.authorizationUri}?${r}`;Ir.goToUrl(e)},Hr=async n=>{const s=await Vt(n,Gt(d0.SIGNIN_REDIRECT_PATH));return s?await Pr(s):{ok:!1,error:"Invalid code."}},wr=async n=>{const s=await Vt(n,Gt(d0.SIGNIN_REDIRECT_PATH));return s?await Tr(s):{ok:!1,error:"Invalid code."}},Vt=async(n,s)=>{const r=new URLSearchParams({code:n,redirect_uri:s}),e={method:"get"},h=`${Sr(d0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await f0(h,e)).data.access_token}catch(v){console.error(v);return}},{Icon:Lr,IdProviderAuthResultHandler:Ur}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:$r}=pragma.auth,Nr={name:"SignInAuth0",components:{Icon:Lr,IdProviderAuthResultHandler:Ur},emits:["success"],setup(n,{emit:s}){const r=K();return $r(Hr,r),{Auth0:d0,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Auth0.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},Or=pragma.ui.helpers.eventEmitter,zr=pragma.auth.initiateLinkAndRedirectWithCode,Wr={name:"LinkAuth0",components:{},emits:["linkResult"],setup(n,{emit:s}){return zr(wr,"Auth0",s,Or),{}},template:"<div/>"},we=pragma.utils,{getRedirectUri:Le}=pragma.auth,Gr={id:"AUTH0",name:"Auth0",iconName:"auth0",enumValue:11,onSignInButtonClick:({router:n})=>{He(Le(d0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{He(Le(d0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInAuth0",path:"/signin-auth0",component:Nr}],registeredLinkRoutes:[{name:"LinkAuth0",path:"/link-auth0",component:Wr}],routerBeforeHook:({router:n})=>{let s=we.getWindowLocation().href;if(s.search("#/access_token=")>=0)return s=s.replace("#/access_token=","&access_token="),we.goToUrl(s),!0}};var qt={exports:{}};function Vr(n){throw new Error('Could not dynamically require "'+n+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var U0={exports:{}};const qr={},Kr=Object.freeze(Object.defineProperty({__proto__:null,default:qr},Symbol.toStringTag,{value:"Module"})),Xr=mr(Kr);var Ue;function N(){return Ue||(Ue=1,function(n,s){(function(r,e){n.exports=e()})(U,function(){var r=r||function(e,h){var v;if(typeof window<"u"&&window.crypto&&(v=window.crypto),typeof self<"u"&&self.crypto&&(v=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(v=globalThis.crypto),!v&&typeof window<"u"&&window.msCrypto&&(v=window.msCrypto),!v&&typeof U<"u"&&U.crypto&&(v=U.crypto),!v&&typeof Vr=="function")try{v=Xr}catch{}var m=function(){if(v){if(typeof v.getRandomValues=="function")try{return v.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof v.randomBytes=="function")try{return v.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},p=Object.create||function(){function i(){}return function(x){var d;return i.prototype=x,d=new i,i.prototype=null,d}}(),A={},t=A.lib={},o=t.Base=function(){return{extend:function(i){var x=p(this);return i&&x.mixIn(i),(!x.hasOwnProperty("init")||this.init===x.init)&&(x.init=function(){x.$super.init.apply(this,arguments)}),x.init.prototype=x,x.$super=this,x},create:function(){var i=this.extend();return i.init.apply(i,arguments),i},init:function(){},mixIn:function(i){for(var x in i)i.hasOwnProperty(x)&&(this[x]=i[x]);i.hasOwnProperty("toString")&&(this.toString=i.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),C=t.WordArray=o.extend({init:function(i,x){i=this.words=i||[],x!=h?this.sigBytes=x:this.sigBytes=i.length*4},toString:function(i){return(i||u).stringify(this)},concat:function(i){var x=this.words,d=i.words,g=this.sigBytes,D=i.sigBytes;if(this.clamp(),g%4)for(var _=0;_<D;_++){var F=d[_>>>2]>>>24-_%4*8&255;x[g+_>>>2]|=F<<24-(g+_)%4*8}else for(var T=0;T<D;T+=4)x[g+T>>>2]=d[T>>>2];return this.sigBytes+=D,this},clamp:function(){var i=this.words,x=this.sigBytes;i[x>>>2]&=4294967295<<32-x%4*8,i.length=e.ceil(x/4)},clone:function(){var i=o.clone.call(this);return i.words=this.words.slice(0),i},random:function(i){for(var x=[],d=0;d<i;d+=4)x.push(m());return new C.init(x,i)}}),a=A.enc={},u=a.Hex={stringify:function(i){for(var x=i.words,d=i.sigBytes,g=[],D=0;D<d;D++){var _=x[D>>>2]>>>24-D%4*8&255;g.push((_>>>4).toString(16)),g.push((_&15).toString(16))}return g.join("")},parse:function(i){for(var x=i.length,d=[],g=0;g<x;g+=2)d[g>>>3]|=parseInt(i.substr(g,2),16)<<24-g%8*4;return new C.init(d,x/2)}},c=a.Latin1={stringify:function(i){for(var x=i.words,d=i.sigBytes,g=[],D=0;D<d;D++){var _=x[D>>>2]>>>24-D%4*8&255;g.push(String.fromCharCode(_))}return g.join("")},parse:function(i){for(var x=i.length,d=[],g=0;g<x;g++)d[g>>>2]|=(i.charCodeAt(g)&255)<<24-g%4*8;return new C.init(d,x)}},l=a.Utf8={stringify:function(i){try{return decodeURIComponent(escape(c.stringify(i)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(i){return c.parse(unescape(encodeURIComponent(i)))}},f=t.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new C.init,this._nDataBytes=0},_append:function(i){typeof i=="string"&&(i=l.parse(i)),this._data.concat(i),this._nDataBytes+=i.sigBytes},_process:function(i){var x,d=this._data,g=d.words,D=d.sigBytes,_=this.blockSize,F=_*4,T=D/F;i?T=e.ceil(T):T=e.max((T|0)-this._minBufferSize,0);var E=T*_,R=e.min(E*4,D);if(E){for(var k=0;k<E;k+=_)this._doProcessBlock(g,k);x=g.splice(0,E),d.sigBytes-=R}return new C.init(x,R)},clone:function(){var i=o.clone.call(this);return i._data=this._data.clone(),i},_minBufferSize:0});t.Hasher=f.extend({cfg:o.extend(),init:function(i){this.cfg=this.cfg.extend(i),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(i){return this._append(i),this._process(),this},finalize:function(i){i&&this._append(i);var x=this._doFinalize();return x},blockSize:16,_createHelper:function(i){return function(x,d){return new i.init(d).finalize(x)}},_createHmacHelper:function(i){return function(x,d){return new B.HMAC.init(i,d).finalize(x)}}});var B=A.algo={};return A}(Math);return r})}(U0)),U0.exports}var $0={exports:{}},$e;function H0(){return $e||($e=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(e){var h=r,v=h.lib,m=v.Base,p=v.WordArray,A=h.x64={};A.Word=m.extend({init:function(t,o){this.high=t,this.low=o}}),A.WordArray=m.extend({init:function(t,o){t=this.words=t||[],o!=e?this.sigBytes=o:this.sigBytes=t.length*8},toX32:function(){for(var t=this.words,o=t.length,C=[],a=0;a<o;a++){var u=t[a];C.push(u.high),C.push(u.low)}return p.create(C,this.sigBytes)},clone:function(){for(var t=m.clone.call(this),o=t.words=this.words.slice(0),C=o.length,a=0;a<C;a++)o[a]=o[a].clone();return t}})}(),r})}($0)),$0.exports}var N0={exports:{}},Ne;function Zr(){return Ne||(Ne=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(){if(typeof ArrayBuffer=="function"){var e=r,h=e.lib,v=h.WordArray,m=v.init,p=v.init=function(A){if(A instanceof ArrayBuffer&&(A=new Uint8Array(A)),(A instanceof Int8Array||typeof Uint8ClampedArray<"u"&&A instanceof Uint8ClampedArray||A instanceof Int16Array||A instanceof Uint16Array||A instanceof Int32Array||A instanceof Uint32Array||A instanceof Float32Array||A instanceof Float64Array)&&(A=new Uint8Array(A.buffer,A.byteOffset,A.byteLength)),A instanceof Uint8Array){for(var t=A.byteLength,o=[],C=0;C<t;C++)o[C>>>2]|=A[C]<<24-C%4*8;m.call(this,o,t)}else m.apply(this,arguments)};p.prototype=v}}(),r.lib.WordArray})}(N0)),N0.exports}var O0={exports:{}},Oe;function Mr(){return Oe||(Oe=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,h=e.lib,v=h.WordArray,m=e.enc;m.Utf16=m.Utf16BE={stringify:function(A){for(var t=A.words,o=A.sigBytes,C=[],a=0;a<o;a+=2){var u=t[a>>>2]>>>16-a%4*8&65535;C.push(String.fromCharCode(u))}return C.join("")},parse:function(A){for(var t=A.length,o=[],C=0;C<t;C++)o[C>>>1]|=A.charCodeAt(C)<<16-C%2*16;return v.create(o,t*2)}},m.Utf16LE={stringify:function(A){for(var t=A.words,o=A.sigBytes,C=[],a=0;a<o;a+=2){var u=p(t[a>>>2]>>>16-a%4*8&65535);C.push(String.fromCharCode(u))}return C.join("")},parse:function(A){for(var t=A.length,o=[],C=0;C<t;C++)o[C>>>1]|=p(A.charCodeAt(C)<<16-C%2*16);return v.create(o,t*2)}};function p(A){return A<<8&4278255360|A>>>8&16711935}}(),r.enc.Utf16})}(O0)),O0.exports}var z0={exports:{}},ze;function p0(){return ze||(ze=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,h=e.lib,v=h.WordArray,m=e.enc;m.Base64={stringify:function(A){var t=A.words,o=A.sigBytes,C=this._map;A.clamp();for(var a=[],u=0;u<o;u+=3)for(var c=t[u>>>2]>>>24-u%4*8&255,l=t[u+1>>>2]>>>24-(u+1)%4*8&255,f=t[u+2>>>2]>>>24-(u+2)%4*8&255,B=c<<16|l<<8|f,i=0;i<4&&u+i*.75<o;i++)a.push(C.charAt(B>>>6*(3-i)&63));var x=C.charAt(64);if(x)for(;a.length%4;)a.push(x);return a.join("")},parse:function(A){var t=A.length,o=this._map,C=this._reverseMap;if(!C){C=this._reverseMap=[];for(var a=0;a<o.length;a++)C[o.charCodeAt(a)]=a}var u=o.charAt(64);if(u){var c=A.indexOf(u);c!==-1&&(t=c)}return p(A,t,C)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function p(A,t,o){for(var C=[],a=0,u=0;u<t;u++)if(u%4){var c=o[A.charCodeAt(u-1)]<<u%4*2,l=o[A.charCodeAt(u)]>>>6-u%4*2,f=c|l;C[a>>>2]|=f<<24-a%4*8,a++}return v.create(C,a)}}(),r.enc.Base64})}(z0)),z0.exports}var W0={exports:{}},We;function Yr(){return We||(We=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,h=e.lib,v=h.WordArray,m=e.enc;m.Base64url={stringify:function(A,t){t===void 0&&(t=!0);var o=A.words,C=A.sigBytes,a=t?this._safe_map:this._map;A.clamp();for(var u=[],c=0;c<C;c+=3)for(var l=o[c>>>2]>>>24-c%4*8&255,f=o[c+1>>>2]>>>24-(c+1)%4*8&255,B=o[c+2>>>2]>>>24-(c+2)%4*8&255,i=l<<16|f<<8|B,x=0;x<4&&c+x*.75<C;x++)u.push(a.charAt(i>>>6*(3-x)&63));var d=a.charAt(64);if(d)for(;u.length%4;)u.push(d);return u.join("")},parse:function(A,t){t===void 0&&(t=!0);var o=A.length,C=t?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var u=0;u<C.length;u++)a[C.charCodeAt(u)]=u}var c=C.charAt(64);if(c){var l=A.indexOf(c);l!==-1&&(o=l)}return p(A,o,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function p(A,t,o){for(var C=[],a=0,u=0;u<t;u++)if(u%4){var c=o[A.charCodeAt(u-1)]<<u%4*2,l=o[A.charCodeAt(u)]>>>6-u%4*2,f=c|l;C[a>>>2]|=f<<24-a%4*8,a++}return v.create(C,a)}}(),r.enc.Base64url})}(W0)),W0.exports}var G0={exports:{}},Ge;function A0(){return Ge||(Ge=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(e){var h=r,v=h.lib,m=v.WordArray,p=v.Hasher,A=h.algo,t=[];(function(){for(var l=0;l<64;l++)t[l]=e.abs(e.sin(l+1))*4294967296|0})();var o=A.MD5=p.extend({_doReset:function(){this._hash=new m.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(l,f){for(var B=0;B<16;B++){var i=f+B,x=l[i];l[i]=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360}var d=this._hash.words,g=l[f+0],D=l[f+1],_=l[f+2],F=l[f+3],T=l[f+4],E=l[f+5],R=l[f+6],k=l[f+7],y=l[f+8],H=l[f+9],w=l[f+10],L=l[f+11],V=l[f+12],O=l[f+13],W=l[f+14],z=l[f+15],I=d[0],S=d[1],P=d[2],b=d[3];I=C(I,S,P,b,g,7,t[0]),b=C(b,I,S,P,D,12,t[1]),P=C(P,b,I,S,_,17,t[2]),S=C(S,P,b,I,F,22,t[3]),I=C(I,S,P,b,T,7,t[4]),b=C(b,I,S,P,E,12,t[5]),P=C(P,b,I,S,R,17,t[6]),S=C(S,P,b,I,k,22,t[7]),I=C(I,S,P,b,y,7,t[8]),b=C(b,I,S,P,H,12,t[9]),P=C(P,b,I,S,w,17,t[10]),S=C(S,P,b,I,L,22,t[11]),I=C(I,S,P,b,V,7,t[12]),b=C(b,I,S,P,O,12,t[13]),P=C(P,b,I,S,W,17,t[14]),S=C(S,P,b,I,z,22,t[15]),I=a(I,S,P,b,D,5,t[16]),b=a(b,I,S,P,R,9,t[17]),P=a(P,b,I,S,L,14,t[18]),S=a(S,P,b,I,g,20,t[19]),I=a(I,S,P,b,E,5,t[20]),b=a(b,I,S,P,w,9,t[21]),P=a(P,b,I,S,z,14,t[22]),S=a(S,P,b,I,T,20,t[23]),I=a(I,S,P,b,H,5,t[24]),b=a(b,I,S,P,W,9,t[25]),P=a(P,b,I,S,F,14,t[26]),S=a(S,P,b,I,y,20,t[27]),I=a(I,S,P,b,O,5,t[28]),b=a(b,I,S,P,_,9,t[29]),P=a(P,b,I,S,k,14,t[30]),S=a(S,P,b,I,V,20,t[31]),I=u(I,S,P,b,E,4,t[32]),b=u(b,I,S,P,y,11,t[33]),P=u(P,b,I,S,L,16,t[34]),S=u(S,P,b,I,W,23,t[35]),I=u(I,S,P,b,D,4,t[36]),b=u(b,I,S,P,T,11,t[37]),P=u(P,b,I,S,k,16,t[38]),S=u(S,P,b,I,w,23,t[39]),I=u(I,S,P,b,O,4,t[40]),b=u(b,I,S,P,g,11,t[41]),P=u(P,b,I,S,F,16,t[42]),S=u(S,P,b,I,R,23,t[43]),I=u(I,S,P,b,H,4,t[44]),b=u(b,I,S,P,V,11,t[45]),P=u(P,b,I,S,z,16,t[46]),S=u(S,P,b,I,_,23,t[47]),I=c(I,S,P,b,g,6,t[48]),b=c(b,I,S,P,k,10,t[49]),P=c(P,b,I,S,W,15,t[50]),S=c(S,P,b,I,E,21,t[51]),I=c(I,S,P,b,V,6,t[52]),b=c(b,I,S,P,F,10,t[53]),P=c(P,b,I,S,w,15,t[54]),S=c(S,P,b,I,D,21,t[55]),I=c(I,S,P,b,y,6,t[56]),b=c(b,I,S,P,z,10,t[57]),P=c(P,b,I,S,R,15,t[58]),S=c(S,P,b,I,O,21,t[59]),I=c(I,S,P,b,T,6,t[60]),b=c(b,I,S,P,L,10,t[61]),P=c(P,b,I,S,_,15,t[62]),S=c(S,P,b,I,H,21,t[63]),d[0]=d[0]+I|0,d[1]=d[1]+S|0,d[2]=d[2]+P|0,d[3]=d[3]+b|0},_doFinalize:function(){var l=this._data,f=l.words,B=this._nDataBytes*8,i=l.sigBytes*8;f[i>>>5]|=128<<24-i%32;var x=e.floor(B/4294967296),d=B;f[(i+64>>>9<<4)+15]=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,f[(i+64>>>9<<4)+14]=(d<<8|d>>>24)&16711935|(d<<24|d>>>8)&4278255360,l.sigBytes=(f.length+1)*4,this._process();for(var g=this._hash,D=g.words,_=0;_<4;_++){var F=D[_];D[_]=(F<<8|F>>>24)&16711935|(F<<24|F>>>8)&4278255360}return g},clone:function(){var l=p.clone.call(this);return l._hash=this._hash.clone(),l}});function C(l,f,B,i,x,d,g){var D=l+(f&B|~f&i)+x+g;return(D<<d|D>>>32-d)+f}function a(l,f,B,i,x,d,g){var D=l+(f&i|B&~i)+x+g;return(D<<d|D>>>32-d)+f}function u(l,f,B,i,x,d,g){var D=l+(f^B^i)+x+g;return(D<<d|D>>>32-d)+f}function c(l,f,B,i,x,d,g){var D=l+(B^(f|~i))+x+g;return(D<<d|D>>>32-d)+f}h.MD5=p._createHelper(o),h.HmacMD5=p._createHmacHelper(o)}(Math),r.MD5})}(G0)),G0.exports}var V0={exports:{}},Ve;function Kt(){return Ve||(Ve=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(){var e=r,h=e.lib,v=h.WordArray,m=h.Hasher,p=e.algo,A=[],t=p.SHA1=m.extend({_doReset:function(){this._hash=new v.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(o,C){for(var a=this._hash.words,u=a[0],c=a[1],l=a[2],f=a[3],B=a[4],i=0;i<80;i++){if(i<16)A[i]=o[C+i]|0;else{var x=A[i-3]^A[i-8]^A[i-14]^A[i-16];A[i]=x<<1|x>>>31}var d=(u<<5|u>>>27)+B+A[i];i<20?d+=(c&l|~c&f)+1518500249:i<40?d+=(c^l^f)+1859775393:i<60?d+=(c&l|c&f|l&f)-1894007588:d+=(c^l^f)-899497514,B=f,f=l,l=c<<30|c>>>2,c=u,u=d}a[0]=a[0]+u|0,a[1]=a[1]+c|0,a[2]=a[2]+l|0,a[3]=a[3]+f|0,a[4]=a[4]+B|0},_doFinalize:function(){var o=this._data,C=o.words,a=this._nDataBytes*8,u=o.sigBytes*8;return C[u>>>5]|=128<<24-u%32,C[(u+64>>>9<<4)+14]=Math.floor(a/4294967296),C[(u+64>>>9<<4)+15]=a,o.sigBytes=C.length*4,this._process(),this._hash},clone:function(){var o=m.clone.call(this);return o._hash=this._hash.clone(),o}});e.SHA1=m._createHelper(t),e.HmacSHA1=m._createHmacHelper(t)}(),r.SHA1})}(V0)),V0.exports}var q0={exports:{}},qe;function Ce(){return qe||(qe=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){return function(e){var h=r,v=h.lib,m=v.WordArray,p=v.Hasher,A=h.algo,t=[],o=[];(function(){function u(B){for(var i=e.sqrt(B),x=2;x<=i;x++)if(!(B%x))return!1;return!0}function c(B){return(B-(B|0))*4294967296|0}for(var l=2,f=0;f<64;)u(l)&&(f<8&&(t[f]=c(e.pow(l,1/2))),o[f]=c(e.pow(l,1/3)),f++),l++})();var C=[],a=A.SHA256=p.extend({_doReset:function(){this._hash=new m.init(t.slice(0))},_doProcessBlock:function(u,c){for(var l=this._hash.words,f=l[0],B=l[1],i=l[2],x=l[3],d=l[4],g=l[5],D=l[6],_=l[7],F=0;F<64;F++){if(F<16)C[F]=u[c+F]|0;else{var T=C[F-15],E=(T<<25|T>>>7)^(T<<14|T>>>18)^T>>>3,R=C[F-2],k=(R<<15|R>>>17)^(R<<13|R>>>19)^R>>>10;C[F]=E+C[F-7]+k+C[F-16]}var y=d&g^~d&D,H=f&B^f&i^B&i,w=(f<<30|f>>>2)^(f<<19|f>>>13)^(f<<10|f>>>22),L=(d<<26|d>>>6)^(d<<21|d>>>11)^(d<<7|d>>>25),V=_+L+y+o[F]+C[F],O=w+H;_=D,D=g,g=d,d=x+V|0,x=i,i=B,B=f,f=V+O|0}l[0]=l[0]+f|0,l[1]=l[1]+B|0,l[2]=l[2]+i|0,l[3]=l[3]+x|0,l[4]=l[4]+d|0,l[5]=l[5]+g|0,l[6]=l[6]+D|0,l[7]=l[7]+_|0},_doFinalize:function(){var u=this._data,c=u.words,l=this._nDataBytes*8,f=u.sigBytes*8;return c[f>>>5]|=128<<24-f%32,c[(f+64>>>9<<4)+14]=e.floor(l/4294967296),c[(f+64>>>9<<4)+15]=l,u.sigBytes=c.length*4,this._process(),this._hash},clone:function(){var u=p.clone.call(this);return u._hash=this._hash.clone(),u}});h.SHA256=p._createHelper(a),h.HmacSHA256=p._createHmacHelper(a)}(Math),r.SHA256})}(q0)),q0.exports}var K0={exports:{}},Ke;function Qr(){return Ke||(Ke=1,function(n,s){(function(r,e,h){n.exports=e(N(),Ce())})(U,function(r){return function(){var e=r,h=e.lib,v=h.WordArray,m=e.algo,p=m.SHA256,A=m.SHA224=p.extend({_doReset:function(){this._hash=new v.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=p._doFinalize.call(this);return t.sigBytes-=4,t}});e.SHA224=p._createHelper(A),e.HmacSHA224=p._createHmacHelper(A)}(),r.SHA224})}(K0)),K0.exports}var X0={exports:{}},Xe;function Xt(){return Xe||(Xe=1,function(n,s){(function(r,e,h){n.exports=e(N(),H0())})(U,function(r){return function(){var e=r,h=e.lib,v=h.Hasher,m=e.x64,p=m.Word,A=m.WordArray,t=e.algo;function o(){return p.create.apply(p,arguments)}var C=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],a=[];(function(){for(var c=0;c<80;c++)a[c]=o()})();var u=t.SHA512=v.extend({_doReset:function(){this._hash=new A.init([new p.init(1779033703,4089235720),new p.init(3144134277,2227873595),new p.init(1013904242,4271175723),new p.init(2773480762,1595750129),new p.init(1359893119,2917565137),new p.init(2600822924,725511199),new p.init(528734635,4215389547),new p.init(1541459225,327033209)])},_doProcessBlock:function(c,l){for(var f=this._hash.words,B=f[0],i=f[1],x=f[2],d=f[3],g=f[4],D=f[5],_=f[6],F=f[7],T=B.high,E=B.low,R=i.high,k=i.low,y=x.high,H=x.low,w=d.high,L=d.low,V=g.high,O=g.low,W=D.high,z=D.low,I=_.high,S=_.low,P=F.high,b=F.low,q=T,G=E,Z=R,$=k,D0=y,E0=H,w0=w,g0=L,r0=V,M=O,S0=W,_0=z,P0=I,R0=S,L0=P,m0=b,n0=0;n0<80;n0++){var t0,l0,T0=a[n0];if(n0<16)l0=T0.high=c[l+n0*2]|0,t0=T0.low=c[l+n0*2+1]|0;else{var ge=a[n0-15],C0=ge.high,F0=ge.low,fr=(C0>>>1|F0<<31)^(C0>>>8|F0<<24)^C0>>>7,_e=(F0>>>1|C0<<31)^(F0>>>8|C0<<24)^(F0>>>7|C0<<25),Re=a[n0-2],B0=Re.high,I0=Re.low,lr=(B0>>>19|I0<<13)^(B0<<3|I0>>>29)^B0>>>6,me=(I0>>>19|B0<<13)^(I0<<3|B0>>>29)^(I0>>>6|B0<<26),Fe=a[n0-7],vr=Fe.high,hr=Fe.low,Ie=a[n0-16],pr=Ie.high,ke=Ie.low;t0=_e+hr,l0=fr+vr+(t0>>>0<_e>>>0?1:0),t0=t0+me,l0=l0+lr+(t0>>>0<me>>>0?1:0),t0=t0+ke,l0=l0+pr+(t0>>>0<ke>>>0?1:0),T0.high=l0,T0.low=t0}var Ar=r0&S0^~r0&P0,ye=M&_0^~M&R0,Er=q&Z^q&D0^Z&D0,Cr=G&$^G&E0^$&E0,Br=(q>>>28|G<<4)^(q<<30|G>>>2)^(q<<25|G>>>7),be=(G>>>28|q<<4)^(G<<30|q>>>2)^(G<<25|q>>>7),Dr=(r0>>>14|M<<18)^(r0>>>18|M<<14)^(r0<<23|M>>>9),gr=(M>>>14|r0<<18)^(M>>>18|r0<<14)^(M<<23|r0>>>9),Se=C[n0],_r=Se.high,Pe=Se.low,Y=m0+gr,v0=L0+Dr+(Y>>>0<m0>>>0?1:0),Y=Y+ye,v0=v0+Ar+(Y>>>0<ye>>>0?1:0),Y=Y+Pe,v0=v0+_r+(Y>>>0<Pe>>>0?1:0),Y=Y+t0,v0=v0+l0+(Y>>>0<t0>>>0?1:0),Te=be+Cr,Rr=Br+Er+(Te>>>0<be>>>0?1:0);L0=P0,m0=R0,P0=S0,R0=_0,S0=r0,_0=M,M=g0+Y|0,r0=w0+v0+(M>>>0<g0>>>0?1:0)|0,w0=D0,g0=E0,D0=Z,E0=$,Z=q,$=G,G=Y+Te|0,q=v0+Rr+(G>>>0<Y>>>0?1:0)|0}E=B.low=E+G,B.high=T+q+(E>>>0<G>>>0?1:0),k=i.low=k+$,i.high=R+Z+(k>>>0<$>>>0?1:0),H=x.low=H+E0,x.high=y+D0+(H>>>0<E0>>>0?1:0),L=d.low=L+g0,d.high=w+w0+(L>>>0<g0>>>0?1:0),O=g.low=O+M,g.high=V+r0+(O>>>0<M>>>0?1:0),z=D.low=z+_0,D.high=W+S0+(z>>>0<_0>>>0?1:0),S=_.low=S+R0,_.high=I+P0+(S>>>0<R0>>>0?1:0),b=F.low=b+m0,F.high=P+L0+(b>>>0<m0>>>0?1:0)},_doFinalize:function(){var c=this._data,l=c.words,f=this._nDataBytes*8,B=c.sigBytes*8;l[B>>>5]|=128<<24-B%32,l[(B+128>>>10<<5)+30]=Math.floor(f/4294967296),l[(B+128>>>10<<5)+31]=f,c.sigBytes=l.length*4,this._process();var i=this._hash.toX32();return i},clone:function(){var c=v.clone.call(this);return c._hash=this._hash.clone(),c},blockSize:1024/32});e.SHA512=v._createHelper(u),e.HmacSHA512=v._createHmacHelper(u)}(),r.SHA512})}(X0)),X0.exports}var Z0={exports:{}},Ze;function jr(){return Ze||(Ze=1,function(n,s){(function(r,e,h){n.exports=e(N(),H0(),Xt())})(U,function(r){return function(){var e=r,h=e.x64,v=h.Word,m=h.WordArray,p=e.algo,A=p.SHA512,t=p.SHA384=A.extend({_doReset:function(){this._hash=new m.init([new v.init(3418070365,3238371032),new v.init(1654270250,914150663),new v.init(2438529370,812702999),new v.init(355462360,4144912697),new v.init(1731405415,4290775857),new v.init(2394180231,1750603025),new v.init(3675008525,1694076839),new v.init(1203062813,3204075428)])},_doFinalize:function(){var o=A._doFinalize.call(this);return o.sigBytes-=16,o}});e.SHA384=A._createHelper(t),e.HmacSHA384=A._createHmacHelper(t)}(),r.SHA384})}(Z0)),Z0.exports}var M0={exports:{}},Me;function Jr(){return Me||(Me=1,function(n,s){(function(r,e,h){n.exports=e(N(),H0())})(U,function(r){return function(e){var h=r,v=h.lib,m=v.WordArray,p=v.Hasher,A=h.x64,t=A.Word,o=h.algo,C=[],a=[],u=[];(function(){for(var f=1,B=0,i=0;i<24;i++){C[f+5*B]=(i+1)*(i+2)/2%64;var x=B%5,d=(2*f+3*B)%5;f=x,B=d}for(var f=0;f<5;f++)for(var B=0;B<5;B++)a[f+5*B]=B+(2*f+3*B)%5*5;for(var g=1,D=0;D<24;D++){for(var _=0,F=0,T=0;T<7;T++){if(g&1){var E=(1<<T)-1;E<32?F^=1<<E:_^=1<<E-32}g&128?g=g<<1^113:g<<=1}u[D]=t.create(_,F)}})();var c=[];(function(){for(var f=0;f<25;f++)c[f]=t.create()})();var l=o.SHA3=p.extend({cfg:p.cfg.extend({outputLength:512}),_doReset:function(){for(var f=this._state=[],B=0;B<25;B++)f[B]=new t.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(f,B){for(var i=this._state,x=this.blockSize/2,d=0;d<x;d++){var g=f[B+2*d],D=f[B+2*d+1];g=(g<<8|g>>>24)&16711935|(g<<24|g>>>8)&4278255360,D=(D<<8|D>>>24)&16711935|(D<<24|D>>>8)&4278255360;var _=i[d];_.high^=D,_.low^=g}for(var F=0;F<24;F++){for(var T=0;T<5;T++){for(var E=0,R=0,k=0;k<5;k++){var _=i[T+5*k];E^=_.high,R^=_.low}var y=c[T];y.high=E,y.low=R}for(var T=0;T<5;T++)for(var H=c[(T+4)%5],w=c[(T+1)%5],L=w.high,V=w.low,E=H.high^(L<<1|V>>>31),R=H.low^(V<<1|L>>>31),k=0;k<5;k++){var _=i[T+5*k];_.high^=E,_.low^=R}for(var O=1;O<25;O++){var E,R,_=i[O],W=_.high,z=_.low,I=C[O];I<32?(E=W<<I|z>>>32-I,R=z<<I|W>>>32-I):(E=z<<I-32|W>>>64-I,R=W<<I-32|z>>>64-I);var S=c[a[O]];S.high=E,S.low=R}var P=c[0],b=i[0];P.high=b.high,P.low=b.low;for(var T=0;T<5;T++)for(var k=0;k<5;k++){var O=T+5*k,_=i[O],q=c[O],G=c[(T+1)%5+5*k],Z=c[(T+2)%5+5*k];_.high=q.high^~G.high&Z.high,_.low=q.low^~G.low&Z.low}var _=i[0],$=u[F];_.high^=$.high,_.low^=$.low}},_doFinalize:function(){var f=this._data,B=f.words;this._nDataBytes*8;var i=f.sigBytes*8,x=this.blockSize*32;B[i>>>5]|=1<<24-i%32,B[(e.ceil((i+1)/x)*x>>>5)-1]|=128,f.sigBytes=B.length*4,this._process();for(var d=this._state,g=this.cfg.outputLength/8,D=g/8,_=[],F=0;F<D;F++){var T=d[F],E=T.high,R=T.low;E=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360,R=(R<<8|R>>>24)&16711935|(R<<24|R>>>8)&4278255360,_.push(R),_.push(E)}return new m.init(_,g)},clone:function(){for(var f=p.clone.call(this),B=f._state=this._state.slice(0),i=0;i<25;i++)B[i]=B[i].clone();return f}});h.SHA3=p._createHelper(l),h.HmacSHA3=p._createHmacHelper(l)}(Math),r.SHA3})}(M0)),M0.exports}var Y0={exports:{}},Ye;function en(){return Ye||(Ye=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){/** @preserve
			(c) 2012 by Cédric Mesnil. All rights reserved.

			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
			*/return function(e){var h=r,v=h.lib,m=v.WordArray,p=v.Hasher,A=h.algo,t=m.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),o=m.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),C=m.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),a=m.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=m.create([0,1518500249,1859775393,2400959708,2840853838]),c=m.create([1352829926,1548603684,1836072691,2053994217,0]),l=A.RIPEMD160=p.extend({_doReset:function(){this._hash=m.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(D,_){for(var F=0;F<16;F++){var T=_+F,E=D[T];D[T]=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360}var R=this._hash.words,k=u.words,y=c.words,H=t.words,w=o.words,L=C.words,V=a.words,O,W,z,I,S,P,b,q,G,Z;P=O=R[0],b=W=R[1],q=z=R[2],G=I=R[3],Z=S=R[4];for(var $,F=0;F<80;F+=1)$=O+D[_+H[F]]|0,F<16?$+=f(W,z,I)+k[0]:F<32?$+=B(W,z,I)+k[1]:F<48?$+=i(W,z,I)+k[2]:F<64?$+=x(W,z,I)+k[3]:$+=d(W,z,I)+k[4],$=$|0,$=g($,L[F]),$=$+S|0,O=S,S=I,I=g(z,10),z=W,W=$,$=P+D[_+w[F]]|0,F<16?$+=d(b,q,G)+y[0]:F<32?$+=x(b,q,G)+y[1]:F<48?$+=i(b,q,G)+y[2]:F<64?$+=B(b,q,G)+y[3]:$+=f(b,q,G)+y[4],$=$|0,$=g($,V[F]),$=$+Z|0,P=Z,Z=G,G=g(q,10),q=b,b=$;$=R[1]+z+G|0,R[1]=R[2]+I+Z|0,R[2]=R[3]+S+P|0,R[3]=R[4]+O+b|0,R[4]=R[0]+W+q|0,R[0]=$},_doFinalize:function(){var D=this._data,_=D.words,F=this._nDataBytes*8,T=D.sigBytes*8;_[T>>>5]|=128<<24-T%32,_[(T+64>>>9<<4)+14]=(F<<8|F>>>24)&16711935|(F<<24|F>>>8)&4278255360,D.sigBytes=(_.length+1)*4,this._process();for(var E=this._hash,R=E.words,k=0;k<5;k++){var y=R[k];R[k]=(y<<8|y>>>24)&16711935|(y<<24|y>>>8)&4278255360}return E},clone:function(){var D=p.clone.call(this);return D._hash=this._hash.clone(),D}});function f(D,_,F){return D^_^F}function B(D,_,F){return D&_|~D&F}function i(D,_,F){return(D|~_)^F}function x(D,_,F){return D&F|_&~F}function d(D,_,F){return D^(_|~F)}function g(D,_){return D<<_|D>>>32-_}h.RIPEMD160=p._createHelper(l),h.HmacRIPEMD160=p._createHmacHelper(l)}(),r.RIPEMD160})}(Y0)),Y0.exports}var Q0={exports:{}},Qe;function Be(){return Qe||(Qe=1,function(n,s){(function(r,e){n.exports=e(N())})(U,function(r){(function(){var e=r,h=e.lib,v=h.Base,m=e.enc,p=m.Utf8,A=e.algo;A.HMAC=v.extend({init:function(t,o){t=this._hasher=new t.init,typeof o=="string"&&(o=p.parse(o));var C=t.blockSize,a=C*4;o.sigBytes>a&&(o=t.finalize(o)),o.clamp();for(var u=this._oKey=o.clone(),c=this._iKey=o.clone(),l=u.words,f=c.words,B=0;B<C;B++)l[B]^=1549556828,f[B]^=909522486;u.sigBytes=c.sigBytes=a,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var o=this._hasher,C=o.finalize(t);o.reset();var a=o.finalize(this._oKey.clone().concat(C));return a}})})()})}(Q0)),Q0.exports}var j0={exports:{}},je;function tn(){return je||(je=1,function(n,s){(function(r,e,h){n.exports=e(N(),Ce(),Be())})(U,function(r){return function(){var e=r,h=e.lib,v=h.Base,m=h.WordArray,p=e.algo,A=p.SHA256,t=p.HMAC,o=p.PBKDF2=v.extend({cfg:v.extend({keySize:128/32,hasher:A,iterations:25e4}),init:function(C){this.cfg=this.cfg.extend(C)},compute:function(C,a){for(var u=this.cfg,c=t.create(u.hasher,C),l=m.create(),f=m.create([1]),B=l.words,i=f.words,x=u.keySize,d=u.iterations;B.length<x;){var g=c.update(a).finalize(f);c.reset();for(var D=g.words,_=D.length,F=g,T=1;T<d;T++){F=c.finalize(F),c.reset();for(var E=F.words,R=0;R<_;R++)D[R]^=E[R]}l.concat(g),i[0]++}return l.sigBytes=x*4,l}});e.PBKDF2=function(C,a,u){return o.create(u).compute(C,a)}}(),r.PBKDF2})}(j0)),j0.exports}var J0={exports:{}},Je;function h0(){return Je||(Je=1,function(n,s){(function(r,e,h){n.exports=e(N(),Kt(),Be())})(U,function(r){return function(){var e=r,h=e.lib,v=h.Base,m=h.WordArray,p=e.algo,A=p.MD5,t=p.EvpKDF=v.extend({cfg:v.extend({keySize:128/32,hasher:A,iterations:1}),init:function(o){this.cfg=this.cfg.extend(o)},compute:function(o,C){for(var a,u=this.cfg,c=u.hasher.create(),l=m.create(),f=l.words,B=u.keySize,i=u.iterations;f.length<B;){a&&c.update(a),a=c.update(o).finalize(C),c.reset();for(var x=1;x<i;x++)a=c.finalize(a),c.reset();l.concat(a)}return l.sigBytes=B*4,l}});e.EvpKDF=function(o,C,a){return t.create(a).compute(o,C)}}(),r.EvpKDF})}(J0)),J0.exports}var ee={exports:{}},et;function X(){return et||(et=1,function(n,s){(function(r,e,h){n.exports=e(N(),h0())})(U,function(r){r.lib.Cipher||function(e){var h=r,v=h.lib,m=v.Base,p=v.WordArray,A=v.BufferedBlockAlgorithm,t=h.enc;t.Utf8;var o=t.Base64,C=h.algo,a=C.EvpKDF,u=v.Cipher=A.extend({cfg:m.extend(),createEncryptor:function(E,R){return this.create(this._ENC_XFORM_MODE,E,R)},createDecryptor:function(E,R){return this.create(this._DEC_XFORM_MODE,E,R)},init:function(E,R,k){this.cfg=this.cfg.extend(k),this._xformMode=E,this._key=R,this.reset()},reset:function(){A.reset.call(this),this._doReset()},process:function(E){return this._append(E),this._process()},finalize:function(E){E&&this._append(E);var R=this._doFinalize();return R},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function E(R){return typeof R=="string"?T:D}return function(R){return{encrypt:function(k,y,H){return E(y).encrypt(R,k,y,H)},decrypt:function(k,y,H){return E(y).decrypt(R,k,y,H)}}}}()});v.StreamCipher=u.extend({_doFinalize:function(){var E=this._process(!0);return E},blockSize:1});var c=h.mode={},l=v.BlockCipherMode=m.extend({createEncryptor:function(E,R){return this.Encryptor.create(E,R)},createDecryptor:function(E,R){return this.Decryptor.create(E,R)},init:function(E,R){this._cipher=E,this._iv=R}}),f=c.CBC=function(){var E=l.extend();E.Encryptor=E.extend({processBlock:function(k,y){var H=this._cipher,w=H.blockSize;R.call(this,k,y,w),H.encryptBlock(k,y),this._prevBlock=k.slice(y,y+w)}}),E.Decryptor=E.extend({processBlock:function(k,y){var H=this._cipher,w=H.blockSize,L=k.slice(y,y+w);H.decryptBlock(k,y),R.call(this,k,y,w),this._prevBlock=L}});function R(k,y,H){var w,L=this._iv;L?(w=L,this._iv=e):w=this._prevBlock;for(var V=0;V<H;V++)k[y+V]^=w[V]}return E}(),B=h.pad={},i=B.Pkcs7={pad:function(E,R){for(var k=R*4,y=k-E.sigBytes%k,H=y<<24|y<<16|y<<8|y,w=[],L=0;L<y;L+=4)w.push(H);var V=p.create(w,y);E.concat(V)},unpad:function(E){var R=E.words[E.sigBytes-1>>>2]&255;E.sigBytes-=R}};v.BlockCipher=u.extend({cfg:u.cfg.extend({mode:f,padding:i}),reset:function(){var E;u.reset.call(this);var R=this.cfg,k=R.iv,y=R.mode;this._xformMode==this._ENC_XFORM_MODE?E=y.createEncryptor:(E=y.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==E?this._mode.init(this,k&&k.words):(this._mode=E.call(y,this,k&&k.words),this._mode.__creator=E)},_doProcessBlock:function(E,R){this._mode.processBlock(E,R)},_doFinalize:function(){var E,R=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(R.pad(this._data,this.blockSize),E=this._process(!0)):(E=this._process(!0),R.unpad(E)),E},blockSize:128/32});var x=v.CipherParams=m.extend({init:function(E){this.mixIn(E)},toString:function(E){return(E||this.formatter).stringify(this)}}),d=h.format={},g=d.OpenSSL={stringify:function(E){var R,k=E.ciphertext,y=E.salt;return y?R=p.create([1398893684,1701076831]).concat(y).concat(k):R=k,R.toString(o)},parse:function(E){var R,k=o.parse(E),y=k.words;return y[0]==1398893684&&y[1]==1701076831&&(R=p.create(y.slice(2,4)),y.splice(0,4),k.sigBytes-=16),x.create({ciphertext:k,salt:R})}},D=v.SerializableCipher=m.extend({cfg:m.extend({format:g}),encrypt:function(E,R,k,y){y=this.cfg.extend(y);var H=E.createEncryptor(k,y),w=H.finalize(R),L=H.cfg;return x.create({ciphertext:w,key:k,iv:L.iv,algorithm:E,mode:L.mode,padding:L.padding,blockSize:E.blockSize,formatter:y.format})},decrypt:function(E,R,k,y){y=this.cfg.extend(y),R=this._parse(R,y.format);var H=E.createDecryptor(k,y).finalize(R.ciphertext);return H},_parse:function(E,R){return typeof E=="string"?R.parse(E,this):E}}),_=h.kdf={},F=_.OpenSSL={execute:function(E,R,k,y,H){if(y||(y=p.random(64/8)),H)var w=a.create({keySize:R+k,hasher:H}).compute(E,y);else var w=a.create({keySize:R+k}).compute(E,y);var L=p.create(w.words.slice(R),k*4);return w.sigBytes=R*4,x.create({key:w,iv:L,salt:y})}},T=v.PasswordBasedCipher=D.extend({cfg:D.cfg.extend({kdf:F}),encrypt:function(E,R,k,y){y=this.cfg.extend(y);var H=y.kdf.execute(k,E.keySize,E.ivSize,y.salt,y.hasher);y.iv=H.iv;var w=D.encrypt.call(this,E,R,H.key,y);return w.mixIn(H),w},decrypt:function(E,R,k,y){y=this.cfg.extend(y),R=this._parse(R,y.format);var H=y.kdf.execute(k,E.keySize,E.ivSize,R.salt,y.hasher);y.iv=H.iv;var w=D.decrypt.call(this,E,R,H.key,y);return w}})}()})}(ee)),ee.exports}var te={exports:{}},tt;function rn(){return tt||(tt=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();e.Encryptor=e.extend({processBlock:function(v,m){var p=this._cipher,A=p.blockSize;h.call(this,v,m,A,p),this._prevBlock=v.slice(m,m+A)}}),e.Decryptor=e.extend({processBlock:function(v,m){var p=this._cipher,A=p.blockSize,t=v.slice(m,m+A);h.call(this,v,m,A,p),this._prevBlock=t}});function h(v,m,p,A){var t,o=this._iv;o?(t=o.slice(0),this._iv=void 0):t=this._prevBlock,A.encryptBlock(t,0);for(var C=0;C<p;C++)v[m+C]^=t[C]}return e}(),r.mode.CFB})}(te)),te.exports}var re={exports:{}},rt;function nn(){return rt||(rt=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.mode.CTR=function(){var e=r.lib.BlockCipherMode.extend(),h=e.Encryptor=e.extend({processBlock:function(v,m){var p=this._cipher,A=p.blockSize,t=this._iv,o=this._counter;t&&(o=this._counter=t.slice(0),this._iv=void 0);var C=o.slice(0);p.encryptBlock(C,0),o[A-1]=o[A-1]+1|0;for(var a=0;a<A;a++)v[m+a]^=C[a]}});return e.Decryptor=h,e}(),r.mode.CTR})}(re)),re.exports}var ne={exports:{}},nt;function an(){return nt||(nt=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){/** @preserve
 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
 * derived from CryptoJS.mode.CTR
 * <NAME_EMAIL>
 */return r.mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function h(p){if((p>>24&255)===255){var A=p>>16&255,t=p>>8&255,o=p&255;A===255?(A=0,t===255?(t=0,o===255?o=0:++o):++t):++A,p=0,p+=A<<16,p+=t<<8,p+=o}else p+=1<<24;return p}function v(p){return(p[0]=h(p[0]))===0&&(p[1]=h(p[1])),p}var m=e.Encryptor=e.extend({processBlock:function(p,A){var t=this._cipher,o=t.blockSize,C=this._iv,a=this._counter;C&&(a=this._counter=C.slice(0),this._iv=void 0),v(a);var u=a.slice(0);t.encryptBlock(u,0);for(var c=0;c<o;c++)p[A+c]^=u[c]}});return e.Decryptor=m,e}(),r.mode.CTRGladman})}(ne)),ne.exports}var ae={exports:{}},at;function on(){return at||(at=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.mode.OFB=function(){var e=r.lib.BlockCipherMode.extend(),h=e.Encryptor=e.extend({processBlock:function(v,m){var p=this._cipher,A=p.blockSize,t=this._iv,o=this._keystream;t&&(o=this._keystream=t.slice(0),this._iv=void 0),p.encryptBlock(o,0);for(var C=0;C<A;C++)v[m+C]^=o[C]}});return e.Decryptor=h,e}(),r.mode.OFB})}(ae)),ae.exports}var oe={exports:{}},ot;function xn(){return ot||(ot=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.mode.ECB=function(){var e=r.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(h,v){this._cipher.encryptBlock(h,v)}}),e.Decryptor=e.extend({processBlock:function(h,v){this._cipher.decryptBlock(h,v)}}),e}(),r.mode.ECB})}(oe)),oe.exports}var ie={exports:{}},it;function sn(){return it||(it=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.pad.AnsiX923={pad:function(e,h){var v=e.sigBytes,m=h*4,p=m-v%m,A=v+p-1;e.clamp(),e.words[A>>>2]|=p<<24-A%4*8,e.sigBytes+=p},unpad:function(e){var h=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=h}},r.pad.Ansix923})}(ie)),ie.exports}var xe={exports:{}},xt;function cn(){return xt||(xt=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.pad.Iso10126={pad:function(e,h){var v=h*4,m=v-e.sigBytes%v;e.concat(r.lib.WordArray.random(m-1)).concat(r.lib.WordArray.create([m<<24],1))},unpad:function(e){var h=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=h}},r.pad.Iso10126})}(xe)),xe.exports}var se={exports:{}},st;function un(){return st||(st=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.pad.Iso97971={pad:function(e,h){e.concat(r.lib.WordArray.create([2147483648],1)),r.pad.ZeroPadding.pad(e,h)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},r.pad.Iso97971})}(se)),se.exports}var ce={exports:{}},ct;function dn(){return ct||(ct=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.pad.ZeroPadding={pad:function(e,h){var v=h*4;e.clamp(),e.sigBytes+=v-(e.sigBytes%v||v)},unpad:function(e){for(var h=e.words,v=e.sigBytes-1,v=e.sigBytes-1;v>=0;v--)if(h[v>>>2]>>>24-v%4*8&255){e.sigBytes=v+1;break}}},r.pad.ZeroPadding})}(ce)),ce.exports}var ue={exports:{}},ut;function fn(){return ut||(ut=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return r.pad.NoPadding={pad:function(){},unpad:function(){}},r.pad.NoPadding})}(ue)),ue.exports}var de={exports:{}},dt;function ln(){return dt||(dt=1,function(n,s){(function(r,e,h){n.exports=e(N(),X())})(U,function(r){return function(e){var h=r,v=h.lib,m=v.CipherParams,p=h.enc,A=p.Hex,t=h.format;t.Hex={stringify:function(o){return o.ciphertext.toString(A)},parse:function(o){var C=A.parse(o);return m.create({ciphertext:C})}}}(),r.format.Hex})}(de)),de.exports}var fe={exports:{}},ft;function vn(){return ft||(ft=1,function(n,s){(function(r,e,h){n.exports=e(N(),p0(),A0(),h0(),X())})(U,function(r){return function(){var e=r,h=e.lib,v=h.BlockCipher,m=e.algo,p=[],A=[],t=[],o=[],C=[],a=[],u=[],c=[],l=[],f=[];(function(){for(var x=[],d=0;d<256;d++)d<128?x[d]=d<<1:x[d]=d<<1^283;for(var g=0,D=0,d=0;d<256;d++){var _=D^D<<1^D<<2^D<<3^D<<4;_=_>>>8^_&255^99,p[g]=_,A[_]=g;var F=x[g],T=x[F],E=x[T],R=x[_]*257^_*16843008;t[g]=R<<24|R>>>8,o[g]=R<<16|R>>>16,C[g]=R<<8|R>>>24,a[g]=R;var R=E*16843009^T*65537^F*257^g*16843008;u[_]=R<<24|R>>>8,c[_]=R<<16|R>>>16,l[_]=R<<8|R>>>24,f[_]=R,g?(g=F^x[x[x[E^F]]],D^=x[x[D]]):g=D=1}})();var B=[0,1,2,4,8,16,32,64,128,27,54],i=m.AES=v.extend({_doReset:function(){var x;if(!(this._nRounds&&this._keyPriorReset===this._key)){for(var d=this._keyPriorReset=this._key,g=d.words,D=d.sigBytes/4,_=this._nRounds=D+6,F=(_+1)*4,T=this._keySchedule=[],E=0;E<F;E++)E<D?T[E]=g[E]:(x=T[E-1],E%D?D>6&&E%D==4&&(x=p[x>>>24]<<24|p[x>>>16&255]<<16|p[x>>>8&255]<<8|p[x&255]):(x=x<<8|x>>>24,x=p[x>>>24]<<24|p[x>>>16&255]<<16|p[x>>>8&255]<<8|p[x&255],x^=B[E/D|0]<<24),T[E]=T[E-D]^x);for(var R=this._invKeySchedule=[],k=0;k<F;k++){var E=F-k;if(k%4)var x=T[E];else var x=T[E-4];k<4||E<=4?R[k]=x:R[k]=u[p[x>>>24]]^c[p[x>>>16&255]]^l[p[x>>>8&255]]^f[p[x&255]]}}},encryptBlock:function(x,d){this._doCryptBlock(x,d,this._keySchedule,t,o,C,a,p)},decryptBlock:function(x,d){var g=x[d+1];x[d+1]=x[d+3],x[d+3]=g,this._doCryptBlock(x,d,this._invKeySchedule,u,c,l,f,A);var g=x[d+1];x[d+1]=x[d+3],x[d+3]=g},_doCryptBlock:function(x,d,g,D,_,F,T,E){for(var R=this._nRounds,k=x[d]^g[0],y=x[d+1]^g[1],H=x[d+2]^g[2],w=x[d+3]^g[3],L=4,V=1;V<R;V++){var O=D[k>>>24]^_[y>>>16&255]^F[H>>>8&255]^T[w&255]^g[L++],W=D[y>>>24]^_[H>>>16&255]^F[w>>>8&255]^T[k&255]^g[L++],z=D[H>>>24]^_[w>>>16&255]^F[k>>>8&255]^T[y&255]^g[L++],I=D[w>>>24]^_[k>>>16&255]^F[y>>>8&255]^T[H&255]^g[L++];k=O,y=W,H=z,w=I}var O=(E[k>>>24]<<24|E[y>>>16&255]<<16|E[H>>>8&255]<<8|E[w&255])^g[L++],W=(E[y>>>24]<<24|E[H>>>16&255]<<16|E[w>>>8&255]<<8|E[k&255])^g[L++],z=(E[H>>>24]<<24|E[w>>>16&255]<<16|E[k>>>8&255]<<8|E[y&255])^g[L++],I=(E[w>>>24]<<24|E[k>>>16&255]<<16|E[y>>>8&255]<<8|E[H&255])^g[L++];x[d]=O,x[d+1]=W,x[d+2]=z,x[d+3]=I},keySize:256/32});e.AES=v._createHelper(i)}(),r.AES})}(fe)),fe.exports}var le={exports:{}},lt;function hn(){return lt||(lt=1,function(n,s){(function(r,e,h){n.exports=e(N(),p0(),A0(),h0(),X())})(U,function(r){return function(){var e=r,h=e.lib,v=h.WordArray,m=h.BlockCipher,p=e.algo,A=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],t=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],o=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],C=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],a=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],u=p.DES=m.extend({_doReset:function(){for(var B=this._key,i=B.words,x=[],d=0;d<56;d++){var g=A[d]-1;x[d]=i[g>>>5]>>>31-g%32&1}for(var D=this._subKeys=[],_=0;_<16;_++){for(var F=D[_]=[],T=o[_],d=0;d<24;d++)F[d/6|0]|=x[(t[d]-1+T)%28]<<31-d%6,F[4+(d/6|0)]|=x[28+(t[d+24]-1+T)%28]<<31-d%6;F[0]=F[0]<<1|F[0]>>>31;for(var d=1;d<7;d++)F[d]=F[d]>>>(d-1)*4+3;F[7]=F[7]<<5|F[7]>>>27}for(var E=this._invSubKeys=[],d=0;d<16;d++)E[d]=D[15-d]},encryptBlock:function(B,i){this._doCryptBlock(B,i,this._subKeys)},decryptBlock:function(B,i){this._doCryptBlock(B,i,this._invSubKeys)},_doCryptBlock:function(B,i,x){this._lBlock=B[i],this._rBlock=B[i+1],c.call(this,4,252645135),c.call(this,16,65535),l.call(this,2,858993459),l.call(this,8,16711935),c.call(this,1,1431655765);for(var d=0;d<16;d++){for(var g=x[d],D=this._lBlock,_=this._rBlock,F=0,T=0;T<8;T++)F|=C[T][((_^g[T])&a[T])>>>0];this._lBlock=_,this._rBlock=D^F}var E=this._lBlock;this._lBlock=this._rBlock,this._rBlock=E,c.call(this,1,1431655765),l.call(this,8,16711935),l.call(this,2,858993459),c.call(this,16,65535),c.call(this,4,252645135),B[i]=this._lBlock,B[i+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function c(B,i){var x=(this._lBlock>>>B^this._rBlock)&i;this._rBlock^=x,this._lBlock^=x<<B}function l(B,i){var x=(this._rBlock>>>B^this._lBlock)&i;this._lBlock^=x,this._rBlock^=x<<B}e.DES=m._createHelper(u);var f=p.TripleDES=m.extend({_doReset:function(){var B=this._key,i=B.words;if(i.length!==2&&i.length!==4&&i.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var x=i.slice(0,2),d=i.length<4?i.slice(0,2):i.slice(2,4),g=i.length<6?i.slice(0,2):i.slice(4,6);this._des1=u.createEncryptor(v.create(x)),this._des2=u.createEncryptor(v.create(d)),this._des3=u.createEncryptor(v.create(g))},encryptBlock:function(B,i){this._des1.encryptBlock(B,i),this._des2.decryptBlock(B,i),this._des3.encryptBlock(B,i)},decryptBlock:function(B,i){this._des3.decryptBlock(B,i),this._des2.encryptBlock(B,i),this._des1.decryptBlock(B,i)},keySize:192/32,ivSize:64/32,blockSize:64/32});e.TripleDES=m._createHelper(f)}(),r.TripleDES})}(le)),le.exports}var ve={exports:{}},vt;function pn(){return vt||(vt=1,function(n,s){(function(r,e,h){n.exports=e(N(),p0(),A0(),h0(),X())})(U,function(r){return function(){var e=r,h=e.lib,v=h.StreamCipher,m=e.algo,p=m.RC4=v.extend({_doReset:function(){for(var o=this._key,C=o.words,a=o.sigBytes,u=this._S=[],c=0;c<256;c++)u[c]=c;for(var c=0,l=0;c<256;c++){var f=c%a,B=C[f>>>2]>>>24-f%4*8&255;l=(l+u[c]+B)%256;var i=u[c];u[c]=u[l],u[l]=i}this._i=this._j=0},_doProcessBlock:function(o,C){o[C]^=A.call(this)},keySize:256/32,ivSize:0});function A(){for(var o=this._S,C=this._i,a=this._j,u=0,c=0;c<4;c++){C=(C+1)%256,a=(a+o[C])%256;var l=o[C];o[C]=o[a],o[a]=l,u|=o[(o[C]+o[a])%256]<<24-c*8}return this._i=C,this._j=a,u}e.RC4=v._createHelper(p);var t=m.RC4Drop=p.extend({cfg:p.cfg.extend({drop:192}),_doReset:function(){p._doReset.call(this);for(var o=this.cfg.drop;o>0;o--)A.call(this)}});e.RC4Drop=v._createHelper(t)}(),r.RC4})}(ve)),ve.exports}var he={exports:{}},ht;function An(){return ht||(ht=1,function(n,s){(function(r,e,h){n.exports=e(N(),p0(),A0(),h0(),X())})(U,function(r){return function(){var e=r,h=e.lib,v=h.StreamCipher,m=e.algo,p=[],A=[],t=[],o=m.Rabbit=v.extend({_doReset:function(){for(var a=this._key.words,u=this.cfg.iv,c=0;c<4;c++)a[c]=(a[c]<<8|a[c]>>>24)&16711935|(a[c]<<24|a[c]>>>8)&4278255360;var l=this._X=[a[0],a[3]<<16|a[2]>>>16,a[1],a[0]<<16|a[3]>>>16,a[2],a[1]<<16|a[0]>>>16,a[3],a[2]<<16|a[1]>>>16],f=this._C=[a[2]<<16|a[2]>>>16,a[0]&4294901760|a[1]&65535,a[3]<<16|a[3]>>>16,a[1]&4294901760|a[2]&65535,a[0]<<16|a[0]>>>16,a[2]&4294901760|a[3]&65535,a[1]<<16|a[1]>>>16,a[3]&4294901760|a[0]&65535];this._b=0;for(var c=0;c<4;c++)C.call(this);for(var c=0;c<8;c++)f[c]^=l[c+4&7];if(u){var B=u.words,i=B[0],x=B[1],d=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360,g=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,D=d>>>16|g&4294901760,_=g<<16|d&65535;f[0]^=d,f[1]^=D,f[2]^=g,f[3]^=_,f[4]^=d,f[5]^=D,f[6]^=g,f[7]^=_;for(var c=0;c<4;c++)C.call(this)}},_doProcessBlock:function(a,u){var c=this._X;C.call(this),p[0]=c[0]^c[5]>>>16^c[3]<<16,p[1]=c[2]^c[7]>>>16^c[5]<<16,p[2]=c[4]^c[1]>>>16^c[7]<<16,p[3]=c[6]^c[3]>>>16^c[1]<<16;for(var l=0;l<4;l++)p[l]=(p[l]<<8|p[l]>>>24)&16711935|(p[l]<<24|p[l]>>>8)&4278255360,a[u+l]^=p[l]},blockSize:128/32,ivSize:64/32});function C(){for(var a=this._X,u=this._C,c=0;c<8;c++)A[c]=u[c];u[0]=u[0]+1295307597+this._b|0,u[1]=u[1]+3545052371+(u[0]>>>0<A[0]>>>0?1:0)|0,u[2]=u[2]+886263092+(u[1]>>>0<A[1]>>>0?1:0)|0,u[3]=u[3]+1295307597+(u[2]>>>0<A[2]>>>0?1:0)|0,u[4]=u[4]+3545052371+(u[3]>>>0<A[3]>>>0?1:0)|0,u[5]=u[5]+886263092+(u[4]>>>0<A[4]>>>0?1:0)|0,u[6]=u[6]+1295307597+(u[5]>>>0<A[5]>>>0?1:0)|0,u[7]=u[7]+3545052371+(u[6]>>>0<A[6]>>>0?1:0)|0,this._b=u[7]>>>0<A[7]>>>0?1:0;for(var c=0;c<8;c++){var l=a[c]+u[c],f=l&65535,B=l>>>16,i=((f*f>>>17)+f*B>>>15)+B*B,x=((l&4294901760)*l|0)+((l&65535)*l|0);t[c]=i^x}a[0]=t[0]+(t[7]<<16|t[7]>>>16)+(t[6]<<16|t[6]>>>16)|0,a[1]=t[1]+(t[0]<<8|t[0]>>>24)+t[7]|0,a[2]=t[2]+(t[1]<<16|t[1]>>>16)+(t[0]<<16|t[0]>>>16)|0,a[3]=t[3]+(t[2]<<8|t[2]>>>24)+t[1]|0,a[4]=t[4]+(t[3]<<16|t[3]>>>16)+(t[2]<<16|t[2]>>>16)|0,a[5]=t[5]+(t[4]<<8|t[4]>>>24)+t[3]|0,a[6]=t[6]+(t[5]<<16|t[5]>>>16)+(t[4]<<16|t[4]>>>16)|0,a[7]=t[7]+(t[6]<<8|t[6]>>>24)+t[5]|0}e.Rabbit=v._createHelper(o)}(),r.Rabbit})}(he)),he.exports}var pe={exports:{}},pt;function En(){return pt||(pt=1,function(n,s){(function(r,e,h){n.exports=e(N(),p0(),A0(),h0(),X())})(U,function(r){return function(){var e=r,h=e.lib,v=h.StreamCipher,m=e.algo,p=[],A=[],t=[],o=m.RabbitLegacy=v.extend({_doReset:function(){var a=this._key.words,u=this.cfg.iv,c=this._X=[a[0],a[3]<<16|a[2]>>>16,a[1],a[0]<<16|a[3]>>>16,a[2],a[1]<<16|a[0]>>>16,a[3],a[2]<<16|a[1]>>>16],l=this._C=[a[2]<<16|a[2]>>>16,a[0]&4294901760|a[1]&65535,a[3]<<16|a[3]>>>16,a[1]&4294901760|a[2]&65535,a[0]<<16|a[0]>>>16,a[2]&4294901760|a[3]&65535,a[1]<<16|a[1]>>>16,a[3]&4294901760|a[0]&65535];this._b=0;for(var f=0;f<4;f++)C.call(this);for(var f=0;f<8;f++)l[f]^=c[f+4&7];if(u){var B=u.words,i=B[0],x=B[1],d=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360,g=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,D=d>>>16|g&4294901760,_=g<<16|d&65535;l[0]^=d,l[1]^=D,l[2]^=g,l[3]^=_,l[4]^=d,l[5]^=D,l[6]^=g,l[7]^=_;for(var f=0;f<4;f++)C.call(this)}},_doProcessBlock:function(a,u){var c=this._X;C.call(this),p[0]=c[0]^c[5]>>>16^c[3]<<16,p[1]=c[2]^c[7]>>>16^c[5]<<16,p[2]=c[4]^c[1]>>>16^c[7]<<16,p[3]=c[6]^c[3]>>>16^c[1]<<16;for(var l=0;l<4;l++)p[l]=(p[l]<<8|p[l]>>>24)&16711935|(p[l]<<24|p[l]>>>8)&4278255360,a[u+l]^=p[l]},blockSize:128/32,ivSize:64/32});function C(){for(var a=this._X,u=this._C,c=0;c<8;c++)A[c]=u[c];u[0]=u[0]+1295307597+this._b|0,u[1]=u[1]+3545052371+(u[0]>>>0<A[0]>>>0?1:0)|0,u[2]=u[2]+886263092+(u[1]>>>0<A[1]>>>0?1:0)|0,u[3]=u[3]+1295307597+(u[2]>>>0<A[2]>>>0?1:0)|0,u[4]=u[4]+3545052371+(u[3]>>>0<A[3]>>>0?1:0)|0,u[5]=u[5]+886263092+(u[4]>>>0<A[4]>>>0?1:0)|0,u[6]=u[6]+1295307597+(u[5]>>>0<A[5]>>>0?1:0)|0,u[7]=u[7]+3545052371+(u[6]>>>0<A[6]>>>0?1:0)|0,this._b=u[7]>>>0<A[7]>>>0?1:0;for(var c=0;c<8;c++){var l=a[c]+u[c],f=l&65535,B=l>>>16,i=((f*f>>>17)+f*B>>>15)+B*B,x=((l&4294901760)*l|0)+((l&65535)*l|0);t[c]=i^x}a[0]=t[0]+(t[7]<<16|t[7]>>>16)+(t[6]<<16|t[6]>>>16)|0,a[1]=t[1]+(t[0]<<8|t[0]>>>24)+t[7]|0,a[2]=t[2]+(t[1]<<16|t[1]>>>16)+(t[0]<<16|t[0]>>>16)|0,a[3]=t[3]+(t[2]<<8|t[2]>>>24)+t[1]|0,a[4]=t[4]+(t[3]<<16|t[3]>>>16)+(t[2]<<16|t[2]>>>16)|0,a[5]=t[5]+(t[4]<<8|t[4]>>>24)+t[3]|0,a[6]=t[6]+(t[5]<<16|t[5]>>>16)+(t[4]<<16|t[4]>>>16)|0,a[7]=t[7]+(t[6]<<8|t[6]>>>24)+t[5]|0}e.RabbitLegacy=v._createHelper(o)}(),r.RabbitLegacy})}(pe)),pe.exports}var Ae={exports:{}},At;function Cn(){return At||(At=1,function(n,s){(function(r,e,h){n.exports=e(N(),p0(),A0(),h0(),X())})(U,function(r){return function(){var e=r,h=e.lib,v=h.BlockCipher,m=e.algo;const p=16,A=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],t=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var o={pbox:[],sbox:[]};function C(f,B){let i=B>>24&255,x=B>>16&255,d=B>>8&255,g=B&255,D=f.sbox[0][i]+f.sbox[1][x];return D=D^f.sbox[2][d],D=D+f.sbox[3][g],D}function a(f,B,i){let x=B,d=i,g;for(let D=0;D<p;++D)x=x^f.pbox[D],d=C(f,x)^d,g=x,x=d,d=g;return g=x,x=d,d=g,d=d^f.pbox[p],x=x^f.pbox[p+1],{left:x,right:d}}function u(f,B,i){let x=B,d=i,g;for(let D=p+1;D>1;--D)x=x^f.pbox[D],d=C(f,x)^d,g=x,x=d,d=g;return g=x,x=d,d=g,d=d^f.pbox[1],x=x^f.pbox[0],{left:x,right:d}}function c(f,B,i){for(let _=0;_<4;_++){f.sbox[_]=[];for(let F=0;F<256;F++)f.sbox[_][F]=t[_][F]}let x=0;for(let _=0;_<p+2;_++)f.pbox[_]=A[_]^B[x],x++,x>=i&&(x=0);let d=0,g=0,D=0;for(let _=0;_<p+2;_+=2)D=a(f,d,g),d=D.left,g=D.right,f.pbox[_]=d,f.pbox[_+1]=g;for(let _=0;_<4;_++)for(let F=0;F<256;F+=2)D=a(f,d,g),d=D.left,g=D.right,f.sbox[_][F]=d,f.sbox[_][F+1]=g;return!0}var l=m.Blowfish=v.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var f=this._keyPriorReset=this._key,B=f.words,i=f.sigBytes/4;c(o,B,i)}},encryptBlock:function(f,B){var i=a(o,f[B],f[B+1]);f[B]=i.left,f[B+1]=i.right},decryptBlock:function(f,B){var i=u(o,f[B],f[B+1]);f[B]=i.left,f[B+1]=i.right},blockSize:64/32,keySize:128/32,ivSize:64/32});e.Blowfish=v._createHelper(l)}(),r.Blowfish})}(Ae)),Ae.exports}(function(n,s){(function(r,e,h){n.exports=e(N(),H0(),Zr(),Mr(),p0(),Yr(),A0(),Kt(),Ce(),Qr(),Xt(),jr(),Jr(),en(),Be(),tn(),h0(),X(),rn(),nn(),an(),on(),xn(),sn(),cn(),un(),dn(),fn(),ln(),vn(),hn(),pn(),An(),En(),Cn())})(U,function(r){return r})})(qt);var Et=qt.exports;const{store:k0}=pragma,{utils:Bn}=pragma,{signIn:Dn,getIdProviderConfig:gn,link:_n,getRedirectUri:Zt,getAuthenticateBaseUri:Rn}=pragma.auth,Q={PROVIDER_ID:"DISCORD",SIGNIN_REDIRECT_PATH:"/redirect/SignInDiscord",LINK_REDIRECT_PATH:"/redirect/LinkDiscord",AUTHORIZE_URL:"https://discord.com/oauth2/authorize",BACKEND_REDIRECT:"/v1/account/discord-redirect"},mn=async n=>await Dn({providerId:Q.PROVIDER_ID,providerToken:n}),Fn=async n=>await _n({providerId:Q.PROVIDER_ID,providerToken:n}),Ct=n=>{const s=yn(128),r=bn(s),e=gn(Q.PROVIDER_ID),h=new URLSearchParams({client_id:e.clientId,code_challenge:r,code_challenge_method:"S256",redirect_uri:n,scope:"identify email guilds",response_type:"code"}),v=`${Q.AUTHORIZE_URL}?${h}`;k0.set("$discordCodeVerifier",s),Bn.goToUrl(v)},In=async n=>{const s=k0.get("$discordCodeVerifier");if(k0.set("$discordCodeVerifier",null),!s)return{ok:!1,error:"Invalid Discord verifier."};const r=await Mt(n,s,Zt(Q.SIGNIN_REDIRECT_PATH));return r?await mn(r):{ok:!1,error:"Invalid code."}},kn=async n=>{const s=k0.get("$discordCodeVerifier");if(k0.set("$discordCodeVerifier",null),!s)return{ok:!1,error:"Invalid Discord verifier."};const r=await Mt(n,s,Zt(Q.LINK_REDIRECT_PATH));return r?await Fn(r):{ok:!1,error:"Invalid code."}},Mt=async(n,s,r)=>{const e=new URLSearchParams({code:n,code_verifier:s,redirect_uri:r}),h={method:"get"},v=`${Rn(Q.BACKEND_REDIRECT)}?${e.toString()}`;try{return(await f0(v,h)).data.access_token}catch(m){return console.error(m),""}},yn=n=>{let s="";const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";for(let e=0;e<n;e++)s+=r.charAt(Math.floor(Math.random()*r.length));return s},bn=n=>Et.SHA256(n).toString(Et.enc.Base64).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),{Icon:Sn,IdProviderAuthResultHandler:Pn}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Tn}=pragma.auth,Hn={name:"SignInDiscord",components:{Icon:Sn,IdProviderAuthResultHandler:Pn},emits:["success"],setup(n,{emit:s}){const r=K();return Tn(In,r),{Discord:Q,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Discord.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},wn=pragma.ui.helpers.eventEmitter,Ln=pragma.auth.initiateLinkAndRedirectWithCode,Un={name:"LinkDiscord",components:{},emits:["linkResult"],setup(n,{emit:s}){return Ln(kn,"Discord",s,wn),{}},template:"<div/>"},{getRedirectUri:Bt}=pragma.auth,$n={id:Q.PROVIDER_ID,name:"Discord",iconName:"discord",enumValue:3,onSignInButtonClick:({router:n})=>{Ct(Bt(Q.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Ct(Bt(Q.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInDiscord",path:Q.SIGNIN_REDIRECT_PATH,component:Hn}],registeredLinkRoutes:[{name:"LinkDiscord",path:Q.LINK_REDIRECT_PATH,component:Un}],routerBeforeHook:({router:n})=>!1},{store:y0}=pragma,{utils:Nn}=pragma,{signIn:On,link:zn,getIdProviderConfig:Wn,getAuthenticateBaseUri:Gn,getRedirectUri:Yt}=pragma.auth,j={SIGNIN_REDIRECT_PATH:"/redirect/SignInEpic",LINK_REDIRECT_PATH:"/redirect/LinkEpic",EPIC_AUTHORIZE_URL:"https://www.epicgames.com/id/authorize",BACKEND_REDIRECT:"/v1/account/oauth-redirect/epic",PROVIDER_ID:"EPIC"},Vn=async n=>await On({providerId:j.PROVIDER_ID,providerToken:n}),qn=async n=>await zn({providerId:j.PROVIDER_ID,providerToken:n}),Dt=n=>{const s=Zn(128),r=Wn(j.PROVIDER_ID),e=["basic_profile","presence","friends_list"],h=new URLSearchParams({client_id:r.clientId,state:s,redirect_uri:n,scope:e.join(" "),response_type:"code"}),v=`${j.EPIC_AUTHORIZE_URL}?${h}`;y0.set("$epicStateVerifier",s),Nn.goToUrl(v)},Kn=async n=>{const s=y0.get("$epicStateVerifier");if(y0.set("$epicStateVerifier",null),!s)return{ok:!1,error:"Invalid Epic verifier."};const r=await Qt(n,s,Yt(j.SIGNIN_REDIRECT_PATH));return r?await Vn(r):{ok:!1,error:"Invalid code."}},Xn=async n=>{const s=y0.get("$epicStateVerifier");if(y0.set("$epicStateVerifier",null),!s)return{ok:!1,error:"Invalid Epic verifier."};const r=await Qt(n,s,Yt(j.LINK_REDIRECT_PATH));return r?await qn(r):{ok:!1,error:"Invalid code."}},Qt=async(n,s,r)=>{const e=new URLSearchParams({code:n,code_verifier:s,redirect_uri:r}),h={method:"get"},v=`${Gn(j.BACKEND_REDIRECT)}?${e.toString()}`;try{return(await f0(v,h)).data.access_token}catch(m){return console.error(m),""}},Zn=n=>{let s="";const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";for(let e=0;e<n;e++)s+=r.charAt(Math.floor(Math.random()*r.length));return s},{Icon:Mn,IdProviderAuthResultHandler:Yn}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Qn}=pragma.auth,jn={name:"SignInEpic",components:{Icon:Mn,IdProviderAuthResultHandler:Yn},emits:["success"],setup(n,{emit:s}){const r=K();return Qn(Kn,r),{Epic:j,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Epic.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},Jn=pragma.ui.helpers.eventEmitter,ea=pragma.auth.initiateLinkAndRedirectWithCode,ta={name:"LinkEpic",components:{},emits:["linkResult"],setup(n,{emit:s}){return ea(Xn,"Epic",s,Jn),{}},template:"<div/>"},{getRedirectUri:gt}=pragma.auth,ra={id:j.PROVIDER_ID,name:"Epic",iconName:"epic",enumValue:2,onSignInButtonClick:({router:n})=>{Dt(gt(j.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Dt(gt(j.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInEpic",path:j.SIGNIN_REDIRECT_PATH,component:jn}],registeredLinkRoutes:[{name:"LinkEpic",path:j.LINK_REDIRECT_PATH,component:ta}],routerBeforeHook:({router:n})=>!1},{utils:na}=pragma,{signIn:aa,link:oa,getIdProviderConfig:ia,getAuthenticateBaseUri:xa,getRedirectUri:jt}=pragma.auth,a0={GOOGLE_OAUTH_URL:"https://accounts.google.com/o/oauth2/v2/auth",SIGNIN_REDIRECT_PATH:"/redirect/SignInGoogle",LINK_REDIRECT_PATH:"/redirect/LinkGoogle",PROVIDER_ID:"GOOGLE",BACKEND_REDIRECT:"/v1/account/oauth-redirect/google"},sa=async n=>await aa({providerId:a0.PROVIDER_ID,providerToken:n}),ca=async n=>await oa({providerId:a0.PROVIDER_ID,providerToken:n}),_t=n=>{const s=ia(a0.PROVIDER_ID),r=new URLSearchParams({client_id:s.clientId,redirect_uri:n,scope:["https://www.googleapis.com/auth/userinfo.profile","https://www.googleapis.com/auth/userinfo.email"].join(" "),response_type:"code",access_type:"offline",prompt:"consent"}),e=`${a0.GOOGLE_OAUTH_URL}?${r}`;na.goToUrl(e)},ua=async n=>{const s=await Jt(n,jt(a0.SIGNIN_REDIRECT_PATH));return s?await sa(s):{ok:!1,error:"Invalid code."}},da=async n=>{const s=await Jt(n,jt(a0.LINK_REDIRECT_PATH));return s?await ca(s):{ok:!1,error:"Invalid code."}},Jt=async(n,s)=>{const r=new URLSearchParams({code:n,redirect_uri:s}),e={method:"get"},h=`${xa(a0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await f0(h,e)).data.access_token}catch(v){console.error(v);return}},{Icon:fa,IdProviderAuthResultHandler:la}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:va}=pragma.auth,ha={name:"SignInGoogle",components:{Icon:fa,IdProviderAuthResultHandler:la},emits:["success"],setup(n,{emit:s}){const r=K();return va(ua,r),{Google:a0,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Google.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},pa=pragma.ui.helpers.eventEmitter,Aa=pragma.auth.initiateLinkAndRedirectWithCode,Ea={name:"LinkGoogle",components:{},emits:["linkResult"],setup(n,{emit:s}){return Aa(da,"Google",s,pa),{}},template:"<div/>"},{utils:Rt}=pragma,{getRedirectUri:mt}=pragma.auth,Ca={id:a0.PROVIDER_ID,name:"Google",iconName:"google",enumValue:6,onSignInButtonClick:({router:n})=>{_t(mt(a0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{_t(mt(a0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInGoogle",path:"/signin-google",component:ha}],registeredLinkRoutes:[{name:"LinkGoogle",path:"/link-google",component:Ea}],routerBeforeHook:({router:n})=>{let s=Rt.getWindowLocation().href;if(s.search("#/access_token=")>=0)return s=s.replace("#/access_token=","&access_token="),Rt.goToUrl(s),!0}},{utils:Ba}=pragma,{signIn:Da,link:ga,getIdProviderConfig:_a,getAuthenticateBaseUri:Ra,getRedirectUri:er}=pragma.auth,o0={GOOGLE_OAUTH_URL:"https://accounts.google.com/o/oauth2/v2/auth",SIGNIN_REDIRECT_PATH:"/redirect/SignInGoogleWorkspace",LINK_REDIRECT_PATH:"/redirect/LinkGoogleWorkspace",PROVIDER_ID:"GOOGLEWORKSPACE",BACKEND_REDIRECT:"/v1/account/oauth-redirect/googleworkspace"},ma=async n=>await Da({providerId:o0.PROVIDER_ID,providerToken:n}),Fa=async n=>await ga({providerId:o0.PROVIDER_ID,providerToken:n}),Ft=n=>{const s=_a(o0.PROVIDER_ID),r=new URLSearchParams({client_id:s.clientId,redirect_uri:n,scope:["https://www.googleapis.com/auth/userinfo.profile","https://www.googleapis.com/auth/userinfo.email"].join(" "),response_type:"code",access_type:"offline",prompt:"consent"}),e=`${o0.GOOGLE_OAUTH_URL}?${r}`;Ba.goToUrl(e)},Ia=async n=>{const s=await tr(n,er(o0.SIGNIN_REDIRECT_PATH));return s?await ma(s):{ok:!1,error:"Invalid code."}},ka=async n=>{const s=await tr(n,er(o0.LINK_REDIRECT_PATH));return s?await Fa(s):{ok:!1,error:"Invalid code."}},tr=async(n,s)=>{const r=new URLSearchParams({code:n,redirect_uri:s}),e={method:"get"},h=`${Ra(o0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await f0(h,e)).data.access_token}catch(v){console.error(v);return}},{Icon:ya,IdProviderAuthResultHandler:ba}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Sa}=pragma.auth,Pa={name:"SignInGoogleWorkspace",components:{Icon:ya,IdProviderAuthResultHandler:ba},emits:["success"],setup(n,{emit:s}){const r=K();return Sa(Ia,r),{GoogleWorkspace:o0,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='GoogleWorkspace.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},Ta=pragma.ui.helpers.eventEmitter,Ha=pragma.auth.initiateLinkAndRedirectWithCode,wa={name:"LinkGoogleWorkspace",components:{},emits:["linkResult"],setup(n,{emit:s}){return Ha(ka,"Google Workspace",s,Ta),{}},template:"<div/>"},It=pragma.utils,{getRedirectUri:kt}=pragma.auth,La={id:o0.PROVIDER_ID,name:"Google Workspace",iconName:"google",enumValue:12,onSignInButtonClick:({router:n})=>{Ft(kt(o0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Ft(kt(o0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInGoogleWorkspace",path:"/signin-googleworkspace",component:Pa}],registeredLinkRoutes:[{name:"LinkGoogleWorkspace",path:"/link-googleworkspace",component:wa}],routerBeforeHook:({router:n})=>{let s=It.getWindowLocation().href;if(s.search("#/access_token=")>=0)return s=s.replace("#/access_token=","&access_token="),It.goToUrl(s),!0}},{utils:Ua}=pragma,{signIn:$a,link:Na,getIdProviderConfig:Oa,getAuthenticateBaseUri:za,getRedirectUri:rr}=pragma.auth,s0={SIGNIN_REDIRECT_PATH:"/redirect/SignInOkta",LINK_REDIRECT_PATH:"/redirect/LinkOkta",PROVIDER_ID:"OKTA",BACKEND_REDIRECT:"/v1/account/oauth-redirect/okta"},Wa=async n=>await $a({providerId:s0.PROVIDER_ID,providerToken:n}),Ga=async n=>await Na({providerId:s0.PROVIDER_ID,providerToken:n}),yt=n=>{const s=Oa(s0.PROVIDER_ID),r=new URLSearchParams({client_id:s.clientId,redirect_uri:n,scope:["openid","profile","email"].join(" "),response_type:"code",state:"login"}),e=`${s.authorizationUri}?${r}`;Ua.goToUrl(e)},Va=async n=>{const s=await nr(n,rr(s0.SIGNIN_REDIRECT_PATH));return s?await Wa(s):{ok:!1,error:"Invalid code."}},qa=async n=>{const s=await nr(n,rr(s0.LINK_REDIRECT_PATH));return s?await Ga(s):{ok:!1,error:"Invalid code."}},nr=async(n,s)=>{const r=new URLSearchParams({code:n,redirect_uri:s}),e={method:"get"},h=`${za(s0.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await f0(h,e)).data.access_token}catch(v){console.error(v);return}},{Icon:Ka,IdProviderAuthResultHandler:Xa}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Za}=pragma.auth,Ma={name:"SignInOkta",components:{Icon:Ka,IdProviderAuthResultHandler:Xa},emits:["success"],setup(n,{emit:s}){const r=K();return Za(Va,r),{Okta:s0,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Okta.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},Ya=pragma.ui.helpers.eventEmitter,Qa=pragma.auth.initiateLinkAndRedirectWithCode,ja={name:"LinkOkta",components:{},emits:["linkResult"],setup({emit:n}){return Qa(qa,"Okta",n,Ya),{}},template:"<div/>"},bt=pragma.utils,{getRedirectUri:St}=pragma.auth,Ja={id:s0.PROVIDER_ID,name:"Okta",iconName:"okta",enumValue:10,onSignInButtonClick:({router:n})=>{yt(St(s0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{yt(St(s0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInOkta",path:"/signin-okta",component:Ma}],registeredLinkRoutes:[{name:"LinkOkta",path:"/link-okta",component:ja}],routerBeforeHook:({router:n})=>{let s=bt.getWindowLocation().href;if(s.search("#/access_token=")>=0)return s=s.replace("#/access_token=","&access_token="),bt.goToUrl(s),!0}},{signIn:eo,link:to}=pragma.auth,{utils:Pt}=pragma,u0={STEAM_OPENID_LOGIN_URL:"https://steamcommunity.com/openid/login",SIGNIN_REDIRECT_PATH:"/redirect/SignInSteam",LINK_REDIRECT_PATH:"/redirect/LinkSteam",PROVIDER_ID:"STEAM"};function Tt(n){const s=new URLSearchParams({"openid.mode":"checkid_setup","openid.ns":"http://specs.openid.net/auth/2.0","openid.identity":"http://specs.openid.net/auth/2.0/identifier_select","openid.claimed_id":"http://specs.openid.net/auth/2.0/identifier_select","openid.return_to":n,"openid.realm":Pt.getWindowLocation().origin});Pt.goToUrl(`${u0.STEAM_OPENID_LOGIN_URL}?${s}`)}const ro=async n=>await eo({providerId:u0.PROVIDER_ID,providerToken:n}),no=async n=>await to({providerId:u0.PROVIDER_ID,providerToken:n});async function ao(n,s,r,e,h,v,m,p,A,t){const o=JSON.stringify({claimedId:e,ns:n,mode:s,opEndpoint:r,identity:h,returnTo:v,responseNonce:m,assocHandle:p,signed:A,sig:t});return await ro(o)}async function oo(n,s,r,e,h,v,m,p,A,t){const o=JSON.stringify({claimedId:e,ns:n,mode:s,opEndpoint:r,identity:h,returnTo:v,responseNonce:m,assocHandle:p,signed:A,sig:t});return await no(o)}const{Icon:io,IdProviderAuthResultHandler:xo}=pragma.ui.components,{utils:so}=pragma,{handleAuthResponse:co}=pragma.auth,i0=(n,s,r)=>n.query[r]||s.get(r),uo={name:"SignInSteam",components:{Icon:io,IdProviderAuthResultHandler:xo},emits:["success"],setup(n,{emit:s}){const r=zt(),e=so.getRawUrlQuery();let h=i0(r,e,"openid.ns"),v=i0(r,e,"openid.mode"),m=i0(r,e,"openid.op_endpoint"),p=i0(r,e,"openid.claimed_id"),A=i0(r,e,"openid.identity"),t=i0(r,e,"openid.return_to"),o=i0(r,e,"openid.response_nonce"),C=i0(r,e,"openid.assoc_handle"),a=i0(r,e,"openid.signed"),u=i0(r,e,"openid.sig");const c=K();return ao(h,v,m,p,A,t,o,C,a,u).then(l=>{c.value=co(l)}),{Steam:u0,authResultRef:c,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Steam.PROVIDER_ID'
        :authResult='authResultRef'
        :canRetrySignIn='false'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},{utils:fo}=pragma,lo=pragma.ui.helpers.eventEmitter,vo=pragma.auth.handleLinkResponse,x0=(n,s)=>n.query[s]||fo.getRawUrlParam(s),ho={name:"LinkSteam",components:{},emits:["linkResult"],setup(n,{emit:s}){Wt().push({name:"LinkedAccounts"});const e=zt();let h=x0(e,"openid.ns"),v=x0(e,"openid.mode"),m=x0(e,"openid.op_endpoint"),p=x0(e,"openid.claimed_id"),A=x0(e,"openid.identity"),t=x0(e,"openid.return_to"),o=x0(e,"openid.response_nonce"),C=x0(e,"openid.assoc_handle"),a=x0(e,"openid.signed"),u=x0(e,"openid.sig");return oo(h,v,m,p,A,t,o,C,a,u).then(c=>{vo(c,"Steam",s,lo)}),{}},template:"<div/>"},{getRedirectUri:Ht}=pragma.auth,po={id:u0.PROVIDER_ID,name:"Steam",iconName:"steam",enumValue:4,onSignInButtonClick:({router:n})=>{Tt(Ht(u0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{Tt(Ht(u0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInSteam",path:u0.SIGNIN_REDIRECT_PATH,component:uo}],registeredLinkRoutes:[{name:"LinkSteam",path:u0.LINK_REDIRECT_PATH,component:ho}],routerBeforeHook:({router:n})=>!1},{utils:Ao}=pragma,{signIn:Eo,link:Co,getIdProviderConfig:Bo,getRedirectUri:ar,getAuthenticateBaseUri:Do}=pragma.auth,J={PROVIDER_ID:"TWITCH",SIGNIN_REDIRECT_PATH:"/redirect/SignInTwitch",LINK_REDIRECT_PATH:"/redirect/LinkTwitch",TWITCH_AUTHORIZE_URL:"https://id.twitch.tv/oauth2/authorize",BACKEND_REDIRECT:"/v1/account/twitch-redirect"},go=async n=>await Eo({providerId:J.PROVIDER_ID,providerToken:n}),_o=async n=>await Co({providerId:J.PROVIDER_ID,providerToken:n}),wt=n=>{const s=Bo(J.PROVIDER_ID),r=new URLSearchParams({client_id:s.clientId,redirect_uri:n,scope:"",response_type:"code"}),e=`${J.TWITCH_AUTHORIZE_URL}?${r}`;Ao.goToUrl(e)},Ro=async n=>{const s=await or(n,ar(J.SIGNIN_REDIRECT_PATH));return s?await go(s):{ok:!1,error:"Invalid code."}},mo=async n=>{const s=await or(n,ar(J.LINK_REDIRECT_PATH));return s?await _o(s):{ok:!1,error:"Invalid code."}},or=async(n,s)=>{const r=new URLSearchParams({code:n,redirect_uri:s}),e={method:"get"},h=`${Do(J.BACKEND_REDIRECT)}?${r.toString()}`;try{return(await f0(h,e)).data.access_token}catch(v){console.error(v);return}},{Icon:Fo,IdProviderAuthResultHandler:Io}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:ko}=pragma.auth,yo={name:"SignInTwitch",components:{Icon:Fo,IdProviderAuthResultHandler:Io},emits:["success"],setup(n,{emit:s}){const r=K();return ko(Ro,r),{Twitch:J,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Twitch.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},bo=pragma.ui.helpers.eventEmitter,So=pragma.auth.initiateLinkAndRedirectWithCode,Po={name:"LinkTwitch",components:{},emits:["linkResult"],setup(n,{emit:s}){return So(mo,"Twitch",s,bo),{}},template:"<div/>"},{getRedirectUri:Lt}=pragma.auth,To={id:J.PROVIDER_ID,name:"Twitch",iconName:"twitch",enumValue:8,onSignInButtonClick:({router:n})=>{wt(Lt(J.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:({router:n})=>{wt(Lt(J.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInTwitch",path:J.SIGNIN_REDIRECT_PATH,component:yo}],registeredLinkRoutes:[{name:"LinkTwitch",path:J.LINK_REDIRECT_PATH,component:Po}],routerBeforeHook:({router:n})=>!1},{authenticatePragmaUnsafe:Ho,link:wo}=pragma.auth,De={PROVIDER_ID:"UNSAFE"},ir=async n=>Ho(n),Lo=async n=>await wo({providerId:De.PROVIDER_ID,providerToken:JSON.stringify({accountId:n,displayName:n})}),{handleAuthResponse:Uo}=pragma.auth,{Icon:$o,IdProviderAuthResultHandler:No}=pragma.ui.components,Oo={name:"SignInUnsafe",components:{Icon:$o,IdProviderAuthResultHandler:No},emits:["cancel","success"],setup(n,{emit:s}){const r=Ee({username:""}),e=K(""),h=K(!1),v=K();return{onFinish:async p=>{const A=p.username.trim();if(e.value="",!A)return e.value="Username is required.";h.value=!0;const t=await ir(A);h.value=!1,v.value=Uo(t)},formState:r,errorMessage:e,disabled:h,Unsafe:De,authResultRef:v,emitSuccess:()=>s("success")}},template:`
    <IdProviderAuthResultHandler
      v-if='authResultRef'
      :idProviderType='Unsafe.PROVIDER_ID'
      :authResult='authResultRef'
      @success='emitSuccess'
    />
    <div v-else>
    <h5 class='header'>
      Sign in to your Pragma Portal account using Pragma Unsafe provider
    </h5>
    <a-form layout='vertical' @finish='onFinish' :model='formState' class="form-sign-in-unsafe">
      <a-form-item name='username' label='Username'
                   :help='errorMessage'
                   :validateStatus="errorMessage && 'error' || ''"
                   class='required'
      >
        <a-input autoFocus size='large' v-model:value='formState.username'></a-input>
      </a-form-item>
      <a-form-item>
        <a-button type='primary' html-type='submit' size='large' :block='true' :disabled='disabled'>
          Sign In
        </a-button>
      </a-form-item>
    </a-form>

    <router-link :to="{name: 'SignIn'}">
      <Icon name='arrowLeft' align='-2' /> &nbsp; Back to all sign in options
    </router-link>
    </div>
  `},{handleAuthResponse:zo}=pragma.auth,{Icon:Wo,IdProviderAuthResultHandler:Go}=pragma.ui.components,Vo={name:"SignUpUnsafe",components:{Icon:Wo,IdProviderAuthResultHandler:Go},emits:["cancel","success"],setup(n,{emit:s}){const r=Ee({username:""}),e=K(""),h=K(!1),v=K();return{onFinish:async p=>{const A=p.username.trim();if(e.value="",!A)return e.value="Username is required.";h.value=!0;const t=await ir(A);h.value=!1,v.value=zo(t)},formState:r,errorMessage:e,disabled:h,Unsafe:De,authResultRef:v,emitSuccess:()=>s("success")}},template:`
    <IdProviderAuthResultHandler
      v-if='authResultRef'
      :idProviderType='Unsafe.PROVIDER_ID'
      :authResult='authResultRef'
      @success='emitSuccess'
    />
    <div v-else>
    <h5 class='header'>
      Create your Pragma Portal account using Pragma Unsafe provider
    </h5>
    <a-form layout='vertical' @finish='onFinish' :model='formState'>
      <a-form-item name='username' label='Username'
                   :help='errorMessage'
                   :validateStatus="errorMessage && 'error' || ''"
                   class='required'
      >
        <a-input autoFocus size='large' v-model:value='formState.username'></a-input>
      </a-form-item>
      <a-form-item>
        <a-button type='primary' html-type='submit' size='large' :block='true' :disabled='disabled'>
          Sign Up
        </a-button>
      </a-form-item>
    </a-form>

    <router-link  :to="{name: 'CreateAccount'}">
      <Icon name='arrowLeft' align='-2' /> &nbsp; Back to all sign up options
    </router-link>
    </div>
  `},{store:qo}=pragma,{Icon:Ko}=pragma.ui.components,Xo=pragma.ui.helpers.eventEmitter,Zo=pragma.utils,Mo={name:"LinkUnsafe",components:{Icon:Ko,BackendInfo:Fr},emits:["linkResult"],setup(n,{emit:s}){const r=Ee({username:""}),e=K(""),h=K(!1),v=Wt();return Zo.capitalizeFirstLetter(qo.get("appInfo.backendMode").toLowerCase()),{onFinish:p=>{const A=p.username.trim();if(e.value="",!A)return e.value="Username is required.";h.value=!0,Lo(A).then(t=>{if(h.value=!1,!t.ok)return e.value="Something went wrong. Please try again.";v.push({name:"LinkedAccounts"}),s("linkResult",t,"Unsafe"),Xo.emit("reloadData")})},formState:r,errorMessage:e,disabled:h}},template:`
    <div class='LinkUnsafe auth-container'>
      <Icon name='logoPortal' size='30' class='logo' />
      <a-card :bordered="false" class='card card-auth'>
        <div>
          <a-space direction='vertical' size='middle' align='center' class='alert'>
            <BackendInfo />
          </a-space>
          
          <div>
          <h5 class='header'>
            Link your Pragma Portal account using Pragma Unsafe provider
          </h5>
          <a-form layout='vertical' @finish='onFinish' :model='formState'>
            <a-form-item name='username' label='Username'
              :help='errorMessage'
              :validateStatus="errorMessage && 'error' || ''"
              class='required'
            >
              <a-input autoFocus size='large' v-model:value='formState.username'></a-input>
            </a-form-item>
            <a-form-item>
              <a-button type='primary' html-type='submit' size='large' :block='true' :disabled='disabled'>
                Link
              </a-button>
            </a-form-item>
          </a-form>
          </div>
          <router-link :to="{name: 'LinkedAccounts'}"><Icon name='arrowLeft' align='-2' /> &nbsp; Back to all link options</router-link>
        </div>
      </a-card>
    </div>
  `},{isCreateAccountUrl:Yo}=pragma.auth,Qo={id:"UNSAFE",name:"Pragma Unsafe",iconName:"unlock",enumValue:1,onSignInButtonClick:({router:n})=>{Yo()?n.push({name:"SignUpUnsafe"}):n.push({name:"SignInUnsafe"})},onLinkButtonClick:({router:n})=>{n.push({name:"LinkUnsafe"})},registeredRoutes:[{name:"SignInUnsafe",path:"/signin-unsafe",component:Oo},{name:"SignUpUnsafe",path:"/signup-unsafe",component:Vo}],registeredLinkRoutes:[{name:"LinkUnsafe",path:"/link-unsafe",component:Mo}],routerBeforeHook:({router:n})=>!1,position:0},{utils:jo}=pragma,{signIn:Jo,link:ei,getIdProviderConfig:xr,getAuthenticateBaseUri:ti,getRedirectUri:sr}=pragma.auth,e0={PLAYSTATION4_AUTHORIZE_URL:"/api/v1/oauth/authorize",PLAYSTATION5_AUTHORIZE_URL:"/api/authz/v3/oauth/authorize",PROVIDER_ID:"PLAYSTATION",BACKEND_REDIRECT:"/v1/account/psn-redirect",SIGNIN_REDIRECT_PATH:"/redirect/SignInPlaystation",LINK_REDIRECT_PATH:"/redirect/LinkPlaystation"},ri=async n=>await Jo({providerId:e0.PROVIDER_ID,providerToken:n}),ni=async n=>await ei({providerId:e0.PROVIDER_ID,providerToken:n}),Ut=n=>{const s=xr(e0.PROVIDER_ID);let r={service_entity:"urn:service-entity:psn",client_id:s.clientId,redirect_uri:n,scope:"psn:s2s",response_type:"code"},e=`https://${s.authorizeHost}${e0.PLAYSTATION4_AUTHORIZE_URL}`;s.loginProvider==="ps5"&&(e=`https://${s.authorizeHost}${e0.PLAYSTATION5_AUTHORIZE_URL}`,r.scope="psn:s2s openid id_token:psn.basic_claims");const h=`${e}?${new URLSearchParams(r)}`;jo.goToUrl(h)},ai=async n=>{const s=await cr(n,sr(e0.SIGNIN_REDIRECT_PATH));return await ri(s)},oi=async n=>{const s=await cr(n,sr(e0.LINK_REDIRECT_PATH));return await ni(s)},cr=async(n,s)=>{const r=xr(e0.PROVIDER_ID),e=new URLSearchParams({code:n,redirect_uri:s}),h={method:"get"},v=`${ti(e0.BACKEND_REDIRECT)}?${e.toString()}`;try{const m=await f0(v,h);return r.loginProvider=="ps5"?m.data.idToken:m.data.accessToken}catch(m){return console.error(m),""}},{Icon:ii,IdProviderAuthResultHandler:xi}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:si}=pragma.auth,ci={name:"SignInPlaystation",components:{Icon:ii,IdProviderAuthResultHandler:xi},emits:["success"],setup(n,{emit:s}){const r=K();return si(ai,r),{Playstation:e0,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Playstation.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},ui=pragma.ui.helpers.eventEmitter,di=pragma.auth.initiateLinkAndRedirectWithCode,fi={name:"LinkPlaystation",components:{},emits:["linkResult"],setup(n,{emit:s}){return di(oi,"PlayStation",s,ui),{}},template:"<div/>"},{getRedirectUri:$t}=pragma.auth,li={id:e0.PROVIDER_ID,name:"Playstation",iconName:"playstation",enumValue:7,onSignInButtonClick:({router:n})=>{Ut($t(e0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:()=>{Ut($t(e0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInPlaystation",path:"/signin-playstation",component:ci}],registeredLinkRoutes:[{name:"LinkPlaystation",path:"/link-playstation",component:fi}],routerBeforeHook:({router:n})=>!1},{store:b0}=pragma,{utils:vi}=pragma,{signIn:hi,link:pi,getIdProviderConfig:Ai,getAuthenticateBaseUri:Ei,getRedirectUri:ur}=pragma.auth,c0={SIGNIN_REDIRECT_PATH:"/redirect/SignInXbox",LINK_REDIRECT_PATH:"/redirect/LinkXbox",XBOX_AUTHORIZE_URL:"https://login.live.com/oauth20_authorize.srf",PROVIDER_ID:"XBOX",BACKEND_REDIRECT:"/v1/account/oauth-redirect/xbox"},Ci=async n=>await hi({providerId:c0.PROVIDER_ID,providerToken:n}),Bi=async n=>await pi({providerId:c0.PROVIDER_ID,providerToken:n}),Nt=n=>{const s=_i(128),r=Ai(c0.PROVIDER_ID),e=new URLSearchParams({client_id:r.clientId,state:s,redirect_uri:n,scope:"Xboxlive.signin Xboxlive.offline_access",response_type:"code",prompt:"select_account"}),h=`${c0.XBOX_AUTHORIZE_URL}?${e}`;b0.set("$xboxStateVerifier",s),vi.goToUrl(h)},Di=async n=>{const s=b0.get("$xboxStateVerifier");if(b0.set("$xboxStateVerifier",null),!s)return{ok:!1,error:"Invalid Xbox verifier."};const r=await dr(n,s,ur(c0.SIGNIN_REDIRECT_PATH));return r?await Ci(r):{ok:!1,error:"Invalid code."}},gi=async n=>{const s=b0.get("$xboxStateVerifier");if(b0.set("$xboxStateVerifier",null),!s)return{ok:!1,error:"Invalid Xbox verifier."};const r=await dr(n,s,ur(c0.LINK_REDIRECT_PATH));return r?await Bi(r):{ok:!1,error:"Invalid code."}},dr=async(n,s,r)=>{const e=new URLSearchParams({code:n,code_verifier:s,redirect_uri:r}),h={method:"get"},v=`${Ei(c0.BACKEND_REDIRECT)}?${e.toString()}`;try{return(await f0(v,h)).data.access_token}catch(m){return console.error(m),""}},_i=n=>{let s="";const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";for(let e=0;e<n;e++)s+=r.charAt(Math.floor(Math.random()*r.length));return s},{Icon:Ri,IdProviderAuthResultHandler:mi}=pragma.ui.components,{initiateAuthenticateAndRedirectWithCode:Fi}=pragma.auth,Ii={name:"SignInXbox",components:{Icon:Ri,IdProviderAuthResultHandler:mi},emits:["success"],setup(n,{emit:s}){const r=K();return Fi(Di,r),{Xbox:c0,authResultRef:r,emitSuccess:()=>s("success")}},template:`
    <div>
      <IdProviderAuthResultHandler
        v-if='authResultRef'
        :idProviderType='Xbox.PROVIDER_ID'
        :authResult='authResultRef'
        @success='emitSuccess'
      />
      <div v-else class='loading'></div>
    </div>
  `},ki=pragma.ui.helpers.eventEmitter,yi=pragma.auth.initiateLinkAndRedirectWithCode,bi={name:"LinkXbox",components:{},emits:["linkResult"],setup(n,{emit:s}){return yi(gi,"Xbox",s,ki),{}},template:"<div/>"},{getRedirectUri:Ot}=pragma.auth,Si={id:"XBOX",name:"Xbox",iconName:"xbox",enumValue:9,onSignInButtonClick:({router:n})=>{Nt(Ot(c0.SIGNIN_REDIRECT_PATH))},onLinkButtonClick:()=>{Nt(Ot(c0.LINK_REDIRECT_PATH))},registeredRoutes:[{name:"SignInXbox",path:"/signin-xbox",component:Ii}],registeredLinkRoutes:[{name:"LinkXbox",path:"/link-xbox",component:bi}],routerBeforeHook:({router:n})=>!1};Gr._source="pragma";$n._source="pragma";ra._source="pragma";Ca._source="pragma";La._source="pragma";Ja._source="pragma";po._source="pragma";To._source="pragma";Qo._source="pragma";li._source="overlay";Si._source="overlay";export{Gr as auth0,$n as discord,ra as epic,Ca as google,La as googleworkspace,Ja as okta,li as playstation,po as steam,To as twitch,Qo as unsafe,Si as xbox};
