{"name": "pragma-portal", "type": "module", "version": "1.0.0", "license": "UNLICENSED", "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "highcharts": "^11.4.8", "highcharts-vue": "^2.0.1", "pako": "^1.0.11", "vue": "^3.2.47", "vue-router": "4.0.12"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.5", "@vue/test-utils": "^2.4.6", "axios-mock-adapter": "^2.1.0", "chalk": "^4.1.2", "esbuild": "^0.14.54", "finalhandler": "^1.2.1", "less": "^4.2.1", "puppeteer": "^23.8.0", "serve-static": "^1.15.0", "vite": "^5.4.11"}, "scripts": {"package": "node buildlib/bin/package.js", "start": "node buildlib/bin/start.js", "test": "npm run test:unit && npm run test:it", "test:unit": "node buildlib/bin/test.js", "test:it": "node buildlib/bin/test.js --integration", "test:rpcs": "node buildlib/bin/check-rpcs.js", "make-overlay": "node buildlib/bin/make-overlay.js", "update-libsha": "node buildlib/bin/update-libsha.js"}}