import CraftingEntriesPage from './pages/CraftingEntries/CraftingEntriesPage.js'
import ContentChangesPage from './pages/ContentChanges/ContentChangesPage.js'
import InstancedSpecsPage from './pages/InstancedSpecs/InstancedSpecsPage.js'
import StackableSpecsPage from './pages/StackableSpecs/StackableSpecsPage.js'
import LimitedGrantsPage from './pages/LimitedGrants/LimitedGrantsPage.js'
import CreateStorePage from './pages/Stores/CreateStorePage.js'
import CreateStoreEntryPage from './pages/Stores/CreateStoreEntryPage.js'
import EditStoreEntryPage from './pages/Stores/EditStoreEntryPage.js'
import EditStorePage from './pages/Stores/EditStorePage.js'
import ViewStorePage from './pages/Stores/ViewStorePage.js'
import StoresPage from './pages/Stores/StoresPage.js'
import ListUpdateEntriesPage from './pages/UpdateEntries/ListUpdateEntriesPage.js'
import CreateUpdateEntryPage from './pages/UpdateEntries/CreateUpdateEntryPage.js'
import EditUpdateEntryPage from './pages/UpdateEntries/EditUpdateEntryPage.js'
import CreateCraftingEntryPage from './pages/CraftingEntries/CreateCraftingEntryPage.js'
import EditCraftingEntryPage from './pages/CraftingEntries/EditCraftingEntryPage.js'
import CreateLimitedGrantPage from './pages/LimitedGrants/CreateLimitedGrantPage.js'
import EditLimitedGrantPage from './pages/LimitedGrants/EditLimitedGrantPage.js'
import RewardsPage from './pages/Rewards/RewardsPage.js'
import RewardSlotsPage from './pages/Rewards/RewardSlotsPage.js'
import RewardTablesPage from './pages/Rewards/RewardTablesPage.js'
import RewardBagsPage from './pages/Rewards/RewardBagsPage.js'
import ItemBundlesPage from './pages/ItemBundles/ItemBundlesPage.js'
import CreateRewardSlotPage from './pages/Rewards/CreateRewardSlotPage.js'
import CreateRewardBagPage from './pages/Rewards/CreateRewardBagPage.js'
import CreateRewardPage from './pages/Rewards/CreateRewardPage.js'
import CreateRewardTablePage from './pages/Rewards/CreateRewardTablePage.js'
import EditRewardTablePage from './pages/Rewards/EditRewardTablePage.js'
import EditRewardSlotPage from './pages/Rewards/EditRewardSlotPage.js'
import EditRewardBagPage from './pages/Rewards/EditRewardBagPage.js'
import EditRewardPage from './pages/Rewards/EditRewardPage.js'
import ContentChangesNavbar from './components/ContentChangesNavbar/ContentChangesNavbar.vue.js'

const {LEGACY_INVENTORY_FEATURE_FLAG} = pragma.ui.helpers.featureToggles

export default portlet =>
  portlet
    .isVisible(() => pragma.ui.helpers.featureToggles.featureIsOn(LEGACY_INVENTORY_FEATURE_FLAG))
    .addNavbarComponent(ContentChangesNavbar)
    .addPageGroup('Items', group =>
      group
        .addPage('StackableEditor', StackableSpecsPage)
        .addPage('InstancedEditor', InstancedSpecsPage)
        .addPage('ItemBundlesEditor', ItemBundlesPage)
    )
    .addPage('StoresEditor', StoresPage, store =>
      store
        .addPage('CreateStore', CreateStorePage)
        .addPage('ViewStoreEditor', ViewStorePage, entry =>
          entry
            .addPage('EditStore', EditStorePage)
            .addPage('CreateEntry', CreateStoreEntryPage)
            .addPage('EditStoreEntry', EditStoreEntryPage)
        )
    )
    .addPage('UpdateEntriesEditor', ListUpdateEntriesPage, entry =>
      entry
        .addPage('CreateUpdateEntry', CreateUpdateEntryPage)
        .addPage('EditUpdateEntry', EditUpdateEntryPage)
    )
    .addPage('CraftingEntriesEditor', CraftingEntriesPage, entry =>
      entry
        .addPage('CreateCraftingEntry', CreateCraftingEntryPage)
        .addPage('EditCraftingEntry', EditCraftingEntryPage)
    )
    .addPageGroup('Rewards System', group =>
      group
        .addPage('RewardsEditor', RewardsPage, addRewardPage)
        .addPage('RewardBagsEditor', RewardBagsPage, addRewardBagsPage)
        .addPage('RewardSlotsEditor', RewardSlotsPage, addRewardSlotPage)
        .addPage('RewardTablesEditor', RewardTablesPage, addRewardTablePage)
    )
    .addPage('LimitedGrantsEditor', LimitedGrantsPage, entry =>
      entry
        .addPage('CreateLimitedGrant', CreateLimitedGrantPage)
        .addPage('EditLimitedGrant', EditLimitedGrantPage)
    )
    .addPageSection(section => section.addPage('ContentChanges', ContentChangesPage))

// Breadcrumb rewards
const addRewardPage = entry =>
  entry.addPage('CreateReward', CreateRewardPage).addPage('EditReward', EditRewardPage)

// Breadcrumb bags
const addRewardBagsPage = entry =>
  entry
    .addPage('CreateRewardBag', CreateRewardBagPage)
    .addPage('EditRewardBag', EditRewardBagPage, entry =>
      entry.addPage('EditRewardBagReward', EditRewardPage)
    )

// Breadcrumb slots
const addRewardSlotPage = entry =>
  entry
    .addPage('CreateRewardSlot', CreateRewardSlotPage)
    .addPage('EditRewardSlot', EditRewardSlotPage, entry =>
      entry.addPage('EditRewardSlotBag', EditRewardBagPage, entry =>
        entry.addPage('EditRewardSlotBagReward', EditRewardPage)
      )
    )

// Breadcrumb table
const addRewardTablePage = entry =>
  entry
    .addPage('CreateRewardTable', CreateRewardTablePage)
    .addPage('EditRewardTable', EditRewardTablePage, entry =>
      entry.addPage('EditRewardTableSlot', EditRewardSlotPage, entry =>
        entry.addPage('EditRewardTableSlotBag', EditRewardBagPage, entry =>
          entry.addPage('EditRewardTableSlotBagReward', EditRewardPage)
        )
      )
    )
