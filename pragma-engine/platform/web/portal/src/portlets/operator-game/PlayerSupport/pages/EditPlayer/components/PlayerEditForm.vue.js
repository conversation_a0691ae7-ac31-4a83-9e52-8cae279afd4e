const helpers = pragma.api.helpers
const rpc = pragma.api.rpc
const toast = pragma.ui.helpers.toast
const useSubscribe = pragma.ui.helpers.eventEmitter.useSubscribe
import { useRouter } from 'vue-router';
import * as Vue from 'vue';

const PlayerEditForm = {
  name: 'EditPlayerForm',

  props: { account: Object },

  setup(props) {
    const formState = Vue.reactive({ name: props.account.displayName })
    const disabled = Vue.ref(false)
    const errorMessage = Vue.ref('')
    const router = useRouter()

    const onPlayerEditSave = async () => {
      const name = formState.name.trim()
      if (disabled.value) return
      if (!name) return (errorMessage.value = 'Display name is required.')

      disabled.value = true
      const response = await rpc.requestSocialRPC('AccountRpc.UpdateDisplayNameOperatorV1Request', {
        pragmaSocialId: props.account.pragmaSocialId,
        requestedDisplayName: name
      })
      disabled.value = false

      if (!response.ok) {
        toast.error('Could not save the player. Try again later.')
        return (errorMessage.value = response.error)
      }
      toast.success(`${props.account.displayName} has been successfully updated.`)

      goBack()
    }

    const goBack = () => helpers.goBackToPage({ name: 'ViewPlayer', router })

    useSubscribe('playerEditSave', onPlayerEditSave)

    return { formState, errorMessage, disabled }
  },

  template: `
    <a-row>
      <a-col :span="12">
        <a-form layout='vertical' :model="formState">
          <a-form-item
            label="Display name"
            :help="errorMessage"
            :validateStatus="errorMessage && 'error' || ''"
            name='name'
            class="required"
          >
            <a-input v-model:value='formState.name' :ref='(el) => el?.focus()' />
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
  `
}

export default PlayerEditForm
