import EditPlayerGroupForm from './components/EditPlayerGroupForm.vue.js'
const { getPageSize, getPageIndex } = pragma.ui.helpers.paginationHelpers

const EditPlayerGroupPage = page =>
  page
    .addSocialRpcData('playerGroupData', 'AccountRpc.ViewPlayerGroupOperatorV1Request', {
      payload: ({ urlParams, queryParams }) => ({
        playerGroupId: urlParams.groupId,
        pageSize: getPageSize(queryParams?.size),
        pageIndex: getPageIndex(queryParams?.page)
      })
    })
    .addSocialRpcData('groups', 'AccountRpc.ViewPlayerGroupsOperatorV1Request')
    .setTitle(({ rpc }) => 'Edit ' + rpc.playerGroupData?.playerGroup.name)
    .addAction({name: 'Cancel', emit: 'goBack', type: 'secondary'})
    .addAction({name: 'Save', emit: 'savePlayerGroup'})
    .addComponent(EditPlayerGroupForm, {
      playerGroup: ({ rpc }) => rpc.playerGroupData.playerGroup,
      groups: ({ rpc }) => rpc.groups.playerGroups
    })

export default EditPlayerGroupPage
