const rpc = pragma.api.rpc
const utils = pragma.utils
const PageSection = pragma.ui.components.PageSection
const Icon = pragma.ui.components.Icon
import AddGroupToFullAccessListModal from './AddGroupToFullAccessListModal.vue.js'
import * as Vue from 'vue';
const confirm = pragma.ui.helpers.confirm
const toast = pragma.ui.helpers.toast

const FullAccessList = {
  name: 'FullAccessList',

  components: {
    PageSection,
    Icon,
    AddGroupToFullAccessListModal
  },

  props: {
    shard: Object,
    playerGroups: Object
  },

  setup(props) {
    const fullAccessPlayerGroupIds = Vue.ref(props.shard.fullAccessPlayerGroupIds)
    const fullAccessGroups = Vue.computed(() =>
      props.playerGroups.filter(playerGroup =>
        fullAccessPlayerGroupIds.value.includes(playerGroup.playerGroupId)
      )
    )

    const availablePlayerGroupsToAdd = Vue.computed(() =>
      props.playerGroups.filter(
        playerGroup => !fullAccessPlayerGroupIds.value.includes(playerGroup.playerGroupId)
      )
    )

    const visible = Vue.ref(false)

    const setShowAddGroupToFullAccessListModal = shouldShow => {
      visible.value = shouldShow
    }

    const onSubmit = async ({ fullAccessListGroups }) => {
      if (fullAccessListGroups.length) {
        const response = await rpc.requestSocialRPC(
          'GameManagementRpc.SetFullAccessPlayerGroupsOperatorV1Request',
          {
            gameShardId: props.shard.id,
            playerGroupIds: [
              ...fullAccessListGroups.map(playerGroup => playerGroup.playerGroupId),
              ...fullAccessGroups.value.map(playerGroup => playerGroup.playerGroupId)
            ]
          }
        )
        if (!response.ok) return toast.error('Error while adding player groups.')

        fullAccessPlayerGroupIds.value = response.data.gameShard.fullAccessPlayerGroupIds
        toast.success('Player group successfully added')
      }
    }

    const removeAccess = async group => {
      const response = await rpc.requestSocialRPC(
        'GameManagementRpc.SetFullAccessPlayerGroupsOperatorV1Request',
        {
          gameShardId: props.shard.id,
          playerGroupIds: fullAccessGroups.value
            .map(playerGroup => playerGroup.playerGroupId)
            .filter(playerGroupId => playerGroupId !== group.playerGroupId)
        }
      )

      if (!response.ok) {
        toast.error('Could not remove group from Full Access List. Please try again.')
        return
      }

      fullAccessPlayerGroupIds.value = response.data.gameShard.fullAccessPlayerGroupIds

      toast.success(`${group.name} has been successfully removed from Full Access List`)
    }

    return {
      fullAccessGroups,
      fullAccessListColumns,
      visible,
      setShowAddGroupToFullAccessListModal,
      availablePlayerGroupsToAdd,
      onSubmit,
      confirm,
      removeAccess
    }
  },

  template: `
    <PageSection title="Full Access List" subTitle="These groups can access this shard at any time">
      <template #actions>
        <a-button
          data-testId="addGroupButton"
          size="small"
          type="ghost"
          @click="setShowAddGroupToFullAccessListModal(true)"
        >
        <Icon
          name="plus"
        />
          Add Group
        </a-button>
      </template>
      <a-table
        :locale="{emptyText: 'No groups added'}"
        class='fullAccessList'
        :columns="fullAccessListColumns"
        :data-source="fullAccessGroups"
        :pagination="fullAccessGroups.length > 10"
        size="middle"
        rowClassName='fullAccessListRow'
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'removePlayerGroup'">
            <a-button
              size="small"
              danger
              ghost
              class='hide actionBtn'
              @click="confirm.danger({
                title: 'Remove ' + record.name + ' from Full Access List? ',
                content: 'Confirm that you want to remove ' + record.name + ' from Full Access List',
                okText: 'Remove',
                onOk: () => {
                  removeAccess(record)
                }
              })"
            ><Icon name='close' size="10" /><span>Remove</span></a-button>
          </template>
        </template>
      </a-table>
      <AddGroupToFullAccessListModal
        :visible='visible'
        :groupsToAdd='availablePlayerGroupsToAdd'
        @cancel="visible = false"
        @submit='onSubmit'
      />
    </PageSection>
  `
}

const fullAccessListColumns = [
  {
    title: 'Display Name',
    dataIndex: 'name',
    sorter: utils.getSorterByField('name')
  },
  {
    title: 'Description',
    dataIndex: 'description'
  },
  {
    title: 'Number Of Players',
    dataIndex: 'playerCount',
    sorter: utils.getSorterByField('playerCount')
  },
  {
    dataIndex: 'removePlayerGroup',
    align: 'right'
  }
]

export default FullAccessList
