const { onAuthClick, redirectAfterSignin, SessionType } = pragma.auth
const { store } = pragma
const { Icon } = pragma.ui.components
const { appBlueprint } = pragma.app.blueprint
import BackendInfo from '../BackendInfo/BackendInfo.vue.js'
import FlashMessage from '../FlashMessage/FlashMessage.vue'
import { useRouter } from 'vue-router';
const utils = pragma.utils
const flashMessage = pragma.ui.helpers.flashMessage

const SignInPage = {
  name: 'SignInPage',
  components: {
    Icon,
    BackendInfo,
    FlashMessage
  },
  setup() {
    const router = useRouter()
    const sessionType = store.get('app.sessionType')
    const isOperatorPortal = sessionType == SessionType.OPERATOR
    const isPlayerPortal = sessionType == SessionType.PLAYER
    const backendMode = utils.capitalizeFirstLetter(store.get('appInfo.backendMode').toLowerCase())
    const flashMessages = flashMessage.retrieveAndRemoveAll()

    const enabledProviders = (store.get('config.authenticationIdProviders') || []).filter(
      p => p.config?.showPortalLoginButton === 'true' && (
        (sessionType == SessionType.PLAYER && p.config?.playerLoginEnabled == 'true') ||
        (sessionType == SessionType.OPERATOR && p.config?.operatorLoginEnabled == 'true')
      )
    )
    
    const authProviders = utils.sortItemsByPosition(appBlueprint.authProviders)
    const showProvider = provider =>
      store.get('config.isDevelopment') || enabledProviders.find(p => p.providerId === provider)

    const redirect = () => redirectAfterSignin(router)

    return {
      showProvider,
      redirect,
      authProviders,
      utils,
      router,
      isOperatorPortal,
      isPlayerPortal,
      flashMessages,
      onAuthClick,
    }
  },

  template: `
    <div class='SignInPage auth-container'>
      <Icon name="logoPortal" size='30' class='logo' />
      <a-card :bordered="false" class='card card-auth'>
        <div>
          <a-space v-if="isOperatorPortal" direction='vertical' size='middle' align='center' class='alert'>
            <BackendInfo />
          </a-space>

          <component
            v-if="$route.meta.component"
            :is='$route.meta.component'
            @success="redirect"
          />

          <div v-else class='options'>
            <h4 class='header'>Sign In</h4>
            <a-space direction='vertical' size='middle' align='center' class='alert'>
              <template v-for='(message, id) in flashMessages' >
                <FlashMessage
                  :id='id'
                  :type='message.type'
                  :message='message.message'
                  :details='message.details'
                />
              </template>
            </a-space>
            <p class='text'>Choose how to sign in to your account</p>
            <a-form layout='vertical' class="auth-form-2col">
              <template v-for="authProvider in authProviders">
                <a-form-item v-if="showProvider(authProvider.id)">
                  <a-button class='ant-btn-secondary' @click="onAuthClick(authProvider, router)"
                    size='large' :block="true" style="text-align: left; justify-content: revert;">
                    <Icon :name='authProvider.iconName' />{{utils.convertCamelToSpaces(authProvider.name)}}
                  </a-button>
                </a-form-item>
              </template>
            </a-form>
            
            <span v-if='isPlayerPortal'>Don't have an account? <router-link :to="{name: 'CreateAccount'}">Create account</router-link></span>
          </div>
          

        </div>
      </a-card>
    </div>
  `
}

export default SignInPage
