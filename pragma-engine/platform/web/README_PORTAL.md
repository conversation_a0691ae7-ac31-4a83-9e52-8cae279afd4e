# Pragma Portal Setup


## Prerequisites



* [Pragma ReadMe](https://github.com/pragmaplatform/pragma-engine/tree/505d3c05d685540e1e7799be4e60f996a6a169d5#readme)
* Node 18 LTS or higher, recommended version 23


## Setup and quick start

Install dependencies for development and testing:


```
cd platform
./pragma portal setup
```

Run the portal in development with custom work in 5-ext/portal:

```
cd platform
./pragma portal start
```

Run the base pragma portal in development:

```
cd platform
./pragma portal start --project=web
```

### Run tests

Tests can also be run in the browser, on the port printed in the console after starting the app in development mode. To run
integration tests in the browser, go to `/it`.

Note: The engine needs to be started in the background before running the test suite for Portal.

## All Portal CLI Commands

For most of the portal operations, the Pragma CLI commands should be used instead of npm. Each command has its own help, accessible by
specifying the `-h` flag after any of the commands below.

| command                       | description |
|-------------------------------|--------------|
| `./pragma portal init`        | initialize a Portal overlay for custom development |
| `./pragma portal setup`       | set up dependencies for Portal development |
| `./pragma portal package`     | build the Portal for production |
| `./pragma portal start`       | start a local development HTTP server |
| `./pragma portal test`        | run unit tests |
| `./pragma portal test --all`  | Run all tests. This should be ran in 4-demo so ITs can work properly. |

## Portal npm commands

Available commands are found under` scripts:` in `platform/web/portal/package.json` and can be also listed with:


```
cd platform/web/portal
npm run
```

## Portal Configuration

All the portal configuration options are documented in `platform/web/portal/src/config/base.js`.

Notes:

* `local-dev.js` overwrites other configurations
* When using an overlay, `default.js` is loaded as the default configuration.
* Use the `PORTAL_CONFIG` environment variable to load a custom configuration file when running portal npm commands


## Portlets

A portal service is called a 'portlet'.

A portlet is considered valid if it is exported from a javascript file with the same name as the parent directory, and it is generated using the `generatePortlet` helper.

Custom portlet file structure example:


```
|-- CustomPortlet
    |-- pages
    |-- CustomPortlet.js
```



## Understanding the portal

The easiest way to get going is to read the code of an existing portlet in `platform/web/portal/src/portlets`  and use it as an example.


### The web app

The web app:



* Is a plain [Vue.js](https://vuejs.org/) app that uses Composition API.
* Is implemented using components from the [Ant.d](https://www.antdv.com/) library (V3).

These dependencies can be imported as normal package imports:
```
import * as Vue from 'vue';
import * as VueRouter from 'vue-router';
import { Modal } from 'ant-design-vue';
```
The ant.d components are also available directly in the vue templates using the `<a-` prefix.

In development and test, the app is generated by default under `.build/dev` and `.build/test`, with unminified files and almost the same structure as the `src` folder, to make debugging easy.

For production, `vite` is used to create compact, minified bundles in `dist`.


### Testing

Tests are run using:



* [Vue Test Utils 2](https://test-utils.vuejs.org/api/)
* [Jasmine](https://jasmine.github.io/api/4.0/matchers.html)

For example:


```
import * as VueTestUtils from '@vue/test-utils'
import ListGroupsTable from "./ListGroupsTable.vue.js";
const { mockedPlayerGroups } = pragma.test.mock;

describe("ListGroupsTable", () => {
  let wrapper;
  beforeEach(() => {
    wrapper = VueTestUtils.mount(ListGroupsTable, {
      props: {
        groups: mockedPlayerGroups,
      },
    });
  });

  it("renders without crashing", () => {
    expect(wrapper.text()).toContain("Name");
    expect(wrapper.text()).toContain(mockedPlayerGroups[0].name);
  });
});
```



### Portlets and pages

To ease development, portlets and pages are generated using a builder api, which allows automated handling of url routes and dynamic generation of layout sections like navigation, headers, or breadcrumbs.


```
// /PlayersDemo/PlayersDemo.js
import ListPlayersPage from "./pages/ListPlayersPage.js";

const PlayersDemo = (portlet) =>
  portlet.addPage("Players", ListPlayersPage)

export default PlayersDemo;
```


Each page can declare one or more needed RPC data sources. These are automatically retrieved and the data can be sent to the rendered vue components.

The main content of the page is composed by a list of vue components, added with `.addComponent`.


```
// PlayersDemo/pages/ListPlayersPage.js

import PlayersTable from "./PlayersTable.vue.js";
const ListPlayersPage = (page) =>
  page
    .addSocialRpcData("accounts", "AccountRpc.ViewSocialIdentitiesV1Request")
    .setTitle("Players")
    .setSubtitle(
      "This service will manage the viewing, adding and editing of Pragma players"
    )
    .addComponent(PlayersTable, {
      accountsRpcData: ({ rpc }) => rpc.accounts,
    });
export default ListPlayersPage;
```



```
// PlayersDemo/pages/PlayersTable.vue.js

const PlayersTable = {
  props: { accountsRpcData: Object },
  setup() {
    return {
      columns: [
        {
          title: "Email",
          dataIndex: "emailAddress",
          key: "email",
        },
        {
          title: "Personal Id",
          dataIndex: "pragmaPersonalId",
          key: "id",
        },
      ],
    };
  },
  template: `
    <a-table :dataSource="$props.accountsRpcData.socialIdentities" :columns="columns"></a-table>
  `,
};
export default PlayersTable;
```



## Using the Pragma Library

When writing portlets and components, the core components and functions should always be accessed by using the Pragma library, made globally available as `pragma`.


### pragma.ui



* pragma.ui.components - contains advanced interface components that can be used in any portlet
* pragma.ui.helpers - contains several functions that render smaller ui components
* pragma.ui.hooks - functions used to insert some of pragma's custom elements
* pragma.ui.router - router and routes generators and helper functions


### pragma.api


* pragma.api.rest - contains REST api request function
* pragma.api.rpc - contains the functions that do rpc calls


### pragma.auth

This contains functions used to authenticate a user, grouped by their ID.



* pragma.auth.auth - default auth system
* pragma.auth.discord - auth code for Discord
* pragma.auth.google - auth code for Google
* pragma.auth.steam - auth code for Steam
* pragma.auth.twitch - auth code for Twitch
* pragma.auth.unsafe - auth code for the Unsafe provider


### Other pragma library elements



* pragma.utils - multiple utility functions that extend the programming language to remove duplication
* pragma.store - this is a global repository used to store data that needs to be accessible from everywhere


## Development


### Recommended VSCode extensions



* [Vue Inline Template](https://marketplace.visualstudio.com/items?itemName=faisalhakim47.vue-inline-template): Syntax highlighting for vue inline templates inside of JavaScript and TypeScript tagged template strings.
* [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode): Code formatter
  * Turn on `formatOnSave` in VS Code Settings
  * Select the Prettier extension as the default formatter.
  * Open the root project `platform/web/portal` to avoid, prettier formating unwanted files on save.
* Use 2 space indentation instead of tabs


### Testing Tips


#### Input

**Select input**


```
const inputEl = document.querySelector(
  "div.ant-modal #form_item_playerGroupToDelete"
);
```


**Change value**


```
inputEl.setAttribute("value", playerGroupTest.name);
```


**Trigger change**


```
inputEl.dispatchEvent(new Event("change"));
await Vue.nextTick();
```



#### Button

**Check disabled**


```
expect(
  document.querySelector("div.ant-modal .ant-btn-dangerous").outerHTML
).toContain("disabled");
```


**Check not disabled**


```
expect(
  document.querySelector("div.ant-modal .ant-btn-dangerous").outerHTML
).not.toContain("disabled");
```



#### Vue

**Wait to apply the change in the DOM**


```
await Vue.nextTick();
```


**Vue Component Wrapper**


```
const appNode = document.querySelector("#app");
const appWrapper = new VueTestUtils.DOMWrapper(appNode);
```
