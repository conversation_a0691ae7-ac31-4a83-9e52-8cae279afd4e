# 2. Lobby Matchmaking

Date: 2025-02-26

## Status

Accepted

## Context

Players of the lobbies need to get into matchmaking as a single group.

Lobby is an entity that exists alongside the `Party` entity, and all the lobby players don't necessarily share a party.

This document describes matchmaking during the PROTOTYPE phase of the project. Points below outline the scope of the phase
([source](https://argentics.atlassian.net/wiki/spaces/LAST/pages/394690627/Game-Matchmaking)):
- Single players can enter matchmaking via the quickmatch button
- On Pragma, a matchmaking ticket is created and enters the queue
- Create a Global Tickets Queue that can handle matchmaking tickets
- In the matchmaking loop, tickets are first matched into custom game lobbies
- Dependency on custom game lobby having a flag for allow backfill
- Remaining tickets are warm-body matched into 5v5
- Cheats for QA to override team size for matchmaker
- Cheats for QA to auto-start match regardless of size 

## Decision

To accommodate to the statements listed in the Context section we need to put a lobby reference into the MatchmakingParty of a player.
This way we'll be relying on the Pragma mechanisms to handle matchmaking for us while still giving us the flexibility to tailor matchmaking process
to our needs.

In order to distinguish lobby parties from others a `lobbyId` needs to be stored in `MatchmakingP.ext` field.

Diagrams presented below are aimed to describe matchmaking processes from high-level perspective and should be used as guidelines to reading code.

### "Class" diagram of involved parties


```mermaid
---
title: Lobby-related classes that are used in matchmaking
---
classDiagram
    Lobby -- Player: contains
    Player -- MatchmakingPlayer: provides data for
    MatchmakingParty -- ExtMatchmakingParty: contains
    MatchmakingParty -- MatchmakingPlayer: contains
    ExtMatchmakingKey -- MatchmakingParty: contains
    LobbyService .. MatchmakingService: calls
    
    class Lobby {
        List~Player~ players
    }
    class MatchmakingParty {
        UUID partyId
        ExtMatchmakingKey matchmakingKey
        String gameServerVersion
        List~MatchmakingPlayer~ matchmakingPlayers
        ExtMatchmakingParty extMatchmakingParty
        List~String~ preferredGameServerZones
    }
    class ExtMatchmakingParty {
        UUID lobbyId
    }
    class LobbyService {
        enterMatchmakingV1()
    }
    class MatchmakingService {
        enterMatchmakingV2()
    }
```
The diagram above shows classes that are involved in the lobby matchmaking.
It's not exhaustive, but it should be sufficient when combined with sequence diagrams below.

Connector labels should be read left-to-right, top-to-bottom. E.g. "`Player` provides data for `MatchmakingPlayer`".

### Sequence diagrams

```mermaid
---
title: Lobby enters matchmaking
---
sequenceDiagram
    actor LobbyOwner as Lobby Owner
    box rgb(51, 51, 0) Platform
        participant LobbyService
        participant PartyService
        participant MatchmakingService
    end

    LobbyOwner ->> +LobbyService: Opt-in lobby for matchmaking
    loop Lobby players
        LobbyService ->> +PartyService: Get player's party
        PartyService -->> -LobbyService: OK
        
        LobbyService ->> LobbyService: Make MatchmakingPlayer

    end
    LobbyService ->> LobbyService: Make MatchmakingParty
    LobbyService ->> +MatchmakingService: Put MatchmakingParty into matchmaking 
    MatchmakingService -->> -LobbyService: OK 
    LobbyService -->> -LobbyOwner: OK
```

This diagram is straightforward: lobby owner opts their lobby into matchmaking, 
then `LobbyService` creates a `MatchmakingParty` with the data at hand or available in the system,
and, finally, `MatchmakingParty` is sent to the `MatchmakingService`.

---

```mermaid
---
title: Lobby during matchmaking
---
sequenceDiagram
    participant MatchmakingQueues
    participant MatchmakingPlugin
    
    loop Over matchables in the queue
        MatchmakingQueues ->> +MatchmakingPlugin: Send 2 matchables to process
        MatchmakingPlugin ->> MatchmakingPlugin: Process matchables
        alt match found
            MatchmakingPlugin -->> MatchmakingQueues: Game Instance
        else match not found
            MatchmakingPlugin -->> MatchmakingQueues: null
        end
        deactivate MatchmakingPlugin
    end
```
Another pretty straightforward diagram: `MatchmakingQueues` sends matchables to the `MatchmakingPlugin` and the latter processes them. Details of 
this processing are described in the next diagram.

```mermaid
---
title: Processing matchables
---
flowchart TB
    subgraph MatchmakingPlugin[MatchmakingPlugin]
        direction TB
        subgraph PartyIterator[1.Iterate over other's parties]
            m1{party.ext.lobbyId != null} -- YES --- m2[Continue/next iteration]
            m1 -- NO --- m3[Check anchor before transfer]
        end
        subgraph AnchorChecks[2.Anchor pre-transfer checks]
            n1{anchor + other's party < player limit} -- YES --- n2[Transfer party]
            n1 -- NO --- n3[Continue/next Iteration]
        end
        subgraph GameInstanceCreation[3.GameInstance Creation]
            direction TB
            o1{anchor full} -- YES --- o2[Create GameInstance]
            o1 -- NO --- o3[Continue/next Iteration]
        end
    end
    A[anchor] --> MatchmakingPlugin
    B[other] --> MatchmakingPlugin
    m3 --> AnchorChecks
    n2 --> GameInstanceCreation
```

Each loop of the `MatchmakingQueues` sends 2 matchables to the `MatchmakingPlugin`. Their corresponding parameters are named `anchor` and `other`.

`MatchmakingPlugin` iterates over `other`'s matchables and uses them to backfill `anchor` with matchmaking parties.

As stated previously, every `MatchmakingParty` can have `lobbyId` associated with it. If it exists -
we skip this party and continue iterating over `other`'s parties.

After successful transfer occurs we check if `anchor` has enough players to start a game. If so execution continues on to the `GameInstanceService`, 
otherwise we return execution to the `MatchmakingPlugin`.

---
## Consequences

We'll be able to put lobbies into matchmaking without forming player parties automatically,
while also avoiding lobby breaks (players of a lobby will go into the same team).
