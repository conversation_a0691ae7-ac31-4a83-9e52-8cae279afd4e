<?xml version="1.0" ?>
<SmellBaseline>
  <ManuallySuppressedIssues/>
  <CurrentIssues>
    <ID>ForbiddenComment:LobbyService.kt$LobbyService$// TODO: assuming json is similar to:</ID>
    <ID>ForbiddenComment:LobbyService.kt$LobbyService$// TODO: evaluate removal of team number from ExtBackendAddPlayer</ID>
    <ID>LargeClass:LobbyService.kt$LobbyService : DistributedServiceConfigHandler</ID>
    <ID>LongMethod:LobbyService.kt$LobbyService$@PragmaRPC( sessionType = SessionType.PLAYER, routingMethod = RoutingMethod.SINGLETON ) suspend fun lobbySelectCharacterV2( session: PlayerSession, request: LobbyRpc.LobbySelectCharacterV2Request, ): LobbyRpc.LobbySelectCharacterV2Response</ID>
    <ID>LongMethod:LobbyService.kt$LobbyService$private suspend fun launchNewGame( lobby: Lobby, lobbyCache: LobbyCache, )</ID>
    <ID>LongMethod:VoiceChatService.kt$VoiceChatService$@PragmaRPC( sessionType = SessionType.PLAYER, routingMethod = RoutingMethod.SINGLETON ) suspend fun generateVoiceChannelTokenV1( session: PlayerSession, request: GenerateVoiceChannelTokenV1Request, ): GenerateVoiceChannelTokenV1Response</ID>
    <ID>LongParameterList:LobbyService.kt$LobbyService$( pragmaNode: PragmaNode, instanceId: InstanceId, private val routingUtils: RoutingUtils = RoutingUtils.defaultInstance, private val sessionClient: SessionClient = SessionClient(), private val instanceCount: Int = pragmaNode.getDistributedServiceInstanceCount(LobbyService::class), private val alertingMutexFactory: AlertingMutexFactory = AlertingMutexFactory(), private val lobbyProxy: LobbyProxy = LobbyProxy(), private val uuidProxy: UUIDProxy = UUIDProxy(), private val timeProxy: TimeProxy = TimeProxy.defaultInstance, private var partyApiProvider: (Service) -&gt; PartyApi = { PartyApi(it) }, private var gameInstanceApiProvider: (Service) -&gt; GameInstanceApi = { GameInstanceApi(it) }, private val random: Random = Random, private val buildConfigVersions: ConcurrentHashMap&lt;String, String&gt; = ConcurrentHashMap&lt;String, String&gt;(), private val configVersions: ConcurrentHashMap&lt;String, JsonConfigDataImpl&gt; = ConcurrentHashMap&lt;String, JsonConfigDataImpl&gt;(), private val countdownManager: LobbyCountdownManager = LobbyCountdownManager(), )</ID>
    <ID>LongParameterList:LobbyServiceTestFactory.kt$LobbyServiceTestFactory$( i: Int, id: LobbyId = lobbyId(i), lobbyMembersByPlayerId: MutableMap&lt;PlayerId, LobbyPlayer&gt; = lobbyMembersByPlayerId(i), maxPlayerCount: Int = i * MAX_PLAYERS_PER_LOBBY, gameServerVersion: String = GameInstanceProtoTestFactory.gameServerVersion(i), lobbyName: String = lobbyName(i), lobbyState: LobbyState = PREGAME, gameInstanceId: GameInstanceId = GameInstanceProtoTestFactory.gameInstanceId(i).toUUID(), gameRegion: GameRegion = US_WEST, clientLocalServer: Boolean = false, mapFilepath: String = mapFilepath(i), rules: String = rules(1), serverArgs: Map&lt;String, String&gt; = mapOf(), configVersion: String = "", timers: LobbyTimers? = null, )</ID>
    <ID>LongParameterList:LobbyServiceTestFactory.kt$LobbyServiceTestFactory$( i: Int, playerId: Fixed128 = SessionTestFactory.pragmaSessionKey(i).pragmaId.toFixed128(), socialId: Fixed128 = SessionTestFactory.pragmaSessionKey(i).socialId.toFixed128(), displayName: DisplayName = PragmaCoreTestFactory.pragmaDisplayName(i).toProto(), isReady: Boolean = false, selectedCharacter: Int = i, selectedCharacterId: String = "", teamNumber: Int = i, isFlagVolunteer: Boolean = false, isFlagCarrier: Boolean = false, isOwner: Boolean = false, )</ID>
    <ID>LongParameterList:LobbyServiceTestFactory.kt$LobbyServiceTestFactory$( i: Int, sessionKey: PragmaSessionKey = SessionTestFactory.pragmaSessionKey(i), displayName: PragmaDisplayName = PragmaCoreTestFactory.pragmaDisplayName(i), gameClientVersion: String = GameInstanceProtoTestFactory.gameClientVersion(i), teamNumber: Int = i, selectedCharacter: Int = i, selectedCharacterId: String? = null, isReady: Boolean = false, isFlagVolunteer: Boolean = false, isFlagCarrier: Boolean = false, instanceInfoSent: Boolean = false, isOwner: Boolean = false, )</ID>
    <ID>NestedBlockDepth:LobbyService.kt$LobbyService$private fun assignCharactersToPlayersIfNeeded(lobby: Lobby)</ID>
    <ID>ReturnCount:BuildMapService.kt$BuildMapService$private suspend fun updateHotfixConfigForBuild(projectName: String, buildVersion: String, configVersion: String): String</ID>
    <ID>SwallowedException:LobbyCountdownManager.kt$LobbyCountdownManager$e: TimeoutCancellationException</ID>
    <ID>ThrowsCount:Lobby.kt$Lobby$fun addToLobby(lobbyPlayer: LobbyPlayer)</ID>
    <ID>ThrowsCount:LobbyService.kt$LobbyService$@PragmaRPC( sessionType = SessionType.PLAYER, routingMethod = RoutingMethod.SINGLETON ) suspend fun lobbySelectCharacterV1( session: PlayerSession, request: LobbyRpc.LobbySelectCharacterV1Request, ): LobbyRpc.LobbySelectCharacterV1Response</ID>
    <ID>ThrowsCount:LobbyService.kt$LobbyService$@PragmaRPC( sessionType = SessionType.PLAYER, routingMethod = RoutingMethod.SINGLETON ) suspend fun lobbySelectCharacterV2( session: PlayerSession, request: LobbyRpc.LobbySelectCharacterV2Request, ): LobbyRpc.LobbySelectCharacterV2Response</ID>
    <ID>ThrowsCount:LobbyService.kt$LobbyService$@PragmaRPC( sessionType = SessionType.PLAYER, routingMethod = RoutingMethod.SINGLETON ) suspend fun lobbySelectTeamV1( session: PlayerSession, request: LobbyRpc.LobbySelectTeamV1Request, ): LobbyRpc.LobbySelectTeamV1Response</ID>
    <ID>ThrowsCount:LobbyService.kt$LobbyService$@PragmaRPC( sessionType = SessionType.PLAYER, routingMethod = RoutingMethod.SINGLETON ) suspend fun lobbySetReadyV1( session: PlayerSession, request: LobbyRpc.LobbySetReadyV1Request, ): LobbyRpc.LobbySetReadyV1Response</ID>
    <ID>ThrowsCount:TextChatService.kt$TextChatService$private fun validateRequest(message: String)</ID>
    <ID>ThrowsCount:VoiceChatService.kt$VoiceChatService$@PragmaRPC( sessionType = SessionType.PLAYER, routingMethod = RoutingMethod.SINGLETON ) suspend fun generateVoiceChannelTokenV1( session: PlayerSession, request: GenerateVoiceChannelTokenV1Request, ): GenerateVoiceChannelTokenV1Response</ID>
    <ID>TooGenericExceptionCaught:JsonConfigDataImpl.kt$JsonConfigDataImpl$e: Throwable</ID>
    <ID>TooGenericExceptionCaught:LobbyCountdownManager.kt$LobbyCountdownManager$e: Exception</ID>
    <ID>TooManyFunctions:LobbyService.kt$LobbyService : DistributedServiceConfigHandler</ID>
  </CurrentIssues>
</SmellBaseline>
