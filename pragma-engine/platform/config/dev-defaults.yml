game:
  core:
    clusterName: "dev-local-game"
    shutdownTimeoutMillis: 10000
    startupTimeoutMillis: 10000
    shardId: 00000000-0000-0000-0000-000000000001
    logging:
      fileLoggerEnabled: false
  serviceConfigs:
    TokenConfig:
      jwtCurrentKeys:
        public: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxynxe/pLETewxjNIY1illZNhxYa/EZgHfVB4wtxdsgkdF7uZMZoLRufyNZDtfILzL6cL5Rx5w+QyoHJzYicQpuub/860LUR+Dvq7QwsGULjmw6U6F1kxcv7x2IAfNx5d6k/Tup7Wwv0TQYwpmYsR4xGnyNOBuV28L/XUtt2nabNFUTh7O7rk6P1IzyiTuedJj8cbrL111r17Kr7pGNZipG6v93Ul1wjtUSqGPggZULvvMgtXALpkuEt1eVp+WP4prSs9cwGqoCr9xbRlnt9QlpAEnMvsaEynGkCDg88TFytm047NLqCYCkVPNTbFpD5fWSJuM911on4j18815C5yuQIDAQAB"
        private: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDHKfF7+ksRN7DGM0hjWKWVk2HFhr8RmAd9UHjC3F2yCR0Xu5kxmgtG5/I1kO18gvMvpwvlHHnD5DKgcnNiJxCm65v/zrQtRH4O+rtDCwZQuObDpToXWTFy/vHYgB83Hl3qT9O6ntbC/RNBjCmZixHjEafI04G5Xbwv9dS23adps0VROHs7uuTo/UjPKJO550mPxxusvXXWvXsqvukY1mKkbq/3dSXXCO1RKoY+CBlQu+8yC1cAumS4S3V5Wn5Y/imtKz1zAaqgKv3FtGWe31CWkAScy+xoTKcaQIODzxMXK2bTjs0uoJgKRU81NsWkPl9ZIm4z3XWifiPXzzXkLnK5AgMBAAECggEBALlrLS01KqoO5P7BQ8VqBE6q9b2F42Cn32EliCgyLPDRV0p/FvJ87b7HtlF0agUfjPRyWrqktiBP1WpW42Ef6Xrdo1CWylvShmG/wJN8Mhb47a57pweRvF3JSLM9JNKV7O12ADKgKmVCY6kEqr4OIUr7Zu+rKfy/bbJg9faBiZj27Ye9yZCQhOts3q3xo0Y8lTJ0tNZRuzT2sqXxByTrbAsQ+ePO4CMjNQkbkaahwnNkf4VPAvqEw7m+eMiG1X0ejxuyfGKk8DIcAl/0B+c+/TolcaEhFQGEwS8VajjRWrKhGKrsUiHXKTASwx+LiTAIQWCeBGVctcOqk7Es3gwepuECgYEA9z7GO6TaXEfOu91MJ1dRteFyuwB18x8RW3F7OiIiTsgua+yvZqf80a6FSVzuL/bz4jXHnBRL3DscH+0hKyooFhVvc5RHKdiyReUEQfTqi+O8cvPjj9D4cy8vejEDWrADR3figrdCtF3n+qTpjNAzv9IDSJjZmB3CAT7gY/5mRL0CgYEAzjdSS+GnTkRQVZlr56G/cczQ/p9HS19cRZmuGaDjr9+WKm2JkkwpBBiG5jgh0feUdthfL54y4HjP5S5f83YL4++rgjfxC1xWEVKxiGyCe3gqu4w/Zd6fzrNPkISmzLgI973dy+ErgsEmi6TxJ9Qx47jV5sfljOhzKksf2K6ja60CgYB2f12e5GnLZJcOrwd6q3z8AOtt+NCPD0rQWtlGiWmL/KFTFIMUXlrrXAuBl8qIroFA36mlgozBJotaOdp1x6SiwO9Do8NfqP34RcT+n5VyKln9diqQmQUan3jPxYHoCwfnTuv+3jpYG9G87SEKXhcPTmZIewl+wHRlCG+fPSRiuQKBgHLmWDB3luUBI1HOnlT9xtH9sSVuUicpBxywuFUHVFYWWnFhfxnI3cGueNulwxJexJZDkSXTG73/Erc707Kvv4hNbl6sFN6joEEetlSxQTLSjdzaFCkKljhxqI95mgbPNA/19rM2zyuXu4ZrbQMoNHQR9P0VvE1651BVc3UkOWIxAoGBANhX1ualqfnOiYduH940QX3WizkkgWUy+OFn1e1wakNsiWcjJvH/rzQNphmJStqOtZ0+/PJR4dnPZyjnQnChRabf5El79Slkjmx44/3Iyi6Y3kXQi4Nzo6eQLINz55JyKtPTDwS3vOOUv3oaDMnWnLbBgEFCxDBfy7K4+rN5uVS1"
    SocialBackendPartnerClientConfig:
      bearerToken: "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    SharedDatabaseConfigServiceConfig:
      databaseConfigsByIdentifier:
        defaultIdentifier:
          username: "superuser"
          password: "password"
          host: "localhost"
    TelemetryDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "local_game_telemetry"
    InventoryDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "local_game_inventory1"
    FulfillmentDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "local_game_fulfillment1"
    PlayerDataDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "local_game_player_data1"
    InventoryServiceConfig:
      inventoryCacheConfig:
        maxTimeBetweenAccessMillis: 900000
        sizeBeforeExpirationEnforced: 250
        sweepIntervalMillis: 300000
    PlayerDataServiceConfig:
      cacheConfig:
        maxTimeBetweenAccessMillis: 900000
        sizeBeforeExpirationEnforced: 250
        sweepIntervalMillis: 300000
    PartyConfig:
      gameServerVersionCompatibility:
        1:
          serverVersion: "defaultGameServerVersion"
          clientVersions:
            1: "*"
    GameInstanceServiceConfig:
      gameServerAllocationTimeoutMillis: 60000
      keepAliveIntervalMillis: 5000
    FleetServiceConfig:
      serverPoolManagementPolicies:
        1:
          id: "defaultPoolManagementPolicy"
          gameCapacityPerServer: 1
          capacityFloor: 0
          capacityCeiling: 2147483647
          capacityBuffer: 0
          serverMaxStartDurationMillis: 300000
          serverHeartbeatPeriodMillis: 1000
          serverHeartbeatExpiryFactor: 5.0
          gameCapacityResolvedTimeoutMillis: 60000
  pluginConfigs:
    GameOperatorGatewayNodeService.routePlugins:
      plugins:
        Portal:
          class: "pragma.gateway.FilePortalModulePlugin"
          config:
            portalSource: "web/portal/dist/operator-game"
social:
  core:
    clusterName: "local-social"
    shutdownTimeoutMillis: 10000
    startupTimeoutMillis: 10000
    shardId: 00000000-0000-0000-0000-000000000001
    logging:
      fileLoggerEnabled: false
  serviceConfigs:
    SharedDatabaseConfigServiceConfig:
      databaseConfigsByIdentifier:
        defaultIdentifier:
          username: "superuser"
          password: "password"
          host: "localhost"
    TokenConfig:
      jwtCurrentKeys:
        public: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxynxe/pLETewxjNIY1illZNhxYa/EZgHfVB4wtxdsgkdF7uZMZoLRufyNZDtfILzL6cL5Rx5w+QyoHJzYicQpuub/860LUR+Dvq7QwsGULjmw6U6F1kxcv7x2IAfNx5d6k/Tup7Wwv0TQYwpmYsR4xGnyNOBuV28L/XUtt2nabNFUTh7O7rk6P1IzyiTuedJj8cbrL111r17Kr7pGNZipG6v93Ul1wjtUSqGPggZULvvMgtXALpkuEt1eVp+WP4prSs9cwGqoCr9xbRlnt9QlpAEnMvsaEynGkCDg88TFytm047NLqCYCkVPNTbFpD5fWSJuM911on4j18815C5yuQIDAQAB"
        private: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDHKfF7+ksRN7DGM0hjWKWVk2HFhr8RmAd9UHjC3F2yCR0Xu5kxmgtG5/I1kO18gvMvpwvlHHnD5DKgcnNiJxCm65v/zrQtRH4O+rtDCwZQuObDpToXWTFy/vHYgB83Hl3qT9O6ntbC/RNBjCmZixHjEafI04G5Xbwv9dS23adps0VROHs7uuTo/UjPKJO550mPxxusvXXWvXsqvukY1mKkbq/3dSXXCO1RKoY+CBlQu+8yC1cAumS4S3V5Wn5Y/imtKz1zAaqgKv3FtGWe31CWkAScy+xoTKcaQIODzxMXK2bTjs0uoJgKRU81NsWkPl9ZIm4z3XWifiPXzzXkLnK5AgMBAAECggEBALlrLS01KqoO5P7BQ8VqBE6q9b2F42Cn32EliCgyLPDRV0p/FvJ87b7HtlF0agUfjPRyWrqktiBP1WpW42Ef6Xrdo1CWylvShmG/wJN8Mhb47a57pweRvF3JSLM9JNKV7O12ADKgKmVCY6kEqr4OIUr7Zu+rKfy/bbJg9faBiZj27Ye9yZCQhOts3q3xo0Y8lTJ0tNZRuzT2sqXxByTrbAsQ+ePO4CMjNQkbkaahwnNkf4VPAvqEw7m+eMiG1X0ejxuyfGKk8DIcAl/0B+c+/TolcaEhFQGEwS8VajjRWrKhGKrsUiHXKTASwx+LiTAIQWCeBGVctcOqk7Es3gwepuECgYEA9z7GO6TaXEfOu91MJ1dRteFyuwB18x8RW3F7OiIiTsgua+yvZqf80a6FSVzuL/bz4jXHnBRL3DscH+0hKyooFhVvc5RHKdiyReUEQfTqi+O8cvPjj9D4cy8vejEDWrADR3figrdCtF3n+qTpjNAzv9IDSJjZmB3CAT7gY/5mRL0CgYEAzjdSS+GnTkRQVZlr56G/cczQ/p9HS19cRZmuGaDjr9+WKm2JkkwpBBiG5jgh0feUdthfL54y4HjP5S5f83YL4++rgjfxC1xWEVKxiGyCe3gqu4w/Zd6fzrNPkISmzLgI973dy+ErgsEmi6TxJ9Qx47jV5sfljOhzKksf2K6ja60CgYB2f12e5GnLZJcOrwd6q3z8AOtt+NCPD0rQWtlGiWmL/KFTFIMUXlrrXAuBl8qIroFA36mlgozBJotaOdp1x6SiwO9Do8NfqP34RcT+n5VyKln9diqQmQUan3jPxYHoCwfnTuv+3jpYG9G87SEKXhcPTmZIewl+wHRlCG+fPSRiuQKBgHLmWDB3luUBI1HOnlT9xtH9sSVuUicpBxywuFUHVFYWWnFhfxnI3cGueNulwxJexJZDkSXTG73/Erc707Kvv4hNbl6sFN6joEEetlSxQTLSjdzaFCkKljhxqI95mgbPNA/19rM2zyuXu4ZrbQMoNHQR9P0VvE1651BVc3UkOWIxAoGBANhX1ualqfnOiYduH940QX3WizkkgWUy+OFn1e1wakNsiWcjJvH/rzQNphmJStqOtZ0+/PJR4dnPZyjnQnChRabf5El79Slkjmx44/3Iyi6Y3kXQi4Nzo6eQLINz55JyKtPTDwS3vOOUv3oaDMnWnLbBgEFCxDBfy7K4+rN5uVS1"
    SocialPlayerGatewayConfig:
      devLoginQueueBypass: true
    AccountServiceConfig:
      gameManagementPollingFrequencyInMillis: 10000
      verificationEmailEndpoint: "http://127.0.0.1:11000/#/verify-email"
    AccountDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "local_social_account"
    DataRightsDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "local_social_data_rights"
    FriendDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "local_social_friend"
    GameDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "local_social_game"
    PaymentDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "local_social_orderhistory1"
    OrderDaoConfig:
      databaseConfig:
        identifierSchemas:
          1:
            identifier: "defaultIdentifier"
            schema: "local_social_order1"
    UnsafeIdentityDaoConfig:
      databaseConfig:
        identifierSchema:
          identifier: "defaultIdentifier"
          schema: "local_social_unsafe_identity_provider"
  pluginConfigs:
    AccountService.identityProviderPlugins:
      plugins:
        Unsafe:
          class: "pragma.account.UnsafeIdentityProviderPlugin"
    SocialOperatorGatewayNodeService.routePlugins:
      plugins:
        Portal:
          class: "pragma.gateway.FilePortalModulePlugin"
          config:
            portalSource: "web/portal/dist/operator-social"
    SocialPlayerGatewayNodeService.routePlugins:
      plugins:
        Portal:
          class: "pragma.gateway.FilePortalModulePlugin"
          config:
            portalSource: "web/portal/dist/player-social"
