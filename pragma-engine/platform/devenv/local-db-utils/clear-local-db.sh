#!/bin/bash

. bash-utils.sh

# checkRequiredParam "host" "$1"
# checkRequiredParam "shard" "$2"

host=127.0.0.1
shard=local
user=superuser
password="password"

echo "This command will delete all player data, and requires a platform restart to be performed after it completes."
read -p 'Confirm the shard name "local" to clear: ' shardConfirmation

if [ "$shardConfirmation" != "$shard" ]; then
    echo "\"$shardConfirmation\" does not match expected shard name: $shard. Exiting"
    exit 1
fi

echo "commencing deletion of all the player data for $host, $shard"
mysql -h "$host" -u "$user" -p$password <<EOF
DROP DATABASE ${shard}_game_game;
DROP DATABASE ${shard}_game_inventory1;
DROP DATABASE ${shard}_game_inventory2;
DROP DATABASE ${shard}_game_player_progression1;
DROP DATABASE ${shard}_game_player_progression2;
DROP DATABASE ${shard}_game_telemetry;
DROP DATABASE ${shard}_social_unsafe_identity_provider;
DROP DATABASE ${shard}_social_account;
EOF