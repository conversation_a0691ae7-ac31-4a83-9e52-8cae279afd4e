#!/bin/bash

ensureDirExists() {
    if [ -z "$1" ]; then
        echo "usage: ensureDirExists [dirName]"
        exit 1
    fi
    dirName=$1

    if [ ! -d "$dirName" ]; then
        mkdir -p $dirName
    fi
}

checkRequiredParam() {
    NAME=$1
    VALUE=$2
    if [ -z "$VALUE" ]; then
        echo "missing $NAME, exiting"
        exit 1
    else
        echo "using $NAME=$VALUE"
    fi
}

checkAndExportRequiredParam() {
    checkRequiredParam $1 $2
    export $1="$2"
}

checkAwsUser() {
    aws sts get-caller-identity
}

generateRandomPassword() {
    openssl rand -hex 6
}