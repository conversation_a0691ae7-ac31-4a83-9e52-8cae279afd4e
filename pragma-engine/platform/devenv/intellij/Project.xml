<code_scheme name="Project copy" version="173">
  <JetCodeStyleSettings>
    <option name="PACKAGES_TO_USE_STAR_IMPORTS">
      <value />
    </option>
    <option name="PACKAGES_IMPORT_LAYOUT">
      <value>
        <package name="" alias="false" withSubpackages="true" />
        <package name="java" alias="false" withSubpackages="true" />
        <package name="javax" alias="false" withSubpackages="true" />
        <package name="kotlin" alias="false" withSubpackages="true" />
        <package name="" alias="true" withSubpackages="true" />
      </value>
    </option>
    <option name="NAME_COUNT_TO_USE_STAR_IMPORT" value="2147483647" />
    <option name="NAME_COUNT_TO_USE_STAR_IMPORT_FOR_MEMBERS" value="2147483647" />
    <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
  </JetCodeStyleSettings>
  <codeStyleSettings language="XML">
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="kotlin">
    <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    <option name="RIGHT_MARGIN" value="180" />
    <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
    <option name="LINE_COMMENT_ADD_SPACE" value="true" />
    <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
    <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="0" />
    <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
    <option name="EXTENDS_LIST_WRAP" value="5" />
    <option name="METHOD_CALL_CHAIN_WRAP" value="5" />
    <option name="ASSIGNMENT_WRAP" value="5" />
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
</code_scheme>