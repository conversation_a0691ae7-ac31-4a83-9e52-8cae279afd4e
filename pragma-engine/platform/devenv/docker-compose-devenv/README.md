This folder contains an optional docker-compose environment to run a Pragma platform locally. 

It currently provides:
* Prometheus (inmem)
* Grafana (sqlite3, stored in `checkedin-storage`)
* Mysqldb (persisted, stored in `local-storage`)

# Usage

## TL;DR

Clean up and start new environment:

```
docker-compose kill && docker-compose down --remove-orphans && docker-compose up -d
```

Starting a local backend can be achieved by running through intellij or make.

## PreReq

This environment is mainly used on Mac platform using `Docker Desktop`. You will need:

* docker
* docker-compose
* Port 3030 free (default Grafana UI port)
* Port 9090 free (default Prometheus admin UI port)
* Port 3306 free (default port for MySql)

## Setup

In this folder run

```
docker-compose up -d
```

## Status

To check the status of the containers, run:

```
docker-compose ps
```

It should look something like this. Any mapped ports will be on the left hand side of the arrow, example: `0.0.0.0:{local_port}->{container_port}`

```
$ docker-compose ps
NAME                              IMAGE                           COMMAND                  SERVICE             CREATED             STATUS              PORTS
docker-compose-devenv-mysqldb-1   mysql:8.0.28-oracle             "docker-entrypoint.s…"   mysqldb             8 seconds ago       Up 4 seconds        0.0.0.0:3306->3306/tcp, 33060/tcp
grafana                           grafana/grafana:9.4.7           "/run.sh"                grafana             8 seconds ago       Up 4 seconds        0.0.0.0:3030->3000/tcp
prometheus                        prom/prometheus:latest          "/etc/prometheus/sta…"   prometheus          8 seconds ago       Up 4 seconds        0.0.0.0:9090->9090/tcp
```

## Logs

If you need to see logs from these commands, run:

```
docker-compose logs -f
```

## Tear down

```
docker-compose kill && docker-compose down --remove-orphans
```

## Prometheus

Admin UI available at [http://localhost:9090](http://localhost:9090). See [here](https://prometheus.io/) for the Prometheus documentation.

It is preconfigured to scrape a local development Pragma platform.

## Grafana

A metric dashboard system, you can find the UI at [http://localhost:3030](http://localhost:3030). Passwordless usage is enabled, and the Prometheus server is already added as the default data source.

## Updating Grafana Dashboard

Grafana has a dashboard json configured at
* devenv/docker-compose-devenv/checkedin-storage/grafana/provisioning/dashboards/LocalOverview.json

If you only need to update the LocalOverview.json file, you can run the following instead of rebuilding the entire env:

```
docker compose run --rm grafanaConfigurator
```

## Mysqldb

See docker-compose.yml for version and user/pass. Listening on port 3306.

# Development

If adding new services to the `docker-compose.yml` file please keep the following in mind:

* We aim to keep the environment preconfigured and able to 'just start correctly'
* For volumes:
  * use `docker-compose-devenv\local-storage` for any volumes that need to persist between restart of the environment. This folder is in
    the `.gitignore` file and should remain so.
  * use `docker-compose-devenv\checkedin-storage` for any volumes that must be checked in and are required to start up a fresh environment.
* For networking:
  * since we support Docker Desktop for Mac Os we are very limited in our ability to create networking rules. Services can talk to your
    local host (and each other) via `host.docker.internal`. Expect some linking between services to be broken. So far, this hasn't been a
    blocker to using this devenv setup. 
  * On Linux, you must specify the environment variable `$DOCKER_HOST_IP=<docker daemon that's running>`, this is usually ********** but \
    check your results from `ifconfig` to verify
    * pro-tip: put into your `.bash_aliases` for fun and profit
