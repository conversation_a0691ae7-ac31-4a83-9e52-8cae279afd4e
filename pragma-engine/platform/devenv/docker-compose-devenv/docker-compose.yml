services:
  mysqldb:
    image: mysql:8.0.37-oracle
    ports:
      - 3306:3306
    restart: on-failure
    environment:
      - DOCKER_HOST_IP=${DOCKER_HOST_IP:-host.docker.internal}
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_USER=superuser
      - MYSQL_PASSWORD=password
    volumes:
      - /var/lib/mysql
      - ./checkedin-storage/mysql:/docker-entrypoint-initdb.d:ro
      - ./checkedin-storage/mysql/my.cnf:/etc/mysql/my.cnf
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8192M
        reservations:
          cpus: '4.0'
          memory: 8192M
  prometheus:
    image: prom/prometheus:latest
    environment:
      - DOCKER_HOST_IP=${DOCKER_HOST_IP:-host.docker.internal}
    container_name: prometheus
    restart: on-failure
    ports:
      - 9090:9090
    entrypoint:
      - /etc/prometheus/start_prometheus.sh
    volumes:
      - ./checkedin-storage/prometheus/start_prometheus.sh:/etc/prometheus/start_prometheus.sh:ro
      - ./checkedin-storage/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml.tmpl:ro
      - ../../metrics/rulegroups:/etc/prometheus/rulegroups
  grafana:
    image: grafana/grafana:9.4.7
    container_name: grafana
    restart: on-failure
    environment:
      - DOCKER_HOST_IP=${DOCKER_HOST_IP:-host.docker.internal}
    ports:
      - 3030:3000
    user: '0:0'
    volumes:
      - ./local-storage/grafana_data:/var/lib/grafana:rw
      - ./checkedin-storage/grafana/provisioning/datasources:/etc/grafana/provisioning/datasources
      - ./checkedin-storage/grafana/provisioning/dashboards/dashboard.yml:/etc/grafana/provisioning/dashboards/dashboard.yml
      - ./checkedin-storage/grafana/grafana.ini:/etc/grafana/grafana.ini:ro
      - ../../metrics/dashboards:/etc/grafana/provisioning/dashboards
  grafanaConfigurator:
    image: alpine:latest
    container_name: grafanaConfigurator
    restart: "no"
    environment:
      - DOCKER_HOST_IP=${DOCKER_HOST_IP:-host.docker.internal}
    depends_on:
      - grafana
    volumes:
      - ./checkedin-storage/grafana/setup.sh:/etc/grafanaConfigurator/setup.sh:ro
    command: sh /etc/grafanaConfigurator/setup.sh
