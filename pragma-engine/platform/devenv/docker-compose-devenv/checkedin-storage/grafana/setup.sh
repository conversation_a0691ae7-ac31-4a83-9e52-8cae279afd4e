#!/bin/sh

#GRAFANA_URL=http://localhost:3030
GRAFANA_URL=http://grafana:3000

apk add curl
curl -qsf --user superuser:password -H "content-type: application/json"  $GRAFANA_URL/api/org/preferences 2>&1 > /dev/null
until [ $? -eq 0 ]; do
    echo "waiting for grafana to be ready"
    sleep 1
    curl -qsf --user superuser:password -H "content-type: application/json"  $GRAFANA_URL/api/org/preferences 2>&1 > /dev/null
done


curl -qsf --user superuser:password -H "content-type: application/json"  $GRAFANA_URL/api/orgs/name/Pragma 2>&1 > /dev/null
if [ $? -ne 0 ]; then
    echo "configuring grafana..."
    
    # update org 1 name
    curl -qsf --user superuser:password -H "content-type: application/json"  -X PUT --data '{"name":"Pragma"}'   $GRAFANA_URL/api/orgs/1  2>&1 > /dev/null

    # use it
    curl -qsf --user superuser:password -H "content-type: application/json"  -X POST $GRAFANA_URL/api/user/using/1  2>&1 > /dev/null

    # set home dashboard
    curl -qsf --user superuser:password -H "content-type: application/json" -X PUT --data '{"homeDashboardId":1}' $GRAFANA_URL/api/org/preferences  2>&1 > /dev/null

    echo "done"
else
    echo "grafana already configured"
fi