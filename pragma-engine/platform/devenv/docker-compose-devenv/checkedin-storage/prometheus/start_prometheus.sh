#!/bin/sh

if [ -z $DOCKER_HOST_IP ]; then
  echo "$DOCKER_HOST_IP isn't set, please ensure it is set and try again"
  exit 1
fi

if [ ! -f /etc/prometheus/pragma_config.yml ]; then
  cp /etc/prometheus/prometheus.yml.tmpl /etc/prometheus/pragma_config.yml
  sed -i 's/{HOST}/'"$DOCKER_HOST_IP"'/g' /etc/prometheus/pragma_config.yml
  echo "SED and CP"
else
  echo "Working as intended"
fi

/bin/prometheus --config.file=/etc/prometheus/pragma_config.yml --web.enable-lifecycle
