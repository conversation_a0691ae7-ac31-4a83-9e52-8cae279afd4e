global:
  scrape_interval:     15s
  evaluation_interval: 15s

rule_files:
  - /etc/prometheus/rulegroups/histogram_rules.yml
# - "second.rules"

# {HOST} is a template to allow better interoperability between windows/mac/linux
# SEE: devenv/docker-compose-devenv/docker-compose.yml
scrape_configs:
  - job_name: prometheus
    static_configs:
      - targets: ['{HOST}:10200','{HOST}:11200','{HOST}:10202','{HOST}:11202',]