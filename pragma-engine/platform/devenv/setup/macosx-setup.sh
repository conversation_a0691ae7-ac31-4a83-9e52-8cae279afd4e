#!/usr/bin/env bash

ARCH=`uname -m`
if [[ $ARCH == "arm64" ]]
then
	# install rosetta for intel emulation on M1
	softwareupdate --install-rosetta --agree-to-license
fi

xcode-select -p 1>/dev/null
if [[ $? -ne 0 ]]; then
	echo "Xcode command line tools are a prerequisite for Homebrew please install through"
	echo "xcode-select --install"
	exit 1
fi

SHELL_STARTUP=.profile
if [[ "$SHELL" == "/bin/zsh" ]]; then
	SHELL_STARTUP=.zshenv
fi

# install Homebrew
which brew
if [[ $? -ne 0 ]]; then
	/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
	echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/$SHELL_STARTUP
	source ~/$SHELL_STARTUP

fi

# add some taps
brew tap homebrew/cask
brew tap homebrew/cask-versions

# install required brews
brew install corretto17
export JAVA_HOME=$(/usr/libexec/java_home)
export PATH=${JAVA_HOME}/bin:$PATH

grep -q "JAVA_HOME" ~/$SHELL_STARTUP
if [[ $? -ne 0 ]]; then
	echo 'export JAVA_HOME=$(/usr/libexec/java_home)' >> ~/$SHELL_STARTUP
	echo 'export PATH=${JAVA_HOME}/bin:$PATH' >> ~/$SHELL_STARTUP
fi

brew install maven
brew install node@16
brew link --overwrite node@16
brew install make

# other brews
brew install --cask drovio
brew install typora
brew install --cask postman
brew install mysql@8.0
brew install --cask mysqlworkbench
brew install --cask unity-hub
brew info --cask unity@2021.3.16f1
if [ $? -ne 0 ]; then
	echo "Please install latest Unity 2021.3.16f1 through Unity Hub"
else
	brew install --cask unity@2021.3.16f1
fi

brew install --cask epic-games

brew install --cask docker
brew install docker-compose

brew install go

# Required for make/script programs.
brew install jq
brew install grep # Note this installs GNU grep (ggrep) (OSX default is BSD grep), which is required for `--perl-regexp`.
brew install coreutils

brew tap homebrew/cask-fonts
brew install --cask font-fira-code

brew install gnupg

git config --global pull.rebase true
git config --list --show-origin | grep -q "gpg.program"
if [[ $? -ne 0 ]]; then
	git config --global gpg.program $(which gpg)
fi

# vcpkg required.
brew install zip
brew install unzip

# needed for generating the documentation website
# brew automatically installs hugo extended which is what we need.
# ATM we expect hugo v0.119.0+extended
brew install hugo

echo "~~~ Mac Setup Complete! ~~~"
echo "Don't forget to manually install IntelliJ 2023.1.2 from https://www.jetbrains.com/idea/download/other.html"
