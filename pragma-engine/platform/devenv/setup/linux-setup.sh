#!/bin/bash
set -u
set -o pipefail

GREEN='\033[0;32m'
NC='\033[0m'
CORRETTO_NUMBER_VERSION=17
IDEA_VERSION="2023.1.7"
NODE_VERSION=16

function print_help() {
    helpText="This script installs dependencies and optional programs to help you use Pragma
and is intended for Debian-based distributions and has been tested against Ubuntu 20.04 LTS.
It will not function on distros that do not use apt or apt-get.

Flags
-d      Defaults:  Installs git, gnupg2, Amazon Corretto $CORRETTO_NUMBER_VERSION (JDK), and Maven
-i      IntelliJ:  Installs IntelliJ $IDEA_VERSION
-n      Node:  Installs NodeJS $NODE_VERSION
-s      SDK: Required packages to build the SDK
-o      Docker:  Installs docker and docker-compose
-e      Extras:  Installs Typora and MySQL Workbench
-g      git setup:  Installs git if necessary, helps you enter your git information,
-z      <PERSON> extended: Installs Hugo extended required for authoring documentation
-m      Mysql: Installs Mysql 8.x

-h      Help:  displays this help message

It's possible to install multiple packages at once.  For example, to install Defaults and IntelliJ, use -di
Or, to install everything and set up git with your username and e-mail, use -dinoeg"

    echo "$helpText"
}

#persist current user's home before switching to sudo
USER_HOME=$HOME

echo "You need root privileges to run this script."


while getopts 'dinsoegzhm' OPTION; do
    case "$OPTION" in
    d)


        sudo apt-get update
        sudo apt-get install -y gnupg2 git software-properties-common make
        echo "Adding corretto.key to pull/install amazon corretto-$CORRETTO_NUMBER_VERSION"
        wget -O- https://apt.corretto.aws/corretto.key | sudo apt-key add -
        sudo add-apt-repository 'deb https://apt.corretto.aws stable main'

        # vcpkg dependencies.
        sudo apt-get install -y zip unzip pkg-config

        sudo apt-get update
        sudo apt-get install -y java-$CORRETTO_NUMBER_VERSION-amazon-corretto-jdk

        echo "Verifying install..."
        JAVA_VERSION=$(java -version 2>&1)

        CORRETTO_DISPLAY_VERSION="Corretto-$CORRETTO_NUMBER_VERSION"
        if [[ "$JAVA_VERSION" == *"$CORRETTO_DISPLAY_VERSION"* ]]; then
            echo "Your system has and is using $CORRETTO_DISPLAY_VERSION"
        else
            echo "Updating your javac alternatives to use $CORRETTO_DISPLAY_VERSION"
            sudo update-alternatives --config javac
            sudo update-alternatives --config java
        fi

        export JAVA_HOME=$(readlink -f /usr/bin/java 2>&1 | sed "s:bin/java::")
        echo "Setting JAVA_HOME environment variables to ${JAVA_HOME}"
        export PATH=${JAVA_HOME}/bin:$PATH
        SHELL_STARTUP=${USER_HOME}/.bash_aliases
        touch $SHELL_STARTUP

        grep -q "JAVA_HOME" $SHELL_STARTUP
        # see if java home already setup
        if [[ $? -ne 0 ]]; then
            echo "Adding JAVA_HOME and path to $SHELL_STARTUP"
            echo "export JAVA_HOME=${JAVA_HOME}" >>$SHELL_STARTUP
            echo "export PATH=${JAVA_HOME}/bin:$PATH" >>$SHELL_STARTUP
        else
            echo "Your machine is already configured with ${JAVA_HOME}"
        fi

        # manual install maven@3.6
        MAVEN_VERSION="3.6.3"
        MAVEN_HOME="/usr/bin/mvn"
        echo "Installing Maven ${MAVEN_VERSION}"

        MAVEN_INSTALL=${USER_HOME}/maven/${MAVEN_VERSION}
        mkdir ${USER_HOME}/maven
        mkdir ${MAVEN_INSTALL}
        wget -O- https://archive.apache.org/dist/maven/maven-3/${MAVEN_VERSION}/binaries/apache-maven-${MAVEN_VERSION}-bin.tar.gz | tar -xzv -C ${MAVEN_INSTALL}
        sudo ln -s ${MAVEN_INSTALL}/apache-maven-${MAVEN_VERSION} ${MAVEN_HOME}

        export MAVEN_HOME=${MAVEN_HOME}
        export PATH=${MAVEN_HOME}/bin:${PATH}
        grep -q "MAVEN_HOME" $SHELL_STARTUP
        # see if mvn is configured already
        if [[ $? -ne 0 ]]; then
            echo "Adding MAVEN_HOME and path to $SHELL_STARTUP"
            echo "export MAVEN_HOME=${MAVEN_HOME}" >>$SHELL_STARTUP
            echo "export PATH=${MAVEN_HOME}/bin:$PATH" >>$SHELL_STARTUP
        else
            echo "Your machine is already configured with ${MAVEN_HOME}"
        fi

        # Clang
        CLANG_VERSION="12"
        echo "Installing Clang version $CLANG_VERSION..."
        wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key|sudo apt-key add -
        sudo apt-get update
        sudo apt-get install -y "clang-$CLANG_VERSION" "lldb-$CLANG_VERSION" "lld-$CLANG_VERSION"
        sudo ln -fsvT "/usr/bin/clang-$CLANG_VERSION" /usr/bin/clang
        sudo ln -fsvT "/usr/bin/clang++-$CLANG_VERSION" /usr/bin/clang++
        ;;

    i)
        IDEA_HOME=${USER_HOME}/jetbrains

        IDEA_INSTALL="${IDEA_HOME}/${IDEA_VERSION}"
        echo "Installing Jetbrains to ${IDEA_INSTALL}"
        mkdir ${IDEA_HOME}
        mkdir ${IDEA_INSTALL}

        wget -O- https://download.jetbrains.com/idea/ideaIC-${IDEA_VERSION}.tar.gz | tar --strip-components=1 -xzv -C ${IDEA_INSTALL}
        chmod +x ${IDEA_INSTALL}/bin/idea.sh
        sudo ln -s ${IDEA_INSTALL}/bin/idea.sh /usr/bin/idea
        ;;

    n)
        echo "Installing node $NODE_VERSION"
        sudo apt update
        wget -qO- https://deb.nodesource.com/setup_$NODE_VERSION.x | bash -
        sudo apt-get install -y nodejs npm
        ;;
    m)
        echo "Installing Mysql"
        sudo apt update
        ## fix odd issue with https://askubuntu.com/questions/1251816/mysqld-cant-read-dir-of-etc-mysql-conf-d-os-errno-2-no-such-file-or-dir
        sudo mkdir -p /etc/mysql/conf.d/
        echo "[mysql]" | sudo tee /etc/mysql/conf.d/mysql.cnf
        ## if you still see the error in askubuntu.com link above, you may need to update your /etc/mysql/my.cnf
        ## and comment out the line trying to load mariadb config: #!includedir /etc/mysql/mariadb.conf.d/
        sudo apt install -y mysql-common mysql-client mysql-server
        ;;

    s)
        echo "Installing packages for SDK build"
        sudo apt update
        sudo apt-get install -y curl ninja-build libssl-dev
        # Clang.
        CLANG_VERSION="12"
        echo "Installing Clang version $CLANG_VERSION..."
        wget https://apt.llvm.org/llvm.sh
        chmod +x ./llvm.sh
        sudo ./llvm.sh $CLANG_VERSION
        sudo ln -fsvT "/usr/bin/clang-$CLANG_VERSION" /usr/bin/clang
        sudo ln -fsvT "/usr/bin/clang++-$CLANG_VERSION" /usr/bin/clang++
        clang --version
        rm -f ./llvm.sh
        ;;

    o)
        echo "Installing docker"
        sudo apt install -y docker docker-compose
        ;;

    e)
        echo "Installing Typora"
        sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys BA300B7755AFCFAE
        wget -qO - https://typora.io/linux/public-key.asc | sudo apt-key add -

        # add Typora's repository
        sudo add-apt-repository 'deb https://typora.io/linux ./'
        sudo apt-get update

        # install typora
        sudo apt-get install -y typora

        # installing mmysqlworkbench is a 2 step process
        # grab the apt config urls first - this is problematic.  If you don't answer with your correct distro, you'll not be able to install until you remove
        # the configged software sources.  Why they don't just give you a public-key to add and use I have no idea.
        # this will likely not work great.
        wget -O ./mysql.config.deb https://dev.mysql.com/get/mysql-apt-config_0.8.17-1_all.deb
        sudo dpkg -i ./mysql.config.deb
        rm mysql.config.deb
        sudo apt update
        sudo apt install -y mysql-workbench-community
        ;;

    g)
        echo "git setup"
        sudo apt install -y git
        ## some of these git config things are also walked through in the start guide
        GIT_EMAIL="user email"
        GIT_USER_NAME="user name"
        GPG_REAL_NAME="gpg real name"
        GPG_TEMP_FILE="$HOME/tempgpgkeyparams"
        GPG_KEY_HASH="hash"
        echo "Setup your git user/email, please input your datas!:"
        read -p "git user email: " GIT_EMAIL
        read -p "git user name: " GIT_USER_NAME
        git config --global user.email ${GIT_EMAIL}
        git config --global user.name ${GIT_USER_NAME}
        git config --global mergetool.keepbackup false
        git config --global pull.rebase true
        echo "Next, let's generate your SSH keys..."
        ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa
        eval "$(ssh-agent -s)"
        ssh-add ~/.ssh/id_rsa
        echo "Now, let's generate your gpg keys..."
        read -p "enter your real name: " GPG_REAL_NAME
        echo "Generating your GPG keys.  Please wait..."
        cat >"$GPG_TEMP_FILE" <<EOF
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: $GPG_REAL_NAME
Name-Email: $GIT_EMAIL
Expire-Date: 1y
EOF
        gpg --batch --generate-key $GPG_TEMP_FILE
        rm $GPG_TEMP_FILE
        GPG_KEY_HASH=$(gpg --list-secret-keys --keyid-format SHORT | grep -B1 $GIT_EMAIL | grep -v $GIT_EMAIL | sed -e 's/^[[:space:]]*//')
        echo "Setting your git signing key..."
        git config --global user.signingkey $GPG_KEY_HASH
        git config --global commit.gpgsign true
        git config --global tag.gpgsign true
        echo ""
        printf "${GREEN}Attention! This is your GPG public key...${NC}"
        echo $GPG_KEY_HASH
        echo ""
        gpg --armor --export $GPG_KEY_HASH
        echo ""
        printf "${GREEN}Next is your SSH public key...${NC}"
        echo ""
        cat ~/.ssh/id_rsa.pub
        echo ""
        printf "${GREEN}Paste these into GitHub > Settings > SSH and GPG keys${NC}"
        echo ""
        ;;

    z)
        HUGO_VERSION="0.119.0"
        HUGO_PACKAGE_NAME="hugo_extended_${HUGO_VERSION}_linux-amd64.deb"
        echo ""
        echo "Installing hugo extended version ${HUGO_VERSION}. "
        echo "The name of the page will be 'hugo'."
        echo "It can be uninstalled with 'dpkg -r hugo'"
        echo ""
        wget -O "${HUGO_PACKAGE_NAME}" https://github.com/gohugoio/hugo/releases/download/v${HUGO_VERSION}/${HUGO_PACKAGE_NAME}
        dpkg --install ${HUGO_PACKAGE_NAME}
        rm ${HUGO_PACKAGE_NAME}
        ;;
    h)
        print_help
        ;;

    ?)
        echo "script usage: $(basename $0) [-d] [-i] [-n] [-o] [-e] [-g] [-z] [-h]" >&2
        print_help
        exit 1
        ;;
    esac
done

# from https://unix.stackexchange.com/questions/287190/execute-default-option-when-no-options-are-specified-in-getopts
if (($OPTIND == 1)); then
    print_help
else
    echo "Pragma Engineers!! - remember to see README_INFRA.md to follow the instructions under the heading 'Setup Git Signatures'"
fi

shift "$(($OPTIND - 1))"



