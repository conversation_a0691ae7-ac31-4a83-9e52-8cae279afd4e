#!/usr/bin/env bash
set -euo pipefail
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

if [[ "$OSTYPE" == "msys" ]]; then
    # msys AKA Git-Bash
    # Run a Windows batch script of the same name as this bash file
    exec cmd.exe //q //c "$SCRIPT_DIR/$(basename "${BASH_SOURCE[0]}").bat" "$@"
elif [[ "$OSTYPE" == "darwin"**	]]; then
	/bin/bash "$SCRIPT_DIR/macosx-setup.sh"
	exit 1
elif [[ "$OSTYPE" == "linux-gnu" ]]; then    
    #is this assuming bash is on path?
    bash "$SCRIPT_DIR/linux-setup.sh"
    exit 1
else
    echo >&2 "ERROR: setup not implemented for $OSTYPE"
    exit 1
fi
