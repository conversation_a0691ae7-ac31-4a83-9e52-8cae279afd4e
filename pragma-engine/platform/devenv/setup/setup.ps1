Set-StrictMode -Version 5

#######################################
# List of packages
#######################################
$intellij_community = @{
    name = "intellijidea-community"
    version = "2023.1.2"
}

$mysql = @{
    name = "mysql"
    version = "8.0.28"
}

$make = @{
    name = "make"
    version = "4.3"
}

$node = @{
    name = "nodejs.install"
    version = "16.13.0"
}

$maven = @{
    name = "maven"
    version = "3.6.3"
}

$corretto = @{
    name = "corretto17jdk"
    version = "17.0.12.71"
}

$postman = @{
    name = "postman"
    version = "latest"
}

$mysql_workbench = @{
    name = "mysql.workbench"
    version = "latest"
}

$unity_hub = @{
    name = "unity-hub"
    version = "latest"
}

$unity_editor = @{
    name = "unity"
    version = "2021.3.16"
}

$vs2019community = @{
    name = "visualstudio2019community"
    version = "latest"
}

$vs2019netcoretools = @{
    name = "visualstudio2019-workload-netcoretools"
    version = "latest"
}

$vs2019vctools = @{
    name = "visualstudio2019-workload-vctools"
    version = "latest"
}

$vs2019netcorebuildtools = @{
    name = "visualstudio2019-workload-netcorebuildtools"
    version = "latest"
}

$vs2019buildtools = @{
    name = "visualstudio2019buildtools"
    version = "latest"
}

$win10SDK = @{
    name = "windows-sdk-10.0"
    version = "latest"
}

$netfx471 = @{
    name = "netfx-4.7.1-devpack"
    version = "latest"
}

$firacode = @{
    name = "firacode"
    version = "latest"
}

$dockerdesktop = @{
    name = "docker-desktop"
    version = "latest"
}

$wsl2 = @{
    name = "wsl2"
    version = "latest"
}

$hugo = @{
    name = "hugo-extended"
    version = "0.119.0"
}

$zip = @{
    name = "zip"
    version = "latest"
}

$unzip = @{
    name = "unzip"
    version = "latest"
}

# These are the lists of packages that we can install
# This is also used to check for typos (when the user invokes this script)
$packages = @{
    default = @($make,$maven,$corretto,$dockerdesktop,$wsl2)
    tools=@($postman)
    node = @($node)
    extras = @($firacode)
    intellij = @($intellij_community)
    unity = @($unity_hub,$unity_editor)
    mysql = @($mysql)
    docker=@($dockerdesktop,$wsl2)
    visualstudio = @($vs2019community,$vs2019netcoretools,$vs2019vctools,$vs2019netcorebuildtools,$vs2019buildtools,$win10SDK,$netfx471)
    hugo = @($hugo,$make)
}

# This array is for all other flags that don't have an associated package list
# It's also used to check user input
$otherOptions = @("uninstall", "sshkey", "gpgkey", "gitkeys", "maxpath", "renewgpgkey")

#######################################
# Other important values
#######################################
$GPG_LOCATION = "$env:ProgramFiles\Git\usr\bin\gpg.exe"

#######################################
# Functions
#######################################
function Install-ChocoPackages {
    param (
        [Parameter(Mandatory=$true)]
        [hashtable[]]$packageList
    )

    foreach ($package in $packageList) {
        $name = $package["name"]
        $version = $package["version"]

        if ($version -eq "latest") {
            choco install -y $name
        } else {
            choco install -y $name --version="$version"
            choco pin add --name $name
        }
    }
}

function Uninstall-ChocoPackages {
    param (
        [Parameter(Mandatory=$true)]
        [hashtable[]]$packageList
    )

    foreach ($package in $packageList) {
        $name = $package["name"]
        $version = $package["version"]

        if ($version -eq "latest") {
            choco uninstall -y $name
        } else {
            choco pin remove --name $name
            choco uninstall -y $name --version="$version"
        }
    }
}

# helper function for Add-JdkPath
function Add-EnvPath {
    param(
        [Parameter(Mandatory=$true)]
        [string] $Path,

        [ValidateSet('Machine', 'User', 'Session')]
        [string] $Container = 'Session'
    )

    if ($Container -ne 'Session') {
        $containerMapping = @{
            Machine = [EnvironmentVariableTarget]::Machine
            User = [EnvironmentVariableTarget]::User
        }
        $containerType = $containerMapping[$Container]

        $persistedPaths = [Environment]::GetEnvironmentVariable('Path', $containerType) -split ';'
        if ($persistedPaths -notcontains $Path) {
            $persistedPaths = $persistedPaths + $Path | Where-Object { $_ }
            [Environment]::SetEnvironmentVariable('Path', $persistedPaths -join ';', $containerType)
        }
    }

    $envPaths = $env:Path -split ';'
    if ($envPaths -notcontains $Path) {
        $envPaths = $envPaths + $Path | Where-Object { $_ }
        $env:Path = $envPaths -join ';'
    }
}

function Add-JdkPath {
    $CorrettoPathVersion = $($corretto.version -replace '^(.+)\.([0-9]+)$','$1_$2')
    Add-EnvPath -Path "C:\Program Files\Amazon Corretto\jdk${CorrettoPathVersion}\bin" -Container 'User'
    [Environment]::SetEnvironmentVariable('JAVA_HOME', "C:\Program Files\Amazon Corretto\jdk${CorrettoPathVersion}", 'User')
    Write-Host -ForegroundColor Cyan "`n*`n* IMPORTANT: you need to restart your shell because PATH and JAVA_HOME were set`n*`n"
}

function Update-Path {
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
}

function New-SshKey {
    param (
        [Parameter(Mandatory=$true)]
        [string]$gitEmail,
        [Parameter(Mandatory=$true)]
        [string]$gitUserName,
        [Parameter(Mandatory=$true)]
        [string]$sshKeyPath
    )

    # Terminate if SSH key already exists - we don't want to overwrite an existing SSH key.
    if (Test-Path -Path $sshKeyPath) {
        Write-Host -ForegroundColor Red "It appears you already have an SSH key generated. No new SSH keys were generated."
        Write-Host "The key that will be outputted below is your preexisting SSH key."
        Write-Host "If you want to generate a new SSH key, you should manually run ssh-keygen or remove your existing keys first."
        Write-Host "Extra care may be required if you are working with multiple SSH keys."
        Write-Host "https://docs.github.com/en/github/authenticating-to-github/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent"
        return
    }

    # git configs
    git.exe config --global user.email $gitEmail
    git.exe config --global user.name $gitUserName
    git.exe config --global mergetool.keepbackup false
    git.exe config --global pull.rebase true

    # generate SSH keys
    Write-Host "Generating SSH keys..."
    $sshFolder = "$env:USERPROFILE\.ssh"
    if (!(Test-Path -Path $sshFolder)) {
        New-Item -Path $sshFolder -ItemType Directory
    }
    ssh-keygen.exe -t rsa -b 4096 -f $PATH_TO_DEFAULT_SSH_KEY
}

# Check to see gpg.exe exists (it's included with git-bash - we don't actually need gpg4win!)
function Test-GpgLocation {
    if (!(Test-Path -Path $GPG_LOCATION)) {
        Write-Host -ForegroundColor Red "Could not find gpg.exe. GPG keys were not generated."
        throw
    }
    Write-Host -ForegroundColor Green "gpg.exe found at $GPG_LOCATION"
}

# Displays the GPG public key corresponding to the input hash
function Show-GpgPublicKey {
    param (
        [Parameter(Mandatory=$true)]
        [string]$gpgHash
    )

    & $GPG_LOCATION --armor --export $gpgHash
}

# Generates a new GPG key pair
function New-GpgKey {
    param (
        [Parameter(Mandatory=$true)]
        [string]$gitEmail
    )

    Test-GpgLocation

    Write-Host "Let's generate your GPG keys."
    $GPG_TEMP_FILE = "$env:TEMP\TempGpgConfigsForKeyCreation"
    $gpgRealName = Read-Host "Enter your real name"
    $gpgConfigs =@"
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: $gpgRealName
Name-Email: $gitEmail
Expire-Date: 1y
"@
    $gpgConfigs | Out-File -FilePath $GPG_TEMP_FILE -Encoding ascii
    & $GPG_LOCATION --batch --generate-key $GPG_TEMP_FILE
    Remove-Item $GPG_TEMP_FILE # Temp gpg file deleted!
    $listSecretKeysOutput = & $GPG_LOCATION --list-secret-keys | Select-String -SimpleMatch $gitEmail -context 1,0
    $untrimmedGpgHash = $listSecretKeysOutput -split ("`r`n") | Select-Object -Index 0
    git.exe config --global user.signingkey $untrimmedGpgHash.trim()
    git.exe config --global commit.gpgsign true
    git.exe config --global tag.gpgsign true
    Write-Host -ForegroundColor Green "Below is your GPG public key...`n"
    Show-GpgPublicKey -gpgHash $untrimmedGpgHash.trim()
}

# Helps the user renew expired GPG keys. This process isn't fully automatable.
function Update-GpgKey {
    Test-GpgLocation
    $NumberExpiredGpgKeys = Get-NumberExpiredGpgKeys

    if ($NumberExpiredGpgKeys -eq 0) {
        Write-Host -ForegroundColor Green "You do not have any expired GPG keys. No action is needed."
    } elseif ($NumberExpiredGpgKeys -eq 1) {
        Write-Host "You have one expired GPG key:"
        & $GPG_LOCATION --list-key | Select-String -Pattern "expired:" -SimpleMatch -Context 0,2
        Show-GpgRenewalInstructions

        # Get GPG key hash and start interactive renewal
        $expiredKeyInfo = & $GPG_LOCATION --list-key | Select-String -Pattern "expired:" -SimpleMatch -Context 0,1
        $expiredGpgHash = $expiredKeyInfo -split ("`r?`n") | Select-Object -Index 1
        Invoke-GpgKeyRenewal -gpgHash $expiredGpgHash.trim()
    } else {
        Write-Host "You have $NumberExpiredGpgKeys expired GPG keys:"
        # & $GPG_LOCATION --list-key | Select-String -Pattern "expired:" -SimpleMatch -Context 0,3
        # Write-Host "This script doesn't support renewal if you have more than one expired key."
        # Write-Host "Renew your keys manually."

        $expiredList = & $GPG_LOCATION --list-key | Select-String -Pattern "expired:" -SimpleMatch -Context 0,3

        for ($i = 0; $i -lt $NumberExpiredGpgKeys; $i++) {
            Write-Host -BackgroundColor Yellow -ForegroundColor Red "[$i]:"
            Write-Host $expiredList[$i]
        }

        # Ask user which key to renew
        do {
            Write-Host -nonewline "Enter the number of the key you wish to renew: "
            $inputString = Read-Host
            $value = $inputString -as [uint16]
            $ok = $null -ne $value -and $value -ge 0 -and $value -lt $NumberExpiredGpgKeys
            if ( -not $ok ) { Write-Host -ForegroundColor Red "Invalid selection. Enter a number between 0 and $($NumberExpiredGpgKeys-1).`n" }
        }
        until ( $ok )

        Show-GpgRenewalInstructions

        # Get GPG key hash and start interactive renewal
        $expiredGpgHash = $expiredList[$value] -split ("`r?`n") | Select-Object -Index 1
        Invoke-GpgKeyRenewal -gpgHash $expiredGpgHash.trim()

        Write-Warning "Would you like to renew another key?" -WarningAction Inquire
        Update-GpgKey
    }
}

function Invoke-GpgKeyRenewal {
    param (
        [Parameter(Mandatory=$true)]
        [string]$gpgHash
    )

    & $GPG_LOCATION --edit-key $gpgHash expire
    Show-GpgPublicKey -gpgHash $gpgHash
    Write-Host -ForegroundColor Green "You may need to reenter the above GPG public key to GitHub."
    Write-Host "You can do so here: https://github.com/settings/keys"
}

function Show-GpgRenewalInstructions {
    Write-Host -ForegroundColor Red "`nGPG Key renewal cannot be fully automated!"
    Write-Host -ForegroundColor Red "Read the following instructions carefully."
    Write-Host @"

First, follow the GPG prompts to renew your public key.

You may then see a warning about your private key:
"WARNING: Your encryption subkey expires soon."

To renew your private key, enter the following commands at the GPG prompt.
Each line represents a separate command. Press the enter key after each command.

key 1
expire

Then follow the GPG prompts again.

To finish renewing your key, type "save" followed by the enter key.

"@

    Write-Warning "Read the preceding instructions then confirm to continue." -WarningAction Inquire
}

function Get-NumberExpiredGpgKeys {
    # Note: Select-String MatchInfo object has empty Matches property due to the -SimpleMatch flag
    $ExpiredGpgKeyNumber = & $GPG_LOCATION --list-key | Select-String -Pattern 'Expired:' -SimpleMatch | Measure-Object
    $ExpiredGpgKeyNumber.Count
}

function Show-Help {
    Write-Host @"
This script installs or uninstalls required and optional packages to support Pragma.
Additionally, SSH and GPG keypairs can also be generated (for use with e.g. GitHub).

To uninstall packages, use --uninstall.  No special flags are required to install packages.

The following package lists are supported:

--default           Installs make, maven, corretto, docker-desktop, and, wsl2
--intellij          Installs intellijidea-community
--tools             Installs postman
--docker            Installs docker-desktop and wsl2
--mysql             Installs and setups superuser for a local MySQL Db
--node              Installs nodejs
--unity             Installs unity-hub and unity
--visualstudio      Installs visualstudio2019community, -workload-netcoretools,
                    -workload-vctools, -workload-netcorebuildtools,
                    visualstudio2019buildtools, windows-sdk-10.0, and netfx-4.7.1-devpack
--hugo              Installs hugo-extended and make (a hugo dependency)
--extras            Installs firacode

--gitkeys           Generates SSH and GPG keypairs
--sshkey            Generates a new SSH key. Does not install prerequisites.
                    Will not generate a new key if one is already present.
--gpgkey            Generates a new GPG key. Does not install prerequisites.
--renewgpgkey       Assists with renewing expired GPG keys.

--all               Installs all available packages and assists with key generation.

You can mix and match flags, with the exception of the --all flag.

Examples:

To install the default and extras packages, use:
setup.cmd --default --extras

To uninstall the default and extras packages, use:
setup.cmd --uninstall --default --extras

Certain packages will have specific versions installed and pinned.
"@
}


#######################################
# Begin script
#######################################

if ($args -contains "--help" -or !$args) {
    Show-Help
    return
}

# Check to see if user wants to uninstall
$uninstallMode = $false

$Function:InstallOrUninstall = ${Function:Install-ChocoPackages}

if ($args -contains "--uninstall") {
    $uninstallMode = $true
    $Function:InstallOrUninstall = ${Function:Uninstall-ChocoPackages}
    if ($args.Count -eq 1) {
        Write-Host "You must specify specific package lists to uninstall.`n" -ForegroundColor red
        Show-Help
        return
    }

    # reset MAX_PATH settings to default if uninstalling
    Write-Host "Resetting MAX_PATH settings to Windows defaults."
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" ` -Name "LongPathsEnabled" -Value 0 -PropertyType DWORD -Force
    git.exe config --system --unset core.longpaths
}

# Install Chocolatey if necessary
if (-not (Get-Command "choco" -ErrorAction SilentlyContinue)) {
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
}

if ($args -contains "--all") {
    foreach ($packageList in $packages.GetEnumerator()) {
        InstallOrUninstall $packageList.Value
    }
    if (!$uninstallMode) {
        Add-JdkPath
    }
} else {
    # check to see if user made any typos and let them know
    $typos = [System.Collections.ArrayList]@()
    $installableOptions = $packages.Keys.ForEach('ToString')
    $validPackageList = [System.Collections.ArrayList]@()
    $validOptions = $installableOptions + $otherOptions
    foreach ($arg in $args) {
        $filteredArg = $arg -replace "--"
        if (!$validOptions.Contains($filteredArg)) {
            $typos.Add($arg) > $null # prevent Add method from printing index to console
        } else {
            if ($installableOptions.Contains($filteredArg)) {
                $validPackageList.Add($filteredArg) > $null
            }
        }
    }

    # warn the user when they make a typo, then quit - do not install or uninstall anything!
    if ($typos.Count -gt 0) {
        Write-Host "Encountered unexpected argument(s):" -ForegroundColor red
        Write-Host ($typos -join ", ")
        Write-Host "Double-check your argument(s) for typos then try again." -ForegroundColor red
        return
    }

    foreach ($packageList in $validPackageList) {
        InstallOrUninstall $packages[$packageList]
    }
}

if ($args -contains "--default" -and !$uninstallMode) {
    Add-JdkPath
}

Update-Path

if ($args -contains "--mysql" -or $args -contains "--all") {
    if ($uninstallMode) { # stop MySQL service and remove files
        Write-Host "un-installing MySQL..."
        net stop mysql
        sc.exe delete mysql
        Remove-Item -path c:\ProgramData\MySQL\ -recurse
        Remove-Item -path c:\tools\mysql -recurse
        Write-Host "Done."
    } else { # run initial setup for local db
        Write-Host "Setting MySQL superuser with password..."
		Push-Location
		Set-Location -Path "${PSScriptRoot}\..\local-db-utils"
		c:\tools\mysql\current\bin\mysql -u root -e "source initial-mysql-local-setup.sql"
		Pop-Location
		Write-Host "Done."
    }
}

# set up longpaths
if (($args -contains "--default" -or $args -contains "--all" -or $args -contains "--gitkeys" -or $args -contains "--maxpath") -and !$uninstallMode) {
    Write-Host "Configuring Windows to avoid MAX_PATH limitations."
    Write-Host "See https://docs.microsoft.com/en-us/windows/win32/fileio/maximum-file-path-limitation for more info."
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" ` -Name "LongPathsEnabled" -Value 1 -PropertyType DWORD -Force
    git.exe config --system core.longpaths true
}

# If user asked to do everything or to generate keys for git, then generate SSH and GPG keys.
if (!$uninstallMode) {
    $gitEmail = git.exe config --global user.email
    $gitUserName = git.exe config --global user.name
    $PATH_TO_DEFAULT_SSH_KEY = "$env:USERPROFILE\.ssh\id_rsa"

    if ($args -contains "--all" -or $args -contains "--gitkeys" -or $args -contains "--sshkey" -or $args -contains "--gpgkey") {
        if ([string]::IsNullOrEmpty($gitEmail)) {
            $gitEmail = Read-Host "Enter your git (GitHub) e-mail address"
        }
    }
    if ($args -contains "--all" -or $args -contains "--gitkeys" -or $args -contains "--sshkey") {
        if ([string]::IsNullOrEmpty($gitUserName)) {
            $gitUserName = Read-Host "Enter your real name"
        }
        New-SshKey -gitEmail $gitEmail -gitUserName $gitUserName -sshKeyPath $PATH_TO_DEFAULT_SSH_KEY
    }
    if ($args -contains "--all" -or $args -contains "--gitkeys" -or $args -contains "--gpgkey") {
        New-GpgKey -gitEmail $gitEmail
    }
    if ($args -contains "--all" -or $args -contains "--gitkeys" -or $args -contains "--sshkey") {
        Write-Host -ForegroundColor Green "Below is your SSH public key...`n"
        Get-Content "${PATH_TO_DEFAULT_SSH_KEY}.pub"
    }
    if ($args -contains "--all" -or $args -contains "--gitkeys" -or $args -contains "--sshkey" -or $args -contains "--gpgkey") {
        Write-Host "`nPaste the above key(s) into the 'SSH and GPG Keys' section in GitHub."
        Write-Host "You can do so here: https://github.com/settings/keys"
    }
}

if (($args -contains "--all" -or $args -contains "--default") -and !$uninstallMode) {
    Write-Host "Please reboot your machine, then follow the instructions that will appear to set up WSL2."
}

if (($args -contains "--renewgpgkey") -and !$uninstallMode) {
    Update-GpgKey
}
