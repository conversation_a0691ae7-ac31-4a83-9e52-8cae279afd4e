#!/bin/bash

echo "Removing gnupg2"
sudo apt remove gnupg2

CORRETTO_NUMBER_VERSION=17
IDEA_VERSION="2023.1.2"

USER_HOME=$HOME
rm ~/.bash_aliases
sudo dpkg --remove java-$CORRETTO_NUMBER_VERSION-amazon-corretto-jdk
MAVEN_VERSION="3.6.3"
MAVEN_INSTALL=${USER_HOME}/maven
echo "Removing Maven ${MAVEN_INSTALL}"
rm -rf ${MAVEN_INSTALL}
unset JAVA_HOME
unset MAVEN_HOME
sudo rm -rf /usr/bin/mvn

IDEA_HOME=${USER_HOME}/jetbrains
IDEA_INSTALL="${IDEA_HOME}/${IDEA_VERSION}"
echo "Removing Jetbrains "
rm -rf ${IDEA_HOME}
sudo rm -f /usr/bin/idea

echo "Removing NodeJs "
sudo apt remove -y nodejs

echo "Removing typeora"
sudo apt remove -y typora

echo "Removing postman"
POSTMAN_HOME=${USER_HOME}/postman
rm -rf ${POSTMAN_HOME}

echo "Removing postman"
flatpak uninstall -y --noninteractive com.getpostman.Postman

echo "Removing jq"
sudo apt remove -y jq

echo "Removing Use Together (not really, don't want to do that right now)"
#sudo apt remove -y drovio

echo "Removing mysql workbench"
sudo apt remove -y mysql-apt-config
sudo apt remove -y mysql-workbench-community

echo "Removing mysql server, client, etc"
sudo apt remove -y mysql-common mysql-client mysql-server

echo "Removing flatpak install flathub com.unity.UnityHub"
flatpak uninstall -y --noninteractive com.unity.UnityHub

echo "Removing docker"
sudo apt remove docker docker-compose

echo "Removing gpgv2"
sudo apt remove gpgv2

echo "Removing hugo"
sudo dpkg -r hugo