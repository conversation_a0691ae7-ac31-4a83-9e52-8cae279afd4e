{"id": "7d53bb5f-a8a1-4473-a7db-0cf262924ff9", "name": "testshard - 2022.10.24", "values": [{"key": "host", "value": "test.internal.demo.pragplat.net", "enabled": true}, {"key": "protocol", "value": "https", "enabled": true}, {"key": "GamePlayerGW", "value": "{{host}}:10000", "enabled": true}, {"key": "GamePartnerGW", "value": "{{host}}:10100", "enabled": true}, {"key": "GameOperatorGW", "value": "{{host}}:10200", "enabled": true}, {"key": "SocialPlayerGW", "value": "{{host}}:11000", "enabled": true}, {"key": "SocialPartnerGW", "value": "{{host}}:11100", "enabled": true}, {"key": "SocialOperatorGW", "value": "{{host}}:11200", "enabled": true}, {"key": "test01PlayerId", "value": "", "enabled": true}, {"key": "discord_access_token", "value": "", "enabled": true}, {"key": "discordPlayerId", "value": "", "enabled": true}, {"key": "gameId", "value": "", "enabled": true}, {"key": "gameShardId", "value": "00000000-0000-0000-0000-000000000001", "enabled": true}, {"key": "test01PlayerName", "value": "", "enabled": true}, {"key": "test01PlayerNameDiscriminator", "value": "", "enabled": true}, {"key": "upgradableItemId", "value": "", "enabled": true}, {"key": "test01PragmaSocialId", "value": "", "enabled": true}, {"key": "test01PragmaPlayerGameToken", "value": "", "enabled": true}, {"key": "test01PragmaPlayerSocialToken", "value": "", "enabled": true}, {"key": "test01PragmaOperatorGameToken", "value": "", "enabled": true}, {"key": "test01PragmaOperatorSocialToken", "value": "", "enabled": true}, {"key": "test01PragmaPartnerGameToken", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "enabled": true}, {"key": "craftableItemId", "value": "", "enabled": true}, {"key": "test02PlayerId", "value": "", "enabled": true}, {"key": "test02PragmaSocialId", "value": "", "enabled": true}, {"key": "test02PragmaPlayerGameToken", "value": "", "enabled": true}, {"key": "test02PragmaPlayerSocialToken", "value": "", "enabled": true}, {"key": "createdGameId", "value": "", "enabled": true}, {"key": "createdGameShardId", "value": "", "enabled": true}, {"key": "websocketProtocol", "value": "wss", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2022-10-24T17:22:53.832Z", "_postman_exported_using": "Postman/10.0.35"}