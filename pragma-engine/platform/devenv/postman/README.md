## PostMan collection
This is an export of a [Postman](https://www.postman.com/) collection and environment to roughly document some Pragma endpoints

### Install
* download Postman
* import BOTH the collection and the environment file

### Usage
* With a local backend running on default ports 
* Start by running (once a day) `Token Refresh -> test01`
  * this will update your environment with a live token
* navigate to the call you want to make and hit `send`

### Update
If you add calls/modify something that you think is relevant with others, use the export features within Postman to re-export the json files and save them on top of the ones in the repo.

### Backups
The Team shared Postman Collections and Environments are backed up on each Buildkite build.